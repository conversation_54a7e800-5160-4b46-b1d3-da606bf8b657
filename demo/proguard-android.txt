-keepattributes Signature,SourceFile,LineNumberTable
-keepattributes *Annotation*
-keeppackagenames
-ignorewarnings
-dontwarn android.support.v4.**,**CompatHoneycomb,com.tenpay.android.**
-optimizations !class/unboxing/enum,!code/simplification/arithmetic

######  add for roboguice2.0 begin ##########
-keep class com.google.inject.**{
    *;
}
-keep public class roboguice.**{
    *;
}
-keep class * extends com.google.inject.Module{
    *;
}
-keepclassmembers class * {
    @javax.inject.Inject <init>(...);
    @com.google.inject.Inject <init>(...);
    @javax.inject.Inject <fields>;
    @com.google.inject.Inject <fields>;
    <init>();
}
######  add for roboguice2.0 end ##########

-keep public class com.actionbarsherlock.** { *; }

-keep class com.meituan.android.common.analyse.mtanalyse.dao.* {
    *;
}

-keep class com.meituan.android.common.analyse.mtanalyse.bean.* {
    *;
}

-keep class com.meituan.android.base.analyse.MeituanAnalyzerFactory$*{
    <init>();
    *;
}

-keep class com.sankuai.meituan.model.dao.** {
    *;
}

-keep class com.sankuai.model.**{
    *;
}

#protobuf
-keep class com.meituan.service.mobile.protobuf.group.api.** {
    *;
}


-keep class sun.misc.Unsafe { *; }
-keep class com.sankuai.meituan.model.JsonBean
-keep @com.sankuai.meituan.model.JsonBean class * {
    *;
}
-keep @com.sankuai.meituan.model.JsonBean enum * {
    *;
}

-keep class com.sankuai.model.JsonBean
-keep @com.sankuai.model.JsonBean class * {
    *;
}
-keep @com.sankuai.model.JsonBean enum * {
    *;
}

-keep class com.flurry.**{
    *;
}

-keep class com.alipay.android.app.IAlixPay{*;}
-keep class com.alipay.android.app.IAlixPay$Stub{*;}
-keep class com.alipay.android.app.IRemoteServiceCallback{*;}
-keep class com.alipay.android.app.IRemoteServiceCallback$Stub{*;}
-keep class com.alipay.android.app.pay.IAlixPay{*;}
-keep class com.alipay.android.app.pay.IAlixPay$Stub{*;}
-keep class com.alipay.android.app.pay.IAlixPayCallback{*;}
-keep class com.alipay.android.app.pay.IAlixPayCallback$Stub{*;}
-keep class com.alipay.android.app.script.**{*;}
-keep class com.alipay.android.app.pay.PayTask{*;}
-keep class com.alipay.android.app.pay.PayTask$OnPayListener{*;}
-keep class com.alipay.android.app.pay.CheckAccountTask{*;}
-keep class com.alipay.android.app.pay.CheckAccountTask$OnCheckListener{*;}
-keep class com.alipay.android.app.encrypt.**{*;}

-keep class com.alipay.mobile.command.*
-keep class android.webkit.*
-keep class com.alipay.mobilesecuritysdk.*
-keep class com.alipay.android.app.*
-keep class com.alipay.android.lib.*
-keep class com.alipay.android.mini.*
-keep class com.alipay.html.*
-keep class org.ccil.cowan.tagsoup.*
-keep class com.squareup.picasso.*
-keep class com.ut.*
-keep class com.alipay.test.ui.core.*
-keep class com.alipay.trobot.external.*
-keep class org.rome.android.ipp.*

-keep class com.alipay.sdk.app.PayTask{ public *;}
-keep class com.alipay.sdk.app.AuthTask{ public *;}
-keep class com.alipay.sdk.app.H5PayCallback {
    <fields>;
    <methods>;
}
-keep class com.alipay.android.phone.mrpc.core.** { *; }
-keep class com.alipay.apmobilesecuritysdk.** { *; }
-keep class com.alipay.mobile.framework.service.annotation.** { *; }
-keep class com.alipay.mobilesecuritysdk.face.** { *; }
-keep class com.alipay.tscenter.biz.rpc.** { *; }
-keep class org.json.alipay.** { *; }
-keep class com.alipay.tscenter.** { *; }
-keep class com.ta.utdid2.** { *;}
-keep class com.ut.device.** { *;}

-keep class com.umpay.**{*;}
-keep class com.tencent.mm.sdk.**{*;}

-keep class **.bean.** {
    *;
}

-keep class com.sankuai.log.dao.**{
    *;
}
-keep class com.amap.api.**  {*;}
-keep class com.autonavi.**  {*;}
-keep class com.a.a.**  {*;}

-dontwarn com.tenpay.android.**
-keep class com.tenpay.android.**{*;}

-keep public class * extends android.support.v4.app.Fragment
-keep class android.support.v4.view.ViewPager.** {*;}
-keep class * extends android.support.v4.view.ViewPager{*;}

-keep class **.R$* {*;}
-keep class com.qihoo360.union.** {*;}

-keep class com.sankuai.pay.**{
    *;
}
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

-keepclassmembers class ** {
    @com.squareup.otto.Subscribe public *;
    @com.squareup.otto.Produce public *;
}


-keep class com.meituan.android.common.**{*;}

-keep class com.meituan.android.common.fingerprint.info.**{
    *;
}

-keep class com.tencent.open.TDialog$*
-keep class com.tencent.open.TDialog$* {*;}
-keep class com.tencent.open.PKDialog
-keep class com.tencent.open.PKDialog {*;}
-keep class com.tencent.open.PKDialog$*
-keep class com.tencent.open.PKDialog$* {*;}
-keep class com.meituan.android.base.util.MapUtils {*;}
-keep class com.meituan.android.base.deal.selector.ICategoryAdapter {*;}
-keep class com.meituan.android.base.locate.**{*;}
-keep class com.meituan.android.common.locate.**{*;}
-keep class com.meituan.android.base.util.DialogUtils.*
-keep class com.sankuai.common.utils.DialogUtils{*;}
-keep class com.meituan.android.base.util.DialogUtils {*;}
-keep class com.google.gson.**{*;}
-keep class com.sankuai.common.utils.Utils {*;}
-keep class com.meituan.android.base.roboguice.**{*;}
-keep class com.meituan.android.base.ui.**{*;}
-keep class com.meituan.android.base.ICityController*{*;}
-keep class com.meituan.android.base.CategoryAdapter*{*;}
-keep class com.meituan.android.base.BaseConfig{*;}
-keep class android.support.v4.app.**{*;}
-keep class android.support.v4.content.**{*;}
-keep class android.support.v7.app.**{*;}
-keep class android.support.v7.content.**{*;}
-keep class com.sankuai.meituan.model.datarequest.DefaultRequestFactory {*;}
-keep class com.sankuai.meituan.model.datarequest.area.AreaListRequest
-keep class com.sankuai.meituan.model.datarequest.poi.map.PoiMapRequest{*;}
-keep class com.sankuai.meituan.model.CollectionUtils {*;}
-keep class com.sankuai.meituan.model.datarequest.Request*{*;}
-keep class com.sankuai.meituan.model.datarequest.poi.**{*;}
-keep class com.sankuai.meituan.model.datarequest.ComboRequest*{*;}
-keep class com.sankuai.meituan.model.datarequest.category.**{*;}
-keep class com.sankuai.meituan.model.datarequest.around.**{*;}
-keep class com.sankuai.meituan.model.Consts*{*;}
-keep class com.sankuai.meituan.model.GsonProvider*{*;}
-keep class org.apache.commons.lang3.ArrayUtils*{*;}
-keep class com.meituan.android.base.util.AddressUtils.**{*;}
-keep class com.meituan.android.base.task**{*;}
-keep class com.meituan.android.base.util.DateTimeUtils*{*;}
-keep class com.meituan.android.base.util.UriUtils{
    android.net.Uri BASE_URI;
    android.content.UriMatcher uriMatcher;
    android.net.Uri$Builder uriBuilder();
}
-keep class com.meituan.android.base.util.AnalyseUtils{
    public void mge(java.lang.String[]);
}
-keep class com.meituan.android.base.util.DistanceFormat{
    public float getDistance(double,double,android.location.Location);
}

-keep class com.sankuai.meituan.model.account.UserCenter{
    public boolean isLogin();
    public long getUserId();
}
-keep class com.meituan.android.base.util.UriUtils*{*;}
-keep class com.meituan.android.pay.utils.JsonBean
-keep @com.meituan.android.pay.utils.JsonBean class * {
    *;
}
-keep @com.meituan.android.pay.utils.JsonBean enum * {
    *;
}

-keep class com.meituan.service.** {
    *;
}

-keep class com.sankuai.meituan.InitAppDelegator { *; }
-keep class com.meituan.android.takeout.library.delegate.AppApplicationDelegate { *; }
-keep class com.meituan.android.takeout.base.TakeoutApplicationDelegate { *; }
-keep class com.meituan.android.takeout.library.db.dao.* {*;}
-keep class com.meituan.android.takeout.library.model.** { *; }
-keep class com.iflytek.**{*;}

### Passport SDK ###
-dontwarn retrofit.**
-keepattributes Signature
-keepattributes Exceptions
-keep class retrofit.** { *; }
-keep class com.meituan.passport.pojo.* { *; }
-keep class com.meituan.passport.api.* { *; }
### Passport SDK END ###

##########################dynamic load dex added#############################

-keep class com.alipay.** {*;}
-keep class com.amap.** {*;}
-keep class com.autonavi.**  {*;}
-keep class com.google.zxing {*;}
-keep class com.iflytek.** {*;}
-keep class com.tencent.** {*;}
-keep class com.tenpay.** {*;}
-keep class com.umpay.** {*;}
-keep class com.weibo.** {*;}
-keep class com.sina.** {*;}
-keep class net.simonvt.numberpicker.** {*;}
-keep class uk.co.senab.photoview.** {*;}

-keep class com.meituan.android.shopping.** {*;}
-keep class com.meituan.android.travel.** {*;}
-keep class com.meituan.android.movie.** {*;}
-keep class com.meituan.android.hotel.** {*;}
-keep class com.meituan.android.apollo.** {*;}
-keep class com.meituan.android.takeout.** {*;}
-keep class com.meituan.android.beauty.** {*;}
-keep class com.meituan.android.group.** {*;}

-keep class com.meituan.android.pay.** {*;}
-keep class com.sankuai.pay.** {*;}
-keep class com.meituan.android.cashier.** {*;}

-keep class com.sankuai.aimeituan.MapLib.** {*;}
-keep class com.sankuai.meituan.zxing.** {*;}
-keep class com.sankuai.meituan.review.** {*;}
-keep class com.sankuai.meituan.share.** {*;}
-keep class com.sankuai.meituan.setting.otherapps.** {*;}
-keep class com.sankuai.meituan.setting.feedback.** {*;}
-keep class com.sankuai.meituan.setting.diagnostic.** {*;}
-keep class com.sankuai.meituan.poi.reporterror.** {*;}
-keep class com.sankuai.meituan.poi.review.** {*;}

-keep class com.sankuai.meituan.comment.** {*;}
-keep class com.sankuai.meituan.around.** {*;}
-keep class com.sankuai.meituan.refund.** {*;}
-keep class com.sankuai.meituan.plugin.** {*;}
-keep class com.sankuai.meituan.buy.** {*;}
-keep class com.sankuai.meituan.dev.** {*;}
-keep class com.sankuai.meituan.common.map.** {*;}
-keep class com.sankuai.meituan.test.** {*;}
-keep class com.sankuai.meituan.topic.** {*;}
-keep class com.sankuai.meituan.coupon.** {*;}
-keep class com.sankuai.meituan.booking.** {*;}
-keep class com.sankuai.meituan.express.** {*;}
-keep class com.sankuai.meituan.discover.** {*;}
-keep class com.sankuai.meituan.order.** {*;}
-keep class com.sankuai.meituan.pay.** {*;}
-keep class com.sankuai.meituan.search.** {*;}
-keep class com.meituan.android.movie.seatorder.** {*;}
-keep class com.sankuai.meituan.tour.** {*;}
-keep class com.sankuai.meituan.voucher.** {*;}
-keep class com.sankuai.meituan.deal.selector.AreaAdapter {*;}
-keep class com.sankuai.meituan.user.favorite.FavoriteController {*;}
-keep class com.sankuai.meituan.runtime.InstrumentationHook {public *;}
-keep class com.sankuai.meituan.runtime.InstrumentationHookBase {public *;}
-keep class com.sankuai.meituan.runtime.InstrumentationHookHoneycomb {public *;}

###### keep pay sdk ######
#camera
-keep class com.meituan.android.indentifycard.** {*;}

#jsonbean
-keep class com.meituan.android.paycommon.lib.utils.JsonBean
-keep @com.meituan.android.paycommon.lib.utils.JsonBean class * {
    *;
}
-keep @com.meituan.android.paycommon.lib.utils.JsonBean enum * {
    *;
}
-keep public class * extends com.sankuai.mesh.core.MeshBaseService{*;}
###### keep pay sdk end ######
-keep class com.meituan.android.common.mtguard.NBridge {*;}
-keep class com.meituan.android.common.dfingerprint.v3.DFPTest {*;}
-keep class com.meituan.uuid.**{*;}
-keep class com.meituan.android.common.unionid.**{*;}
-keep class com.sankuai.meituan.dev.horn.HornDeveloperKit{ *;}