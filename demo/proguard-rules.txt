# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
#
# Starting with version 2.2 of the Android plugin for Gradle, these files are no longer used. Newer
# versions are distributed with the plugin and unpacked at build time. Files in this directory are
# no longer maintained.
#
# Optimizations: If you don't want to optimize, use the
# proguard-android.txt configuration file instead of this one, which
# turns off the optimization flags.  Adding optimization introduces
# certain risks, since for example not all optimizations performed by
# ProGuard works on all versions of Dalvik.  The following flags turn
# off various optimizations known to have issues, but the list may not
# be complete or up to date. (The "arithmetic" optimization can be
# used if you are only targeting Android 2.0 or later.)  Make sure you
# test thoroughly if you go this route.
-allowaccessmodification


-keepattributes Signature,!SourceFile,LineNumberTable
# 防止内部类被混淆，无法访问
-keepattributes Exceptions,InnerClasses,Deprecated,*Annotation*,EnclosingMethod

-keeppackagenames
-dontwarn android.support.v4.**,**CompatHoneycomb,com.tenpay.android.**

# The remainder of this file is identical to the non-optimized version
# of the Proguard configuration file (except that the other file has
# flags to turn off optimization).

-dontusemixedcaseclassnames
-verbose

-keep class com.meituan.android.paladin.Paladin{*;}

-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService

#注意！！！不能开启下面的useuniqueclassmembernames规则
#会增大keep的范围，并且可能导致getter-setter-inline等优化插件出错！！
#详见下面链接
#https://mp.weixin.qq.com/s?__biz=MzUyMDAxMjQ3Ng==&mid=2247499277&idx=1&sn=f000399c0dd17b4aadb98eada12fe08c
#-useuniqueclassmembernames

# keep setters in Views so that animations can still work.
# see http://proguard.sourceforge.net/manual/examples.html#beans
-keepclassmembers public class * extends android.view.View {
   void set*(***);
   *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick
-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}

# For enumeration classes, see http://proguard.sourceforge.net/manual/examples.html#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator CREATOR;
}

# The support library contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version.  We know about them, and they are safe.
-dontwarn android.support.**

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

######  add for roboguice2.0 begin ##########
-keep class com.google.inject.name.Named {
    *;
}
-keep class com.google.inject.Inject {
    *;
}
-keep class com.google.inject.Provides {
    *;
}
-keep class com.google.inject.Singleton {
    *;
}
-keep class * extends com.google.inject.Module{
    *;
}
-keepclassmembers class * {
    @javax.inject.Inject <init>(...);
    @com.google.inject.Inject <init>(...);
    @javax.inject.Inject <fields>;
    @com.google.inject.Inject <fields>;
    <init>();
}
######  add for roboguice2.0 end ##########

-keep public class com.actionbarsherlock.** { *; }

-keep class com.meituan.android.common.analyse.mtanalyse.dao.* {
    <fields>;
}

-keep class com.meituan.android.common.analyse.mtanalyse.bean.* {
    *;
}

-keep class com.meituan.android.base.analyse.MeituanAnalyzerFactory$*{
    <init>();
    *;
}

-keep class com.sankuai.meituan.model.dao.** {
    <fields>;
}

-keep class com.sankuai.model.**{
    <fields>;
}

#protobuf
-keep class com.meituan.service.mobile.protobuf.group.api.** {
    *;
}

-keep class * implements com.meituan.android.pt.group.retrofit.ConvertData{
    public *** convert(...);
}

-keep class * implements com.meituan.android.hotel.terminus.retrofit.base.ConverterData{
    public *** convertData(...);
}

-keep class * implements com.meituan.android.flight.retrofit.ConvertData{
    public *** convert(...);
}

-keep class * implements com.meituan.android.train.retrofit.ConvertData{
    public *** convert(...);
}

-keep class * implements com.sankuai.meituan.skeleton.net.retrofit.ConvertData{
    public *** convert(...);
}

-keep class * implements com.meituan.android.base.gson.ConverterData{
    public *** convertData(...);
}

-keep class * implements com.meituan.android.food.retrofit.base.ConverterData{
    public *** convertData(...);
}

-keep class * implements com.meituan.android.trip.base.gson.ConverterData{
    public *** convertData(...);
}

-keep class sun.misc.Unsafe { *; }

-keep class com.sankuai.model.NoProguard
-keep @com.sankuai.model.NoProguard class * {
    <fields>;
}

-keep class com.sankuai.meituan.wearableprotocol.WearableProtocolService {*;}
-keep class com.google.android.gms.wearable.WearableListenerService {*;}

-keep class com.sankuai.meituan.wearableprotocol.annotation.NoProguard
-keep @com.sankuai.meituan.wearableprotocol.annotation.NoProguard class * {
    <fields>;
}

-keep @com.sankuai.model.NoProguard enum * {
    *;
}

-keepclassmembers class com.meituan.android.hotel.** {
    @com.meituan.android.hotel.terminus.invoke.InvokeMethod <methods>;
}

-keepclassmembers class com.meituan.android.zufang.** {
    @com.meituan.android.hotel.terminus.invoke.InvokeMethod <methods>;
}

-keepclassmembers class com.meituan.android.travel.** {
    @com.meituan.android.travel.hoteltrip.common.intentkey.InvokeMethod <methods>;
}

-keep class com.sankuai.model.JsonBean
-keep @com.sankuai.model.JsonBean class * {
    <fields>;
}
-keep @com.sankuai.model.JsonBean enum * {
    *;
}
-keep @com.meituan.android.hbnbridge.HbnbJsonBean class * {
    *;
}
-keep class com.flurry.**{
    *;
}

# keep酒旅自定义view类名
-keep class com.meituan.android.travel.** extends android.view.View
-keep class com.meituan.android.hotel.** extends android.view.View
-keep class com.meituan.android.flight.** extends android.view.View
-keep class com.meituan.android.train.** extends android.view.View
-keep class com.meituan.android.overseahotel.** extends android.view.View

-keep class com.alipay.android.app.IAlixPay{*;}
-keep class com.alipay.android.app.IAlixPay$Stub{*;}
-keep class com.alipay.android.app.IRemoteServiceCallback{*;}
-keep class com.alipay.android.app.IRemoteServiceCallback$Stub{*;}
-keep class com.alipay.android.app.pay.IAlixPay{*;}
-keep class com.alipay.android.app.pay.IAlixPay$Stub{*;}
-keep class com.alipay.android.app.pay.IAlixPayCallback{*;}
-keep class com.alipay.android.app.pay.IAlixPayCallback$Stub{*;}
-keep class com.alipay.android.app.script.**{*;}
-keep class com.alipay.android.app.pay.PayTask{*;}
-keep class com.alipay.android.app.pay.PayTask$OnPayListener{*;}
-keep class com.alipay.android.app.pay.CheckAccountTask{*;}
-keep class com.alipay.android.app.pay.CheckAccountTask$OnCheckListener{*;}
-keep class com.alipay.android.app.encrypt.**{*;}

-keep class com.alipay.sdk.app.PayTask{ public *;}
-keep class com.alipay.sdk.app.AuthTask{ public *;}
-keep class com.alipay.sdk.app.H5PayCallback {
    <fields>;
    <methods>;
}

-keep class com.alipay.android.phone.mrpc.core.** { *; }
-keep class com.alipay.apmobilesecuritysdk.** { *; }
-keep class com.alipay.mobile.framework.service.annotation.** { *; }
-keep class com.alipay.mobilesecuritysdk.face.** { *; }
-keep class com.alipay.tscenter.biz.rpc.** { *; }
-keep class org.json.alipay.** { *; }
-keep class com.alipay.tscenter.** { *; }
-keep class com.ta.utdid2.** { *;}
-keep class com.ut.device.** { *;}

-keep class com.alipay.mobile.command.*
-keep class android.webkit.*
-keep class com.alipay.mobilesecuritysdk.*
-keep class com.alipay.android.app.*
-keep class com.alipay.android.lib.*
-keep class com.alipay.android.mini.*
-keep class com.alipay.html.*
-keep class org.ccil.cowan.tagsoup.*
-keep class com.squareup.picasso.*
-keep class com.ut.*
-keep class com.alipay.test.ui.core.*
-keep class com.alipay.trobot.external.*
-keep class org.rome.android.ipp.*

-keep class com.umpay.**{*;}
-keep class com.tencent.mm.sdk.**{*;}
-keepclasseswithmembers class * extends com.meiutan.android.library.mvvm.AndroidViewModel{
 public <init>(android.app.Application);
 <fields>;

}
-keepclasseswithmembers class * extends com.meiutan.android.library.mvvm.ViewModel{
 public <init>();
 <fields>;

}
-keepclasseswithmembers class * {
 @com.meiutan.android.library.mvvm.utils.Bind <methods>;
 <fields>;

}



-keep class **.bean.** {
    <fields>;
}

-keep class com.sankuai.log.dao.**{
    <fields>;
}
-keep class com.amap.api.**  {*;}
-keep class com.autonavi.**  {*;}
-keep class com.a.a.**  {*;}

-dontwarn com.tenpay.android.**
-keep class com.tenpay.android.**{*;}

-keep public class * extends android.support.v4.app.Fragment
-keep class android.support.v4.view.ViewPager.** {*;}
-keep class * extends android.support.v4.view.ViewPager{*;}
-keep class android.support.v4.widget.NestedScrollView{*;}

-keep class com.qihoo360.union.** {*;}

-keep class com.sankuai.pay.**{
    *;
}
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

-keepclassmembers class ** {
    @com.squareup.otto.Subscribe public *;
    @com.squareup.otto.Produce public *;
}

-keep class com.dianping.dynamicbridge.**{*;}
-keep class com.dianping.picassomodule.**{*;}
-keep class com.dianping.base.picasso.**{*;}

-keep class com.meituan.android.common.unionid.**{*;}
-keep class com.meituan.android.common.candy.**{*;}
-keep class com.meituan.android.common.datacollection.**{*;}
-keep class com.meituan.android.common.mtguard.**{*;}
-keep class com.meituan.android.common.channel.**{*;}
-keep class com.meituan.android.common.rootdetection.**{*;}
-keep class com.meituan.android.common.emulatordetection.**{*;}
-keep class com.meituan.android.common.encryption.**{*;}
-keep class com.meituan.android.common.utils.**{*;}
-keep class com.meituan.android.common.touchtracer.**{*;}
-keep class com.meituan.android.common.fingerprint.info.**{
    *;
}
-keep class com.meituan.android.common.fingerprint.**{*;}
-keep class com.meituan.android.common.runtimestatus.**{*;}
-keep class com.meituan.android.common.pmsprotector.PMSProtector
-keep class com.meituan.android.common.pmsprotector.PMSProtector{*;}
-keep class com.meituan.android.common.pmsprotector.PMSProtector$*
-keep class com.meituan.android.common.pmsprotector.PMSProtector$*{*;}

-keep class com.tencent.open.TDialog$*
-keep class com.tencent.open.TDialog$* {*;}
-keep class com.tencent.open.PKDialog
-keep class com.tencent.open.PKDialog {*;}
-keep class com.tencent.open.PKDialog$*
-keep class com.tencent.open.PKDialog$* {*;}
-keep class com.meituan.android.base.deal.selector.ICategoryAdapter {*;}
-keep class com.meituan.android.base.locate.**{*;}
-keep class com.sankuai.common.utils.DialogUtils{*;}
-keep class com.google.gson.**{*;}
-keep class com.sankuai.common.utils.Utils {*;}
-keep class com.meituan.android.base.roboguice.**{*;}
-keep class com.meituan.android.base.ui.**{*;}
-keep class com.meituan.android.base.ICityController*{*;}
-keep class com.meituan.android.widget.EditTextWithClearButton*{*;}
-keep class com.meituan.android.base.CategoryAdapter*{*;}
-keep class com.meituan.android.base.BaseConfig{*;}
-keep class android.support.v4.app.Fragment{
    *;
}
-keep class android.support.v4.app.FragmentActivity{
    *;
}
-keep class com.sankuai.meituan.model.datarequest.DefaultRequestFactory {*;}
-keep class com.sankuai.meituan.model.datarequest.area.AreaListRequest
-keep class com.sankuai.meituan.model.datarequest.poi.map.PoiMapRequest{*;}
-keep class com.sankuai.meituan.model.CollectionUtils {*;}
-keep class com.sankuai.meituan.model.datarequest.Request*{*;}
-keep class com.sankuai.meituan.model.datarequest.poi.**{*;}
-keep class com.sankuai.meituan.model.datarequest.ComboRequest*{*;}
-keep class com.sankuai.meituan.model.datarequest.category.**{*;}
-keep class com.sankuai.meituan.model.datarequest.around.**{*;}
-keep class com.sankuai.meituan.trip.model.datarequest.area.AreaListRequest
-keep class com.sankuai.meituan.trip.model.datarequest.poi.**{*;}
-keep class com.sankuai.meituan.model.Consts*{*;}
-keep class com.sankuai.meituan.model.GsonProvider*{*;}
-keep class org.apache.commons.lang3.ArrayUtils*{*;}
-keep class com.meituan.android.base.util.AddressUtils.**{*;}
-keep class com.meituan.android.base.task**{*;}
-keep class com.meituan.android.base.util.DateTimeUtils*{*;}
-keep class com.meituan.android.base.util.UriUtils{
    android.net.Uri BASE_URI;
    android.content.UriMatcher uriMatcher;
    android.net.Uri$Builder uriBuilder();
}
-keep class com.meituan.android.base.util.AnalyseUtils{
    public void mge(java.lang.String[]);
}

-keep class com.sankuai.meituan.model.account.UserCenter{
    public boolean isLogin();
    public long getUserId();
}
-keep class com.meituan.android.base.util.UriUtils*{*;}
-keep class com.meituan.android.pay.utils.JsonBean
-keep @com.meituan.android.pay.utils.JsonBean class * {
    *;
}
-keep @com.meituan.android.pay.utils.JsonBean enum * {
    *;
}

-keep class com.meituan.service.** {
    *;
}
-keep class com.sankuai.meituan.pay.thrift.** {
    *;
}
-keep class com.meituan.android.hui.thrift.**{
    *;
}
-keep class com.meituan.meishi.groupapi.thrift.**{
    *;
}

-keep class com.sankuai.meituan.orderdetail.service.**{
    *;
}


-keep class com.sankuai.meituan.InitAppDelegator { *; }
-keep class com.meituan.android.takeout.library.delegate.AppApplicationDelegate { *; }
-keep class com.meituan.android.takeout.library.delegate.TakeoutApplicationDelegate { *; }
-keep class com.meituan.android.takeout.library.base.BaseWebViewActivity$* { *; }
-keep class com.meituan.android.tower.h5.TowerWebViewActivity$* { *; }
-keep class com.meituan.android.takeout.library.db.dao.* {
    <fields>;
}
-keep public class com.sankuai.waimai.platform.db.** { *; }
-keep class com.meituan.android.takeout.library.model.** { *; }
-keep class com.meituan.android.takeout.library.configcenter.** {*;}
-keep class com.iflytek.**{*;}

-keepclasseswithmembers class * {
    @retrofit.http.* <methods>;
}

### 首页相关
-keep class android.support.design.widget.AppBarLayout$Behavior {
    int setHeaderTopBottomOffset(...);
    private void animateOffsetTo(...);
}
-keep class android.support.design.widget.HeaderBehavior {
    <fields>;
}
### 首页相关end

### Passport SDK ###
-dontwarn retrofit.**
-keep class retrofit.** { *; }
-keep class com.meituan.passport.pojo.* { *; }
-keep class com.meituan.passport.api.* { *; }
### Passport SDK END ###

### dynamic load dex START ###
-keep class com.sankuai.meituan.runtime.InstrumentationHook {public *;}
-keep class com.sankuai.meituan.runtime.InstrumentationHookBase {public *;}
-keep class com.sankuai.meituan.runtime.InstrumentationHookHoneycomb {public *;}
-keep class com.sankuai.meituan.test.JacocoInstrumentation {public *;}
-keep class com.sankuai.meituan.test.JacocoInstrumentationHoneycomb {public *;}
### dynamic load dex END ###

-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
    public static int println(...);
}
-assumenosideeffects class java.lang.Throwable {
    public void printStackTrace();
}

### pay sdk ###
#camera
-keep class com.meituan.android.indentifycard.** {*;}

#JsonBean
-keep class com.meituan.android.paybase.utils.JsonBean
-keep @com.meituan.android.paybase.utils.JsonBean class * {
    *;
}
-keep @com.meituan.android.paybase.utils.JsonBean enum * {
    *;
}

-keep class com.meituan.android.paycommon.lib.config.** {*;}
-keep class com.sankuai.android.spawn.utils.AnalyseUtils {*;}
###hotfix###
-keep class com.taobao.**{*;}
-keep class * extends java.lang.annotation.Annotation
-keepclasseswithmembers class * {
  native <methods>;
}
-keep class com.sankuai.hotfix.** { *; }
-keep class com.alipay.euler.andfix.** { *; }
###robust###
-keep class com.meituan.robust.**{*;}
-keep class com.meituan.robust.assistant.**{*;}
### pay sdk ###

-keep class rx.internal.util.unsafe.** {
    *;
}

-keep class com.meituan.android.teemo.thrift.** {
    *;
}

-keep class com.meituan.android.teemo.poi.view.** {
    *;
}

-keep class com.meituan.android.teemo.deal.view.** {
    *;
}

### Hbnb桥协议js ###
-keepclasseswithmembers @com.meituan.android.hbnbridge.JsBridgeObject class * {*;}

#### 常用信息公共组件本地校验策略类 ###
-keep @com.meituan.android.contacts.strategy.CommonInfoCheckerCategory class * {*;}

#上海的joy的keep.
-keepclasseswithmembers class com.meituan.android.joy.base.BaseAgent{ <init>(...); }
-keepclasseswithmembers class * extends com.meituan.android.joy.base.BaseAgent{ <init>(...); }
-keepclasseswithmembers class com.dianping.agentsdk.framework.AgentInterface{ <init>(...); }
-keepclasseswithmembers class * extends com.dianping.agentsdk.framework.AgentInterface{ <init>(...); }

-keep class com.dianping.shield.ShieldMappingInterface{*;}
-keep class * implements com.dianping.shield.ShieldMappingInterface{*;}
-keep class com.dianping.shield.AgentConfigParser{*;}
-keep class com.dianping.shield.AgentRegisterKey{*;}
-keep class com.dianping.shield.AgentsRegisterMapping{*;}

#httpdns
-keep class com.meituan.android.httpdns.DnsResult {*;}
-keep class com.meituan.android.httpdns.DnsRecord {*;}

#### 旅游block
-keep class com.meituan.travelblock.Keep
-keep @com.meituan.travelblock.Keep enum * {
	*;
}
-keep @com.meituan.travelblock.Keep class * {
	<fields>;
}

-dontwarn com.huawei.android.pushagent.**
-dontwarn com.huawei.android.pushselfshow.**
-dontwarn com.huawei.android.microkernel.Activator

-keep class com.huawei.android.pushagent.**{*;}
-keep class com.huawei.android.pushselfshow.**{*;}
-keep class com.huawei.android.microkernel.**{*;}

-keep class com.meituan.android.food.poi.FoodPoiDetailFragment{
    public static ** newInstance(...);
    public void restoreActionBarStatus();
    public void forbidChangeActionBarStatus();
}

-keep class com.meituan.android.food.refactorpoi.base.FoodPoiDetailFragmentV2{
    public static ** newInstance(...);
}

-keep class com.meituan.android.food.refactorpoi.base.FoodBasePoiDetailFragment{
    public void restoreActionBarStatus();
    public void forbidChangeActionBarStatus();
}

# 旅游景点简介aar数据类防止混淆
-keep class com.meituan.android.hplus.travelscenicintro.annotation.NoProguard
-keep @com.meituan.android.hplus.travelscenicintro.annotation.NoProguard class * {
    <fields>;
}
-keep @com.meituan.android.hplus.travelscenicintro.annotation.NoProguard enum * {
    *;
}
-keep class com.squareup.picasso.GlideConfigModule
-keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$ImageType{
      **[] $VALUES;
      public *;
    }
-keepclassmembers class * extends de.greenrobot.dao.AbstractDao {
    public static java.lang.String TABLENAME;
}
-keep class **$Properties

-keep class com.sankuai.meituan.model.dao.**$Properties

# 点评js引擎 和 picasso 和 picassobaseview
-keep class com.dianping.jscore.** { *;}
-keep class com.dianping.picasso.** { *;}
-keep class * extends com.dianping.picasso.creator.BaseViewWrapper{ *;}

# ad-sdk 广告模块防止混淆

-keep class com.midas.ad.resource.model.MidasMetaInfo {*;}
-keep class com.midas.ad.resource.model.MidasMetaInfo$VersionInfo {*;}
-keep class com.midas.ad.resource.model.MidasMetaInfo$VersionInfo$ViewInfo {*;}
-keep class com.midas.ad.resource.model.MidasMetaInfo$VersionInfo$PicassoInfo {*;}
-keep class com.midas.ad.resource.model.MidasPackageInfo {*;}
-keep class com.midas.ad.resource.model.MidasPackageInfo$ViewInfoPKG {*;}
-keep class com.midas.ad.resource.model.MidasPackageInfo$PicassoInfoPKG {*;}

# knb相关的的，防止混淆
# 现在先全部keep住，之后再开放出来。
-keep class com.sankuai.titans.** { *;}
-keep class com.dianping.titans.** { *;}
-keep class com.dianping.titansadapter.** { *;}
-keep class com.sankuai.meituan.android.knb.** { *;}
#-keep class com.sankuai.meituan.android.knb.bean.** { *;}
#-keep class com.sankuai.meituan.android.knb.image.** { *;}
#-keep class com.sankuai.meituan.android.knb.ui.KNBPullToRefreshView { *;}

#paysdk-wechatpay (引入微信支付SDK后需要加入)
-keep class com.tencent.mm.opensdk.** {
   *;
}
-keep class com.tencent.wxop.** {
   *;
}

#腾讯地图防止lib包下类被混淆
-keep class com.tencent.map.lib.** {
   *;
}

#银联HCE相关
#-keep class com.gieseckedevrient.android.**{*;}
#-keep class com.unionpayhce.sdk.**{*;}
#-keep class com.meituan.android.quickpass.manage.lib.**{*;}

-keep public class com.unionpay.sdk.** { public protected *;}
-dontwarn com.unionpayhce.sdk.**
-keepclassmembers class com.unionpayhce.sdk.**{
    public void *(***);
}

-keep class com.gieseckedevrient.android.** {*;}

# hydra need
-keep class android.support.v7.view.ContextThemeWrapper { *; }
-keep class android.support.v7.widget.TintContextWrapper { *; }
-keep class com.meituan.android.hydra.SignatureChecker { *; }

# 闪付公交卡
-keep class com.bjleisen.iface.sdk.** {*;}
-keep class com.bjleisen.bluetooth.** {*;}

# zxing
-keep class net.sourceforge.zbar.JniUtil {*;}
-keep class net.sourceforge.zbar.Pico {*;}

# 闪付银联TSM
-keep class com.unionpay.tsmservice.** {*;}

# 银联sdk
-keep class com.unionpay.**{*;}

# 二维码
-keep class org.fpe4j.**{*;}
-keep class com.meituan.android.quickpass.qrcode.safe.* {*;}
-keep class com.meituan.android.quickpass.qrcode.entity.* {*;}

# webp动图
-keep,allowobfuscation @interface com.dianping.animated.base.DoNotStrip
-keep @com.com.dianping.animated.base.DoNotStrip class *
-keepclassmembers class * { @com.dianping.animated.base.DoNotStrip *; }

# 航旅纵横sdk
-keep class com.umetrip.umesdk.** { *;}

# MapSDK
-keep class com.sankuai.meituan.mapsdk.mapcore.area.OutlineConfig{*;}
-keep class com.sankuai.meituan.mapsdk.mapcore.outline.**{*;}
-keep class com.sankuai.meituan.mapsdk.mapcore.config.**{*;}
-keep public class com.meituan.sankuai.mapsdk.mapcore.geojson.*{
  public <fields>;
  public <methods>;
}
-keep public class com.meituan.sankuai.mapsdk.mapcore.utils.MTMapException{
  public <fields>;
  public <methods>;
}
-keep public class com.meituan.sankuai.mapsdk.mapcore.utils.LogUtil{
  public <fields>;
  public <methods>;
}
-keep public class com.sankuai.meituan.mapsdk.maps.*{
  public <fields>;
  public <methods>;
}
-keep public class com.sankuai.meituan.mapsdk.maps.model.**{
  public <fields>;
  public <methods>;
}

# GaodeMap Adapter(map-gaode)
-keep class com.sankuai.meituan.mapsdk.gaodeadapter.GaodeMapAdapter{*;}
-keep class com.sankuai.meituan.mapsdk.gaodeadapter.GaodeCameraUpdateFactory{*;}
-keep  class com.amap.api.maps.**{*;}
-keep  class com.autonavi.**{*;}
-keep  class com.amap.api.trace.**{*;}
-dontwarn com.amap.api.maps.**
-dontwarn com.autonavi.**
-dontwarn com.amap.api.trace.**s

# BaiduMap Adapter（map-baidu）
-keep class com.sankuai.meituan.mapsdk.baiduadapter.BaiduMapAdapter{*;}
-keep class com.sankuai.meituan.mapsdk.baiduadapter.BaiduCameraUpdateFactory{*;}
-keep class com.baidu.** {*;}
-keep class mapsdkvi.com.** {*;}
-dontwarn com.baidu.**

# TencentMap Adapter（map-tencent）
-keep class com.sankuai.meituan.mapsdk.tencentadapter.TencentMapAdapter{*;}
-keep class com.sankuai.meituan.mapsdk.tencentadapter.TencentCameraUpdateFactory{*;}
-keep class com.tencent.tencentmap.**{*;}
-keep class com.tencent.map.**{*;}
-keep class com.tencent.beacontmap.**{*;}
-keep class navsns.**{*;}
-dontwarn com.tencent.tencentmap.**
-dontwarn com.tencent.map.**

# MapSDK Service（map-service）
-keep public class com.sankuai.meituan.mapsdk.services.base.*{ *;}
-keep public class com.sankuai.meituan.mapsdk.services.geo.*{ *;}
-keep public class com.sankuai.meituan.mapsdk.services.poi.*{ *;}
-keep public class com.sankuai.meituan.mapsdk.services.route.*{ *;}

# aidl文件不混淆
-keep class * implements android.os.IInterface {*;}

# Agora语音通话sdk去掉混淆
-keep class io.agora.**{*;}
-keep class com.meituan.android.customerservice.cscallsdk.state.**{*;}
-keep class com.meituan.android.customerservice.callbase.utils.**{*;}
-keep class com.meituan.android.pike.proto.**{*;}
-keep class com.meituan.android.customerservice.callbase.proto.**{*;}

# CMCC
-keep class com.blueware.agent.**{*;}
-dontwarn com.blueware..agent.**
-keepclassmembers class com.blueware.** {
    public <init>(org.json.JSONObject);
}

# 大象 SDK 图片加载
-keep public class * implements com.sankuai.xm.imageloader.IImageModelLoader

# babel 文件上报
-keep class com.meituan.android.common.kitefly.Log {*;}
-keep class com.meituan.android.common.fileuploader.PickupUploader {*;}
-keep class com.meituan.android.common.fileuploader.PickupUploadCallback {*;}
-keep class * extends com.dianping.titans.js.jshandler.BaseJsHandler {*;}

#ripper2 路由Task
-keepclasseswithmembers ,allowobfuscation class * {
    @com.meituan.android.hplus.tendon.router.annotation.TaskMethod <methods>;
}
-keepclasseswithmembers ,allowobfuscation class * {
    @com.meituan.android.hplus.tendon.router.annotation.TaskSetup <methods>;
}
-keepclasseswithmembers ,allowobfuscation class * {
    @com.meituan.android.hplus.tendon.router.annotation.TaskField <fields>;
}

-keep class com.dianping.voyager.picasso.**{*;}
-keep class com.dianping.bizcomponent.**{*;}

# 非关键路径长连IdleShark

-keep class dianping.com.idleshark.net.** { *; }
-dontwarn dianping.com.idleshark.net.**

-keep class dianping.com.idleshark.IdleShark{*;}
-dontwarn dianping.com.idleshark.IdleShark
-dontwarn dianping.com.idleshark.**

# Shark底层解耦库NVLinker
-keep class dianping.com.nvlinker.** { *; }
-keep class dianping.com.nvlinker.stub.** { *; }
-dontwarn dianping.com.nvlinker.**
-dontwarn dianping.com.nvlinker.stub.**
-keep class com.dianping.luban.LubanModuleInfo {*;}
-keep class com.dianping.luban.LubanService {*;}
-keep class com.dianping.nvnetwork.NVDefaultNetworkService {*;}
-keep class com.dianping.nvnetwork.Request$Builder{ *; }
-dontwarn dianping.com.idleshark.LubanModuleInfo
-dontwarn dianping.com.idleshark.LubanService
-dontwarn com.dianping.nvnetwork.NVDefaultNetworkService

# Push
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-dontwarn com.vivo.push.**
-keep class com.vivo.push.**{*; }
#新的配置add for push sdk
-keep class com.dianping.xiaomipush.MiPushMessageReceiver {*;}
-keep class com.dianping.vivopush.VIVOReceiverImpl{*;}
-keep class com.hianalytics.android.**{*;}
-keep class com.huawei.updatesdk.**{*;}
-keep class com.huawei.hms.**{*;}
-keep class com.huawei.android.hms.agent.**{*;}
-keep class com.huawei.gamebox.plugin.gameservice.**{*;}

## 保留所有 Parcelable 实现类的特殊属性.
-keepclassmembers class * implements android.os.Parcelable {
 static android.os.Parcelable$Creator CREATOR;
}

-keepclassmembers enum * {
        public static **[] values();
        public static ** valueOf(java.lang.String);
}

## 用到序列化的实体类
-keepclassmembers class * implements java.io.Serializable {
     static final long serialVersionUID;
         static final java.io.ObjectStreamField[] serialPersistentFields;
     private void writeObject(java.io.ObjectOutputStream);
     private void readObject(java.io.ObjectInputStream);
     java.lang.Object writeReplace();
     java.lang.Object readResolve();
}

## for pushManager
-keep class com.meizu.cloud.pushsdk.PushManager{ *; }
-dontwarn com.meizu.cloud.pushsdk.PushManager

-keep class com.meizu.cloud.pushsdk.notification.MPushMessage{ *; }
-dontwarn com.meizu.cloud.pushsdk.notification.MPushMessage

-keep class com.meizu.cloud.pushsdk.handler.MessageV3 {*;}
-dontwarn com.meizu.cloud.pushsdk.handler.MessageV3

-keep class com.meizu.cloud.pushsdk.handler.MessageV4 {*;}
-dontwarn com.meizu.cloud.pushsdk.handler.MessageV4

-keep class com.meizu.cloud.pushsdk.handler.MzPushMessage {*;}
-dontwarn com.meizu.cloud.pushsdk.handler.MzPushMessage

-keep class com.meizu.cloud.pushsdk.notification.PushNotificationBuilder{ *; }
-dontwarn com.meizu.cloud.pushsdk.notification.PushNotificationBuilder

-keep class com.meizu.cloud.pushsdk.platform.message.BasicPushStatus{*;}
-dontwarn com.meizu.cloud.pushsdk.platform.message.BasicPushStatus

-keep class com.meizu.cloud.pushsdk.platform.message.PushSwitchStatus{*;}
-dontwarn com.meizu.cloud.pushsdk.platform.message.PushSwitchStatus

-keep class com.meizu.cloud.pushsdk.platform.message.RegisterStatus{*;}
-dontwarn com.meizu.cloud.pushsdk.platform.message.RegisterStatus

-keep class com.meizu.cloud.pushsdk.platform.message.SubAliasStatus{*;}
-dontwarn com.meizu.cloud.pushsdk.platform.message.SubAliasStatus

-keep class com.meizu.cloud.pushsdk.platform.message.UnRegisterStatus{*;}
-dontwarn com.meizu.cloud.pushsdk.platform.message.UnRegisterStatus

-keep class com.meizu.cloud.pushsdk.platform.message.SubTagsStatus{*;}
-dontwarn com.meizu.cloud.pushsdk.platform.message.SubTagsStatus

-keep class com.meizu.cloud.pushsdk.platform.message.SubTagsStatus$*{*;}

-keep class com.meizu.cloud.pushsdk.notification.model.styleenum.BaseStyleModel{*;}
-dontwarn com.meizu.cloud.pushsdk.notification.model.styleenum.BaseStyleModel

-keep class com.meizu.cloud.pushsdk.notification.model.styleenum.InnerStyleLayout{*;}
-dontwarn com.meizu.cloud.pushsdk.notification.model.styleenum.InnerStyleLayout

-keep class com.meizu.cloud.pushsdk.notification.model.ActVideoSetting{*;}
-dontwarn com.meizu.cloud.pushsdk.notification.model.ActVideoSetting
-keep class com.meizu.cloud.pushsdk.notification.model.AdvanceSetting{*;}
-dontwarn com.meizu.cloud.pushsdk.notification.model.AdvanceSetting
-keep class com.meizu.cloud.pushsdk.notification.model.AppIconSetting{*;}
-dontwarn com.meizu.cloud.pushsdk.notification.model.AppIconSetting
-keep class com.meizu.cloud.pushsdk.notification.model.NotificationStyle{*;}
-dontwarn com.meizu.cloud.pushsdk.notification.model.NotificationStyle
-keep class com.meizu.cloud.pushsdk.notification.model.NotifyType{*;}
-dontwarn com.meizu.cloud.pushsdk.notification.model.NotifyType
-keep class com.meizu.cloud.pushsdk.notification.model.TimeDisplaySetting{*;}
-dontwarn com.meizu.cloud.pushsdk.notification.model.TimeDisplaySetting

-keep class com.meizu.cloud.pushsdk.platform.PlatformMessageSender{
    public void launchStartActivity(android.content.Context, java.lang.String, java.lang.String,java.lang.String);
 }

-keep class com.meizu.cloud.pushsdk.constants.PushConstants{ *; }
-dontwarn com.meizu.cloud.pushsdk.constants.PushConstants

-keep class com.meizu.cloud.pushsdk.util.MzSystemUtils{*;}
-dontwarn com.meizu.cloud.pushsdk.util.MzSystemUtils

-keep class com.meizu.cloud.pushsdk.util.MinSdkChecker{ *;}
-dontwarn com.meizu.cloud.pushsdk.util.MinSdkChecker

-keep class com.meizu.cloud.pushsdk.MzPushMessageReceiver{ *; }
-dontwarn com.meizu.cloud.pushsdk.MzPushMessageReceiver

-keep class com.meizu.cloud.pushinternal.DebugLogger{*;}
-dontwarn com.meizu.cloud.pushinternal.DebugLogger

# x5内核
-keep class com.tencent.smtt.** {
	*;
}

-keep class com.meituan.hydra.runtime.reflect.Hack {
	*;
}

# Graft
-keep class com.meituan.android.graft.** {
    *;
}

# RxBus
-keepclasseswithmembers class * { @com.sankuai.waimai.platform.rxbus.annotation.Subscribe <methods>; }
-keep enum com.sankuai.waimai.platform.rxbus.annotation.ThreadMode { *; }
# Component
-keep @com.sankuai.waimai.store.architecture.annotation.Component class *

# Cube
-keepclasseswithmembers class * { @com.meituan.android.bus.annotation.Subscribe <methods>; }
-keep enum com.meituan.android.bus.annotation.ThreadMode { *; }
-keep @com.meituan.android.cube.annotation.Cube class *
-keep class com.dianping.homefeed.model.**{ *; }
-keep class com.dianping.homefeed.FeedFragment{*;}

# 美团发现tab接入点评数据模型
-keep class com.dianping.mtcontent.bridge.**{*;}

# 到综图片浏览大图页数据模型
-keep class com.dianping.pioneer.model.**{ *; }
-keep class com.dianping.pioneer.widgets.videoplayer.model.**{ *; }

#外卖Mach
-keep class com.sankuai.waimai.mach.js.**{*;}
-keep class com.sankuai.waimai.mach.Mach{*;}
-keep class com.sankuai.waimai.mach.model.net.MachResponse{*;}
-keep class com.huawei.android.hms.agent.**{*;}
-keep class com.huawei.gamebox.plugin.gameservice.**{*;}

-keep class com.sankuai.meituan.aop.**{*;}

-keep class android.view.View {*;}
-keep class com.facebook.litho.** {*;}
-keep class com.meituan.android.dynamiclayout.controller.DynamicClickListener {*;}
-keep class com.sankuai.litho.** {*;}
-keep class android.graphics.drawable {*;}
-keep class android.support.v4.util.** {*;}
-keep class com.facebook.litho.MountItem {*;}

# MTLive
-keep class com.tencent.** { *; }

-keep class com.meituan.android.common.kitefly.Log.Builder { *; }

-keep class com.sankuai.adc.protocol.** {*;}


#腾讯定位SDK
-keepclassmembers class com.tencent.map.geolocation.** {
    public void on*Event(...);
}

-keep class c.t.**{*;}
-keep class com.tencent.map.geolocation.**{*;}

-dontwarn  org.eclipse.jdt.annotation.**
-dontwarn  c.t.**

#腾讯优量汇SDK
-keepclassmembers class com.qq.e.** {
    public protected *;
}

-keep class com.qq.e.** {
    public protected *;
}

# R8临时适配，为了编译通过
-keep class com.xiaomi.** {*;}

# Retain generic signatures of TypeToken and its subclasses with R8 version 3.0 and higher.
-keep,allowobfuscation,allowshrinking class com.google.gson.reflect.TypeToken
-keep,allowobfuscation,allowshrinking class * extends com.google.gson.reflect.TypeToken

###### MtRetrofit更新配置规则，适配R8
# Keep inherited services.
-if interface * { @com.sankuai.meituan.retrofit2.http.* <methods>; }
-keep interface * extends <1>
# With R8 full mode generic signatures are stripped for classes that are not
# kept. Suspend functions are wrapped in continuations where the type argument
# is used.
-keep,allowobfuscation,allowshrinking class kotlin.coroutines.Continuation
# R8 full mode strips generic signatures from return types if not kept.
-if interface * { @com.sankuai.meituan.retrofit2.http.* public *** *(...); }
-keep class <3>
-keep class com.meituan.android.pay.debugkit.* { *; }
-keep class com.meituan.android.pay.debugkit.**.* { *; }
