apply plugin: 'defensor'
apply plugin: 'service_loader'
apply plugin: 'dynloader_uploader'
apply plugin: 'com.github.ben-manes.versions'

defensor {
    enable = false // 编译时是否开启插件
    enableOnDebug = true // debug模式是否开启插件
    abortOnError = true // 出现错误是否终止编译过程
    includePackageNames = [] // 指定检查指定的包名下的文件，默认为空，也就是全部检查
    excludePackageNames = [] //指定不检查指定的包名
    excludeClasses = [] // 指定不检查的类名，注意：不是全类名，并且不能包含.class后缀名
    knownMissedReferencedClasses =
            ['org.jacoco',
             'org.apache.',
             'sun.misc.Unsafe',
             'org.codehaus',
             'com.amap.api.',
             'com.fasterxml.jackson.core.',
             'javax.servlet',
             'javax.annotation', // LeakCanary中的haha库用到了，但确实缺失。 例如：javax.annotation.Nullable。
             'java.lang.invoke',
             'sun.misc',
             'sun.nio.ch',
             'cloudwns',//tencent wns
             'com.tencent.wns',//tencent wns
             'com.google.appengine',
             'com.xtremelabs',
             'com.google.android.maps',
             'org.junit.runners',
             'net.sf.cglib',
             'roboguice.test',
             'com.google.common',
             'com.google.common.collect',
             'com.sankuai.model.JsonBean',
             'com.meituan.android.common.fingerprint.info.InstalledAppManager',
             'com.squareup.haha.guava.collect.AbstractMapBasedMultimap$WrappedList$WrappedListIterator',
             'retrofit.appengine.UrlFetchClient',
             'com.alipay.android.phone.mrpc.core.',
             //以下这行屏蔽对okhttp相关检测
             'com.squareup.okhttp', 'okio',
             //以下这行屏蔽对facebook相关检测
             'com.facebook.',
             //以下这行屏蔽对小米、华为push相关的检测
             'com.huawei.', 'com.xiaomi.push.',
             //以下这行屏蔽对android.util.FloatMath类floor、ceil、sqrt方法检测
             'com.handmark.pulltorefresh.library.PullToRefreshWebView', 'com.viewpagerindicator.LinePageIndicator', 'uk.co.senab.photoview.', 'com.amap.api.mapcore2d',
             //以下这行屏蔽对android.app.Notification.setLatestEventInfo检测
             'android.support.v4.app.NotificationCompat', 'android.support.v4.app.NotificationCompatGingerbread',
             //以下屏蔽dao-1.2.jar中FastCursor类未实现（api23）android.jar中Cursor接口的setExtras和getNotificationUri方法
             'de.greenrobot.dao.FastCursor',
             'org.slf4j.impl.StaticLoggerBinder',
             // 以下屏蔽腾讯地图相关检测
             'com.tencent.tencentmap.',
             'com.tencent.halley.',
             // 高德地图白名单
             'com.amap.api.',
             // 以下屏蔽对aws sdk检测
             'com.amazonaws.',
             // hydra
             'com.meituan.hydra.runtime.',
             'com.bjleisen.iface.sdk',
             'org.codehaus.mojo.animal_sniffer',
             // 此class由WMRouter插件的Transform生成，且调用做了try-catch处理
             'com.sankuai.waimai.router.generated.ServiceLoaderInit',
             // runtime LimitOffsetDataSource  父类TiledDataSource没有依赖
             'android.arch.persistence.room.paging.LimitOffsetDataSource',


             'dalvik.system.VMStack',
             'com.cmic.sso.sdk.',
             'com.meituan.android.dynamiclayout.widget.DynamicTextView',
             'com.facebook.litho.widget.DynamicTextSpec',
             'android.graphics.',
             'android.graphics.Typeface',
             'org.apache.http',
             'org.apache.commons',
             "org.json.JSONML",
             "com.dianping.android.oversea.translate.utils.OsTransCameraUtils",
             "com.oliveapp.face.livenessdetectionviewsdk.verification_controller.VerificationController",
             "com.maoyan.android.presentation.mediumstudio.moviedetail.blocks.MovieDetailHeaderBlock",
             "com.meituan.android.food.map.widget.FoodSlidingUpPanelLayout",
             "com.meituan.android.movie.tradebase.seat.MovieSeatGifView",
             "com.alipay.test",
             "org.json",
             "com.meituan.android.food.search.searchlistheader",
             "org.json.XML",
             "com.maoyan.android.presentation.qanswer.utils.MovieMediaUtils",
             "com.maoyan.android.presentation.mc.MYBigImageShareHelper",
             "com.meituan.android.travel.utils",
             "com.maoyan.android.presentation.mediumstudio.shortcomment.MYBigImageShareHelper",
             "com.dianping.voyager.joy.backroom.widget.ecogallery.BlurEcoGalleryAdapter",
             "com.maoyan.android.presentation.actor.ActorDetailFragment",
             "com.maoyan.android.cinema.cinemalist.bymovie.movieinfo",
             "com.oliveapp.face.livenessdetectionviewsdk.verification_controller.FaceCaptureController",
             "de.greenrobot.dao.test.DbTest",
             "com.meituan.android.joy.backroom.widget",
             "com.maoyan.android.cinema.show.view.MovieGalleryCenterScrollListener",
             "com.sankuai.meituan.search.result.view.custom",
             "com.sankuai.common.utils.BlurUtils",
             "com.oliveapp.face.livenessdetectionviewsdk.verification_controller",
             "com.meituan.android.base.util.BarCodeFactory",
             "com.maoyan.android.presentation.qanswer.utils.MYBigImageShareHelper",
             "com.meituan.android.movie.tradebase.fansmeeting.MovieViewToImageFragment",
             "android.support.v4.widget.SlidingPaneLayout",
             "com.meituan.android.oversea.search.result.view.custom",
             "com.sankuai.meituan.search.ui.VoiceSearchWebViewContainer",
             "com.dianping.titans.js.jshandler.UploadLogJsHandler",
             "com.dianping.networklog.Logan",
             "com.meituan.android.mrn.",
             "okhttp3.",
             "com.meituan.android.common.babel."
            ] // 白名单 ： 指定包名下的类不用检查
}

service_loader {
    enable true
    enableOnDebug true
    // debug期间是否开启对声明的实现类检查（检查接口和实现类是否真实存在）
    checkServicesOnDebug true
    // 打开插件执行过程中的详细log输出
    enableLog false
}

dynloader_uploader {
    enable false
    enableOnDebug = false
    // 指定要上传的so
    soUploadList = [
            "lib/armeabi/libCardOcr.so",
            "lib/armeabi/libextractCard.so",
            "lib/armeabi/libeidjni.so",
            "lib/armeabi/libuptsmaddon.so",
            "lib/armeabi/libuptsmaddonmi.so",
            "lib/armeabi/libwasai_platform_dyn.so",

            "lib/arm64-v8a/libCardOcr.so",
            "lib/arm64-v8a/libextractCard.so",
            "lib/arm64-v8a/libeidjni.so",
            "lib/arm64-v8a/libuptsmaddon.so",
            "lib/arm64-v8a/libuptsmaddonmi.so",
            "lib/arm64-v8a/libwasai_platform_dyn.so",
    ]
}