<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.meituan.android.paydemo"
    android:installLocation="auto">

    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.CAMERA" />

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />
    <uses-feature
        android:name="android.hardware.telephony"
        android:required="false" />

    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.NFC" />

    <application
        android:name="com.meituan.android.pay.demo.AppApplication"
        android:allowBackup="false"
        android:extractNativeLibs="true"
        android:label="@string/app_name"
        android:theme="@style/Theme.AppCompat.Light"
        android:usesCleartextTraffic="true"
        tools:replace="android:label, android:theme,android:allowBackup">
        <meta-data
            android:name="leakcanary.config.disable"
            android:value="true" />

        <activity
            android:name="com.meituan.android.pay.demo.MainActivity"
            android:theme="@style/pay_debug_activity_theme"
            android:windowSoftInputMode="adjustPan|stateHidden">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>



        <activity
            android:name="com.meituan.android.pay.demo.CaptureActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateAlwaysHidden"
            tools:ignore="AppLinkUrlError">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="debugkit"
                    android:path="/capture"
                    android:scheme="pay" />
            </intent-filter>
        </activity>

        <activity android:name="com.meituan.android.wallet.scheme.SchemeRouteActivity">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="payment"
                    android:scheme="paydemo" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="payment"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.sankuai.meituan.decp.PayRouteActivity"
            android:exported="true"
            android:launchMode="standard"
            android:theme="@android:style/Theme.Translucent.NoTitleBar">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />

                <data
                    android:host="test"
                    android:path="/path"
                    android:scheme="wednesday" />
                <data
                    android:host="wallet"
                    android:path="/dcep"
                    android:scheme="imeituan" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.sankuai.meituan.wxapi.WXPayEntryActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <meta-data
            android:name="horn_token"
            android:value="5ca5a80f28cbe03265b9727d" />
    </application>

</manifest>