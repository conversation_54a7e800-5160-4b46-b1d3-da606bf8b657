<?xml version="1.0" encoding="UTF-8"?>
<!--
 Copyright (C) 2008 ZXing authors

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
 -->
<merge xmlns:android="http://schemas.android.com/apk/res/android"
       xmlns:tools="http://schemas.android.com/tools">

  <SurfaceView android:id="@+id/preview_view"
               android:layout_width="fill_parent"
               android:layout_height="fill_parent"/>

  <com.google.zxing.client.android.ViewfinderView
      android:id="@+id/viewfinder_view"
      android:layout_width="fill_parent"
      android:layout_height="fill_parent"/>

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginTop="16dp"
        android:layout_marginLeft="16dp"
        android:layout_gravity="top|left"
        android:src="@drawable/scanner_back_btn"/>

    <TextView
        android:id="@+id/tv_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="@string/scan_scanner_tip"
        android:textColor="@color/pay_debug_capture_description_text"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:textSize="12sp"
        android:gravity="center"
        android:layout_gravity="center_horizontal"
        android:background="@color/pay_debug_transparent"/>

</merge>
