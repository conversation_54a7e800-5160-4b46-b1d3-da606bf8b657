package com.sankuai.meituan.decp;

import android.content.Intent;
import android.os.Bundle;

import com.meituan.android.cashier.CashierAPI;
import com.meituan.android.pay.debugkit.main.activity.PayDebugActivity;

public class PayRouteActivity extends PayDebugActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        CashierAPI.onDCEPPayResultGot(getIntent().getData());
        finish();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        CashierAPI.onDCEPPayResultGot(getIntent().getData());
        finish();
    }
}