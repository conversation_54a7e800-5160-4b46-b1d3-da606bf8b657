package com.sankuai.meituan.wxapi;

import android.content.Intent;
import android.os.Bundle;

import com.meituan.android.cashier.CashierAPI;
import com.meituan.android.pay.debugkit.main.activity.PayDebugActivity;
import com.meituan.android.paymentchannel.utils.WechatUtils;
import com.tencent.mm.opensdk.modelbase.BaseReq;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler;


/**
 * Created by ljj
 * Date:16/1/25
 * Time:下午5:43
 */
public class WXPayEntryActivity extends PayDebugActivity implements IWXAPIEventHandler {
    private IWXAPI api;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        api = WechatUtils.creatWXAPI(this);
        api.handleIntent(getIntent(), this);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        api.handleIntent(intent, this);
    }

    @Override
    public void onReq(BaseReq req) {
    }

    @Override
    public void onResp(BaseResp resp) {
        CashierAPI.onWXPayResultGot(getApplicationContext(), resp);
        finish();
    }
}
