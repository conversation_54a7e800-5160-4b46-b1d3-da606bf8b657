package com.meituan.android.pay.demo.tasks.first;

import android.app.Application;

import com.meituan.android.pay.demo.AppInfo;
import com.meituan.android.pay.demo.tasks.InitTask;
import com.meituan.metrics.Metrics;
import com.meituan.metrics.config.MetricsConfig;

/**
 * <AUTHOR>
 */
public class MetricsTask implements InitTask {
    private static final String METRICS_TOKEN = "5af3f71abd70fe4435c39c62";

    @Override
    public void init(Application application, AppInfo info) {
        Metrics.getInstance().init(application, new MetricsConfig() {
            @Override
            public String getAppName() {
                return info.getAppName();
            }

            @Override
            public String getToken() {
                return METRICS_TOKEN;
            }

            @Override
            public String getUuid() {
                return info.getUUID();
            }

            @Override
            public String getUserId() {
                return info.getUserId();
            }

            @Override
            public String getChannel() {
                return info.getChannel();
            }

            @Override
            public long getCityId() {
                return info.getCityIdCode();
            }
        });
    }
}
