package com.meituan.android.pay.demo;

import android.os.Build;

import com.meituan.android.common.locate.MtLocation;
import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.paycommon.lib.BaseConfig;
import com.meituan.android.paydemo.BuildConfig;
import com.meituan.android.privacy.locate.MtLocationCache;
import com.meituan.android.singleton.CityControllerSingleton;
import com.meituan.android.singleton.FingerprintManagerSingleton;
import com.meituan.android.singleton.UUIDProviderSingleton;
import com.meituan.android.singleton.UserCenterSingleton;

/**
 * <AUTHOR>
 */
public class AppInfo {
    private static final String APP_ID_DEMO = "92";
    private static final int APP_ID_DEMO_CODE = 92;
    private static final String APP_NAME_DEMO = "pay-demo";
    private static final String APP_NAME_GROUP = "group";

    public String getUUID() {
        return UUIDProviderSingleton.getInstance().getUUID();
    }

    public long getUserIdCode() {
        return UserCenterSingleton.getInstance().getUserId();
    }

    public String getUserId() {
        return String.valueOf(getUserIdCode());
    }

    public String getUserToken() {
        return UserCenterSingleton.getInstance().getToken();
    }


    public long getCityIdCode() {
        return CityControllerSingleton.getInstance().getCityId();
    }

    public String getCityId() {
        return String.valueOf(getCityIdCode());
    }

    public String getCityName() {
        return CityControllerSingleton.getInstance().getCityName();
    }


    public String getDemoAppName() {
        return APP_NAME_DEMO;
    }

    public String getAppName() {
        return APP_NAME_GROUP;
    }

    public String getAppVersionName() {
        return BuildConfig.VERSION_NAME;
    }

    public int getAppVersionCode() {
        return BuildConfig.VERSION_CODE;
    }

    public String getAppFlavor() {
        return BuildConfig.FLAVOR;
    }

    public String getAppJoinKey() {
        return BuildConfig.JOIN_KEY;
    }

    public String getAppWXKey() {
        return BuildConfig.WX_KEY;
    }

    public String getApplicationId() {
        return BuildConfig.APPLICATION_ID;
    }

    public String getAppId() {
        return APP_ID_DEMO;
    }

    public int getAppIdCode() {
        return APP_ID_DEMO_CODE;
    }

    public String getDeviceId() {
        return "pay-demo-test";
    }

    public String getDeviceLevel() {
        return "TEST";
    }

    public String getChannel() {
        return "meituaninternaltest";
    }

    public String getPlatform() {
        return "android";
    }

    public String getPayVersion() {
        return BaseConfig.VERSION;
    }

    public MtLocation getLocation() {
        return MtLocationCache.getInstance().getLastKnownLocation("");
    }

    public String getOSVersion() {
        return String.valueOf(Build.VERSION.SDK_INT);
    }

    public boolean isDebug() {
        return true;
    }

    public boolean isOffline() {
        return DebugManager.isOffline();
    }

    public boolean isLogin() {
        return false;
    }

    public String getCampaign() {
        return "AwaimaiBwaimai";
    }

    public String getLatitude() {
        return "";
    }

    public String getLongitude() {
        return "";
    }

    public String getFingerprint() {
        return FingerprintManagerSingleton.getInstance().fingerprint();
    }

    private static class InnerSingleHolder {
        private static final AppInfo INSTANCE = new AppInfo();
    }

    public static AppInfo instance() {
        return InnerSingleHolder.INSTANCE;
    }
}
