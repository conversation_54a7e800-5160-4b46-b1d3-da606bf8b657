package com.meituan.android.pay.demo.tasks.first;

import android.app.Application;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.meituan.android.common.horn.Horn;
import com.meituan.android.common.horn.HornConfiguration;
import com.meituan.android.common.horn.extra.monitor.IHornMonitorService;
import com.meituan.android.common.horn.extra.sharkpush.ISharkPushService;
import com.meituan.android.common.horn.extra.sharkpush.SharkPushServiceMgr;
import com.meituan.android.common.horn.extra.uuid.IUUIDService;
import com.meituan.android.common.horn.extra.uuid.UUIDServiceMgr;
import com.meituan.android.common.horn.monitor.HornMonitor;
import com.meituan.android.pay.demo.AppInfo;
import com.meituan.android.pay.demo.tasks.InitTask;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class HornTask implements InitTask {

    @Override
    public void init(Application application, AppInfo info) {
        Horn.mock(application, true);
        Horn.markMeituanInternal();
        Horn.init(application, new HornConfiguration() {
            @Override
            public IUUIDService uuidService() {
                return UUIDServiceMgr.get().service();
            }

            @Override
            public ISharkPushService sharkPushService() {
                return SharkPushServiceMgr.get().service();
            }

            @Override
            public IHornMonitorService monitorService() {
                return new IHornMonitorService() {
                    @Override
                    public void catchException(@Nullable String msg, int priority, @Nullable Throwable th) {

                    }

                    @Override
                    public void logReport(String name, Map<String, Object> options) {

                    }

                    @Override
                    public boolean shouldMonitorChange(@NonNull String type) {
                        return true;
                    }

                    @Override
                    public void onConfigChange(@NonNull Map<String, Object> tags) {

                    }

                    @Override
                    public long sampleForController() {
                        return 100;
                    }

                    @Override
                    public void log(@NonNull String message) {

                    }
                };
            }
        });
    }
}
