package com.meituan.android.pay.demo.tasks.first;

import android.app.Application;

import com.meituan.android.pay.demo.AppInfo;
import com.meituan.android.pay.demo.tasks.InitTask;
import com.meituan.android.singleton.CityControllerSingleton;

/**
 * <AUTHOR>
 */
public class MockTask implements InitTask {
    @Override
    public void init(Application application, AppInfo info) {
        // 北京
        CityControllerSingleton.getInstance().setCityId(1L, application);

    }
}
