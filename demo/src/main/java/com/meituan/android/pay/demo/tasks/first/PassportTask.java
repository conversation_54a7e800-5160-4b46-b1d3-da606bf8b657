package com.meituan.android.pay.demo.tasks.first;

import android.app.Application;
import android.support.annotation.NonNull;

import com.meituan.android.common.mtguard.wtscore.plugin.sign.interceptors.Ok3CandyInterceptor;
import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.pay.debugkit.capacity.DebugPassport;
import com.meituan.android.pay.demo.AppInfo;
import com.meituan.android.pay.demo.tasks.InitTask;
import com.meituan.android.paybase.net.DefaultRetrofitFactory;
import com.meituan.android.paybase.net.cat.Ok3NetExceptionCatMonitorInterceptor;
import com.meituan.android.paybase.utils.SystemInfoUtils;
import com.meituan.android.singleton.FingerprintManagerSingleton;
import com.meituan.android.singleton.RetrofitCallFactorySingleton;
import com.meituan.passport.PassportConfig;
import com.meituan.passport.PassportUIConfig;
import com.meituan.passport.plugins.DebugHook;
import com.meituan.passport.plugins.FingerPrintHook;
import com.meituan.passport.plugins.PassportPlugins;
import com.meituan.passport.plugins.RestAdapterHook;
import com.sankuai.meituan.kernel.net.INetInjector;
import com.sankuai.meituan.retrofit2.raw.RawCall;
import com.sankuai.meituan.skyeye.library.core.ISkyeyeMonitorDataProvider;
import com.sankuai.meituan.skyeye.library.core.SkyeyeCenter;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class PassportTask implements InitTask {

    @Override
    public void init(Application application, AppInfo info) {
        PassportConfig.getBuilder().setJoinKey(info.getAppJoinKey());
        PassportPlugins.getInstance().registerPassportBroadcastReceiver();
        PassportPlugins.getInstance().registerRestAdapterHook(new RestAdapterHook() {
            @Override
            public RawCall.Factory getCallFactory() {
                return RetrofitCallFactorySingleton.getInstance(new INetInjector() {
                    @Override
                    public void onOkHttpBuild(@NonNull okhttp3.OkHttpClient.Builder builder) {
                        builder.connectTimeout(30, TimeUnit.SECONDS)
                                .writeTimeout(30, TimeUnit.SECONDS)
                                .readTimeout(30, TimeUnit.SECONDS)
                                .addInterceptor(new Ok3NetExceptionCatMonitorInterceptor());

                        if (SystemInfoUtils.isApkDebuggable(application)) {
                            builder.addInterceptor(new Ok3CandyInterceptor(application)); //mtguard验签
                            // 注意,签名一定要使用原始域名进行签名,也就是在替换mock域名之前,进行签名.否则会报验签失败
                            builder.addInterceptor(new DefaultRetrofitFactory.Ok3MockInterceptor());
                        } else {
                            builder.addNetworkInterceptor(new Ok3CandyInterceptor(application)); //mtguard验签
                        }
                    }
                });
            }

            @Override
            public int getNetEnv() {
                if (DebugManager.isOffline()) {
                    return RestAdapterHook.OFFLINE_TEST;
                } else if (DebugManager.isStage()) {
                    return RestAdapterHook.ONLINE_TEST;
                } else {
                    return RestAdapterHook.ONLINE;
                }
            }
        });
        PassportPlugins.getInstance().registerDebugHook(new DebugHook() {
            @Override
            public boolean debugSwitch() {
                return info.isDebug();
            }
        });
        PassportPlugins.getInstance().registerFingerPrintHook(new FingerPrintHook() {
            @Override
            protected String syncRequestfingerPrint() {
                return FingerprintManagerSingleton.getInstance().fingerprint();
            }
        });
        DebugManager.info().getOfflineStatus().subscribe(isOffline -> {
            if (DebugManager.isOffline() ^ isOffline) {
                DebugPassport.logout();
            }
            if (isOffline) {
                PassportUIConfig.getBuilder().title("欢迎登录测试环境");
            } else {
                PassportUIConfig.getBuilder().title("欢迎登录线上环境");
            }
        });
        SkyeyeCenter.init(application, "", new ISkyeyeMonitorDataProvider() {
            @Override
            public String getLaunchChannel() {
                return "";
            }

            @Override
            public String getCarrierName() {
                return "";
            }

            @Override
            public String getPushAuthority() {
                return "";
            }

            @Override
            public String getLoginType() {
                return "";
            }

            @Override
            public String getPushToken() {
                return "";
            }

            @Override
            public boolean getProxyStatus() {
                return false;
            }
        });
    }
}
