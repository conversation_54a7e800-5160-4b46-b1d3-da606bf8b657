package com.meituan.android.pay.demo.tasks.first;

import android.app.Application;

import com.meituan.android.pay.demo.AppInfo;
import com.meituan.android.pay.demo.tasks.InitTask;
import com.meituan.android.yoda.plugins.NetEnvHook;
import com.meituan.android.yoda.plugins.YodaPlugins;

/**
 * <AUTHOR>
 */
public class YodaTask implements InitTask {

    @Override
    public void init(Application application, AppInfo info) {
        YodaPlugins.getInstance().registerNetEnvHook(new NetEnvHook() {
            @Override
            public int getNetEnv() {
                return NetEnvHook.OFFLINE_VERIFY_INF_TEST;
            }
        });
    }
}
