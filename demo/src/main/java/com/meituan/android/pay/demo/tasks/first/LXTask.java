package com.meituan.android.pay.demo.tasks.first;

import android.app.Application;

import com.meituan.android.common.statistics.Interface.IEnvironment;
import com.meituan.android.common.statistics.Statistics;
import com.meituan.android.pay.demo.AppInfo;
import com.meituan.android.pay.demo.tasks.InitTask;

/**
 * <AUTHOR>
 */
public class LXTask implements InitTask {

    @Override
    public void init(Application application, AppInfo info) {
        Statistics.initStatistics(application, new IEnvironment() {
            @Override
            public String getAppName() {
                return info.getAppName();
            }

            @Override
            public String getCh() {
                return info.getChannel();
            }

            @Override
            public String getLat() {
                return info.getLatitude();
            }

            @Override
            public String getLng() {
                return info.getLongitude();
            }

            @Override
            public String getUid() {
                return info.getUUID();
            }

            @Override
            public String getLoginType() {
                return null;
            }

            @Override
            public int getAppId() {
                return info.getAppIdCode();
            }

            @Override
            public String getCityId() {
                return info.getCityId();
            }
        });
    }
}
