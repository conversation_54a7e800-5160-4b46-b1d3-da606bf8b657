package com.meituan.android.pay.demo.tasks.second;

import android.app.Application;

import com.meituan.android.pay.demo.AppInfo;
import com.meituan.android.pay.demo.tasks.InitTask;
import com.sankuai.mesh.core.IMeshProvider;
import com.sankuai.mesh.core.MeshSdk;

/**
 * <AUTHOR>
 */
public class MeshTask implements InitTask {

    @Override
    public void init(Application application, AppInfo info) {
        MeshSdk.init(application, new IMeshProvider() {
            @Override
            public String getAppName() {
                return info.getAppName();
            }

            @Override
            public String getAppVersion() {
                return info.getAppVersionName();
            }

            @Override
            public String getPayVersion() {
                return info.getPayVersion();
            }
        });
    }
}
