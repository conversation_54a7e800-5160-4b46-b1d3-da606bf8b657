package com.meituan.android.pay.demo;

import android.os.Bundle;
import android.text.TextUtils;

import com.google.zxing.client.android.BaseCaptureActivity;
import com.google.zxing.client.android.Result;
import com.meituan.android.pay.base.compat.ActivityCompat;
import com.meituan.android.pay.base.utils.function.Cond;
import com.meituan.android.pay.debugkit.capacity.DebugMock;
import com.meituan.android.paybase.common.dialog.PayDialog;
import com.meituan.android.paybase.utils.UriUtils;

public class CaptureActivity extends BaseCaptureActivity {
    public static final String MOCK_HOST = "appmock.sankuai.com";

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActivityCompat.UI.hideActionBar(this);
    }

    @Override
    protected void handleDecodeResult(Result rawResult) {
        String resultUrl = rawResult.getText();
        if (!TextUtils.isEmpty(resultUrl)) {
            if (Cond.isContains(resultUrl, MOCK_HOST)) {
                DebugMock.bindMockUrl(resultUrl).subscribe(s -> finish());
            } else {
                UriUtils.open(this, resultUrl);
                finish();
            }
        } else {
            new PayDialog.Builder(this)
                    .title("提示")
                    .msg("识别错误")
                    .leftBtn(PayDialog.DEFAULT_BUTTON_TEXT, dialog ->
                            restartPreviewAfterDelay(500/*ms*/))
                    .build()
                    .show();
        }
    }

    @Override
    protected void handleOpenCameraException() {
        new PayDialog.Builder(this)
                .title("提示")
                .msg("启动异常")
                .leftBtn(PayDialog.DEFAULT_BUTTON_TEXT, null)
                .build()
                .show();
    }
}
