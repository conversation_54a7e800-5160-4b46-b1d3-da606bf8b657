package com.meituan.android.pay.demo;

import android.app.Application;
import android.util.Log;

import com.meituan.android.common.locate.util.LogUtils;
import com.meituan.android.pay.demo.tasks.first.CommonTask;
import com.meituan.android.pay.demo.tasks.first.DynLoaderTask;
import com.meituan.android.pay.demo.tasks.first.HornTask;
import com.meituan.android.pay.demo.tasks.InitTask;
import com.meituan.android.pay.demo.tasks.first.LXTask;
import com.meituan.android.pay.demo.tasks.first.LoganTask;
import com.meituan.android.pay.demo.tasks.first.MSITask;
import com.meituan.android.pay.demo.tasks.second.MeshTask;
import com.meituan.android.pay.demo.tasks.first.MetricsTask;
import com.meituan.android.pay.demo.tasks.first.NetworkTask;
import com.meituan.android.pay.demo.tasks.first.PassportTask;
import com.meituan.android.pay.demo.tasks.second.PaySDKTask;
import com.meituan.android.pay.demo.tasks.first.RaptorTask;
import com.meituan.android.pay.demo.tasks.first.TitansTask;
import com.meituan.android.pay.demo.tasks.first.UUIDTask;
import com.meituan.android.pay.demo.tasks.first.YodaTask;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AppApplication extends Application {

    @Override
    public void onCreate() {
        super.onCreate();
        LogUtils.setLogEnabled(true);
        initFirstTasks(this);
        initSecondTasks(this);
    }

    public static void initFirstTasks(Application application) {
        Log.d("DEMO_INIT", "initFirstTasks: START====================");
        List<InitTask> tasks = getFirstInitTasks();
        for (InitTask task : tasks) {
            task.init(application, AppInfo.instance());
            Log.d("DEMO_INIT", "initFirstTasks: " + task.getClass().getSimpleName());
        }
        Log.d("DEMO_INIT", "initFirstTasks: END====================");
    }

    public static void initSecondTasks(Application application) {
        Log.d("DEMO_INIT", "initSecondTasks: START====================");
        List<InitTask> tasks = getSecondInitTasks();
        for (InitTask task : tasks) {
            task.init(application, AppInfo.instance());
            Log.d("DEMO_INIT", "initSecondTasks: " + task.getClass().getSimpleName());
        }
        Log.d("DEMO_INIT", "initSecondTasks: END====================");
    }

    private static List<InitTask> getFirstInitTasks() {
        return Arrays.asList(
                new CommonTask(),
                new UUIDTask(),
                new NetworkTask(),
                new PassportTask(),
                new MetricsTask(),
                new LoganTask(),
                new LXTask(),
                new RaptorTask(),
                new HornTask(),
                new DynLoaderTask(),
                new TitansTask(),
                new MSITask(),
                new YodaTask()
        );
    }

    private static List<InitTask> getSecondInitTasks() {
        return Arrays.asList(
                new PaySDKTask(),
                new MeshTask());
    }

}
