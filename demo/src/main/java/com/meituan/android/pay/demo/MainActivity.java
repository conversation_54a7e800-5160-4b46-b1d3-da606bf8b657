package com.meituan.android.pay.demo;

import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.support.v7.app.AppCompatActivity;
import android.view.View;

import com.meituan.android.pay.debugkit.main.activity.PayDebugMainActivity;
import com.meituan.android.paydemo.R;

/**
 * <AUTHOR>
 */
public class MainActivity extends AppCompatActivity {

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 初始化次级任务
        startActivity(new Intent(MainActivity.this, PayDebugMainActivity.class));
    }

    @Override
    protected void onPause() {
        super.onPause();
        finish();
    }

    public void clickInitSecondaryTasks() {
        setContentView(R.layout.activity_main);
        findViewById(R.id.button).setOnClickListener(v -> {
            // 初始化次级任务
            AppApplication.initSecondTasks(getApplication());
            startActivity(new Intent(MainActivity.this, PayDebugMainActivity.class));
        });
    }
}