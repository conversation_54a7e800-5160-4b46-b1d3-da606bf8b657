package com.meituan.android.pay.demo.tasks.first;

import android.app.Application;

import com.dianping.networklog.Logan;
import com.meituan.android.pay.demo.AppInfo;
import com.meituan.android.pay.demo.tasks.InitTask;

import dianping.com.nvlinker.NVLinker;

/**
 * <AUTHOR>
 */
public class LoganTask implements InitTask {

    @Override
    public void init(Application application, AppInfo info) {
        Logan.setBeta(true);
        Logan.setAllowInit(true);
        Logan.init(application,
                info.getAppIdCode(),
                info.getAppFlavor(),
                info.getAppVersionName(),
                new NVLinker.ILikner() {
                    @Override
                    public String getUnionID() {
                        return info.getUUID();
                    }

                    @Override
                    public String getCityID() {
                        return info.getCityId();
                    }
                });
    }
}
