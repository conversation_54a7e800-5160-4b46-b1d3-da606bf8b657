package com.meituan.android.pay.demo.tasks.first;

import android.app.Application;
import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.dianping.titans.js.IJSHandlerDelegate;
import com.dianping.titans.js.JsBridgeResult;
import com.dianping.titansadapter.AbstractJSBPerformer;
import com.dianping.titansmodel.TTBind;
import com.dianping.titansmodel.TTCityInfo;
import com.dianping.titansmodel.TTFingerprint;
import com.dianping.titansmodel.TTPay;
import com.dianping.titansmodel.TTResult;
import com.dianping.titansmodel.TTShare;
import com.dianping.titansmodel.TTUploadPhoto;
import com.dianping.titansmodel.TTUserInfo;
import com.dianping.titansmodel.apimodel.BindTitans;
import com.dianping.titansmodel.apimodel.GetFingerprintTitans;
import com.dianping.titansmodel.apimodel.PayTitans;
import com.dianping.titansmodel.apimodel.ShareTitans;
import com.dianping.titansmodel.apimodel.UploadPhotoTitans;
import com.meituan.android.pay.demo.AppInfo;
import com.meituan.android.pay.demo.tasks.InitTask;
import com.sankuai.meituan.android.knb.KNBConfig;
import com.sankuai.meituan.android.knb.KNBWebManager;

import org.json.JSONObject;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class TitansTask implements InitTask {
    private static final List<String> URL_SCHEMA = Arrays.asList("http", "https");
    private static final List<String> URI_PREFIX = Arrays.asList("tel", "geo",
            "mailto", "imeituan", "meituanpayment", "weixin", "mqqapi", "alipay",
            "alipays", "mttower", "wtloginmqq", "qqmap", "bizmeituan", "baidumap",
            "iosamap", "comgooglemaps", "paesuperbank", "meituanwaimai", "yoda");
    private static final List<String> WHITE_HOST = Arrays.asList("meituan.com",
            "sankuai.com", "dianping.com", "maoyan.com", "51ping.com", "dpfile.com",
            "alpha.dp", "dper.com", "kuxun.cn", "meituan.net", "m-zl-st.cfcmu.cn",
            "m-zl.mucfc.com");

    @Override
    public void init(Application application, AppInfo info) {
        KNBWebManager.init(application,
                new CustomJSBPerformer(info),
                "mt",
                info.getAppIdCode(),
                new CustomEnvironment(info));
    }

    private static class CustomWhiteSet implements KNBWebManager.IWhiteSet {
        @NonNull
        @Override
        public Set<String> getSchemeWhiteSet() {
            return new HashSet<>(URL_SCHEMA);
        }

        @Override
        public Set<String> getHostWhiteSet() {
            return new HashSet<>(WHITE_HOST);
        }

        @NonNull
        @Override
        public Set<String> getPrefixWhiteSet() {
            return new HashSet<>(URI_PREFIX);
        }
    }

    private static class CustomSetting implements KNBWebManager.ISetting {
        private final AppInfo info;

        public CustomSetting(AppInfo info) {
            this.info = info;
        }

        @Override
        public boolean isDebug() {
            return info.isDebug();
        }

        @Override
        public int getWebTimeout() {
            return DEFAULT_TIMEOUT;
        }
    }

    private static class CustomJSBPerformer extends AbstractJSBPerformer {
        private final AppInfo info;

        public CustomJSBPerformer(AppInfo info) {
            this.info = info;
        }

        @Override
        public void bind(BindTitans param, IJSHandlerDelegate<TTBind> callback) {

        }

        @Override
        public void login(IJSHandlerDelegate<TTResult> callback) {

        }

        @Override
        public void getCityInfo(IJSHandlerDelegate<TTCityInfo> callback) {
            TTCityInfo cityInfo = new TTCityInfo();
            cityInfo.cityId = String.valueOf(1L);
            cityInfo.cityName = "北京";
            cityInfo.locCityId = String.valueOf(1L);
            cityInfo.locCityName = "北京";
            cityInfo.type = "mt";
            if (TextUtils.isEmpty(cityInfo.cityName)) {
                cityInfo.errorMsg = "city info is null";
                callback.failCallback(cityInfo);
            } else {
                callback.successCallback(cityInfo);
            }
        }

        @Override
        public void getUserInfo(IJSHandlerDelegate<TTUserInfo> callback) {
            TTUserInfo userInfo = new TTUserInfo();
            if (info.isLogin()) {
                userInfo.userId = info.getUserId();
                userInfo.token = info.getUserToken();
            } else {
                userInfo.userId = "-1";
                userInfo.errorMsg = "user not login.";
            }
            userInfo.unionId = info.getUUID();
            userInfo.writeToJSON();
            callback.successCallback(userInfo);
        }

        @Override
        public void getFingerprint(GetFingerprintTitans param, IJSHandlerDelegate<TTFingerprint> callback) {
            TTFingerprint fingerprint = new TTFingerprint();
            fingerprint.fingerprint = "asd";
            callback.successCallback(fingerprint);
        }

        @Override
        public void getLocation(JSONObject param, IJSHandlerDelegate<JsBridgeResult> callback) {
            if (callback != null) {
                JsBridgeResult result = new JsBridgeResult();
                result.errorCode = -500;
                result.errorMsg = "no context";
                callback.failCallback(result);
                callback.successCallback(result);
            }
        }

        @Override
        public void logout(IJSHandlerDelegate<TTResult> callback) {

        }

        @Override
        public void pay(PayTitans param, IJSHandlerDelegate<TTPay> callback) {

        }

        @Override
        public void share(ShareTitans param, IJSHandlerDelegate<TTShare> callback) {

        }

        @Override
        public void uploadPhoto(UploadPhotoTitans param, IJSHandlerDelegate<TTUploadPhoto> callback) {

        }
    }

    private static class CustomEnvironment implements KNBWebManager.IEnvironment {
        private final AppInfo info;

        public CustomEnvironment(AppInfo info) {
            this.info = info;
        }

        @Override
        public String getUserToken() {
            return info.getUserToken();
        }

        @Override
        public String getCityId() {
            return info.getCityId();
        }

        @Override
        public String getCityName() {
            return info.getCityName();
        }

        @Override
        public String getLocateCityId() {
            return info.getCityId();
        }

        @Override
        public String getLocateCityName() {
            return info.getCityName();
        }

        @Override
        public String getLat() {
            return info.getLatitude();
        }

        @Override
        public String getLng() {
            return info.getLongitude();
        }

        @Override
        public String getUserId() {
            return info.getUserId();
        }

        public String getChannel() {
            return info.getChannel();
        }

        public boolean geolocationEnable() {
            return KNBConfig.getBooleanConfig(KNBConfig.CONFIG_SWITCH_ALLOW_GEOLOCATION, false);
        }

        @Override
        public String getDeviceLevel() {
            return info.getDeviceLevel();
        }

        @Override
        public String getUUID() {
            return info.getUUID();
        }

        @Override
        public String getFingerprint() {
            return info.getFingerprint();
        }

        @Override
        public String getDeviceId() {
            return info.getDeviceId();
        }

        @Override
        public String getWebviewUri() {
            return "meituanpayment://www.meituan.com/web";
        }

        @Override
        public String getKNBAppId() {
            // 标识团购的appid的测试环境, 该参数必传，否则容器初始化会失败
            return info.isDebug() ? "10121" : "10120";
        }

        public Map<String, String> getAppInfoExtras() {
            return new HashMap<>();
        }

        public String getIMEI() {
            return null;
        }

        public String getMac() {
            return null;
        }

    }
}
