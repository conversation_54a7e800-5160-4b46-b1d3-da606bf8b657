package com.meituan.android.pay.demo.tasks.first;

import android.app.Application;

import com.dianping.codelog.IExtraLogInfo;
import com.dianping.codelog.NovaCodeLog;
import com.meituan.android.pay.demo.AppInfo;
import com.meituan.android.pay.demo.tasks.InitTask;
import com.meituan.android.paycommon.lib.BaseConfig;

import org.json.JSONObject;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class RaptorTask implements InitTask {

    @Override
    public void init(Application application, AppInfo info) {
        NovaCodeLog.init(application, new IExtraLogInfo() { //传入applicationContext
            //appId: https://raptor.mws.sankuai.com/client/settings/config-global/constants
            @Override
            public String getAppId() {
                return info.getAppId();
            }

            @Override
            public String getUnionId() {
                return info.getUUID();
            }

            @Override
            public JSONObject getOptionalData() {
                return new JSONObject(Map.of(
                        "mapId", "", // mapID从1.1.2版本开始这个是必需上传的，混淆用
                        "appVersion", BaseConfig.VERSION));// 通常还会上传app版本
            }
        });
    }
}
