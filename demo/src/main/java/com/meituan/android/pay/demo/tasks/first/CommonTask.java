package com.meituan.android.pay.demo.tasks.first;

import android.app.Application;

import com.meituan.android.common.metricx.helpers.AppBus;
import com.meituan.android.common.mtguard.MTGuard;
import com.meituan.android.pay.base.utils.exception.Catch;
import com.meituan.android.pay.demo.AppInfo;
import com.meituan.android.pay.demo.tasks.InitTask;
import com.meituan.android.singleton.ContextSingleton;
import com.sankuai.meituan.Lifecycle.ActivityLifecycleCallbacksSingleton;
import com.sankuai.meituan.arbiter.hook.ArbiterHook;
import com.sankuai.meituan.router.RouteInstrumentation;
import com.sankuai.meituan.serviceloader.ServiceLoader;

/**
 * <AUTHOR>
 */
public class CommonTask implements InitTask {

    @Override
    public void init(Application application, AppInfo info) {
        // singleton
        ContextSingleton.bindInstance(application);

        // lifecycle
        application.registerActivityLifecycleCallbacks(ActivityLifecycleCallbacksSingleton.getInstance());
        AppBus.getInstance().init(application);

        // ServiceLoader
        ServiceLoader.init(application, error -> Catch.with(error).log());

        // arbiter
        try {
            ArbiterHook.injectInstrumentationHook(application);
            ArbiterHook.addMTInstrumentation(new RouteInstrumentation(application, null));
        } catch (Exception e) {
            Catch.with(e).log();
        }

        // MTGuard
        MTGuard.init(application);

    }
}
