package com.meituan.android.pay.demo.tasks.second;

import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.util.Log;

import com.google.zxing.BarcodeFormat;
import com.meituan.android.cipstorage.CIPStorageCenter;
import com.meituan.android.common.locate.MtLocation;
import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.pay.debugkit.utils.system.ZXingUtils;
import com.meituan.android.pay.demo.AppInfo;
import com.meituan.android.pay.demo.tasks.InitTask;
import com.meituan.android.paybase.imageloader.PayBaseImageLoaderInterface;
import com.meituan.android.paybase.login.LoginInterface;
import com.meituan.android.paybase.login.LoginStateListener;
import com.meituan.android.paybase.utils.AppUtils;
import com.meituan.android.paycommon.lib.config.MTPayConfig;
import com.meituan.android.paycommon.lib.config.MTPayProvider;
import com.meituan.android.payimage.glide.PayGlideImageLoader;
import com.meituan.android.payimage.mtpicasso.PayMTPicassoImageLoader;
import com.meituan.android.singleton.FingerprintManagerSingleton;
import com.meituan.android.singleton.UserCenterSingleton;
import com.meituan.passport.LoginActivity;
import com.meituan.passport.UserCenter;
import com.meituan.passport.UserLockHandler;
import com.meituan.passport.pojo.LogoutInfo;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class PaySDKTask implements InitTask {

    @Override
    public void init(Application application, AppInfo info) {
        DebugManager.info().setInPayDemo(true);
        Log.d("DEMO_INIT", "PaySDKTask init: step 1");
        MTPayConfig.config(application, new MTPayProvider() {
            @Override
            public String getChannel() {
                return info.getChannel();
            }

            @Override
            public String getPlatform() {
                return info.getPlatform();
            }

            @Override
            public String getOsVersion() {
                return info.getOSVersion();
            }

            @Override
            public MtLocation getLocation() {
                return info.getLocation();
            }

            @Override
            public String getCityId() {
                return info.getCityId();
            }

            @Override
            public String getDeviceId() {
                return info.getDeviceId();
            }

            @Override
            public String getUserId() {
                return info.getUserId();
            }

            @Override
            public String getUuid() {
                return info.getUUID();
            }

            @Override
            public String getAppName() {
                return info.getAppName();
            }

            @Override
            public String getAppVersionName() {
                return info.getAppVersionName();
            }

            @Override
            public int getAppVersionCode() {
                return info.getAppVersionCode();
            }

            @Override
            public String getCampaign() {
                return info.getCampaign();
            }

            @Override
            public String getFingerprint() {
                return FingerprintManagerSingleton.getInstance().fingerprint();
            }

            @Override
            public String getWechatKey() {
                return info.getAppWXKey();
            }

            @Override
            public String getUserToken() {
                return info.getUserToken();
            }

            @Override
            public Bitmap createCode128(String str, int codeWidth, int codeHeight) {
                return ZXingUtils.createBarCode(str, BarcodeFormat.CODE_128, codeWidth, codeHeight);
            }

            @Override
            public Bitmap createQRCODE(String str, int codeWidth, int codeHeight) {
                return ZXingUtils.createBarCode(str, BarcodeFormat.QR_CODE, codeWidth, codeHeight);
            }

            @Override
            public PayBaseImageLoaderInterface getImageLoader() {
                String imageMode = PayProvider.module().getPayStorage().getString("image", "glide");
                if ("glide".equals(imageMode)) {
                    return new PayGlideImageLoader(application);
                } else {
                    return new PayMTPicassoImageLoader(application);
                }
            }

            @Override
            public LoginInterface getAccountLogin() {
                return new DemoLoginImpl(info);
            }

            @Override
            public boolean isAppMockOn() {
                CIPStorageCenter cipStorageCenter = CIPStorageCenter
                        .instance(application, "debug_net", CIPStorageCenter.MODE_PRIVATE);
                return cipStorageCenter.getBoolean("enable_dianping_mock", false);
            }

            @Override
            public String getAppMockUrl() {
                CIPStorageCenter cipStorageCenter = CIPStorageCenter
                        .instance(application, "debug_net", CIPStorageCenter.MODE_PRIVATE);
                return cipStorageCenter.getString("dianping_mock_url", "");
            }

            @Override
            public Map<ResourceId, Integer> getResourceMap() {
                return new HashMap<>();
            }
        });
        Log.d("DEMO_INIT", "PaySDKTask init: step 2");
        MTPayConfig.configUserLockExceptionHandler((activity, code, message) -> {
            //指定com.meituan.android.library作为组件名
            String componentName = info.getApplicationId();
            //用来统计登出信息，支付的登出是通用网络拦截，url不确定
            LogoutInfo logoutInfo = new LogoutInfo(componentName, new LogoutInfo.NativeUrlData("url unknown", code), null);
            UserLockHandler.getInstance().newUserUnlock(activity, code, message, new UserLockHandler.Callback() {
                @Override
                public void callback(boolean unlockFinish, Throwable e) {
                    if (unlockFinish) {
                        activity.finish();
                    }
                }
            }, logoutInfo);
        });
        Log.d("DEMO_INIT", "PaySDKTask init: step 3");
        MTPayConfig.init(application);
        Log.d("DEMO_INIT", "PaySDKTask init: step 4");
        MTPayConfig.fetchDowngradeData();
        Log.d("DEMO_INIT", "PaySDKTask init: step 5");
        // debug 设置国密环境
        AppUtils.setIsGmProd(false);
        Log.d("DEMO_INIT", "PaySDKTask init: step 6");
    }

    private static class DemoLoginImpl implements LoginInterface {
        private final AppInfo info;

        public DemoLoginImpl(AppInfo info) {
            this.info = info;
        }

        @Override
        public boolean isLogin(Context context) {
            return info.isLogin();
        }

        @Override
        public void login(Context context) {
            context.startActivity(new Intent(context, LoginActivity.class));
        }

        @Override
        public void setLoginStateChangedListener(LoginStateListener listener) {
            UserCenterSingleton.getInstance().loginEventObservable().subscribe(loginEvent -> {
                if (loginEvent != null && listener != null) {
                    if (loginEvent.type == UserCenter.LoginEventType.login) {
                        listener.onLoginStateChanged(true);
                    } else if (loginEvent.type == UserCenter.LoginEventType.logout) {
                        listener.onLoginStateChanged(false);
                    }
                }
            });
        }

        @Override
        public String getPayUserID() {
            return info.getUserId();
        }
    }
}
