package com.meituan.android.pay.demo.tasks.first;

import android.app.Application;
import android.content.Context;

import com.meituan.android.loader.impl.DynLoaderInit;
import com.meituan.android.loader.impl.DynParamsProvider;
import com.meituan.android.pay.demo.AppInfo;
import com.meituan.android.pay.demo.tasks.InitTask;
import com.meituan.met.mercury.load.core.DDLoaderManager;
import com.meituan.met.mercury.load.core.LoaderEnvironment;

/**
 * <AUTHOR>
 */
public class DynLoaderTask implements InitTask {
    @Override
    public void init(Application application, AppInfo info) {
        //初始化 DDD，dynLoader 依赖于 DDD 的下载功能
        DDLoaderManager.init(application, new LoaderEnvironment() {
            @Override
            protected String getUuid() {
                return info.getUUID();
            }

            @Override
            protected String getUserId() {
                return info.getUserId();
            }

            @Override
            protected String getChannel() {
                return info.getChannel();
            }

            @Override
            protected int getMobileAppId() {
                return info.getAppIdCode();
            }

            @Override
            public boolean enableDebug() {
                return info.isDebug();
            }
        });
        // 初始化 DynLoader，标记已下载的资源为可用状态
        DynLoaderInit.initWithoutBatchDownload(application, new DynParamsProvider() {
            @Override
            public long getUserID(Context context) {
                return info.getUserIdCode();
            }

            @Override
            public String getVersionName(Context context) {
                return info.getAppVersionName();
            }

            @Override
            public String getChannel(Context context) {
                return info.getChannel();
            }

            @Override
            public String getUUID(Context context) {
                return info.getUUID();
            }
        });
        //下载并解压所有资源，给没有预下载框架的app使用
        DynLoaderInit.triggerBatchDownload();
    }
}
