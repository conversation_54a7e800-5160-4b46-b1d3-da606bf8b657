package com.meituan.android.pay.demo.tasks.first;

import android.app.Application;

import com.meituan.android.pay.demo.AppInfo;
import com.meituan.android.pay.demo.tasks.InitTask;
import com.meituan.msi.ApiPortalGlobalEnv;
import com.meituan.msi.provider.MsiProvider;

/**
 * <AUTHOR>
 */
public class MSITask implements InitTask {
    @Override
    public void init(Application application, AppInfo info) {
        ApiPortalGlobalEnv.initApp(application, new MsiProvider() {

            @Override
            public String getAppID() {
                return info.getAppId();
            }

            @Override
            public String getUUID() {
                return info.getUUID();
            }

            @Override
            public String getAppCode() {
                return "";
            }

            @Override
            public String getChannel() {
                return info.getChannel();
            }

            @Override
            public String getUserId() {
                return info.getUserId();
            }

            @Override
            public boolean isDebugMode() {
                return info.isDebug();
            }
        });
    }
}
