package com.meituan.android.pay.demo.tasks.first;

import android.app.Application;
import android.content.Context;
import android.text.TextUtils;

import com.dianping.sharkpush.SharkPush;
import com.meituan.android.base.common.util.net.UUIDProvider;
import com.meituan.android.pay.demo.AppInfo;
import com.meituan.android.pay.demo.tasks.InitTask;
import com.meituan.android.singleton.ContextSingleton;
import com.meituan.android.singleton.LazySingletonProvider;
import com.meituan.android.singleton.UUIDProviderSingleton;
import com.meituan.uuid.GetUUID;
import com.meituan.uuid.UUIDListener;

import java.util.ArrayList;
import java.util.List;

import rx.Observable;
import rx.Scheduler;
import rx.Subscriber;
import rx.schedulers.Schedulers;

/**
 * <AUTHOR>
 */
public class UUIDTask implements InitTask {
    @Override
    public void init(Application application, AppInfo info) {
        UUIDProviderSingleton.bindUUIDProviderProvider(new LazySingletonProvider<UUIDProvider>() {
            @Override
            protected UUIDProvider createInstance() {
                return uuidProvider();
            }
        });
        Observable.create(subscriber -> {
                    UUIDProviderSingleton.getInstance();
                    subscriber.onNext(true);
                    subscriber.onCompleted();
                })
                .subscribeOn(Schedulers.newThread())
                .subscribe();
    }

    private UUIDProvider uuidProvider() {
        GetUUID.init(null);

        final GetUUID uuidGetter = GetUUID.getInstance();

        final InternalUUIDProvider UUIDProvider = new InternalUUIDProvider();

        UUIDProvider.registerUUIDListener(SharkPush::updateUnionid);

        uuidGetter.registerUUIDListener(new UUIDListener() {
            @Override
            public void notify(Context context, String uuid) {
                if (!TextUtils.isEmpty(uuid)) {
                    uuidGetter.unregisterUUIDListener(this);
                    UUIDProvider.notifyListeners(uuid);
                }
            }
        });

        uuidGetter.getSyncUUID(ContextSingleton.getInstance(), new UUIDListener() {
            @Override
            public void notify(Context context, String uuid) {
                if (!TextUtils.isEmpty(uuid)) {
                    uuidGetter.unregisterUUIDListener(this);
                    UUIDProvider.notifyListeners(uuid);
                }
            }
        });

        return UUIDProvider;
    }

    private static class InternalUUIDProvider implements UUIDProvider {
        private final List<MtUUIDListener> uuidListeners = new ArrayList<>();
        private volatile boolean isNotify = false;
        private String uuid;

        private void notifyListeners(final String uuid) {
            synchronized (uuidListeners) {
                if (isNotify) {
                    uuidListeners.clear();
                    return;
                }
                isNotify = true;
                for (MtUUIDListener listener : uuidListeners) {
                    if (listener != null) {
                        listener.notify(uuid);
                    }
                }
                uuidListeners.clear();
            }
        }

        @Override
        public void registerUUIDListener(MtUUIDListener listener) {
            synchronized (uuidListeners) {
                if (isNotify) return;
                uuidListeners.add(listener);
            }
        }

        @Override
        public void removeUUIDListener(MtUUIDListener listener) {
            synchronized (uuidListeners) {
                if (isNotify) return;
                uuidListeners.remove(listener);
            }
        }

        @Override
        public String getUUID() {
            if (TextUtils.isEmpty(uuid)) {
                uuid = GetUUID.getInstance().getSyncUUID(ContextSingleton.getInstance(), null);
            }
            return uuid;
        }
    }
}
