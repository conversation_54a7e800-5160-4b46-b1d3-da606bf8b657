package com.meituan.android.pay.demo.tasks.first;

import android.app.Application;

import com.dianping.nvnetwork.NVGlobal;
import com.meituan.android.common.locate.MtLocation;
import com.meituan.android.pay.demo.AppInfo;
import com.meituan.android.pay.demo.tasks.InitTask;
import com.meituan.android.singleton.RetrofitCallFactorySingleton;
import com.sankuai.meituan.kernel.net.IAnalyseInfor;
import com.sankuai.meituan.kernel.net.impl.INetFactoryImpl;

/**
 * <AUTHOR>
 */
public class NetworkTask implements InitTask {
    private static final int NETWORK_LOGIN_TYPE = 1;

    @Override
    public void init(Application application, AppInfo info) {
        // shark
        NVGlobal.init(application, info.getAppIdCode(), 0, info.getChannel(), info::getUUID);
        NVGlobal.setBackgroundMode(false);

        // retrofit
        RetrofitCallFactorySingleton.init(application, new INetFactoryImpl(), new IAnalyseInfor() {
            @Override
            public long userId() {
                return info.getUserIdCode();
            }

            @Override
            public String token() {
                return info.getUserToken();
            }

            @Override
            public int loginType() {
                return NETWORK_LOGIN_TYPE;
            }

            @Override
            public long cityId() {
                return info.getCityIdCode();
            }

            @Override
            public String sessionId() {
                return "";
            }

            @Override
            public MtLocation location() {
                return info.getLocation();
            }

            @Override
            public String pageName() {
                return "";
            }

            @Override
            public String UUID() {
                return info.getUUID();
            }

            @Override
            public int appId() {
                return info.getAppIdCode();
            }

            @Override
            public String deviceId() {
                return info.getDeviceId();
            }

            @Override
            public String channel() {
                return info.getChannel();
            }

            @Override
            public String versionName() {
                return info.getAppVersionName();
            }

            @Override
            public int versionCode() {
                return info.getAppVersionCode();
            }

            @Override
            public boolean debug() {
                return info.isDebug();
            }

            @Override
            public String utmCampaign() {
                return info.getCampaign();
            }
        });

    }
}
