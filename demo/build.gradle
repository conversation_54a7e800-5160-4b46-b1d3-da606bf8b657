apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply from: file('plugin.gradle')

android {
    compileSdk Integer.parseInt(project.ANDROID_BUILD_SDK_VERSION)
    buildToolsVersion project.ANDROID_BUILD_TOOLS_VERSION
    defaultConfig {
        applicationId project.PACKAGE_NAME
        buildConfigField "String", "WX_KEY", "\"wxa552e31d6839de85\""
        buildConfigField "String", "APP_NAME", "\"group\""
        buildConfigField "String", "JOIN_KEY", "\"100854_1203370120\""
        minSdkVersion Integer.parseInt(project.ANDROID_BUILD_MIN_SDK_VERSION)
        targetSdkVersion Integer.parseInt(project.ANDROID_BUILD_TARGET_SDK_VERSION)
        versionCode Integer.parseInt(project.VERSION_CODE)
        versionName project.VERSION_NAME

        ndk {
            abiFilters "arm64-v8a"
        }
    }

    ndkVersion "21.4.7075529"

    signingConfigs {
        sankuai {
            //密钥的MD5 B3:54:61:04:73:E3:C7:D6:B8:E3:06:4C:11:53:3D:5C
            storeFile file("keystore/meituan-debug.keystore")
            storePassword "1234567"
            keyAlias "meituan"
            keyPassword "12345678"
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.sankuai
            minifyEnabled true
            proguardFile 'proguard-rules.txt'
            manifestPlaceholders = [
                    // 必须使用美团的release签名，验签才能生效！！！
                    "mtguard_appkey": "9b69f861-e054-4bc4-9daf-d36ae205ed3e",
                    "mtguard_pic"   : "ms_com.sankuai.meituan.png",
                    "mtguard_sec"   : "ppd_com.sankuai.meituan.xbt",
                    "app_name"      : "pay-demo"
            ]
        }

        debug {
            signingConfig signingConfigs.sankuai
            debuggable true
            minifyEnabled false
            proguardFiles 'proguard-dontobfuscate.txt'
            manifestPlaceholders = [amapkey: "3c952ad1664ed27d4097e01d53b87bae"]
            manifestPlaceholders = [
                    "mtguard_appkey": "ac4319b6-a51d-4cb9-acf9-fd0cc15b9a30",
                    "mtguard_pic"   : "ms_com.sankuai.meituan.paydemo.png",
                    "mtguard_sec"   : "ppd_com.sankuai.meituan.paydemo.xbt",
                    "app_name"      : "paydemo测试"
            ]
        }
    }

    flavorDimensions "paydemo"

    productFlavors {
        meituan {
            applicationId "com.sankuai.meituan"
            buildConfigField "String", "WX_KEY", "\"wxa552e31d6839de85\""
            buildConfigField "String", "APP_NAME", "\"group\""
            buildConfigField "String", "JOIN_KEY", "\"100137_47212118\""
            flavorDimensions "paydemo"
        }
    }

    lintOptions {
        abortOnError false
    }

    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/LICENSE'
        exclude 'NOTICE'
        exclude 'LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
        exclude 'NOTICE.txt'
        exclude 'LICENSE.txt'
        exclude 'META-INF/MANIFEST.MF'
        exclude "META-INF/*.kotlin_module"
        exclude "kotlin/**"
        exclude 'META-INF/rxjava.properties'

        // MRN和Picasso公用so部分
        pickFirst 'lib/*/libjsc.so'
        pickFirst 'lib/*/libicu_common.so'
        pickFirst '**/libc++_shared.so'

        // MSC 和 MRN 公用so部分
        pickFirst 'lib/**/libglog.so'
        pickFirst 'lib/**/libglog_init.so'
        pickFirst 'lib/**/libfolly_json.so'

        doNotStrip "*/armeabi/libYTCommon.so"
        doNotStrip "*/armeabi-v7a/libYTCommon.so"
        doNotStrip "*/x86/libYTCommon.so"
        doNotStrip "*/arm64-v8a/libYTCommon.so"
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    useLibrary 'org.apache.http.legacy'

    sourceSets {
        meituan { assets.srcDirs = ['src/meituan/assets', 'src/meituan/assets/'] }
    }

    dexOptions {
        maxProcessCount 16 // 最大 dx 并发进程进程数, 受电脑内存限制, 32G 内存可以写16
        javaMaxHeapSize "2048m" // Specifies the {@code -Xmx} value when calling dx. Example value is {@code "2048m"}.
        preDexLibraries true
        // 预编译库, 加速增量打包速度, Whether to pre-dex libraries. This can improve incremental builds, but clean builds may be slower.
        jumboMode true // Enable jumbo mode in dx ({@code --force-jumbo}). 大工程模式
        dexInProcess true
        // 在 Gradle 守护中运行 dx, 可以很大改善性能 Whether to run the {@code dx} compiler as a separate process or inside the Gradle daemon JVM.
        threadCount 8 // 默认运行 dx 的线程数, 默认值为4, 多核可加大. Number of threads to use when running dx. Defaults to 4.
    }
}

apply from: file('../configurations.gradle')
apply from: file('../dependency_meituan.gradle')
apply from: file('../dependency_pay.gradle')

dependencies {
    api project(':debugkit')

    // zxing
    implementation('com.google.zxing:core:3.1.0')
    implementation('com.meituan.android.zxing:library:0.2.81') {
        exclude group: 'com.meituan.android.loader', module: 'dynloader'
        exclude group: 'com.sankuai.meituan.pylon', module: 'basemodule'
        exclude group: 'com.sankuai.meituan.pylon', module: 'util'
        exclude group: 'com.sankuai.meituan.pylon', module: 'netsingleton'
    }
}

configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    resolutionStrategy.eachDependency { DependencyResolveDetails details ->
        def requested = details.requested
        if (requested.group == 'com.android.support') {
            if (!requested.name.startsWith("multidex")) {
                details.useVersion '26.0.2'
            }
        }
    }
}