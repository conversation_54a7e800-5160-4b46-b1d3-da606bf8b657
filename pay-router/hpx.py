#!/usr/bin/env python3
# coding=utf-8
# import os
# import subprocess


# def cmd_check_call(cmd, root=None, timeout=None):
#     return subprocess.check_call(cmd, shell=True, cwd=root, timeout=timeout)
#
#
# if __name__ == "__main__":
#     import argparse
#
#     parser = argparse.ArgumentParser()
#     parser.add_argument('--context-json', type=str, help='input json path')
#     args = parser.parse_args()
#     cmd_check_call('git clone ssh://*******************/payan/pay-static-analysis.git')
#     os.chdir('pay-static-analysis')
#     cmd_check_call('./script/hpx.py \'%s\'' % open(args.context_json, encoding='utf-8').read())
#     os.chdir('../')
