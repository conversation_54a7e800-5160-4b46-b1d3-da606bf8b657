// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    def useNewGradle = gradle.gradleVersion >= '7.0.0'
    def gradle_version = useNewGradle ? '7.2.2' : '3.2.1'
    def kotlin_version = useNewGradle ? '1.3.61' : '1.2.51'
    repositories {
        maven {
            url 'http://depot.sankuai.com/nexus/content/groups/public/'
            if (useNewGradle) {
                allowInsecureProtocol = true
            }
        }
        maven {
            url "http://depot.sankuai.com/nexus/content/groups/public/"
            if (useNewGradle) {
                allowInsecureProtocol = true
            }
        }
        maven {
            url "http://mvn.dianpingoa.com/android-nova"
            if (useNewGradle) {
                allowInsecureProtocol = true
            }
        }
        maven {
            url "http://mvn.dianpingoa.com/android-nova-snapshot"
            if (useNewGradle) {
                allowInsecureProtocol = true
            }
        }
        mavenCentral()
    }
    dependencies {
        classpath "com.android.tools.build:gradle:${gradle_version}"
        classpath 'com.github.dcendents:android-maven-gradle-plugin:1.4.1'
        classpath 'me.tatarka:gradle-retrolambda:3.7.1'
        classpath 'com.meituan.plugin:mtjacoco:1.0.10'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:${kotlin_version}"
    }
}

allprojects {
    repositories {
        def useNewGradle = gradle.gradleVersion >= '7.0.0'
        maven {
            url "http://pixel.sankuai.com/repository/mtdp"
            if (useNewGradle) {
                allowInsecureProtocol = true
            }
        }
        maven {
            url 'http://depot.sankuai.com/nexus/content/groups/public/'
            if (useNewGradle) {
                allowInsecureProtocol = true
            }
        }
        mavenLocal()
    }

    tasks.withType(JavaCompile) { options.encoding = "UTF-8" }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
