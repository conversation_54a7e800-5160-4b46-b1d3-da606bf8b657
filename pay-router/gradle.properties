# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default Value: org.gradle.jvmargs=-Xmx1536m
# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

org.gradle.jvmargs=-Xmx5120m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.daemon=true
RELEASE_REPOSITORY_URL=http://nexus:8081/nexus/content/repositories/thirdparty
SNAPSHOT_REPOSITORY_URL=http://nexus:8081/nexus/content/repositories/3rdPartySnapshot
POM_DESCRIPTION=meituan pau router
POM_URL=
POM_SCM_URL=
POM_SCM_CONNECTION=
POM_SCM_DEV_CONNECTION=
POM_LICENCE_NAME=
POM_LICENCE_URL=
POM_LICENCE_DIST=
POM_DEVELOPER_ID=
POM_DEVELOPER_NAME=

ANDROID_BUILD_TARGET_SDK_VERSION=35
ANDROID_BUILD_MIN_SDK_VERSION=21
ANDROID_BUILD_TOOLS_VERSION=28.0.3
ANDROID_BUILD_SDK_VERSION=30

GROUP=com.meituan.android.pay-router
HPX_JDK_VERSION=11