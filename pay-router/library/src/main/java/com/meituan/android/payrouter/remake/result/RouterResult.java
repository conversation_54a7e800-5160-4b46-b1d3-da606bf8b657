package com.meituan.android.payrouter.remake.result;

import android.content.Intent;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.support.annotation.Keep;
import android.support.annotation.NonNull;

import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.paybase.utils.MapUtils;

import java.util.HashMap;
import java.util.Map;

@Keep
public class RouterResult implements Parcelable {
    public static final String DATA_EXTRA_JSON = "router_extra_json";

    /**
     * 返回code
     */
    private int code;

    /**
     * 返回message
     */
    private String message;

    /**
     * 支付业务携带的信息
     */
    private Intent data;

    /**
     * 支付路由携带的信息
     */
    private Bundle extras;

    public RouterResult() {
    }

    protected RouterResult(Parcel in) {
        code = in.readInt();
        message = in.readString();
        data = in.readParcelable(Intent.class.getClassLoader());
        extras = in.readBundle(Bundle.class.getClassLoader());
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(code);
        dest.writeString(message);
        dest.writeParcelable(data, flags);
        dest.writeBundle(extras);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<RouterResult> CREATOR = new Creator<RouterResult>() {
        @Override
        public RouterResult createFromParcel(Parcel in) {
            return new RouterResult(in);
        }

        @Override
        public RouterResult[] newArray(int size) {
            return new RouterResult[size];
        }
    };

    public int getCode() {
        return code;
    }

    public Intent getData() {
        return data;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Bundle getExtras() {
        return extras;
    }

    public void setExtras(Bundle extras) {
        this.extras = extras;
    }

    public static RouterResult newResult(int code, String message, Intent data) {
        RouterResult result = new RouterResult();
        result.code = code;
        result.message = message;
        result.data = data;
        return result;
    }

    public static RouterResult newResult(int code, String message) {
        return newResult(code, message, null);
    }

    public boolean isSuccess() {
        return code == 200;
    }

    public boolean isFail() {
        return code >= 500;
    }

    public boolean isCancel() {
        return code >= 400 && code < 500;
    }

    public boolean isError() {
        return isSuccess() || isFail() || isCancel();
    }

    @NonNull
    @Override
    public String toString() {
        return GsonProvider.getInstance().toJson(this);
    }

    public Map<String, Object> toReport() {
        String dataString = data != null ? String.valueOf(data.getExtras()) : "<Empty Data>";
        String extrasString = extras != null ? String.valueOf(extras) : "Empty Extras";
        return MapUtils.builder().add("code", code)
                .add("message", message)
                .add("data", dataString)
                .add("extras", extrasString).build();
    }
}
