package com.meituan.android.payrouter.utils.bus;

public interface RouterBus {

    boolean sendMessage(Message message);

    boolean flushMessage(Message message);

    void subscribe(String trace, Observer observer);

    void unsubscribe(String trace);

    void unsubscribe(String trace, Observer observer);

    interface Observer {
        boolean onReceived(RouterBus bus, Message message);
    }

}
