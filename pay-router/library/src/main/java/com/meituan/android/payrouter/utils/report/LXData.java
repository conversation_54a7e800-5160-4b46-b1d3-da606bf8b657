package com.meituan.android.payrouter.utils.report;

import android.support.annotation.Keep;
import android.text.TextUtils;

import com.meituan.android.paybase.utils.StatisticsUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Keep
public class LXData implements Serializable {
    private String cid = StatisticsUtils.CID_DEFAULT;
    private String bid;
    private String pageInfoKey;
    private String businessKey = "com.meituan.android.cashier.common.CashierStaticsUtils";
    private Map<String, Object> valLab;
    private boolean mis;

    private LXData() {
        valLab = new HashMap<>();
    }

    public String getCid() {
        return cid;
    }

    public String getBid() {
        return bid;
    }

    public String getPageInfoKey() {
        return pageInfoKey;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public Map<String, Object> getValLab() {
        return valLab;
    }

    public boolean isMis() {
        return mis;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static Builder bid(String bid) {
        return new Builder().bid(bid);
    }

    public static Builder builder(LXData lxData) {
        return new Builder(lxData);
    }

    public static class Builder {
        private final LXData lxData;

        private Builder() {
            this.lxData = new LXData();
        }

        private Builder(LXData lxData) {
            this.lxData = lxData != null ? lxData : new LXData();
        }

        public LXData build() {
            return lxData;
        }

        public Builder cid(String cid) {
            lxData.cid = cid;
            return this;
        }

        public Builder bid(String bid) {
            lxData.bid = bid;
            return this;
        }

        public Builder pageInfoKey(String pageInfoKey) {
            lxData.pageInfoKey = pageInfoKey;
            return this;
        }

        public Builder businessKey(String businessKey) {
            lxData.businessKey = businessKey;
            return this;
        }

        public Builder valLab(Map<String, Object> valLab) {
            if (valLab != null) {
                lxData.valLab.putAll(valLab);
            }
            return this;
        }

        public Builder valLab(String key, Serializable val) {
            if (!TextUtils.isEmpty(key) && val != null) {
                lxData.valLab.put(key, val);
            }
            return this;
        }

        // 设置是否进入mis系统
        public Builder mis() {
            lxData.mis = true;
            return this;
        }

    }
}
