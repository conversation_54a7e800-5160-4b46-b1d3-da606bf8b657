package com.meituan.android.payrouter.utils.report;

import android.support.annotation.Keep;
import android.text.TextUtils;

import com.meituan.android.paybase.utils.CollectionUtils;

import java.io.Serializable;
import java.util.Map;

@Keep
public class RouterReportData implements Serializable {
    private LXData lxData;
    private RaptorData raptorData;
    private String trace;
    private Map<String, Object> extras;

    public LXData getLxData() {
        return lxData;
    }

    public RaptorData getRaptorData() {
        return raptorData;
    }

    public RouterReportData setLxData(LXData lxData) {
        this.lxData = lxData;
        return this;
    }

    public RouterReportData setRaptorData(RaptorData raptorData) {
        this.raptorData = raptorData;
        return this;
    }

    public RouterReportData setExtras(Map<String, Object> extras) {
        if (CollectionUtils.isEmpty(extras)) {
            return this;
        }
        if (lxData != null) {
            lxData.getValLab().putAll(extras);
        }
        if (raptorData != null) {
            raptorData.getCustom().putAll(extras);
        }
        return this;
    }

    public RouterReportData setExtra(String key, Object val) {
        if (TextUtils.isEmpty(key) || val == null) {
            return this;
        }
        if (lxData != null) {
            lxData.getValLab().put(key, val);
        }
        if (raptorData != null) {
            raptorData.getCustom().put(key, val);
        }
        return this;
    }

    public static RouterReportData create() {
        return new RouterReportData();
    }


}
