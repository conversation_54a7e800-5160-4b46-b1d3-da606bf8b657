package com.meituan.android.payrouter.remake.manager;

import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.android.paybase.utils.CatchException;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.payrouter.remake.base.InterfaceChecker;
import com.meituan.android.payrouter.remake.base.NoUniquely;
import com.meituan.android.payrouter.utils.ProxyUtils;
import com.meituan.android.payrouter.utils.Reflector;

import java.util.Collection;
import java.util.LinkedList;

/**
 * <AUTHOR>
 * 一个基于类型 T 的观察者对象
 */
public class RouterObservableImpl<T> implements RouterObservable<T> {
    private static final String DEFAULT_TAG = "default";

    /**
     * 观察者对象，用 RouterLifecycleType 标记观察者对象的生命周期，销毁时会按照 RouterLifecycleType 的类型进行销毁
     */
    private final LinkedList<Item> observerList = new LinkedList<>();

    /**
     * Class类型，用于类型判定
     */
    private final Class<T> clz;
    /**
     * 是否唯一：如果唯一，则不允许从register中进行注册，只能通过registerUniquely进行注册。
     */
    private boolean unique;

    public RouterObservableImpl() {
        //noinspection unchecked
        this.clz = (Class<T>) Reflector.getGenericSuperInterfaceClass(this, RouterObservable.class);
    }

    public RouterObservableImpl(Class<T> clz) {
        this.clz = clz;
    }

    public RouterObservable<T> proxy(String tag) {
        return new Proxy(tag);
    }

    void subscribe(T observer, String tag) {
        if (!unique || observerList.isEmpty()) {
            unique = false;
            observerList.add(new Item(tag, observer));
        }
    }

    /**
     * 强制注册为唯一的观察者，和set的效果相同。
     * 不可以被强制注册的类型必须是被{@link NoUniquely}标记的类型，否则都可以注册为唯一观察者。
     *
     * @param observer T 类型的观察者
     */
    void subscribeUniquely(T observer, String tag) {
        if (!InterfaceChecker.isNoUniquelyInterface(clz)) {
            unique = true;
            observerList.clear();
        }
        observerList.add(new Item(tag, observer));
    }

    void unsubscribe(String tag) {
        CollectionUtils.removeIf(observerList, item -> item == null || item.isTag(tag));
    }

    @Override
    public void subscribe(T observer) {
        subscribe(observer, DEFAULT_TAG);
    }

    @Override
    public void subscribeUniquely(T observer) {
        subscribeUniquely(observer, DEFAULT_TAG);
    }

    @Override
    public void unsubscribe(T observer) {
        CollectionUtils.removeIf(observerList, item -> item == null || item.isObserver(observer));
    }

    @Override
    public void unsubscribe() {
        unsubscribe(DEFAULT_TAG);
    }

    @Override
    public Collection<T> observers() {
        return new LinkedList<>();
    }

    @Override
    public T dispatch(boolean consumedReturn) {
        return ProxyUtils.transferList(clz, observerList, consumedReturn, item -> item.observer);
    }

    @Override
    public T dispatch() {
        return dispatch(false);
    }

    @Override
    public T subscriber() {
        return CatchException.run(() -> observers().iterator().next()).value();
    }

    private class Proxy implements RouterObservable<T> {
        private final String tag;

        public Proxy(String tag) {
            this.tag = tag;
        }

        @Override
        public void subscribe(T observer) {
            RouterObservableImpl.this.subscribe(observer, tag);
        }

        @Override
        public void subscribeUniquely(T observer) {
            RouterObservableImpl.this.subscribeUniquely(observer, tag);
        }

        @Override
        public void unsubscribe(T observer) {
            RouterObservableImpl.this.unsubscribe(observer);
        }

        @Override
        public void unsubscribe() {
            RouterObservableImpl.this.unsubscribe(tag);
        }

        @Override
        public Collection<T> observers() {
            return RouterObservableImpl.this.observers();
        }

        @Override
        public RouterObservable<T> proxy(String tag) {
            return RouterObservableImpl.this.proxy(tag);
        }

        @Override
        public T dispatch(boolean consumedReturn) {
            return RouterObservableImpl.this.dispatch(consumedReturn);
        }

        @Override
        public T dispatch() {
            return RouterObservableImpl.this.dispatch();
        }

        @Override
        public T subscriber() {
            return RouterObservableImpl.this.subscriber();
        }
    }

    private class Item {
        String tag;

        T observer;

        Item(String tag, T observer) {
            this.tag = tag;
            this.observer = observer;
        }

        @Override
        public boolean equals(@Nullable Object obj) {
            if (obj instanceof RouterObservableImpl.Item && observer != null) {
                //noinspection unchecked
                return observer.equals(((Item) obj).observer);
            }
            return super.equals(obj);
        }

        public boolean isTag(String tag) {
            return TextUtils.equals(this.tag, tag);
        }

        public boolean isObserver(T observer) {
            return this.observer == observer;
        }
    }
}
