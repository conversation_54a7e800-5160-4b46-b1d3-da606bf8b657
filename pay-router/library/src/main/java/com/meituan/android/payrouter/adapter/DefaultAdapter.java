package com.meituan.android.payrouter.adapter;

import com.meituan.android.payrouter.load.RouterLoadResultData;
import com.meituan.android.payrouter.router.RouterRequestData;

public class DefaultAdapter extends PayRouterAdapterInterface {

    @Override
    public ConsumeResult isConsume(RouterRequestData routerRequestData) {
        return ConsumeResult.success("default adapter");
    }

    @Override
    public void invoke(RouterRequestData routerRequestData) {
        if (routerLoadCallback != null) {
            routerLoadCallback.loadResultCallback(new RouterLoadResultData(RouterLoadResultData.LoadResultCode.ROUTER_LOAD_FAIL,"enter default adapter"));
        } else {
            reportCommonError("RouterLoadCallback for RouterAdapter is null");
        }
    }
}
