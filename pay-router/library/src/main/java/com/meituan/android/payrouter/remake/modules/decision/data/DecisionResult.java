package com.meituan.android.payrouter.remake.modules.decision.data;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.support.annotation.Keep;
import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.android.paybase.utils.GsonProvider;

@Keep
public class DecisionResult extends DowngradeData implements Parcelable {
    private boolean success; // 是否决策成功，决策成功则可以继续执行，否则会进行降级

    private boolean fatalError; // 严重错误，无法执行决策逻辑，需要进行降级

    private String message;

    public DecisionResult() {
    }

    public DecisionResult(DowngradeData downgradeData) {
        super(downgradeData);
    }

    protected DecisionResult(Parcel in) {
        super(in);
        success = in.readByte() != 0;
        fatalError = in.readByte() != 0;
        message = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        super.writeToParcel(dest, flags);
        dest.writeByte((byte) (success ? 1 : 0));
        dest.writeByte((byte) (fatalError ? 1 : 0));
        dest.writeString(message);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<DecisionResult> CREATOR = new Creator<DecisionResult>() {
        @Override
        public DecisionResult createFromParcel(Parcel in) {
            return new DecisionResult(in);
        }

        @Override
        public DecisionResult[] newArray(int size) {
            return new DecisionResult[size];
        }
    };

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public boolean isFatalError() {
        return fatalError;
    }

    public void setFatalError(boolean fatalError) {
        this.fatalError = fatalError;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static boolean isValid(DecisionResult result) {
        return result != null && !TextUtils.isEmpty(result.getDestProductType()) && !TextUtils.isEmpty(result.getDestAdapterType());
    }

    public static boolean isSuccess(DecisionResult result) {
        return isValid(result) && result.isSuccess();
    }

    public static boolean isFatalError(DecisionResult result) {
        return result == null || result.isFatalError();
    }

    public static DecisionResult success(String destProductType, String destAdapterType) {
        return success(destProductType, destAdapterType, null);
    }

    public static DecisionResult success(String destProductType, String destAdapterType, Bundle downgradeExtras) {
        DecisionResult decisionResult = new DecisionResult();
        decisionResult.setSuccess(true);
        decisionResult.setDestProductType(destProductType);
        decisionResult.setDestAdapterType(destAdapterType);
        decisionResult.setDowngradeExtras(downgradeExtras);
        return decisionResult;
    }

    public static DecisionResult fail(String message) {
        DecisionResult result = new DecisionResult();
        result.setMessage(message);
        return result;
    }

    public static DecisionResult fatalError(String message) {
        DecisionResult result = fail(message);
        result.setFatalError(true);
        return result;
    }

    public static DecisionResult getWithDowngradeExtras(DecisionResult result, DowngradeData downgradeData) {
        if (result != null && downgradeData != null) {
            result.setDowngradeExtras(downgradeData.getDowngradeExtras());
            result.setFromBusiness(downgradeData.isFromBusiness());
        }
        return result;
    }

    @NonNull
    @Override
    public String toString() {
        return GsonProvider.getInstance().toJson(this);
    }
}
