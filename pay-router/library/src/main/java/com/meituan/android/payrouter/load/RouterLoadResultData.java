package com.meituan.android.payrouter.load;


import android.support.annotation.Keep;

import java.io.Serializable;
import java.util.HashMap;

/**
 * 加载模块回调数据
 */
@Keep
public class RouterLoadResultData implements Serializable {

    private static final long serialVersionUID = -3843966507093513871L;
    private RouterLoadResultData.LoadResultCode resultCode;
    //降级的数据，可以指定 productType 和 adapterType
    private RouterDowngradeData downGradeData;
    private String msg;
    private HashMap<String, Object> map;

    public RouterLoadResultData(LoadResultCode resultCode, String msg, RouterDowngradeData downGradeData) {
        this.resultCode = resultCode;
        this.msg = msg;
        this.downGradeData = downGradeData;
    }

    public RouterLoadResultData(LoadResultCode resultCode, String msg, RouterDowngradeData downGradeData, HashMap<String, Object> extraMap) {
        this.resultCode = resultCode;
        this.msg = msg;
        this.downGradeData = downGradeData;
        this.map = extraMap;
    }

    public RouterLoadResultData(LoadResultCode resultCode, HashMap<String, Object> map) {
        this.resultCode = resultCode;
        this.map = map;
    }

    public RouterLoadResultData(LoadResultCode resultCode, String msg) {
        this.resultCode = resultCode;
        this.msg = msg;
    }

    public static RouterLoadResultData build(LoadResultCode resultCode, String msg) {
        return new RouterLoadResultData(resultCode, msg);
    }


    public HashMap<String, Object> getMap() {
        return map;
    }

    public RouterLoadResultData setMap(HashMap<String, Object> map) {
        this.map = map;
        return this;
    }

    public RouterLoadResultData setLoadResultCode(LoadResultCode resultCode) {
        this.resultCode = resultCode;
        return this;
    }

    public RouterDowngradeData getDowngradeData() {
        return downGradeData;
    }

    public RouterLoadResultData setDowngradeData(RouterDowngradeData downGradeData) {
        this.downGradeData = downGradeData;
        return this;
    }

    public String getMsg() {
        return msg;
    }

    public LoadResultCode getResultCode() {
        return resultCode;
    }

    public boolean isLoadSuccess() {
        return resultCode == LoadResultCode.ROUTER_LOAD_SUCCESS;
    }

    public boolean isLoadFail() {
        return resultCode == LoadResultCode.ROUTER_LOAD_FAIL;
    }

    public boolean isLoadDowngrade() {
        return resultCode == LoadResultCode.ROUTER_LOAD_DOWNGRADE;
    }

    @Keep
    public enum LoadResultCode implements Serializable  {
        //打开成功
        ROUTER_LOAD_SUCCESS,
        //打开失败
        ROUTER_LOAD_FAIL,
        //降级
        ROUTER_LOAD_DOWNGRADE
    }
}
