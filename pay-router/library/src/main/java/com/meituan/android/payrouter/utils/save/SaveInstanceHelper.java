package com.meituan.android.payrouter.utils.save;

import android.os.Bundle;
import android.os.Parcelable;

import com.meituan.android.paybase.utils.CatchException;

import java.io.Serializable;
import java.lang.reflect.Field;

public final class SaveInstanceHelper {

    public static void save(Bundle outState, Object object, Class<?> targetClz) {
        if (outState == null || object == null || targetClz == null || !targetClz.isInstance(object)) {
            return;
        }
        Field[] fields = targetClz.getDeclaredFields();
        for (Field field : fields) {
            if (!field.isAnnotationPresent(Save.class)) {
                continue;
            }
            CatchException.run(() -> {
                field.setAccessible(true);
                String key = getKey(targetClz, field);
                if (field.get(object) instanceof Serializable) {
                    outState.putSerializable(key, (Serializable) field.get(object));
                } else if (field.get(object) instanceof Parcelable) {
                    outState.putParcelable(key, (Parcelable) field.get(object));
                }
            }).catchForReport("SaveInstanceHelper_save");
        }
        if (targetClz != Object.class) {
            save(outState, object, targetClz.getSuperclass());
        }
    }

    public static void restore(Bundle outState, Object object, Class<?> targetClz) {
        if (outState == null || object == null || targetClz == null || !targetClz.isInstance(object)) {
            return;
        }
        Field[] fields = targetClz.getDeclaredFields();
        for (Field field : fields) {
            if (!field.isAnnotationPresent(Save.class)) {
                continue;
            }
            CatchException.run(() -> {
                field.setAccessible(true);
                String key = getKey(targetClz, field);
                Object value = outState.get(key);
                if (value instanceof Serializable) {
                    field.set(object, value);
                } else if (value instanceof Parcelable) {
                    field.set(object, value);
                }
            }).catchForReport("SaveInstanceHelper_restore");
        }
        if (targetClz != Object.class) {
            restore(outState, object, targetClz.getSuperclass());
        }
    }

    private static String getKey(Class<?> clazz, Field field) {
        return clazz.getName() + "." + field.getName();
    }
}
