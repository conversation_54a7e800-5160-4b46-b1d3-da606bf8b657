package com.meituan.android.payrouter.remake.router.data;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.support.annotation.Keep;

import com.meituan.android.payrouter.remake.modules.decision.data.DecisionResult;

import java.io.Serializable;

@Keep
public class RouterData implements Parcelable {
    /**
     * Router 类型索引，用于获取 Router 的实现
     */
    private String routerType;

    /**
     * Router 流程中所需要的业务数据，用于决策、降级、加载等流程
     */
    private Bundle businessData;

    private DecisionResult finalDecisionResult;

    private String finalProductData;


    public RouterData() {
    }

    protected RouterData(Parcel in) {
        routerType = in.readString();
        businessData = in.readBundle(getClass().getClassLoader());
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(routerType);
        dest.writeBundle(businessData);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<RouterData> CREATOR = new Creator<RouterData>() {
        @Override
        public RouterData createFromParcel(Parcel in) {
            return new RouterData(in);
        }

        @Override
        public RouterData[] newArray(int size) {
            return new RouterData[size];
        }
    };

    public String getRouterType() {
        return routerType;
    }

    public void setRouterType(String routerType) {
        this.routerType = routerType;
    }

    public Bundle businessData() {
        if (businessData == null) {
            businessData = new Bundle();
        }
        return businessData;
    }

    public static RouterData create(String routerType) {
        RouterData routerData = new RouterData();
        routerData.setRouterType(routerType);
        return routerData;
    }

    public static Builder builder(String routerType) {
        return new Builder().setRouterType(routerType);
    }

    public static class Builder {
        private String routerType;

        private Bundle tmpData = new Bundle();

        private Bundle businessData = new Bundle();

        public Builder setRouterType(String routerType) {
            this.routerType = routerType;
            return this;
        }

        public Builder setBusinessData(Bundle businessData) {
            this.businessData = businessData;
            return this;
        }

        public Builder addStringData(String key, String value) {
            tmpData.putString(key, value);
            return this;
        }

        public Builder addSerializableData(String key, Serializable value) {
            tmpData.putSerializable(key, value);
            return this;
        }

        public Builder addParcelableData(String key, Parcelable value) {
            tmpData.putParcelable(key, value);
            return this;
        }

        public RouterData build() {
            RouterData routerData = new RouterData();
            routerData.routerType = routerType;
            if (businessData == null) {
                businessData = tmpData;
            } else {
                businessData.putAll(tmpData);
            }
            routerData.businessData = businessData;
            return routerData;
        }
    }

}
