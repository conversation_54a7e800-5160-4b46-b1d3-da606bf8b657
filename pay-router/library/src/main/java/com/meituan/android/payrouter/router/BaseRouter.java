package com.meituan.android.payrouter.router;

import android.os.Bundle;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.paybase.common.activity.PayBaseActivity;
import com.meituan.android.paybase.payrouter.RouterActivityProxy;
import com.meituan.android.paybase.payrouter.callback.RouterCallback;

/**
 * 实现了路由代理层的接口RouterObserver
 */
public abstract class BaseRouter implements RouterObserver {

    @SerializedName("routerUniqueId")
    private final String routerUniqueId;

    public BaseRouter(String routerUniqueId) {
        this.routerUniqueId = routerUniqueId;
    }

    public String getRouterUniqueId() {
        return routerUniqueId;
    }

    public abstract void open();

    /**
     * 路由开始
     */
    public void onCreate(RouterActivityProxy routerActivityProxy, RouterData routerData, boolean restored) {
        RouterManagerOld.register(routerUniqueId, this);
    }

    /**
     * 路由结束
     */
    public void onDestroy() {
        RouterManagerOld.unregister(routerUniqueId);
    }

    public abstract RouterActivityProxy getRouterProxy();

    public abstract void restoreByActivity(PayBaseActivity activity, RouterCallback routerCallback, Bundle bundle);
}
