package com.meituan.android.payrouter.remake.router;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.support.annotation.Nullable;

import com.meituan.android.payrouter.remake.Constants;
import com.meituan.android.payrouter.remake.base.OnDestroy;
import com.meituan.android.payrouter.remake.base.OnLoadFinished;
import com.meituan.android.payrouter.remake.base.Restorable;
import com.meituan.android.payrouter.remake.base.Traceable;
import com.meituan.android.payrouter.remake.manager.RouterObservable;
import com.meituan.android.payrouter.remake.manager.RouterObservableManager;
import com.meituan.android.payrouter.remake.modules.decision.DecisionObserver;
import com.meituan.android.payrouter.remake.modules.decision.RouterDecisionManager;
import com.meituan.android.payrouter.remake.modules.decision.data.DecisionResult;
import com.meituan.android.payrouter.remake.modules.decision.data.DowngradeData;
import com.meituan.android.payrouter.remake.modules.load.LoaderObserver;
import com.meituan.android.payrouter.remake.modules.load.RouterLoaderManager;
import com.meituan.android.payrouter.remake.modules.load.data.LoadData;
import com.meituan.android.payrouter.remake.modules.load.data.LoadResult;
import com.meituan.android.payrouter.remake.result.RouterResult;
import com.meituan.android.payrouter.remake.router.adapter.RouterAdapterManager;
import com.meituan.android.payrouter.remake.router.context.RouterContext;
import com.meituan.android.payrouter.remake.router.context.RouterRequest;
import com.meituan.android.payrouter.remake.router.data.RouterData;
import com.meituan.android.payrouter.remake.router.manager.RouterManager;
import com.meituan.android.payrouter.utils.Tracer;
import com.meituan.android.payrouter.utils.report.RouterReporter;
import com.meituan.android.payrouter.utils.save.Save;
import com.meituan.android.payrouter.utils.save.SaveInstanceHelper;

/**
 * AbstractRouter主要用于适配内部模块，实现了重建恢复，对接Decision，Loader，AdapterManager等模块，进行模块之间的逻辑调用。
 *
 * @noinspection FieldMayBeFinal
 */
public class Router implements RouterContext, Traceable, Restorable, DecisionObserver, LoaderObserver, OnDestroy {

    private final String trace;
    /*
    初始化顺序：
    1. 先初始化路由适配器，右适配器引申获取决策模块的索引
    2. 由决策模块的索引，获取决策模块的实现。
    3. 初始化加载模块。
     */
    protected RouterDecisionManager decisionManager;
    protected RouterAdapterManager adapterManager;
    protected RouterLoaderManager loaderManager;

    @Save
    private RouterData data; // 业务数据

    private String type;
    private Status status;
    private Context context;
    protected RouterRequest request;

    public Router(Context context, RouterData data) {
        this(context, data, Tracer.newId());
    }

    public Router(Context context, RouterData data, String trace) {
        this.trace = trace;
        this.context = context;
        this.status = Status.READY;
        this.data = data != null ? data : RouterData.builder("unknown").build();
        this.type = data != null ? data.getRouterType() : "unKnown";
        this.request = new RouterRequest() {
            @Override
            public void load(LoadData data) {
                requestNext(Operation.next(Status.LOAD).setLoadData(data));
            }

            @Override
            public void downgrade(DowngradeData data) {
                requestNext(Operation.next(Status.DOWNGRADE)
                        .setDowngradeData(DowngradeData.wrapDowngradeDataFromBusiness(data)));
            }

            @Override
            public void finish(RouterResult result) { // 通过RouterManager通知路由结果
                requestNext(Operation.next(Status.RESULT).setResult(result));
            }
        };
    }

    // ***** Custom 生命周期
    public void onCreate(@Nullable Bundle savedInstanceState) {
        status = savedInstanceState == null ? Status.CREATE : Status.RECREATE;
        // 启动前先重建数据(RouterData)
        if (savedInstanceState != null) {
            SaveInstanceHelper.restore(savedInstanceState, this, getClass());
        }
        // 检查数据，数据不合法会报错，直接返回路由失败
        if (data == null) {
            RouterManager.notifier(trace()).notifyRouterResult(RouterResult.newResult(Constants.ERROR_CODE_COMMON, "init data is null"));
            return;
        }
        observable(DecisionObserver.class).subscribe(this);
        observable(LoaderObserver.class).subscribe(this);

        // 创建并初始化
        loaderManager = new RouterLoaderManager(this);
        adapterManager = new RouterAdapterManager(this, data);
        decisionManager = new RouterDecisionManager(this, data);

        adapterManager.onCreate(savedInstanceState);
        decisionManager.onCreate(savedInstanceState);

        RouterReporter.reporter().getExtras(trace).clear();
        RouterReporter.reporter().getExtras(trace).put("create_type", String.valueOf(status));
        RouterReporter.reporter().getExtras(trace).put("router_rebuild", "1");
        RouterReporter.reporter().routerStart(trace, data);
    }

    @Override
    public void onSaveInstanceState(Bundle state) {
        SaveInstanceHelper.save(state, this, getClass());
        decisionManager.onSaveInstanceState(state);
        adapterManager.onSaveInstanceState(state);
    }

    @Override
    public void onDestroy() {
        status = Status.DESTROY;
        decisionManager.onDestroy();
        adapterManager.onDestroy();
    }

    public void onStart() {
        decisionManager.onStart();
    }

    private void requestNext(Operation operation) {
        // 如果未执行CREATE，或者已经是RESULT、DESTROY以后了，不再进行处理
        if (!status.isInitialized() || status.isUnWorkable()) {
            return;
        }
        this.status = operation.nextStatus;
        if (operation.nextStatus == Status.DOWNGRADE) {
            decisionManager.downgrade(operation.downgradeData); //  请求降级，可以执行多次。
        } else if (operation.nextStatus == Status.ADAPTER) {
            adapterManager.invoke();
        } else if (operation.nextStatus == Status.LOAD) {
            loaderManager.onLoad(operation.loadData); // 请求加载，可以执行多次；都生成新的Loader，如果加载数据错误则会触发降级
        } else if (operation.nextStatus == Status.ERROR) {
            requestNext(Operation.next(Status.RESULT).setResult(operation.result));
        } else if (operation.nextStatus == Status.RESULT) {
            RouterManager.notifier(trace()).notifyRouterResult(operation.result);
            RouterReporter.reporter().routerEnd(trace, data, operation.result);
        } else {
            // fatal error
        }
    }

    @Override
    public void onDecisionResult(DecisionResult result) {
        if (DecisionResult.isFatalError(result)) { // 严重错误无法获取正确的决策结果，需要返回给业务层错误。
            requestNext(Operation.next(Status.ERROR).setError(result));
            return;
        }
        // 决策失败 或 routerAdapter 不接受决策结果，则进行降级
        if (!DecisionResult.isSuccess(result) || !adapterManager.isDecisionResultValid(result)) {
            DowngradeData downgradeData = DowngradeData.create(result.getDestProductType());
            requestNext(Operation.next(Status.DOWNGRADE).setDowngradeData(downgradeData));
        } else { // 决策成功，执行启动流程
            requestNext(Operation.next(Status.ADAPTER));
        }
    }

    @Override
    public void onLoadResult(LoadResult result) {
        DowngradeData downgradeData = LoadResult.prepareDowngrade(result); // downgradeData为null表示load成功
        observable(OnLoadFinished.class).dispatch(false).onLoadFinished(downgradeData == null); // 广播加载完成的消息
        if (downgradeData != null) {
            requestNext(Operation.next(Status.DOWNGRADE).setDowngradeData(downgradeData));
        }
    }

    //******************** RouterContext ********************
    @Override
    public <T> RouterObservable<T> observable(Class<T> clz) {
        return RouterObservableManager.with(this).observable(clz, "router");
    }

    @Override
    public RouterRequest request() {
        return request;
    }

    @Override
    public Activity getActivity() {
        return context instanceof Activity ? (Activity) context : null;
    }

    @Override
    public Context getContext() {
        return context;
    }

    @Override
    public String trace() {
        return trace;
    }

    public String type() {
        return type;
    }

    /**
     * READY、CREATE、DESTROY三个状态是内部控制的，不受外部行为干扰
     * 1. READY、CREATE是路由初始化状态
     * 2. DECISION、DOWNGRADE、ADAPTER、LOAD是路由内部状态
     * 3. ERROR、RESULT、DESTROY是路由的结束状态
     */
    private enum Status {
        READY,
        CREATE,
        RECREATE,
        DOWNGRADE,
        ADAPTER,
        LOAD,
        ERROR,
        RESULT,
        DESTROY;

        public boolean isInitialized() {
            return this.compareTo(CREATE) >= 0;
        }

        public boolean isUnWorkable() {
            return this.compareTo(RESULT) >= 0;
        }

        public boolean isWorkableOnError(Status status) {
            return this != ERROR || status == RESULT;
        }

    }

    private static class Operation {
        private Status nextStatus;
        private DowngradeData downgradeData;
        private LoadData loadData;
        private RouterResult result;

        private Operation setNextStatus(Status nextStatus) {
            this.nextStatus = nextStatus;
            return this;
        }

        private Operation setDowngradeData(DowngradeData downgradeData) {
            this.downgradeData = downgradeData;
            return this;
        }

        private Operation setLoadData(LoadData loadData) {
            this.loadData = loadData;
            return this;
        }

        private Operation setResult(RouterResult result) {
            this.result = result;
            return this;
        }

        private Operation setError(DecisionResult decisionResult) {
            String errorMessage = decisionResult != null ? decisionResult.getMessage() : "decision result is fatal error";
            this.result = RouterResult.newResult(Constants.ERROR_CODE_COMMON, errorMessage);
            return this;
        }

        private static Operation next(Status routerStatus) {
            return new Operation().setNextStatus(routerStatus);
        }

    }
}
