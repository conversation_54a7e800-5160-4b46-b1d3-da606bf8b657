package com.meituan.android.payrouter.decision;

import android.os.Bundle;

import com.meituan.android.paybase.payrouter.RouterActivityProxy;
import com.meituan.android.paybase.payrouter.constants.RouterAdapterConstants;
import com.meituan.android.paybase.utils.SaveInstanceUtil;
import com.meituan.android.payrouter.adapter.PayRouterAdapterInterface;
import com.meituan.android.payrouter.data.RouterReportModule;
import com.meituan.android.payrouter.load.RouterDowngradeData;
import com.meituan.android.payrouter.router.RouterData;

/**
 * 决策模块标准接口
 */
public abstract class RouterDecisionInterface {

    private RouterActivityProxy routerProxy;

    public static DefaultDecisionModule getDefaultDecision() {
        return new DefaultDecisionModule();
    }

    public RouterActivityProxy getRouterProxy() {
        return routerProxy;
    }

    public void setProxy(RouterActivityProxy routerProxy) {
        this.routerProxy = routerProxy;
    }

    public final void baseDecide(@RouterAdapterConstants.AdapterType String currentAdapterType, RouterData data, DecisionCallback decisionCallback) {
        //进行决策
        RouterReportModule.decisionStart(data);
        decide(currentAdapterType, data, decisionCallback);
    }

    /**
     * 进行决策，用callback 输出决策路由type
     *
     * @param currentAdapterType //当前适配器类型
     * @param data
     * @param decisionCallback
     */
    public abstract void decide(@RouterAdapterConstants.AdapterType String currentAdapterType, RouterData data, DecisionCallback decisionCallback);

    /**
     * @param downGradeData    路由降级数据
     * @param decisionCallback 决策回调
     */
    public abstract void downgrade(RouterDowngradeData downGradeData, RouterData routerData, DecisionCallback decisionCallback);

    public void onSaveState(Bundle bundle) {
        SaveInstanceUtil.saveInstanceOfClass(this, getClass(), bundle);
    }

    public void onRestore(Bundle bundle, RouterActivityProxy routerActivityProxy, RouterData routerData, PayRouterAdapterInterface routerAdapter, DecisionCallback decisionCallback) {
        SaveInstanceUtil.restoreInstanceOfClass(this, getClass(), bundle);
    }
}
