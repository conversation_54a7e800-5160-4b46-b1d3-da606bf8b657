package com.meituan.android.payrouter.remake.router.data;

import android.os.Parcel;
import android.os.Parcelable;
import android.support.annotation.Keep;

/**
 * <AUTHOR>
 */
@Keep
public class InvokeInfo implements Parcelable {
    private String downgradeFrom;

    public InvokeInfo() {
    }

    protected InvokeInfo(Parcel in) {
        downgradeFrom = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(downgradeFrom);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<InvokeInfo> CREATOR = new Creator<InvokeInfo>() {
        @Override
        public InvokeInfo createFromParcel(Parcel in) {
            return new InvokeInfo(in);
        }

        @Override
        public InvokeInfo[] newArray(int size) {
            return new InvokeInfo[size];
        }
    };

    public String getDowngradeFrom() {
        return downgradeFrom;
    }

    public void setDowngradeFrom(String downgradeFrom) {
        this.downgradeFrom = downgradeFrom;
    }
}
