package com.meituan.android.payrouter.adapter;

import android.content.Intent;
import android.os.Bundle;

import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.payrouter.RouterActivityProxy;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.payrouter.data.RouterReportModule;
import com.meituan.android.payrouter.load.RouterLoadCallback;
import com.meituan.android.payrouter.load.RouterLoadResultData;
import com.meituan.android.payrouter.router.RouterData;
import com.meituan.android.payrouter.router.RouterObserver;
import com.meituan.android.payrouter.router.RouterRequestData;

import java.util.Map;

public abstract class PayRouterAdapterInterface implements RouterObserver, IRequestCallback {
    protected RouterData routerData;
    protected RouterActivityProxy routerActivityProxy;
    protected RouterRequestData routerRequestData;
    protected RouterLoadCallback routerLoadCallback;

    public final void init(RouterActivityProxy routerActivityProxy, RouterData routerData) {
        this.routerData = routerData;
        this.routerActivityProxy = routerActivityProxy;
        this.routerRequestData = routerData.getRouterRequestData();
    }

    public RouterLoadCallback getRouterLoadCallback() {
        return routerLoadCallback;
    }

    public void setRouterLoadCallback(RouterLoadCallback routerLoadCallback) {
        this.routerLoadCallback = routerLoadCallback;
    }


    protected void startActivityForResult(Intent intent, int requestCode) {
        if (routerActivityProxy == null || routerData == null) {
            reportCommonError("context && data is null");
            return;
        }
        routerActivityProxy.startActivityForResult(intent, requestCode, routerData.getRouterUniqueId());
    }

    public static PayRouterAdapterInterface getDefaultAdapter() {
        return new DefaultAdapter();
    }

    public final ConsumeResult isConsumed(RouterRequestData routerRequestData) {
        if (routerActivityProxy == null) {
            return ConsumeResult.fail("consumed result : context is null");
        }
        return isConsume(routerRequestData);
    }


    public void onDestroy(boolean release) {
    }

    //适配器实现
    public abstract ConsumeResult isConsume(RouterRequestData routerRequestData);

    public abstract void invoke(RouterRequestData routerRequestData);

    protected void reportCommonError(String errorMessage) {
        Map<String, Object> errorData = new AnalyseUtils.MapBuilder().add("errorMsg", errorMessage).build();
        RouterReportModule.routerCommonError(routerData, errorData);
    }


    //加载回调
    @Override
    public void onLoadCallback(RouterLoadResultData resultData) {
    }

    @Override
    public void onResultCallback(int requestCode, int resultCode, Intent data) {
    }

    //恢复重建
    @Override
    public String onSaveState(Bundle bundle) {
        return null;
    }

    @Override
    public void onRestoreState(Bundle bundle, String key, RouterActivityProxy routerActivityProxy) {
    }


    //网络请求回调
    @Override
    public void onRequestSucc(int tag, Object obj) {

    }

    @Override
    public void onRequestException(int tag, Exception e) {

    }

    @Override
    public void onRequestFinal(int tag) {

    }

    @Override
    public void onRequestStart(int tag) {

    }

    public static class ConsumeResult {
        private boolean consumed;
        private String message;

        public ConsumeResult(boolean consumed, String message) {
            this.consumed = consumed;
            this.message = message;
        }

        public ConsumeResult() {
        }

        public boolean isConsumed() {
            return consumed;
        }

        public void setConsumed(boolean consumed) {
            this.consumed = consumed;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public static ConsumeResult fail(String message) {
            ConsumeResult result = new ConsumeResult();
            result.setConsumed(false);
            result.setMessage(message);
            return result;
        }

        public static ConsumeResult success(String message) {
            ConsumeResult result = new ConsumeResult();
            result.setConsumed(true);
            result.setMessage(message);
            return result;
        }

        public static ConsumeResult success() {
            ConsumeResult result = new ConsumeResult();
            result.setConsumed(true);
            return result;
        }
    }
}
