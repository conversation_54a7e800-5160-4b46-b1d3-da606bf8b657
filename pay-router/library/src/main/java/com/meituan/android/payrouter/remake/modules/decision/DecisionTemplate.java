package com.meituan.android.payrouter.remake.modules.decision;

import android.os.Bundle;

import com.meituan.android.payrouter.remake.base.OnCreate;
import com.meituan.android.payrouter.remake.base.Restorable;
import com.meituan.android.payrouter.remake.modules.decision.data.DecisionResult;

/**
 * 决策模板
 */
public interface DecisionTemplate {

    void onStart(Bundle businessData);

    void onDecisionStart(DecisionProxy proxy);

    void onDecisionResult(DecisionResult result);

    /**
     * 降级逻辑：兜底降级结果。任意的决策模块都需要配置当数据出错时的兜底降级逻辑。
     *
     * @return 兜底降级结果
     */
    DecisionResult finalDowngrade();

    /**
     * 配置内容：业务层需要设置自身的默认路由配置。
     *
     * @return 默认的路由决策数据
     */
    String finalProductData();


}
