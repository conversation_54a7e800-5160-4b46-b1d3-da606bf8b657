package com.meituan.android.payrouter.remake.router.data;

import android.os.Parcel;
import android.os.Parcelable;
import android.support.annotation.Keep;

@Keep
public final class CheckResult implements Parcelable {
    private boolean valid;

    private String errorCode;

    private String errorMessage;

    public CheckResult() {
    }

    public CheckResult(Parcel in) {
        valid = in.readByte() != 0;
        errorCode = in.readString();
        errorMessage = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeByte((byte) (valid ? 1 : 0));
        dest.writeString(errorCode);
        dest.writeString(errorMessage);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<CheckResult> CREATOR = new Creator<CheckResult>() {
        @Override
        public CheckResult createFromParcel(Parcel in) {
            return new CheckResult(in);
        }

        @Override
        public CheckResult[] newArray(int size) {
            return new CheckResult[size];
        }
    };

    public boolean isValid() {
        return valid;
    }

    public void setValid(boolean valid) {
        this.valid = valid;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    /**
     * 多重check，叠加两层check结果。
     * @param result 第二层check
     * @return check结果
     */
    public CheckResult check(CheckResult result) {
        if (isValid() && result != null && !result.isValid()) {
            return result;
        }
        return this;
    }

    public static CheckResult success() {
        CheckResult result = new CheckResult();
        result.setValid(true);
        return result;
    }

    public static CheckResult fail(String errorCode, String errorMessage) {
        CheckResult result = new CheckResult();
        result.setValid(false);
        result.setErrorCode(errorCode);
        result.setErrorMessage(errorMessage);
        return result;
    }
}
