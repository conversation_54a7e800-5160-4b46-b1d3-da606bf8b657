package com.meituan.android.payrouter.utils;

import static com.meituan.android.payrouter.utils.Reflector.BASE_CLASS;
import static com.meituan.android.payrouter.utils.Reflector.BOOL_CLASS;
import static com.meituan.android.payrouter.utils.Reflector.NUMBER_CLASS;

import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

public class ProxyUtils {
    private static final Object DEFAULT_OBJECT = new Object();

    /**
     * 对proxyObject进行代理
     */
    public static <P> P single(Class<P> clz, Object proxyObject) {
        //noinspection unchecked
        return (P) Proxy.newProxyInstance(clz.getClassLoader(), new Class[]{clz}, (proxy, method, args) -> {
            if (method.getDeclaringClass() == Object.class) {
                return method.invoke(proxyObject != null ? proxyObject : DEFAULT_OBJECT, args);
            }
            if (proxyObject != null && clz.isAssignableFrom(proxyObject.getClass())) {
                return method.invoke(proxyObject, args);
            }
            // 当proxyObject无法处理时，需要兜底处理返回类型
            return invokeBaseType(method, args);
        });
    }

    /**
     * 对List类型增加通用处理。分别对List中的对象进行直接代理
     */
    public static <P> P list(Class<P> clz, Collection<?> proxies, boolean consumedReturn) {
        //noinspection unchecked
        return (P) Proxy.newProxyInstance(clz.getClassLoader(), new Class[]{clz}, (proxy, method, args) -> {
            if (method.getDeclaringClass() == Object.class) {
                return method.invoke(DEFAULT_OBJECT, args);
            }
            for (Object proxyObject : proxies) {
                if (proxyObject == null || !clz.isAssignableFrom(proxyObject.getClass())) {
                    continue;
                }
                Object returnObj = method.invoke(proxyObject, args);
                // 如果返回类型是boolean类型，并且返回的是true，则直接返回，不再执行下一个proxy
                if (consumedReturn && checkBooleanReturnType(method.getReturnType(), returnObj)) {
                    return true;
                }
            }
            return invokeBaseType(method, args);
        });
    }

    /**
     * 代理转换，可以增加代理的层数
     */
    public static <P> P transfer(Class<P> clz, ProxyTransformer transfer) {
        //noinspection unchecked
        return (P) Proxy.newProxyInstance(clz.getClassLoader(), new Class[]{clz}, (proxy, method, args) -> {
            if (method.getDeclaringClass() == Object.class) {
                return method.invoke(transfer, args);
            }
            if (transfer != null) {
                Object returnObj = transfer.invoke(method, args);
                if (checkReturnType(method.getReturnType(), returnObj)) {
                    return returnObj;
                }
            }
            // 返回值类型不符合预期时，需要兜底处理返回类型
            return invokeBaseType(method, args);
        });
    }

    /**
     * 对List类型增加通用处理。代理转换，可以增加代理的层数。
     */
    public static <P, T> P transferList(Class<P> clz, Collection<T> subjects, boolean consumedReturn, ProxySubjectMapper<T, P> mapper) {
        return transfer(clz, (method, args) -> {
            if (method.getDeclaringClass() == Object.class) {
                return method.invoke(DEFAULT_OBJECT, args);
            }
            for (T subject : subjects) {
                if (subject == null) {
                    continue;
                }
                if (consumedReturn) {
                    Object returnObj = method.invoke(mapper.map(subject), args);
                    if (checkBooleanReturnType(method.getReturnType(), returnObj)) {
                        return true;
                    }
                } else {
                    method.invoke(mapper.map(subject), args);
                }
            }
            return invokeBaseType(method, args);
        });
    }

    private static Object invokeBaseType(Method method, Object[] args) {
        Class<?> returnType = method.getReturnType();
        if (returnType == void.class) {
            return null;
        }
        // 当proxyObject无法处理时，需要兜底处理返回类型
        if (returnType == boolean.class || returnType == Boolean.class) {
            return false;
        } else if (NUMBER_CLASS.contains(returnType)
                || returnType == char.class
                || returnType == Character.class) {
            return 0;
        }
        return null;
    }

    public static boolean checkReturnType(Class<?> returnType, Object returnObj) {
        // 如果是空返回类型或者是returnType的子类型，则为true
        if (returnType == void.class || returnType.isInstance(returnObj)) {
            return true;
        }
        // 如果返回值为空，但返回类型不是基础类型，则为true，其他情况为false
        return !BASE_CLASS.contains(returnType);
    }

    public static boolean checkBooleanReturnType(Class<?> returnType, Object returnObj) {
        return BOOL_CLASS.contains(returnType) && returnObj instanceof Boolean && (boolean) returnObj;
    }

    /**
     * 【重要】默认不为空的对象方法执行结果：
     * 1. boolean类型，一律返回false
     * 2. Number类型，一律返回0
     * 3. Character类型，一律返回0
     * 3. 对象类型，一律返回null
     * 如果方法返回类型不符合以上逻辑，则不能使用该方法。
     *
     * @param clz            需要被代理的Class类型
     * @param sourceExecutor 如果该对象不为空，则使用该对象处理
     * @param <P>            代理对象泛型
     * @return 返回一个不为空的对象
     */
    public static <P> P nonNullObject(Class<P> clz, P sourceExecutor) {
        if (sourceExecutor != null) {
            return sourceExecutor;
        }
        return single(clz, null);
    }

    /**
     * 代理的中间转换层，可以增加代理的层数
     */
    public interface ProxyTransformer {
        Object invoke(Method method, Object[] args) throws Throwable;
    }

    /**
     * 代理列表的转换器，用于将代理执行的T类型转换为可以invoke的P类型，可以增加代理的层数。
     *
     * @param <P>用于invoke的类型
     * @param <T>原始类型
     */
    public interface ProxySubjectMapper<T, P> {
        P map(T proxyObject);
    }
}
