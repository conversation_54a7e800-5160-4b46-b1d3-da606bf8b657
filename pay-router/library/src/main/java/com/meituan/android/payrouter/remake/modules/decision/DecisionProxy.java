package com.meituan.android.payrouter.remake.modules.decision;

import com.meituan.android.payrouter.remake.router.context.RouterContext;

/**
 * <AUTHOR>
 * 决策代理
 */
public interface DecisionProxy {
    void updateProductData(String productData);

    void notifyProductType(String productType);

    void notifyDecisionError(String message);

    boolean isAvailableProductType(String productType);

    RouterContext routerContext();
}
