package com.meituan.android.payrouter.utils.bus;

import android.os.Parcel;
import android.os.Parcelable;
import android.support.annotation.Keep;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Keep
public class Message implements Parcelable {
    private String trace;

    private Parcelable content;

    private Serializable subContent;

    protected Message() {
    }

    protected Message(Parcel in) {
        trace = in.readString();
        content = in.readParcelable(getClass().getClassLoader());
        subContent = in.readSerializable();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(trace);
        dest.writeParcelable(content, flags);
        dest.writeSerializable(subContent);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<Message> CREATOR = new Creator<Message>() {
        @Override
        public Message createFromParcel(Parcel in) {
            return new Message(in);
        }

        @Override
        public Message[] newArray(int size) {
            return new Message[size];
        }
    };

    public String trace() {
        return trace;
    }

    public void setTrace(String trace) {
        this.trace = trace;
    }

    public Parcelable getContent() {
        return content;
    }

    public void setContent(Parcelable content) {
        this.content = content;
    }

    public Serializable getSubContent() {
        return subContent;
    }

    public void setSubContent(Serializable subContent) {
        this.subContent = subContent;
    }

    public static Message make(String trace, Serializable subMessage) {
        Message routerBusMessage = new Message();
        routerBusMessage.setTrace(trace);
        routerBusMessage.setSubContent(subMessage);
        return routerBusMessage;
    }

    public static Message make(String trace, Parcelable message) {
        Message routerBusMessage = new Message();
        routerBusMessage.setTrace(trace);
        routerBusMessage.setContent(message);
        return routerBusMessage;
    }
}
