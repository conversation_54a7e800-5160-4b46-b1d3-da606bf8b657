package com.meituan.android.payrouter.load;

import android.support.annotation.NonNull;

import com.meituan.android.paybase.payrouter.RouterActivityProxy;
import com.meituan.android.payrouter.adapter.PayRouterAdapterInterface;
import com.meituan.android.paybase.payrouter.constants.RouterConstants;
import com.meituan.android.payrouter.router.RouterData;

/**
 * 路由加载模块标准接口
 * RouterLoader暂时不需要观察PayRouter的生命周期，所以只有对应的onRestore一个方法
 */
public abstract class RouterLoadInterface {

    /**
     * 获取决策模块，增加通用性
     *
     * @param routerType 路由类型
     * @return 返回加载模块对象
     */
    public static RouterLoadInterface getDecisionByType(@RouterConstants.RouterType String routerType) {
        return new RouterLoadModule();
    }

    /**
     * 加载场景调用
     */
    public abstract void loadAdapter(RouterActivityProxy routerActivityProxy, RouterData routerData, PayRouterAdapterInterface adapter, @NonNull RouterLoadCallback loadCallback);

    /**
     * 重建场景需要调用
     */
    public void onRestore(RouterActivityProxy routerActivityProxy, RouterData routerData, PayRouterAdapterInterface adapter, RouterLoadCallback loadCallback) {
    }


}
