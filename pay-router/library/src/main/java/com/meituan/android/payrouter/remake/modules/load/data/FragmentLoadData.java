package com.meituan.android.payrouter.remake.modules.load.data;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.support.annotation.Keep;
import android.support.v4.app.Fragment;

@Keep
public class FragmentLoadData implements Parcelable {
    private String fragmentClz;

    private String fragmentTag;

    private Bundle fragmentArgs;

    public FragmentLoadData() {
    }

    protected FragmentLoadData(Parcel in) {
        fragmentClz = in.readString();
        fragmentTag = in.readString();
        fragmentArgs = in.readBundle(getClass().getClassLoader());
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(fragmentClz);
        dest.writeString(fragmentTag);
        dest.writeBundle(fragmentArgs);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<FragmentLoadData> CREATOR = new Creator<FragmentLoadData>() {
        @Override
        public FragmentLoadData createFromParcel(Parcel in) {
            return new FragmentLoadData(in);
        }

        @Override
        public FragmentLoadData[] newArray(int size) {
            return new FragmentLoadData[size];
        }
    };

    public String getFragmentClz() {
        return fragmentClz;
    }

    public void setFragmentClz(String fragmentClz) {
        this.fragmentClz = fragmentClz;
    }

    public void setFragmentClz(Class<? extends Fragment> fragmentClz) {
        this.fragmentClz = fragmentClz.getName();
    }

    public String getFragmentTag() {
        return fragmentTag;
    }

    public void setFragmentTag(String fragmentTag) {
        this.fragmentTag = fragmentTag;
    }

    public Bundle getFragmentArgs() {
        return fragmentArgs;
    }

    public void setFragmentArgs(Bundle fragmentArgs) {
        this.fragmentArgs = fragmentArgs;
    }
}
