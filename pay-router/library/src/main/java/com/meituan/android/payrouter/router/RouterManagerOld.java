package com.meituan.android.payrouter.router;

import android.app.Activity;
import android.os.Bundle;
import android.text.TextUtils;

import com.meituan.android.paybase.common.activity.PayBaseActivity;
import com.meituan.android.paybase.payrouter.RouterActivityProxy;
import com.meituan.android.paybase.payrouter.callback.RouterCallback;
import com.meituan.android.paybase.payrouter.constants.RouterConstants;
import com.meituan.android.paybase.utils.AppUtils;

import java.util.HashMap;
import java.util.Map;

public class RouterManagerOld {

    private static final Map<String, BaseRouter> payRouters = new HashMap<>();

    static void register(String routerUniqueId, BaseRouter router) {
        if (!TextUtils.isEmpty(routerUniqueId) && router != null) {
            payRouters.put(routerUniqueId, router);
        }
    }

    static void unregister(String routerUniqueId) {
        payRouters.remove(routerUniqueId);
    }

    public static BaseRouter get(String routerUniqueId) {
        return payRouters.get(routerUniqueId);
    }
    public static BaseRouter getByActivity(Activity activity) {
        return payRouters.get(RouterActivityProxy.pickUniqueId(activity));
    }

    /**
     * 通过路由打开具体业务
     *
     * @param routerRequestData
     * @param routerType        要打开的业务类型
     * @param activity
     */
    public static String open(@RouterConstants.RouterType String routerType, RouterRequestData routerRequestData, PayBaseActivity activity,  RouterCallback routerCallback ) {
        // 路由初始化
        BaseRouter router = new PayRouter(AppUtils.genUniqueId());
        RouterActivityProxy routerActivityProxy = RouterActivityProxy.get(activity);
        routerActivityProxy.setRouterCallback(router.getRouterUniqueId(),routerCallback);
        // 初始化
        router.onCreate(routerActivityProxy, new RouterData(routerType, routerRequestData), false);
        // 路由启动
        router.open();
        return router.getRouterUniqueId();
    }

    public static void close(String routerUniqueId) {
        BaseRouter router = get(routerUniqueId);
        if (router != null) {
            router.onDestroy();
        }
    }

    public static void restore(String routerUniqueId, PayBaseActivity activity, RouterCallback routerCallback, Bundle bundle) {
        BaseRouter baseRouter = get(routerUniqueId);
        baseRouter.restoreByActivity(activity,routerCallback,bundle);
    }
}
