package com.meituan.android.payrouter.remake.modules.load.data;

import android.content.Intent;
import android.os.Parcel;
import android.os.Parcelable;
import android.support.annotation.Keep;

import java.util.HashMap;
import java.util.Map;

@Keep
public final class ActivityLoadData implements Parcelable {
    /**
     * 加载时需要的requestCode
     */
    private int requestCode;
    /**
     * 需要加载的 intent
     */
    private Intent intent;

    /**
     * 需要追加的 uri 的 query 参数，可以用于生成动态变化的参数列表
     */
    private Map<String, String> queryParams = new HashMap<>();

    public ActivityLoadData() {
    }


    public ActivityLoadData(Parcel in) {
        requestCode = in.readInt();
        intent = in.readParcelable(Intent.class.getClassLoader());
        //noinspection unchecked
        queryParams = in.readHashMap(HashMap.class.getClassLoader());
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(requestCode);
        dest.writeParcelable(intent, flags);
        dest.writeMap(queryParams);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<ActivityLoadData> CREATOR = new Creator<ActivityLoadData>() {
        @Override
        public ActivityLoadData createFromParcel(Parcel in) {
            return new ActivityLoadData(in);
        }

        @Override
        public ActivityLoadData[] newArray(int size) {
            return new ActivityLoadData[size];
        }
    };

    public int getRequestCode() {
        return requestCode;
    }

    public void setRequestCode(int requestCode) {
        this.requestCode = requestCode;
    }

    public Intent getIntent() {
        return intent;
    }

    public void setIntent(Intent intent) {
        this.intent = intent;
    }

    public Map<String, String> getQueryParams() {
        return queryParams;
    }

    public void setQueryParams(Map<String, String> queryParams) {
        this.queryParams = queryParams;
    }
}
