package com.meituan.android.payrouter.remake.base;

import android.content.Intent;

public interface OnActivityResult {

    /**
     *
     * @param requestCode 和startActivityForResult相同
     * @param resultCode 和startActivityForResult相同
     * @param data 和startActivityForResult相同
     * @return true，则后续不再通知其他Observer；false，继续向后询问其他Observer
     */
    boolean onActivityResult(int requestCode, int resultCode, Intent data);
}
