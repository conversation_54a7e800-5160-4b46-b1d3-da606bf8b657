package com.meituan.android.payrouter.load;

import android.support.annotation.NonNull;

import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.payrouter.RouterActivityProxy;
import com.meituan.android.paybase.payrouter.callback.RouterCallback;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.payrouter.adapter.PayRouterAdapterInterface;
import com.meituan.android.payrouter.data.RouterReportModule;
import com.meituan.android.payrouter.router.RouterData;

import java.util.Map;

public class RouterLoadModule extends RouterLoadInterface {

    private void loadStart(RouterData routerData, PayRouterAdapterInterface adapter) {
        RouterReportModule.loadStart(routerData);
    }

    private void loadEnd(RouterData routerData, RouterLoadResultData loadResult) {
        RouterReportModule.loadEnd(routerData, loadResult);
    }

    @Override
    public void onRestore(RouterActivityProxy routerActivityProxy, RouterData routerData, PayRouterAdapterInterface adapter, RouterLoadCallback loadCallback) {
        adapter.setRouterLoadCallback(wrapWithLoadFunc(routerData, loadCallback));
    }

    @Override
    public void loadAdapter(RouterActivityProxy routerActivityProxy, RouterData routerData, PayRouterAdapterInterface adapter, @NonNull RouterLoadCallback loadCallback) {
        loadStart(routerData, adapter);
        adapter.setRouterLoadCallback(wrapWithLoadFunc(routerData, loadCallback));
        try {
            adapter.invoke(routerData.getRouterRequestData());
        } catch (Exception e) {
            loadCallback.loadResultCallback(new RouterLoadResultData(RouterLoadResultData.LoadResultCode.ROUTER_LOAD_FAIL,e.getMessage()));
            LoganUtils.logError("RouterLoadModule_loadAdapter", e.getMessage());
        }
    }

    private RouterLoadCallback wrapWithLoadFunc(RouterData routerData, RouterLoadCallback loadCallback) {
        return new RouterLoadCallback() {
            @Override
            public void loadResultCallback(RouterLoadResultData resultData) {
                loadEnd(routerData, resultData);
                if (loadCallback != null) {
                    loadCallback.loadResultCallback(resultData);
                } else {
                    reportCommonError("RouterLoadCallback for loadResultCallback is null");
                }
            }

            @Override
            public void completionCallback( RouterCallback.Result result) {
                if (loadCallback != null) {
                    loadCallback.completionCallback( result);
                } else {
                    reportCommonError("RouterLoadCallback for completionCallback is null");
                }
            }
        };
    }

    private void reportCommonError(String errorMessage) {
        Map<String, Object> errorData = new AnalyseUtils.MapBuilder()
                .add("errorMsg", errorMessage).build();
        RouterReportModule.routerCommonError(null, errorData);
    }

}
