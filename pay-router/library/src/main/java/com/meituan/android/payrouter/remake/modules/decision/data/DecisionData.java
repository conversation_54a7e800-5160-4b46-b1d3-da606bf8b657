package com.meituan.android.payrouter.remake.modules.decision.data;

import android.os.Parcel;
import android.os.Parcelable;
import android.support.annotation.Keep;

import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.payrouter.remake.config.RouterDecisionConfigManager;

import java.util.HashMap;
import java.util.Map;

/**
 * 包含全部的决策数据，需要持久化
 */
@Keep
public class DecisionData implements Parcelable {
    private final String routerType;

    // 业务决策结果：原始产品类型
    private String originProductType;

    // 业务决策结果：产品类型
    private String destProductType;

    // 业务决策结果：适配器类型
    private String destAdapterType;

    // 路由动态决策数据（外部传入）
    private Map<String, DecisionProductTypeData> productData = new HashMap<>();

    // 路由静态决策数据（Horn + 默认配置）
    private Map<String, DecisionProductTypeData> productDataFromRouter = new HashMap<>();

    /**
     * 降级路径, key: destAdapterType; value: curAdapterType；
     */
    private Map<String, String> downgradeTrace = new HashMap<>();

    private DecisionResult currentDecisionResult;

    public DecisionData(String routerType, String finalProductData) {
        this.routerType = routerType;
        initProductDataConfig(finalProductData);
    }

    protected DecisionData(Parcel in) {
        routerType = in.readString();
        originProductType = in.readString();
        destProductType = in.readString();
        destAdapterType = in.readString();
        //noinspection unchecked
        productData = in.readHashMap(DecisionProductTypeData.class.getClassLoader());
        //noinspection unchecked
        downgradeTrace = in.readHashMap(String.class.getClassLoader());
        currentDecisionResult = in.readParcelable(DecisionResult.class.getClassLoader());
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(routerType);
        dest.writeString(originProductType);
        dest.writeString(destProductType);
        dest.writeString(destAdapterType);
        dest.writeMap(productData);
        dest.writeMap(downgradeTrace);
        dest.writeParcelable(currentDecisionResult, flags);
    }

    public String getOriginProductType() {
        return originProductType;
    }

    public void setOriginProductType(String originProductType) {
        this.originProductType = originProductType;
    }

    public String getDestProductType() {
        return destProductType;
    }

    public void setDestProductType(String destProductType) {
        this.destProductType = destProductType;
    }

    public String getDestAdapterType() {
        return destAdapterType;
    }

    public void setDestAdapterType(String destAdapterType) {
        this.destAdapterType = destAdapterType;
    }

    public DecisionResult getCurrentDecisionResult() {
        return currentDecisionResult;
    }

    public void setCurrentDecisionResult(DecisionResult currentDecisionResult) {
        this.currentDecisionResult = currentDecisionResult;
    }

    public void setProductData(Map<String, DecisionProductTypeData> productData) {
        if (!CollectionUtils.isEmpty(productData)) {
            this.productData = productData;
        }
    }

    public Map<String, DecisionProductTypeData> getProductData() {
        return productData;
    }

    public DecisionProductTypeData getProductTypeData(String productType) {
        DecisionProductTypeData productTypeData = productData.get(productType);
        if (productTypeData != null) {
            return productTypeData;
        }
        return productDataFromRouter.get(productType);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<DecisionData> CREATOR = new Creator<DecisionData>() {
        @Override
        public DecisionData createFromParcel(Parcel in) {
            return new DecisionData(in);
        }

        @Override
        public DecisionData[] newArray(int size) {
            return new DecisionData[size];
        }
    };

    public void addDowngradeTrace(String destAdapterType, String curAdapterType) {
        downgradeTrace.put(destAdapterType, curAdapterType);
    }

    public boolean hasNotDowngraded(String destAdapterType) {
        return !downgradeTrace.containsKey(destAdapterType);
    }

    public boolean isAvailableProductType(String productType) {
        return getProductTypeData(productType) != null;
    }

    public boolean isAvailableProductData() {
        return !CollectionUtils.isEmpty(productData);
    }

    public String routerType() {
        return routerType;
    }

    /**
     * 初始化静态决策数据（Horn + 固定配置）
     */
    private void initProductDataConfig(String finalProductData) {
        // 先获取默认配置
        Map<String, DecisionProductTypeData> defaultProductData = RouterDecisionConfigManager.parseDefaultConfigString(routerType, finalProductData);
        if (!CollectionUtils.isEmpty(defaultProductData)) {
            this.productDataFromRouter.putAll(defaultProductData);
        }
        // 如果有Horn配置，则对应的Horn配置会覆盖默认配置
        Map<String, DecisionProductTypeData> dynamicProductData = RouterDecisionConfigManager.getDynamicConfig(routerType);
        if (!CollectionUtils.isEmpty(dynamicProductData)) {
            this.productDataFromRouter.putAll(dynamicProductData);
        }
    }

}
