package com.meituan.android.payrouter.utils.bus;

import android.os.Bundle;
import android.text.TextUtils;

import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.payrouter.remake.base.Restorable;
import com.meituan.android.payrouter.remake.manager.RouterObservable;
import com.meituan.android.payrouter.remake.manager.RouterObservableImpl;
import com.meituan.android.payrouter.utils.save.Save;
import com.meituan.android.payrouter.utils.save.SaveInstanceHelper;

import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Message发送后在Bus中有3种循环状态：
 * 1. 消费；没有消费成功，则进入保存状态。
 * 2. 保存；保存状态可触发持久化的操作，并进行恢复。
 * 3. 请求；如果Observer再次注册，则能够重新请求保存着的信息，并再次发送。
 */
public class RouterBusImpl implements RouterBus, Restorable {
    @Save
    private final Map<String, List<Message>> stickyMessages = new HashMap<>();
    private final Map<String, RouterObservable<Observer>> busObservables = new HashMap<>();

    private RouterMessageHandler handler;

    public RouterBusImpl() {
    }

    public RouterBusImpl(RouterMessageHandler handler) {
        this.handler = handler;
    }

    @Override
    public boolean sendMessage(Message message) {
        if (flushMessage(message)) {
            return true;
        }
        if (handler != null && handler.onThrowMessage(this, message)) {
            return true;
        }
        saveMessage(message);
        return false;
    }

    @Override
    public boolean flushMessage(Message message) {
        if (message == null) {
            return true;
        }
        String trace = message.trace();
        if (TextUtils.isEmpty(trace)) {
            return true;
        }
        RouterObservable<Observer> observable = busObservables.get(trace);
        if (observable != null) {
            return observable.dispatch(true).onReceived(this, message);
        }
        return false;
    }

    private void saveMessage(Message message) {
        if (message == null) {
            return;
        }
        String trace = message.trace();
        if (TextUtils.isEmpty(trace)) {
            return;
        }
        stickyMessages(trace).add(message);
    }

    public void obtainMessage(String trace) {
        if (!TextUtils.isEmpty(trace)) {
            CollectionUtils.removeIf(stickyMessages(trace), this::sendMessage);
        }
    }

    @Override
    public void subscribe(String trace, Observer observer) {
        if (!TextUtils.isEmpty(trace)) {
            busObservable(trace).subscribe(observer);
            obtainMessage(trace);
        }
    }

    @Override
    public void unsubscribe(String trace) {
        if (!TextUtils.isEmpty(trace)) {
            busObservable(trace).unsubscribe();
        }
    }

    @Override
    public void unsubscribe(String trace, Observer observer) {
        if (!TextUtils.isEmpty(trace)) {
            busObservable(trace).unsubscribe(observer);
        }
    }

    private List<Message> stickyMessages(String trace) {
        List<Message> messages = stickyMessages.get(trace);
        if (messages == null) {
            messages = new LinkedList<>();
            stickyMessages.put(trace, messages);
        }
        return messages;
    }

    private RouterObservable<Observer> busObservable(String trace) {
        RouterObservable<Observer> observable = busObservables.get(trace);
        if (observable == null) {
            observable = new RouterObservableImpl<>(Observer.class);
            busObservables.put(trace, observable);
        }
        return observable;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        SaveInstanceHelper.restore(savedInstanceState, this, getClass());
    }

    @Override
    public void onSaveInstanceState(Bundle savedInstanceState) {
        SaveInstanceHelper.save(savedInstanceState, this, getClass());
    }
}
