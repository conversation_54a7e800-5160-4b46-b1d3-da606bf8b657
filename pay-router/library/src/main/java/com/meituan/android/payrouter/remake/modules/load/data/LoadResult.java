package com.meituan.android.payrouter.remake.modules.load.data;

import android.os.Parcel;
import android.os.Parcelable;
import android.support.annotation.Keep;

import com.meituan.android.payrouter.remake.modules.decision.data.DowngradeData;

@Keep
public class LoadResult implements Parcelable {
    public static final String CODE_SUCCESS = "200";

    private String code;

    private String message;

    private DowngradeData downgradeData;

    public LoadResult() {
    }

    protected LoadResult(Parcel in) {
        code = in.readString();
        message = in.readString();
        downgradeData = in.readParcelable(DowngradeData.class.getClassLoader());
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(code);
        dest.writeString(message);
        dest.writeParcelable(downgradeData, flags);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<LoadResult> CREATOR = new Creator<LoadResult>() {
        @Override
        public LoadResult createFromParcel(Parcel in) {
            return new LoadResult(in);
        }

        @Override
        public LoadResult[] newArray(int size) {
            return new LoadResult[size];
        }
    };

    public String getCode() {
        return code;
    }

    public int getConvertedCode() {
        if (isSuccess()) {
            return 0;
        }
        return downgradeData == null ? 1 : 2;
    }

    public String getMessage() {
        return message;
    }

    public DowngradeData getDowngradeData() {
        return downgradeData;
    }

    public boolean isSuccess() {
        return CODE_SUCCESS.equals(code);
    }

    public static Builder success(String message) {
        return new Builder().setCode(CODE_SUCCESS).setMessage(message);
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public static class Builder {
        private String code;
        private String message;
        private DowngradeData downgradeData;

        public Builder success(String message) {
            setCode(CODE_SUCCESS);
            setMessage(message);
            return this;
        }

        public Builder setCode(String code) {
            this.code = code;
            return this;
        }

        public Builder setMessage(String message) {
            this.message = message;
            return this;
        }

        public Builder setDowngradeData(DowngradeData downgradeData) {
            this.downgradeData = downgradeData;
            return this;
        }

        public LoadResult build() {
            LoadResult loadResult = new LoadResult();
            loadResult.code = this.code;
            loadResult.message = this.message;
            loadResult.downgradeData = this.downgradeData;
            return loadResult;
        }
    }

    public static DowngradeData prepareDowngrade(LoadResult result) {
        if (!isSuccess(result)) {
            DowngradeData data = result == null ? new DowngradeData() : result.getDowngradeData();
            return DowngradeData.wrapDowngradeDataFromBusiness(data);
        }
        return null;
    }

    public static boolean isSuccess(LoadResult result) {
        return result != null && CODE_SUCCESS.equals(result.getCode());
    }
}
