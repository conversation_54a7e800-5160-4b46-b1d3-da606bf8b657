package com.meituan.android.payrouter.utils;

import com.meituan.android.paybase.utils.CollectionUtils;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class CachedParamCenter<K, V> {
    private static final Map<Class<?>, CachedParamCenter<?, ?>> globalCachedParams = new HashMap<>();

    protected final Map<K, V> cachedParams = newCachedParams();

    protected final Class<V> verifyClz;
    protected final Initialization<K, V> initialization;

    protected CachedParamCenter(Class<V> verifyClz, Initialization<K, V> initialization) {
        this.verifyClz = verifyClz;
        this.initialization = initialization;
    }

    protected Map<K, V> newCachedParams() {
        return new HashMap<>();
    }

    public static <K, V> CachedParamCenter<K, V> withClz(Class<V> clz, Initialization<K, V> initialization) {
        //noinspection unchecked
        CachedParamCenter<K, V> cachedParams = (CachedParamCenter<K, V>) globalCachedParams.get(clz);
        if (cachedParams == null) {
            cachedParams = new CachedParamCenter<>(clz, initialization);
            globalCachedParams.put(clz, cachedParams);
        }
        return cachedParams;
    }

    public static <K, V> CachedParamCenter<K, V> with(V obj, Initialization<K, V> initialization) {
        //noinspection unchecked
        Class<V> clz = obj != null ? (Class<V>) obj.getClass() : null;
        return withClz(clz, initialization);
    }

    public V get(K key) {
        V value = cachedParams.get(key);
        if (value == null && initialization != null) {
            value = initialization.init(key);
            cachedParams.put(key, value);
        }
        return value;
    }

    public K key(V value) {
        return CollectionUtils.findKey(cachedParams, value);
    }

    public Collection<V> values() {
        return cachedParams.values();
    }

    public Set<Map.Entry<K, V>> entrySet() {
        return cachedParams.entrySet();
    }

    public interface Initialization<K, V> {
        V init(K key);
    }

}
