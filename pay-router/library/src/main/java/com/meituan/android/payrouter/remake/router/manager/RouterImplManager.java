package com.meituan.android.payrouter.remake.router.manager;

import android.text.TextUtils;

import com.meituan.android.payrouter.remake.modules.decision.DecisionTemplate;
import com.meituan.android.payrouter.remake.modules.decision.RouterDecisionManager;
import com.meituan.android.payrouter.remake.router.adapter.AbstractRouterAdapter;
import com.meituan.android.payrouter.remake.router.adapter.RouterAdapterManager;
import com.sankuai.meituan.serviceloader.ServiceLoader;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RouterImplManager {
    public static Map<String, String> routerAdapterManagers = new HashMap<>();
    public static Map<String, String> routerAdapters = new HashMap<>();
    public static Map<String, String> decisions = new HashMap<>();

    public static void init() {
        Map<String, Map<String, String>> serviceMap = ServiceLoader.servicesMap();
        Map<String, String> routerAdapterManagerMap = serviceMap.get(RouterAdapterManager.class.getName());
        if (routerAdapterManagerMap != null) {
            routerAdapterManagers.putAll(routerAdapterManagerMap);
        }
        Map<String, String> routerAdapterMap = serviceMap.get(AbstractRouterAdapter.class.getName());
        if (routerAdapterMap != null) {
            routerAdapters.putAll(routerAdapterMap);
        }
        Map<String, String> decisionMap = serviceMap.get(RouterDecisionManager.class.getName());
        if (decisionMap != null) {
            decisions.putAll(decisionMap);
        }
    }

    public static String getDecisionKeyFromClass(RouterDecisionManager abstractDecision) {
        if (abstractDecision == null) {
            return null;
        }
        for (Map.Entry<String, String> entry : decisions.entrySet()) {
            if (TextUtils.equals(abstractDecision.getClass().getName(), entry.getValue())) {
                return entry.getKey();
            }
        }
        return null;
    }

    public static boolean isValidRouterAdapterManager(String adapterType) {
        return routerAdapterManagers.containsKey(adapterType);
    }

    public static boolean isValidRouterAdapter(String adapterType) {
        return routerAdapters.containsKey(adapterType);
    }

    public static boolean isValidDecision(String decisionType) {
        return decisions.containsKey(decisionType);
    }

    public static RouterAdapterManager loadRouterAdapterManager(String key) {
        return loadInternal(RouterAdapterManager.class, key);
    }

    public static AbstractRouterAdapter loadRouterAdapter(String key) {
        return loadInternal(AbstractRouterAdapter.class, key);
    }

    public static DecisionTemplate loadDecision(String key) {
        return loadInternal(DecisionTemplate.class, key);
    }

    private static <T> T loadInternal(Class<T> clz, String key) {
        List<T> list = ServiceLoader.load(clz, key);
        if (list != null && list.size() == 1) {
            return list.get(0);
        }
        return null;
    }

}
