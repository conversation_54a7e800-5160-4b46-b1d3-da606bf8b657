package com.meituan.android.payrouter.load;

import android.support.annotation.Keep;

import com.meituan.android.paybase.payrouter.constants.RouterAdapterConstants;

import java.io.Serializable;
import java.util.HashMap;

/**
 * adapter里调用降级时需要的数据
 */
@Keep
public class RouterDowngradeData implements Serializable {

    private static final long serialVersionUID = 6937470720730552665L;
    /**
     * 产品类型
     */
    private String destProductType;
    //目标适配器
    private String destAdapterType;

    /**
     * 这两个值不能同时为空
     * @param productType 要降级到那个产品
     * @param adapterType 要降级到那个适配器
     */
    public RouterDowngradeData(String productType, @RouterAdapterConstants.AdapterType String adapterType) {
        this.destProductType = productType;
        this.destAdapterType = adapterType;
    }

    public String getDestProductType() {
        return destProductType;
    }

    public void setDestProductType(String destProductType) {
        this.destProductType = destProductType;
    }

    public String getDestAdapterType() {
        return destAdapterType;
    }

    public void setDestAdapterType(String destAdapterType) {
        this.destAdapterType = destAdapterType;
    }
}
