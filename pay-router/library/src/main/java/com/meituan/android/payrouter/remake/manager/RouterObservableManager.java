package com.meituan.android.payrouter.remake.manager;

import java.util.HashMap;
import java.util.Map;
import java.util.WeakHashMap;

/**
 * RouterObservableManager 维护了全局的 RouterObservableManager。
 * 并通过 RouterContext 对象索引到对应的观察者中心（RouterObservableImpl）
 */
public class RouterObservableManager {
    private static final Map<Object, RouterObservableManager> managers = new WeakHashMap<>();

    private final Map<Class<?>, RouterObservableImpl<?>> observables = new HashMap<>();

    private <T> RouterObservableImpl<T> impl(Class<T> clz) {
        //noinspection unchecked
        RouterObservableImpl<T> impl = (RouterObservableImpl<T>) observables.get(clz);
        if (impl == null) {
            impl = new RouterObservableImpl<>(clz);
            observables.put(clz, impl);
        }

        return impl;
    }

    public <T> RouterObservable<T> observable(Class<T> clz, String tag) {
        return impl(clz).proxy(tag);
    }

    public <T> RouterObservable<T> observable(Class<T> clz) {
        return impl(clz);
    }

    public void unsubscribe(String tag) {
        for (RouterObservableImpl<?> impl : observables.values()) {
            if (impl != null) {
                impl.unsubscribe(tag);
            }
        }
    }

    public static RouterObservableManager with(Object holder) {
        if (holder == null) {
            return new RouterObservableManager();
        }
        RouterObservableManager manager = managers.get(holder);
        if (manager == null) {
            manager = new RouterObservableManager();
            managers.put(holder, manager);
        }
        return manager;
    }
}
