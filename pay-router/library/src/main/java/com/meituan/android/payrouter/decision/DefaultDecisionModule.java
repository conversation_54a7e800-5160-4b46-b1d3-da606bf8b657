package com.meituan.android.payrouter.decision;

import android.os.Bundle;

import com.meituan.android.paybase.payrouter.RouterActivityProxy;
import com.meituan.android.paybase.payrouter.constants.RouterAdapterConstants;
import com.meituan.android.paybase.payrouter.constants.RouterDecisionConstant;
import com.meituan.android.payrouter.adapter.PayRouterAdapterInterface;
import com.meituan.android.payrouter.load.RouterDowngradeData;
import com.meituan.android.payrouter.router.PayRouter;
import com.meituan.android.payrouter.router.RouterData;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * 默认决策，返回决策失败
 */
@ServiceLoaderInterface(key = RouterDecisionConstant.ROUTER_DECISION_TYPE_DEFAULT, interfaceClass = RouterDecisionInterface.class)
public class DefaultDecisionModule extends RouterDecisionInterface {
    @Override
    public void decide(@RouterAdapterConstants.AdapterType String currentAdapterType, RouterData data, DecisionCallback decisionCallback) {
        decisionCallback.decisionResult(new RouterDecisionResultData(null, RouterDecisionResultData.DecisionResultCode.ROUTER_DECISION_FAIL));
    }

    @Override
    public void downgrade(RouterDowngradeData downGradeData, RouterData routerData, DecisionCallback decisionCallback) {
        decisionCallback.decisionResult(new RouterDecisionResultData(null, RouterDecisionResultData.DecisionResultCode.ROUTER_DECISION_FAIL));
    }

}
