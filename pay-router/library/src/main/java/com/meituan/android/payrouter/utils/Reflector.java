package com.meituan.android.payrouter.utils;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.List;

public class Reflector {
    public static final List<Class<?>> BOOL_CLASS = Arrays.asList(boolean.class, Boolean.class);

    public static final List<Class<?>> CHAR_CLASS = Arrays.asList(char.class, Character.class);

    public static final List<Class<?>> BASE_CLASS = Arrays.asList(byte.class, short.class,
            int.class, long.class, float.class, double.class, char.class);

    public static final List<Class<? extends Number>> NUMBER_CLASS = Arrays.asList(byte.class, Byte.class,
            short.class, Short.class, int.class, Integer.class, long.class, Long.class, float.class, Float.class, double.class, Double.class);

    public static Type getGenericSuperInterfaceType(Object obj, Class<?> targetInterface) {
        if (obj == null) {
            return null;
        }
        Type[] genericSuperInterfaces = obj.getClass().getGenericInterfaces();
        for (Type type : genericSuperInterfaces) {
            if (!(type instanceof ParameterizedType)) {
                continue;
            }
            ParameterizedType parameterizedType = (ParameterizedType) type;
            if (parameterizedType.getRawType() != targetInterface) {
                continue;
            }
            if (parameterizedType.getActualTypeArguments().length != 0) {
                return parameterizedType.getActualTypeArguments()[0];
            }
        }
        return null;
    }

    public static Class<?> getGenericSuperInterfaceClass(Object obj, Class<?> targetInterface) {
        Type type = getGenericSuperInterfaceType(obj, targetInterface);
        if (type instanceof Class) {
            return (Class<?>) type;
        }
        return null;
    }

}
