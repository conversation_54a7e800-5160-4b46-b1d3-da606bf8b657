package com.meituan.android.payrouter.router;

import android.support.annotation.Keep;

import com.meituan.android.paybase.payrouter.constants.RouterConstants;
import com.meituan.android.paybase.payrouter.constants.RouterAdapterConstants;
import com.meituan.android.payrouter.data.RouterReportData;
import com.meituan.android.payrouter.decision.RouterDecisionResultData;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 路由封装的参数,用于在路由各个模块中传递
 */
@Keep
public class RouterData implements Serializable {
    private static final long serialVersionUID = 2234401281904635517L;

    @RouterConstants.RouterType
    private String routerType; //路由目标类型
    @RouterAdapterConstants.AdapterType
    private String currentAdapterType; // 当前适配器类型-可为空

    private String routerUniqueId;

    private transient RouterRequestData routerRequestData; // 业务层请求数据


    private RouterDecisionResultData routerDecisionResultData; // 决策结果数据


    private RouterReportData routerReportData; // 上报数据
    private Map<String, Object> analysisData; //埋点相关数据

    public RouterData(@RouterConstants.RouterType String routerType, RouterRequestData routerRequestData) {
        this.routerType = routerType;
        this.routerRequestData = routerRequestData;
    }

    public String getRouterType() {
        return routerType;
    }

    public void setRouterType(String routerType) {
        this.routerType = routerType;
    }

    public String getCurrentAdapterType() {
        return currentAdapterType;
    }

    public void setCurrentAdapterType(String currentAdapterType) {
        this.currentAdapterType = currentAdapterType;
    }

    public String getRouterUniqueId() {
        return routerUniqueId;
    }

    public void setRouterUniqueId(String routerUniqueId) {
        this.routerUniqueId = routerUniqueId;
    }

    public RouterRequestData getRouterRequestData() {
        if (routerRequestData ==null){
            routerRequestData = new RouterRequestData();
        }
        return routerRequestData;
    }

    public void setRouterRequestData(RouterRequestData routerRequestData) {
        this.routerRequestData = routerRequestData;
    }

    public RouterDecisionResultData getRouterDecisionResultData() {
        return routerDecisionResultData ==null?new RouterDecisionResultData(null, RouterDecisionResultData.DecisionResultCode.ROUTER_DECISION_FAIL, "DecisionResultData is null"):routerDecisionResultData;
    }

    public void setRouterDecisionResultData(RouterDecisionResultData routerDecisionResultData) {
        this.routerDecisionResultData = routerDecisionResultData;
    }

    public RouterReportData getRouterReportData() {
        return routerReportData;
    }

    public void setRouterReportData(RouterReportData routerReportData) {
        this.routerReportData = routerReportData;
    }

    public Map<String, Object> getAnalysisData() {
        return analysisData != null ? analysisData : new HashMap<>();
    }

    public void setAnalysisData(Map<String, Object> analysisData) {
        this.analysisData = analysisData;
    }
}
