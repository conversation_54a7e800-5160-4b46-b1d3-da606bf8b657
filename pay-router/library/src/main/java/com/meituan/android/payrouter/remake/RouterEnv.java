package com.meituan.android.payrouter.remake;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Bundle;

import com.meituan.android.paybase.utils.AppUtils;
import com.meituan.android.payrouter.remake.config.RouterConfigManager;
import com.meituan.android.payrouter.remake.router.manager.RouterImplManager;

public class RouterEnv {
    private static RouterInitHandler initHandler;

    public synchronized static void init(RouterInitHandler initHandler) {
        if (RouterEnv.initHandler != null) {
            return;
        }
        RouterEnv.initHandler = initHandler;
        RouterImplManager.init();
        RouterConfigManager.init();

        getApplication().unregisterActivityLifecycleCallbacks(RouterLifecycleCallback.getInstance());
        getApplication().registerActivityLifecycleCallbacks(RouterLifecycleCallback.getInstance());
    }

    public static boolean isDebug() {
        return AppUtils.isDebug();
    }

    public static Application getApplication() {
        return (Application) initHandler.getContext().getApplicationContext();
    }

    private static class RouterLifecycleCallback implements Application.ActivityLifecycleCallbacks {

        private RouterLifecycleCallback() {
        }

        private static class InnerSingleHolder {
            private static final RouterLifecycleCallback INSTANCE = new RouterLifecycleCallback();
        }

        public static RouterLifecycleCallback getInstance() {
            return InnerSingleHolder.INSTANCE;
        }

        @Override
        public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
        }

        @Override
        public void onActivityStarted(Activity activity) {
        }

        @Override
        public void onActivityResumed(Activity activity) {
        }

        @Override
        public void onActivityPaused(Activity activity) {
        }

        @Override
        public void onActivityStopped(Activity activity) {
        }

        @Override
        public void onActivitySaveInstanceState(Activity activity, Bundle outState) {
        }

        @Override
        public void onActivityDestroyed(Activity activity) {
        }
    }

    public interface RouterInitHandler {
        Context getContext();

        String getUserId();
    }
}
