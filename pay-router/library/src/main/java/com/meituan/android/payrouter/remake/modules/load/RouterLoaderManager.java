package com.meituan.android.payrouter.remake.modules.load;

import android.os.Parcelable;

import com.meituan.android.payrouter.remake.modules.load.data.LoadData;
import com.meituan.android.payrouter.remake.modules.load.data.LoadResult;
import com.meituan.android.payrouter.remake.router.context.RouterContext;
import com.meituan.android.payrouter.remake.router.manager.RouterManager;
import com.meituan.android.payrouter.utils.bus.Message;
import com.meituan.android.payrouter.utils.bus.RouterBus;

/**`
 * 三个阶段：
 * 1. 启动：onLoad
 * 2. 加载结果：onResult
 * 3. 加载结束（Activity回调onActivityResult，Fragment 通过Router调用remove）重置loader=null
 */
public class RouterLoaderManager implements RouterBus.Observer {
    private final RouterContext routerContext;

    public RouterLoaderManager(RouterContext context) {
        this.routerContext = context;
        RouterManager.manager(routerContext.getContext()).bus().subscribe(routerContext.trace(), this);
    }

    public void onLoad(LoadData loadData) {
        // Invalid的loadData按照错误进行回调
        if (!LoadData.isValid(loadData)) {
            routerContext.observable(LoaderObserver.class).dispatch().onLoadResult(
                    LoadResult.newBuilder().setCode("500").setMessage("load error").build());
            return;
        }
        AbstractLoader loader;
        if (loadData.loadType() == LoadData.LOAD_TYPE_FRAGMENT) {
            loader = new FragmentLoader(routerContext);
        } else {
            loader = new ActivityLoader(routerContext);
        }
        loader.onLoad(loadData);
    }

    public void onResult(LoadResult result) {
        routerContext.observable(LoaderObserver.class).dispatch().onLoadResult(result);
    }

    @Override
    public boolean onReceived(RouterBus bus, Message message) {
        if (message == null) {
            return false;
        }
        Parcelable parcelable = message.getContent();
        if (parcelable instanceof LoadResult) {
            onResult((LoadResult) parcelable);
            return true;
        }
        return false;
    }
}
