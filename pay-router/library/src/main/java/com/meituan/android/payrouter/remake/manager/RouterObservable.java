package com.meituan.android.payrouter.remake.manager;

import java.util.Collection;

public interface RouterObservable<T> {

    void subscribe(T observer);

    void subscribeUniquely(T observer);

    void unsubscribe(T observer);

    void unsubscribe();

    Collection<T> observers();

    RouterObservable<T> proxy(String tag);

    T dispatch(boolean consumedReturn);

    T dispatch();

    T subscriber();

}
