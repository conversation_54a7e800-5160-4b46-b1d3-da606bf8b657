package com.meituan.android.payrouter.remake.modules.decision.data;

import android.os.Parcel;
import android.os.Parcelable;
import android.support.annotation.Keep;

import com.google.gson.annotations.SerializedName;

import java.util.List;

@Keep
public class DecisionProductTypeData implements Parcelable {
    @SerializedName("decisionType")
    private String decisionType;

    @SerializedName("downgradeList")
    private List<String> downgradeList;

    protected DecisionProductTypeData(Parcel in) {
        decisionType = in.readString();
        downgradeList = in.createStringArrayList();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(decisionType);
        dest.writeStringList(downgradeList);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<DecisionProductTypeData> CREATOR = new Creator<DecisionProductTypeData>() {
        @Override
        public DecisionProductTypeData createFromParcel(Parcel in) {
            return new DecisionProductTypeData(in);
        }

        @Override
        public DecisionProductTypeData[] newArray(int size) {
            return new DecisionProductTypeData[size];
        }
    };

    public String getDecisionType() {
        return decisionType;
    }

    public void setDecisionType(String decisionType) {
        this.decisionType = decisionType;
    }

    public List<String> getDowngradeList() {
        return downgradeList;
    }

    public void setDowngradeList(List<String> downgradeList) {
        this.downgradeList = downgradeList;
    }
}
