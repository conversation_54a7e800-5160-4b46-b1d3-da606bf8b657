package com.meituan.android.payrouter.remake.router.manager;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.os.Parcelable;
import android.text.TextUtils;

import com.meituan.android.pay.base.compat.BundleCompat;
import com.meituan.android.pay.base.compat.OSCompat;
import com.meituan.android.paybase.config.PayBaseConfig;
import com.meituan.android.paybase.utils.CatchException;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.payrouter.remake.base.OnDestroy;
import com.meituan.android.payrouter.remake.base.Restorable;
import com.meituan.android.payrouter.remake.manager.RouterObservable;
import com.meituan.android.payrouter.remake.modules.load.data.LoadResult;
import com.meituan.android.payrouter.remake.result.RouterCallback;
import com.meituan.android.payrouter.remake.result.RouterResult;
import com.meituan.android.payrouter.remake.router.Router;
import com.meituan.android.payrouter.remake.router.data.RouterData;
import com.meituan.android.payrouter.remake.router.impl.ErrorRouter;
import com.meituan.android.payrouter.utils.CachedParamCenter;
import com.meituan.android.payrouter.utils.CachedWeakParamCenter;
import com.meituan.android.payrouter.utils.ProxyUtils;
import com.meituan.android.payrouter.utils.bus.Message;
import com.meituan.android.payrouter.utils.bus.RouterBus;
import com.meituan.android.payrouter.utils.bus.RouterBusManager;

import java.io.Serializable;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 基于Context维护的RouterManager
 */
public class RouterManager implements Restorable, OnDestroy {
    private static final String SAVE_STATE_KEY_ROUTER_TRACES = "router_manager_router_traces";

    private static final CachedWeakParamCenter<Context, RouterManager> MANAGERS
            = CachedWeakParamCenter.withClz(RouterManager.class, RouterManager::new);

    private final Map<String, Router> routersOfTrace = new HashMap<>();

    private final WeakReference<Context> weakContext;

    private RouterManager(Context context) {
        this.weakContext = new WeakReference<>(context);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        if (savedInstanceState == null) {
            return;
        }
        // 恢复 bus 中的数据
        RouterBusManager.getRestorable(this).onCreate(savedInstanceState);
        ArrayList<String> traces = savedInstanceState.getStringArrayList(SAVE_STATE_KEY_ROUTER_TRACES);
        if (CollectionUtils.isEmpty(traces)) {
            return;
        }
        for (String trace : traces) {
            // 恢复 Routers 中的数据
            Router router = new Router(context(), null, trace);
            Bundle bundle = savedInstanceState.getBundle(trace);
            if (OSCompat.isAndroid10()) {
                BundleCompat.fixBundleClassLoader(context(), bundle);
            }
            performRouterCreate(router, bundle);
        }
    }

    @Override
    public void onSaveInstanceState(Bundle savedInstanceState) {
        if (savedInstanceState == null) {
            return;
        }
        ArrayList<String> traces = new ArrayList<>();
        for (Map.Entry<String, Router> entry : routersOfTrace.entrySet()) {
            traces.add(entry.getKey());
            // 保存 Routers 的数据
            performRouterSaveState(entry.getValue(), savedInstanceState);
        }
        // 保存 Routers 的 Key
        savedInstanceState.putStringArrayList(SAVE_STATE_KEY_ROUTER_TRACES, traces);
        // 保存 Bus 的数据
        RouterBusManager.getRestorable(this).onSaveInstanceState(savedInstanceState);
    }

    @Override
    public void onDestroy() {
        for (Router router : routersOfTrace.values()) {
            performRouterDestroy(router);
        }
    }

    /**
     * 【新建场景需要调用】开启一个新路由
     *
     * @param data     路由需要的数据
     * @param callback 路由的回调函数
     * @return trace        路由追踪标志
     */
    public String open(RouterData data, RouterCallback callback) {
        Router router = new Router(context(), data);
        link(router.trace(), callback);
        performRouterCreate(router, null);
        return router.trace();
    }

    /**
     * 【重建场景需要调用】链接路由和回调。
     * 重建场景，路由和业务侧的回调会断开，需要业务侧拿着路由返回的trace，重新和路由进行绑定链接。
     *
     * @param trace    路由追踪标志
     * @param callback 路由回调
     */
    public void link(String trace, RouterCallback callback) {
        if (trace != null) {
            bus().subscribe(trace, (bus, message) -> handleMessage(bus, message, callback));
        }
    }

    protected boolean handleMessage(RouterBus bus, Message message, RouterCallback callback) {
        if (message == null) {
            return false;
        }
        String trace = message.trace();
        Parcelable content = message.getContent();
        if (content instanceof RouterResult) {
            bus.unsubscribe(trace);
            performRouterDestroy(helper(trace).router());
            callback.onRouterFinished((RouterResult) content);
            return true;
        }
        return false;
    }

    protected void performRouterCreate(Router router, Bundle savedInstanceState) {
        if (router != null) {
            CatchException.run(() -> router.onCreate(savedInstanceState)).catchForReport("RouterManager_performRouter_onCreate");
            routersOfTrace.put(router.trace(), router);
            CatchException.run(router::onStart).catchForReport("RouterManager_performRouter_onStart");
        }
    }

    protected void performRouterSaveState(Router router, Bundle savedInstanceState) {
        if (router != null && savedInstanceState != null) {
            Bundle routerBundle = new Bundle();
            router.onSaveInstanceState(routerBundle);
            savedInstanceState.putBundle(router.trace(), routerBundle);
        }
    }

    protected void performRouterDestroy(Router router) {
        if (router != null) {
            routersOfTrace.remove(router.trace());
            CatchException.run(router::onDestroy).catchForReport("RouterManager_performRouter_onDestroy");
        }
    }

    public Context context() {
        return weakContext.get();
    }

    public RouterBus bus() {
        return RouterBusManager.getBus(this);
    }

    /**
     * 分发给当前所有的router下的observer
     *
     * @param clz 调用方法对应的接口类型
     * @param consumedReturn 存在消费行为后是否终止。
     *                       消费行为：returnType为boolean类型且return true
     * @return 接口类型对应的动态代理
     */
    public <T> T dispatch(Class<T> clz, boolean consumedReturn) {
        return ProxyUtils.transferList(clz, routersOfTrace.values(), consumedReturn,
                router -> router.observable(clz).dispatch(consumedReturn));
    }

    public <T> T dispatch(Class<T> clz) {
        return dispatch(clz, true);
    }

    /**
     * 分发给当前所有的router
     *
     * @param clz 调用方法对应的接口类型
     * @param consumedReturn 存在消费行为后是否终止。
     *                       消费行为：returnType为boolean类型且return true
     * @return 接口类型对应的动态代理
     */
    public <T> T dispatchRouter(Class<T> clz, boolean consumedReturn) {
        return ProxyUtils.list(clz, routersOfTrace.values(), consumedReturn);
    }

    public static RouterManager manager(Context context) {
        return context != null ? MANAGERS.get(context) : new RouterManager(PayBaseConfig.getProvider().getApplicationContext());
    }

    public static Launcher launcher(Context context) {
        return new Launcher(context);
    }

    public static RouterHelper helper(String trace) {
        return RouterHelper.HELPERS.get(trace);
    }

    public static Notifier notifier(String trace) {
        return Notifier.NOTIFIERS.get(trace);
    }

    public static Notifier notifier(Activity calledActivity) {
        return new Notifier(calledActivity);
    }

    public static class RouterHelper {
        private static final CachedParamCenter<String, RouterHelper> HELPERS
                = CachedParamCenter.withClz(RouterHelper.class, RouterHelper::new);

        protected final Router router;

        protected final String trace;

        protected RouterManager manager;

        public RouterHelper(String trace) {
            this.trace = trace;
            for (RouterManager manager : MANAGERS.values()) {
                if (manager == null) {
                    continue;
                }
                Router router = manager.routersOfTrace.get(trace);
                if (router != null) {
                    this.manager = manager;
                    this.router = router;
                    return;
                }
            }
            this.manager = manager(null);
            this.router = ErrorRouter.getInstance();
        }


        public <T> RouterObservable<T> observeOn(Class<T> clz) {
            return router.observable(clz);
        }

        public String type() {
            return router.type();
        }

        public Router router() {
            return router;
        }
    }

    public static class Launcher {
        private final RouterManager manager;

        private final Bundle businessData;

        private String type;

        private String trace;

        private RouterCallback callback;

        public Launcher(Context context) {
            this.manager = manager(context);
            this.businessData = new Bundle();
        }

        public Launcher type(String routerType) {
            this.type = routerType;
            return this;
        }

        public Launcher trace(String trace) {
            this.trace = trace;
            return this;
        }

        public Launcher addData(String key, Serializable data) {
            businessData.putSerializable(key, data);
            return this;
        }

        public Launcher addData(String key, Parcelable data) {
            businessData.putParcelable(key, data);
            return this;
        }

        public Launcher callback(RouterCallback callback) {
            this.callback = callback;
            return this;
        }

        public String launch(boolean restore) {
            if (!restore) {
                return manager.open(RouterData.builder(type).setBusinessData(businessData).build(), callback);
            } else {
                manager.link(trace, callback);
                return trace;
            }
        }
    }

    public static class Notifier extends RouterHelper {
        private static final CachedParamCenter<String, Notifier> NOTIFIERS
                = CachedParamCenter.withClz(Notifier.class, Notifier::new);

        public static final String KEY_ROUTER_TRACE = "router_trace";

        public Notifier(String trace) {
            super(trace);
        }

        public Notifier(Activity calledActivity) {
            super(parseTrace(calledActivity));
        }

        public void notifyLoadSuccess(String msg) {
            android.util.Log.d("com.demo.pay", "Notifier|notifyLoadSuccess|" + msg);
            if (!TextUtils.isEmpty(trace)) {
                Message message = Message.make(trace, LoadResult.success(msg).build());
                manager.bus().sendMessage(message);
            }
        }

        public void notifyRouterResult(RouterResult result) {
            if (!TextUtils.isEmpty(trace)) {
                Message message = Message.make(trace, result);
                manager.bus().sendMessage(message);
            }
        }

        private static String parseTrace(Activity calledActivity) {
            return CatchException.run(() -> calledActivity.getIntent().getStringExtra(KEY_ROUTER_TRACE), null)
                    .catchForReport("RouterManager_parseTrace").value();
        }
    }
}
