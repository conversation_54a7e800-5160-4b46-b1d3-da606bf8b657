package com.meituan.android.payrouter.utils.report;

import android.support.annotation.Keep;
import android.text.TextUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Keep
public class RaptorData implements Serializable {
    private String command;
    private Map<String, Object> custom;

    private RaptorData() {
        custom = new HashMap<>();
    }

    public String getCommand() {
        return command;
    }

    public Map<String, Object> getCustom() {
        return custom;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static Builder command(String command) {
        return new Builder().command(command);
    }

    public static Builder builder(RaptorData raptorData) {
        return new Builder(raptorData);
    }

    public static class Builder {
        private final RaptorData raptorData;

        private Builder() {
            this.raptorData = new RaptorData();
        }

        private Builder(RaptorData raptorData) {
            this.raptorData = raptorData != null ? raptorData : new RaptorData();
        }

        public RaptorData build() {
            return raptorData;
        }

        public Builder command(String command) {
            raptorData.command = command;
            return this;
        }

        public Builder custom(Map<String, Object> custom) {
            if (custom != null) {
                raptorData.custom.putAll(custom);
            }
            return this;
        }

        public Builder custom(String key, Object val) {
            if (!TextUtils.isEmpty(key) && val != null) {
                raptorData.custom.put(key, val);
            }
            return this;
        }
    }
}
