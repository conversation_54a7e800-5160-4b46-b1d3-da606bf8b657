package com.meituan.android.payrouter.remake.modules.decision.data;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.support.annotation.Keep;
import android.text.TextUtils;

@Keep
public class DowngradeData implements Parcelable {
    private static final String DOWNGRADE_INFO = "downgrade_info";

    private String destProductType;

    private String destAdapterType;

    private Bundle downgradeExtras;


    private transient boolean fromBusiness;

    public DowngradeData() {
    }

    public DowngradeData(DowngradeData downgradeData) {
        if (downgradeData != null) {
            this.destProductType = downgradeData.getDestProductType();
            this.destAdapterType = downgradeData.getDestAdapterType();
            this.downgradeExtras = downgradeData.getDowngradeExtras();
        }
    }

    protected DowngradeData(Parcel in) {
        destProductType = in.readString();
        destAdapterType = in.readString();
        downgradeExtras = in.readBundle(getClass().getClassLoader());
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(destProductType);
        dest.writeString(destAdapterType);
        dest.writeBundle(downgradeExtras);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<DowngradeData> CREATOR = new Creator<DowngradeData>() {
        @Override
        public DowngradeData createFromParcel(Parcel in) {
            return new DowngradeData(in);
        }

        @Override
        public DowngradeData[] newArray(int size) {
            return new DowngradeData[size];
        }
    };

    public String getDestProductType() {
        return destProductType;
    }

    public void setDestProductType(String destProductType) {
        this.destProductType = destProductType;
    }

    public String getDestAdapterType() {
        return destAdapterType;
    }

    public void setDestAdapterType(String destAdapterType) {
        this.destAdapterType = destAdapterType;
    }

    public Bundle getDowngradeExtras() {
        return downgradeExtras;
    }

    public void setDowngradeExtras(Bundle downgradeExtras) {
        this.downgradeExtras = downgradeExtras;
    }

    public String getDowngradeInfo() {
        return downgradeExtras != null ? downgradeExtras.getString(DOWNGRADE_INFO, "") : "";
    }

    public boolean isFromBusiness() {
        return fromBusiness;
    }

    public void setFromBusiness(boolean fromBusiness) {
        this.fromBusiness = fromBusiness;
    }

    public static DowngradeData wrapDowngradeDataFromBusiness(DowngradeData downgradeData) {
        if (downgradeData == null) {
            downgradeData = new DowngradeData();
        }
        downgradeData.setFromBusiness(true);
        return downgradeData;
    }

    public static boolean isValid(DowngradeData downgradeData) {
        return downgradeData != null && !TextUtils.isEmpty(downgradeData.getDestProductType());
    }

    public static DowngradeData create(String destProductType) {
        return create(destProductType, null, null);
    }

    public static DowngradeData create(String destProductType, String destAdapterType) {
        return create(destProductType, destAdapterType, null);
    }

    public static DowngradeData create(String destProductType, String destAdapterType, String downgradeInfo) {
        DowngradeData downgradeData = new DowngradeData();
        downgradeData.setDestProductType(destProductType);
        downgradeData.setDestAdapterType(destAdapterType);
        if (!TextUtils.isEmpty(downgradeInfo)) {
            Bundle downgradeExtras = new Bundle();
            downgradeExtras.putString("downgrade_info", downgradeInfo);
            downgradeData.setDowngradeExtras(downgradeExtras);
        }
        return downgradeData;
    }
}
