package com.meituan.android.payrouter.router;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.paybase.common.activity.PayBaseActivity;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.payrouter.RouterActivityProxy;
import com.meituan.android.paybase.payrouter.callback.RouterCallback;
import com.meituan.android.paybase.payrouter.constants.RouterRegisterManager;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.payrouter.adapter.PayRouterAdapterInterface;
import com.meituan.android.payrouter.data.RouterReportModule;
import com.meituan.android.payrouter.decision.RouterDecisionInterface;
import com.meituan.android.payrouter.decision.RouterDecisionResultData;
import com.meituan.android.payrouter.load.RouterDowngradeData;
import com.meituan.android.payrouter.load.RouterLoadCallback;
import com.meituan.android.payrouter.load.RouterLoadInterface;
import com.meituan.android.payrouter.load.RouterLoadResultData;
import com.sankuai.meituan.serviceloader.ServiceLoader;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PayRouter extends BaseRouter implements RouterCallback, RouterLoadCallback {
    @SerializedName("routerData")
    private RouterData routerData; //路由内部各个模块传递参数

    // 不持有Activity，防止由于路由的长时间调用导致Activity内存泄漏，持有Activity的代理对象。
    private transient RouterActivityProxy routerActivityProxy;
    private transient RouterDecisionInterface routerDecisionModule;
    private transient RouterLoadInterface routerLoaderModule;
    private transient PayRouterAdapterInterface routerAdapter;
    private String restoreKey;

    public PayRouter(String routerUniqueId) {
        super(routerUniqueId);
    }

    /**
     * 通过系统方法返回的内容会通过RouterProxy代理给PayRouter，并返回给业务Adapter处理
     *
     * @param requestCode 通过系统请求的requestCode，不能重复
     * @param resultCode  通过系统返回的resultCode
     * @param data        返回数据集合
     */
    @Override
    public void onResultCallback(int requestCode, int resultCode, Intent data) {
        if (routerAdapter != null) {
            routerAdapter.onResultCallback(requestCode, resultCode, data);
        }
    }

    /**
     *
     */
    @Override
    public void onLoadCallback(RouterLoadResultData resultData) {
        if (routerAdapter != null) {
            routerAdapter.onLoadCallback(resultData);
        }
    }

    /**
     * 获取当前的routerAdapter
     *
     * @return
     */
    public PayRouterAdapterInterface getRouterAdapter() {
        return routerAdapter;
    }

    @Override
    public String onSaveState(Bundle bundle) {
        String key = getRouterUniqueId();
        bundle.putString(key, GsonProvider.getInstance().toJson(routerData));
        if (routerAdapter != null) {
            routerAdapter.onSaveState(bundle);
        }
        if (this.routerDecisionModule!=null){
            this.routerDecisionModule.onSaveState(bundle);
        }
        return key;
    }

    /**
     * 对于restore方法来说，不仅需要重新恢复数据及对象，还要恢复它们的状态。
     * init()->恢复Serializable类型数据
     * initLoader()->恢复LoaderModule对象（无状态）
     * initDecision()->恢复DecisionModule对象（无状态）
     * initAdapterFromDecision()->恢复Adapter对象（有状态）
     *
     * @param bundle                                         公共存储对象，所有路由需要持久化的内容都在bundle中。
     * @param key，存储时保留的key，会在onRestoreState中返回，用于找到之前存储的内容。
     * @param routerActivityProxy                            路由代理对象，用于恢复操作。
     */
    @Override
    public void onRestoreState(Bundle bundle, String key, RouterActivityProxy routerActivityProxy) {
        String restoredData = bundle.getString(key);
        RouterData routerData = GsonProvider.getInstance().fromJson(restoredData, RouterData.class);
        onCreate(routerActivityProxy, routerData, false);
        initLoader();
        initDecision();
        this.restoreKey =key;
        /* 除了初始数据 & Loader & Decision模块，还需要重建Adapter模块，Adapter模块的定制化程度较高，所以需要进行调用对应的数据来进行恢复
        由于同一个bundle中可能同时存在多个相同Adapter类实例，所以通过类恢复
         */
        routerAdapter = createAdapterFromDecision(routerData.getRouterDecisionResultData());
        // new对象后进行Loader和Adapter的初始化
        if (routerLoaderModule != null) {
            routerLoaderModule.onRestore(routerActivityProxy, routerData, routerAdapter, this);
        }
        if (this.routerDecisionModule!=null){
            this.routerDecisionModule.onRestore(bundle,routerActivityProxy, routerData, routerAdapter, (data) -> PayRouter.this.decideResult(data, false));
        }
    }

    @Override
    public void onCreate(RouterActivityProxy routerActivityProxy, RouterData routerData, boolean restored) {
        super.onCreate(routerActivityProxy, routerData, restored);
        this.routerActivityProxy = routerActivityProxy;
        this.routerActivityProxy.registerRouterProxyCallback(getRouterUniqueId(), this);
        this.routerData = routerData;
        this.routerData.setRouterUniqueId(getRouterUniqueId());
        this.routerData.setAnalysisData(new HashMap<>());
        if (!restored) {
            RouterReportModule.routerStart(routerData);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public RouterActivityProxy getRouterProxy() {
        return routerActivityProxy;
    }


    @Override
    public void restoreByActivity(PayBaseActivity activity, RouterCallback routerCallback, Bundle bundle) {
        routerActivityProxy.setRouterCallback(getRouterUniqueId(), routerCallback);
        routerAdapter.onRestoreState(bundle,restoreKey,routerActivityProxy);
        //通知业务方找到合适的adapter了
        if (routerCallback != null) {
            routerCallback.onRouterCashierReady(routerData.getRouterType(), routerAdapter);
        }
    }

    /**
     * 打开特定的业务
     */
    public void open() {
        if (!checkData()) {
            // TODO & ERROR & REPORT
            return;
        }
        initLoader();
        initDecision();
        decideStart();
    }

    private boolean checkData() {
        if (routerActivityProxy == null) {
            routerFail(RouterCode.ROUTER_RESULT_INVALID_PARAMETER, "Context is null");
            return false;
        }
        if (routerActivityProxy.getRouterCallback(getRouterUniqueId()) == null) {
            reportCommonError("RouterCallback for checkData is null");
            return false;
        }
        if (TextUtils.isEmpty(routerData.getRouterType())) {
            routerFail(RouterCode.ROUTER_RESULT_INVALID_PARAMETER, "RouterType is null");
            return false;
        }
        return true;
    }

    /**
     * 初始化加载模块，目前就一个加载模块
     */
    private void initLoader() {
        this.routerLoaderModule = RouterLoadInterface.getDecisionByType(routerData.getRouterType());
    }

    /**
     * 初始化决策模块，找不到返回默认决策
     */
    private void initDecision() {
        //通过注册的类型找到并加载决策模块
        String decisionType = RouterRegisterManager.getRouterDecisionType(routerData.getRouterType());
        List<RouterDecisionInterface> decisionInterfaces = ServiceLoader.load(RouterDecisionInterface.class, decisionType);
        if (CollectionUtils.isEmpty(decisionInterfaces) || decisionInterfaces.get(0) == null) {
            reportCommonError("RouterDecision for open is null");
            this.routerDecisionModule = RouterDecisionInterface.getDefaultDecision();
        } else {
            this.routerDecisionModule = decisionInterfaces.get(0);
        }
        this.routerDecisionModule.setProxy(getRouterProxy());
    }

    //--------- 加载模块回调

    /**
     * 加载模块回调成功
     */
    @Override
    public void loadResultCallback(RouterLoadResultData loadResultData) {
        //把adapter透传的数据在传递给下个adapter，会覆盖
        HashMap<String, Object> map = loadResultData.getMap();
        routerData.getRouterRequestData().setAdapterExtraData(map);

        if (loadResultData.isLoadSuccess()) {
            //TODO nothing
        } else if (loadResultData.isLoadDowngrade()) {
            //业务方load后指定降级
            RouterReportModule.downgradeStart(routerData);
            RouterDowngradeData downGradeData = loadResultData.getDowngradeData();
            this.routerDecisionModule.downgrade(downGradeData, routerData,
                    (data) -> PayRouter.this.decideResult(data, true));
        } else {
            routerFail(RouterCode.ROUTER_RESULT_BUSINESS_ERROR, "open failure");
        }
    }

    /**
     * 业务完成回调
     *
     * @param result 加载结果
     */
    @Override
    public void completionCallback(RouterCallback.Result result) {
        RouterReportModule.routerEnd(routerData, result);
        onRouterComplete(result);
    }

    //--------- 决策模块回调

    /**
     * 开始执行决策
     */
    private void decideStart() {
        try {
            routerDecisionModule.baseDecide(null, routerData, (data) -> PayRouter.this.decideResult(data, false));
        } catch (Exception e) {
            routerFail(RouterCode.ROUTER_RESULT_DECISION_ERROR, e.getMessage());
        }
    }

    /**
     * 处理决策/降级回调
     */
    private void decideResult(RouterDecisionResultData data, boolean isDowngrade) {
        int failCode = isDowngrade ? RouterCode.ROUTER_RESULT_DOWN_GRADE_ERROR : RouterCode.ROUTER_RESULT_DECISION_ERROR;
        if (data == null || !data.isDecisionSuccess()) {
            String msg;
            if (data == null) {
                msg = "RouterDecisionResultData is null";
            } else {
                msg = data.getMsg();
            }
            String failMsg = isDowngrade ? "downgrade failure " + msg : "decision failure " + msg;
            if (isDowngrade) {
                RouterReportModule.downgradeEndFail(routerData, failMsg);
            } else {
                RouterReportModule.decisionEndFail(routerData, failMsg);
            }
            routerFail(failCode, failMsg);
            return;
        }

        if (TextUtils.isEmpty(data.getDestAdapterType())) {
            String failMsg = isDowngrade ? "downgrade type is null " + data.getMsg() : "decision type is null " + data.getMsg();
            if (isDowngrade) {
                RouterReportModule.downgradeEndFail(routerData, failMsg);
            } else {
                RouterReportModule.decisionEndFail(routerData, failMsg);
            }
            routerFail(failCode, failMsg);
            return;
        }

        if (data.isDecisionSuccess()) {
            //决策循环了，需要跳出
            if (TextUtils.equals(routerData.getCurrentAdapterType(), data.getDestAdapterType())) {
                String failMsg = isDowngrade ? "downgrade type is same " + data.getMsg() : "decision type is same " + data.getMsg();
                if (isDowngrade) {
                    RouterReportModule.downgradeEndFail(routerData, failMsg);
                } else {
                    RouterReportModule.decisionEndFail(routerData, failMsg);
                }
                routerFail(failCode, failMsg);
                return;
            }
            routerData.setRouterDecisionResultData(data);
            // 确定出RouterAdapter
            routerAdapter = createAdapterFromDecision(data);
            // 根据决策结果加载adapter，进行业务决策
            executeAdapterConsumed(routerAdapter, isDowngrade);
        }
    }

    /**
     * Adapter：初始化
     *
     * @param routerDecisionResultData 决策数据
     */
    private PayRouterAdapterInterface createAdapterFromDecision(RouterDecisionResultData routerDecisionResultData) {
        String decisionAdapterType = routerDecisionResultData.getDestAdapterType();
        List<PayRouterAdapterInterface> adapterInterfaceList = ServiceLoader.load(PayRouterAdapterInterface.class, decisionAdapterType);
        PayRouterAdapterInterface routerAdapter;
        if (adapterInterfaceList == null || adapterInterfaceList.size() == 0 || adapterInterfaceList.get(0) == null) {
            reportCommonError("PayRouterAdapter for decisionResult is null");
            routerAdapter = PayRouterAdapterInterface.getDefaultAdapter();
        } else {
            routerAdapter = adapterInterfaceList.get(0);
        }
        // 创建完adapter 后就更新当前适配器类型数据
        routerData.setCurrentAdapterType(decisionAdapterType);
        routerAdapter.init(routerActivityProxy, routerData);
        return routerAdapter;
    }

    /**
     * Adapter：执行consumed方法
     *
     * @param isDowngrade 是否是降级场景
     */
    private void executeAdapterConsumed(PayRouterAdapterInterface routerAdapter, boolean isDowngrade) {
        int failCode = isDowngrade ? RouterCode.ROUTER_RESULT_DOWN_GRADE_ERROR : RouterCode.ROUTER_RESULT_DECISION_ERROR;
        try {
            PayRouterAdapterInterface.ConsumeResult result = routerAdapter.isConsumed(routerData.getRouterRequestData());
//
            if (result.isConsumed()) {
                if (isDowngrade) {
                    RouterReportModule.downgradeEnd(routerData);
                } else {
                    RouterReportModule.decisionEnd(routerData);
                }
                RouterCallback routerCallback = routerActivityProxy.getRouterCallback(getRouterUniqueId());
                //通知业务方找到合适的adapter了
                if (routerCallback != null) {
                    routerCallback.onRouterCashierReady(routerData.getRouterType(), routerAdapter);
                }
                //业务决策成功，进行加载
                routerLoaderModule.loadAdapter(routerActivityProxy, routerData, routerAdapter, PayRouter.this);
            } else {
                RouterReportModule.adapterConsumeResult(routerData, result);
                if (isDowngrade) {
                    RouterReportModule.downgradeStart(routerData);
                    //适配器决策失败，需要重新走决策模块进行决策
                    routerDecisionModule.downgrade(null, routerData,
                            (data) -> PayRouter.this.decideResult(data, isDowngrade));
                } else {
                    //适配器决策失败，需要重新走决策模块进行决策
                    routerDecisionModule.baseDecide(routerData.getCurrentAdapterType(), routerData,
                            (data) -> PayRouter.this.decideResult(data, isDowngrade));
                }
            }

//            adapterConsumeResult(routerAdapter, result.isConsumed(), result.getMessage(), isDowngrade);
        } catch (Exception e) {
            routerFail(failCode, "adapter invoke fail :" + e.getMessage());
        }
    }

    //--------- 适配器回调
    private void adapterConsumeResult(PayRouterAdapterInterface routerAdapter, boolean isSuccess, String message, boolean isDowngrade) {

    }

    @Override
    public void onRouterError(String routerType, int code, String message) {
        RouterCallback routerCallback = routerActivityProxy.getRouterCallback(getRouterUniqueId());
        if (routerCallback != null) {
            routerCallback.onRouterError(routerType, code, message);
        } else {
            reportCommonError("RouterCallback for routerError is null");
        }
    }

    @Override
    public void onRouterComplete(Result result) {
        RouterCallback routerCallback = routerActivityProxy.getRouterCallback(getRouterUniqueId());
        if (routerCallback != null) {
            routerCallback.onRouterComplete(result);
        } else {
            reportCommonError("RouterCallback for routerComplete is null");
        }
    }

    @Override
    public void onRouterCashierReady(String routerType, Object routerAdapter) {

    }

    /**
     * 失败回调给业务方
     *
     * @param code    错误码
     * @param message 错误信息
     */
    private void routerFail(int code, String message) {
        Map<String, Object> map = new HashMap<>();
        map.put("code", code);
        map.put("message", message);
        RouterReportModule.routerEndFail(routerData, map);
        onRouterError(routerData.getRouterType(), code, message);
    }

    private void reportCommonError(String errorMessage) {
        Map<String, Object> errorData = new AnalyseUtils.MapBuilder().add("errorMsg", errorMessage).build();
        RouterReportModule.routerCommonError(routerData, errorData);
    }

    /**
     * 路由结果code
     */
    public interface RouterCode {
        //参数缺失
        int ROUTER_RESULT_INVALID_PARAMETER = 0;
        //决策失败
        int ROUTER_RESULT_DECISION_ERROR = 1;
        /// 降级失败
        int ROUTER_RESULT_DOWN_GRADE_ERROR = 2;
        // 业务失败
        int ROUTER_RESULT_BUSINESS_ERROR = 3;
    }

}
