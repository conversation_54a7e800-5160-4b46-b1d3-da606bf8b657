package com.meituan.android.payrouter.decision.common;

import android.support.annotation.Keep;
import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.meituan.android.paybase.utils.GsonProvider;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Keep
public class CommonDecideData implements Serializable {
    private static final long serialVersionUID = -5485572131646650558L;
    private String productType;

    //horn 下发的全部数据，包含各种porductType里的决策和降级数据
    private JSONObject allData;

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public JSONObject getAllData() {
        return allData;
    }

    public void setAllData(JSONObject allData) {
        this.allData = allData;
    }

    /**
     * 获取特定产品类型的 决策数据
     *
     * @param destProductType
     * @return
     */
    public ProductData getProductData(String destProductType) {
        if (TextUtils.isEmpty(destProductType) || allData == null) {
            return null;
        }
        try {
            JSONObject object = allData.optJSONObject(destProductType);
            if (object == null) {
                return null;
            }
            ProductData productData = new ProductData();
            String decisionType = object.getString("decisionType");
            productData.setDestAdapter(decisionType);

            JSONArray downgradeList = object.getJSONArray("downgradeList");
            if (downgradeList != null) {
                ArrayList<String> arrayList = GsonProvider.getInstance().fromJson(downgradeList.toString(),
                        new TypeToken<ArrayList<String>>() {
                        }.getType());
                productData.setDowngradeList(arrayList);
            }
            return productData;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Keep
    public static class ProductData implements Serializable {
        private static final long serialVersionUID = -8491770053057948406L;
        private String decisionType;
        private List<String> downgradeList;

        public ProductData() {
        }

        public String getDestAdapter() {
            return decisionType;
        }

        public void setDestAdapter(String destAdapter) {
            this.decisionType = destAdapter;
        }

        public List<String> getDowngradeList() {
            return downgradeList == null ? new ArrayList<>() : downgradeList;
        }

        public void setDowngradeList(List<String> downgradeList) {
            this.downgradeList = downgradeList;
        }
    }
}
