package com.meituan.android.payrouter.remake.modules.load.data;

import android.os.Parcel;
import android.os.Parcelable;
import android.support.annotation.Keep;

/**
 * 用于给 Loader 模块使用的加载信息
 */
@Keep
public class LoadData implements Parcelable {
    public static final int LOAD_TYPE_ERROR = 0;
    public static final int LOAD_TYPE_ACTIVITY = 1;
    public static final int LOAD_TYPE_FRAGMENT = 2;

    private int loadType = LOAD_TYPE_ERROR;

    private final String trace;

    private final ActivityLoadData activityLoadData;

    private final FragmentLoadData fragmentLoadData;

    public LoadData(String trace) {
        this.trace = trace;
        activityLoadData = new ActivityLoadData();
        fragmentLoadData = new FragmentLoadData();
    }

    protected LoadData(Parcel in) {
        loadType = in.readInt();
        trace = in.readString();
        activityLoadData = in.readParcelable(ActivityLoadData.class.getClassLoader());
        fragmentLoadData = in.readParcelable(FragmentLoadData.class.getClassLoader());
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(loadType);
        dest.writeString(trace);
        dest.writeParcelable(activityLoadData, flags);
        dest.writeParcelable(fragmentLoadData, flags);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<LoadData> CREATOR = new Creator<LoadData>() {
        @Override
        public LoadData createFromParcel(Parcel in) {
            return new LoadData(in);
        }

        @Override
        public LoadData[] newArray(int size) {
            return new LoadData[size];
        }
    };

    public ActivityLoadData activity() {
        loadType = LOAD_TYPE_ACTIVITY;
        return activityLoadData != null ? activityLoadData : new ActivityLoadData();
    }

    public FragmentLoadData fragment() {
        loadType = LOAD_TYPE_FRAGMENT;
        return fragmentLoadData != null ? fragmentLoadData : new FragmentLoadData();
    }

    public int loadType() {
        return loadType;
    }

    public static boolean isValid(LoadData loadData) {
        return loadData != null && loadData.loadType() != LOAD_TYPE_ERROR;
    }

    public String trace() {
        return trace;
    }
}
