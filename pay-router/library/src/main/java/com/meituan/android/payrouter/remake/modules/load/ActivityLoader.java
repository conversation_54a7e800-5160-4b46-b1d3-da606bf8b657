package com.meituan.android.payrouter.remake.modules.load;

import android.content.Intent;
import android.net.Uri;

import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.payrouter.remake.base.OnActivityResult;
import com.meituan.android.payrouter.remake.modules.load.data.LoadData;
import com.meituan.android.payrouter.remake.router.context.RouterContext;

import java.util.Map;

public class ActivityLoader extends AbstractLoader implements OnActivityResult {
    public static final int REQUEST_CODE_ACTIVITY_LOADER = 0xAC00;
    public static final int LOAD_ACTIVITY_STATUS_READY = 0;
    public static final int LOAD_ACTIVITY_STATUS_START = 1;
    public static final int LOAD_ACTIVITY_STATUS_RESULT = -1;
    public static final String QUERY_KEY_ROUTER_TRACE = "router_trace";

    private int requestCode;

    public ActivityLoader(RouterContext context) {
        super(context);
    }

    @Override
    public void onLoad(LoadData loadData) {
        super.onLoad(loadData);
        Intent intent = loadData.activity().getIntent();
        // 合并公共参数
        appendQueryParams(intent, loadData.activity().getQueryParams());
        // 追加路由参数
        appendRouterLoaderInfo(intent);
        // 启动新页面
        this.requestCode = loadData.activity().getRequestCode();
        if (requestCode == 0) {
            requestCode = REQUEST_CODE_ACTIVITY_LOADER;
        }
        startActivityForResult(intent, requestCode);
    }

    private void appendQueryParams(Intent intent, Map<String, String> queryParams) {
        if (intent == null || intent.getData() == null || CollectionUtils.isEmpty(queryParams)) {
            return;
        }
        Uri uri = intent.getData();
        Uri.Builder builder = uri.buildUpon();
        for (Map.Entry<String, String> entry : queryParams.entrySet()) {
            builder.appendQueryParameter(entry.getKey(), entry.getValue());
        }
        intent.setData(builder.build());
    }

    private void appendRouterLoaderInfo(Intent intent) {
        if (intent != null) {
            intent.putExtra(QUERY_KEY_ROUTER_TRACE, routerContext.trace());
        }
    }

    private void startActivityForResult(Intent intent, int requestCode) {
        routerContext.getActivity().startActivityForResult(intent, requestCode);
    }

    @Override
    public boolean onActivityResult(int requestCode, int resultCode, Intent data) {
        return this.requestCode == requestCode;
    }
}
