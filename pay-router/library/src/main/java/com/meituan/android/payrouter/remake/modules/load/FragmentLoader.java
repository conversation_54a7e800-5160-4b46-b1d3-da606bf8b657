package com.meituan.android.payrouter.remake.modules.load;

import com.meituan.android.payrouter.remake.modules.load.data.LoadData;
import com.meituan.android.payrouter.remake.router.context.RouterContext;

public class FragmentLoader extends AbstractLoader {

    public FragmentLoader(RouterContext context) {
        super(context);
    }

    @Override
    public void onLoad(LoadData loadData) {
        super.onLoad(loadData);
    }

}
