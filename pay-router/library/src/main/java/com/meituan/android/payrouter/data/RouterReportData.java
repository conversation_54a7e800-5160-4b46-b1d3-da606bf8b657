package com.meituan.android.payrouter.data;


import android.support.annotation.Keep;

import java.io.Serializable;
@Keep
public class RouterReportData implements Serializable {

    private static final long serialVersionUID = -5416920742383654601L;
    private long routerStartTimeStamp;
    private long decisionStartTimeStamp;
    private long downgradeStartTimeStamp;
    private long loadStartTimeStamp;
    private int loadCount = 0;

    public int getLoadCount() {
        return loadCount;
    }

    public int incrementLoadCount() {
        this.loadCount++;
        return loadCount;
    }

    public long getRouterStartTimeStamp() {
        return routerStartTimeStamp;
    }

    public void setRouterStartTimeStamp(long routerStartTimeStamp) {
        this.routerStartTimeStamp = routerStartTimeStamp;
    }

    public long getDecisionStartTimeStamp() {
        return decisionStartTimeStamp;
    }

    public void setDecisionStartTimeStamp(long decisionStartTimeStamp) {
        this.decisionStartTimeStamp = decisionStartTimeStamp;
    }

    public long getDowngradeStartTimeStamp() {
        return downgradeStartTimeStamp;
    }

    public void setDowngradeStartTimeStamp(long downgradeStartTimeStamp) {
        this.downgradeStartTimeStamp = downgradeStartTimeStamp;
    }

    public long getLoadStartTimeStamp() {
        return loadStartTimeStamp;
    }

    public void setLoadStartTimeStamp(long loadStartTimeStamp) {
        this.loadStartTimeStamp = loadStartTimeStamp;
    }

    public void resetLoadCount() {
        loadCount = 0;
    }
}
