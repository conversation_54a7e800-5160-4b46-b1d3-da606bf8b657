package com.meituan.android.payrouter.utils;

import android.text.TextUtils;

import java.util.Map;
import java.util.UUID;
import java.util.WeakHashMap;

public class Tracer {
    private static final String NULL_TRACE_ID = "0123456789";
    private static final Map<Object, String> traceIds = new WeakHashMap<>();

    public static String newId() {
        return UUID.randomUUID().toString();
    }

    public static String id(Object weakRef) {
        if (weakRef == null) {
            return NULL_TRACE_ID;
        }
        String traceId = traceIds.get(weakRef);
        if (traceId == null) {
            traceId = newId();
            traceIds.put(weakRef, traceId);
        }
        return traceId;
    }

    public static void mark(Object weakRef, String trace) {
        if (weakRef != null && !TextUtils.isEmpty(trace)) {
            traceIds.put(weakRef, trace);
        }
    }

}
