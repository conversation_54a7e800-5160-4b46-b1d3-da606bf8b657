package com.meituan.android.payrouter.remake.router.impl;

import android.content.Context;

import com.meituan.android.payrouter.remake.RouterEnv;
import com.meituan.android.payrouter.remake.router.Router;
import com.meituan.android.payrouter.remake.router.data.RouterData;

public class ErrorRouter extends Router {
    public static final String TRACE_ERROR_ROUTER = "*router*error*";

    private ErrorRouter(Context context, RouterData routerData) {
        super(context, routerData);
    }

    @Override
    public String trace() {
        return TRACE_ERROR_ROUTER;
    }

    public static ErrorRouter getInstance() {
        return new ErrorRouter(RouterEnv.getApplication(), null);
    }
}
