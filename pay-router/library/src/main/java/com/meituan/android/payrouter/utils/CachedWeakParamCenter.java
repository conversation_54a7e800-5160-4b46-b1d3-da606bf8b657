package com.meituan.android.payrouter.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.WeakHashMap;

/**
 * <AUTHOR>
 */
public class CachedWeakParamCenter<K, V> extends CachedParamCenter<K, V> {
    private static final Map<Class<?>, CachedWeakParamCenter<?, ?>> globalCachedParams = new HashMap<>();

    protected CachedWeakParamCenter(Class<V> verifyClz, Initialization<K, V> initialization) {
        super(verifyClz, initialization);
    }

    @Override
    protected Map<K, V> newCachedParams() {
        return new WeakHashMap<>();
    }

    public static <K, V> CachedWeakParamCenter<K, V> withClz(Class<V> clz, Initialization<K, V> initialization) {
        //noinspection unchecked
        CachedWeakParamCenter<K, V> cachedParams = (CachedWeakParamCenter<K, V>) globalCachedParams.get(clz);
        if (cachedParams == null) {
            cachedParams = new CachedWeakParamCenter<>(clz, initialization);
            globalCachedParams.put(clz, cachedParams);
        }
        return cachedParams;
    }

    public static <K, V> CachedWeakParamCenter<K, V> with(V obj, Initialization<K, V> initialization) {
        //noinspection unchecked
        Class<V> clz = obj != null ? (Class<V>) obj.getClass() : null;
        return withClz(clz, initialization);
    }
}
