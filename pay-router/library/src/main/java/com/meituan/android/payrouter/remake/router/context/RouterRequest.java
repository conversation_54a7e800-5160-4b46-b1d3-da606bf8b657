package com.meituan.android.payrouter.remake.router.context;

import com.meituan.android.payrouter.remake.modules.decision.data.DowngradeData;
import com.meituan.android.payrouter.remake.modules.load.data.LoadData;
import com.meituan.android.payrouter.remake.result.RouterResult;

/**
 * 直接调用路由的流程，主要有3种行为：
 * 1. 加载，由于无法确定加载的准确时机
 * 2. 降级，业务可以在自己认为需要降级的时机进行降级（如果无法终止当前流程，需要进行降级）
 * 3. 业务结果，在适当的时候，可以直接返回业务结果，终止当前流程。
 */
public interface RouterRequest {
    void load(LoadData data);

    void downgrade(DowngradeData data);

    void finish(RouterResult result);
}
