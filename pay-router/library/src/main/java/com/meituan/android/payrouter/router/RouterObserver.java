package com.meituan.android.payrouter.router;

import com.meituan.android.paybase.payrouter.callback.RouterProxyCallback;
import com.meituan.android.payrouter.load.RouterLoadResultData;

import java.util.Map;

/**
 * author:  likang
 * date:    3/30/23
 * description: 路由行为观察者，主要观察如下的流程：
 * 1. onLoadCallback 加载流程的通知
 *
 *
 * 目前Loader、 Adapter 需要观察路由的行为
 */
public interface RouterObserver extends RouterProxyCallback {

    /**
     *
     */
    void onLoadCallback(RouterLoadResultData resultData);
}
