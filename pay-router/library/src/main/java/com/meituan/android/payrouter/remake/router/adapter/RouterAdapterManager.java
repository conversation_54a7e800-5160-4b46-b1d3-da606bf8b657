package com.meituan.android.payrouter.remake.router.adapter;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;

import com.meituan.android.paybase.utils.MapUtils;
import com.meituan.android.payrouter.remake.base.OnLoadFinished;
import com.meituan.android.payrouter.remake.base.Restorable;
import com.meituan.android.payrouter.remake.base.Traceable;
import com.meituan.android.payrouter.remake.manager.RouterObservable;
import com.meituan.android.payrouter.remake.manager.RouterObservableManager;
import com.meituan.android.payrouter.remake.modules.decision.data.DecisionResult;
import com.meituan.android.payrouter.remake.router.context.AdapterContext;
import com.meituan.android.payrouter.remake.router.context.RouterContext;
import com.meituan.android.payrouter.remake.router.context.RouterRequest;
import com.meituan.android.payrouter.remake.router.data.CheckResult;
import com.meituan.android.payrouter.remake.router.data.InvokeInfo;
import com.meituan.android.payrouter.remake.router.data.RouterData;
import com.meituan.android.payrouter.remake.router.manager.RouterImplManager;
import com.meituan.android.payrouter.utils.report.RouterReporter;
import com.meituan.android.payrouter.utils.save.Save;
import com.meituan.android.payrouter.utils.save.SaveInstanceHelper;

/**
 * 用于管理RouterAdapter。
 * 业务层可以继承该类用于一些自定义的Adapter管理方式
 */
public class RouterAdapterManager implements Traceable, Restorable, AdapterContext, OnLoadFinished {
    private static final String TAG_ADAPTER_OBSERVER = "**routerAdapter**";
    private final RouterContext routerContext;
    private final Bundle businessData;

    /**
     * 加载成功（ = 渲染成功）次数，在加载成功时+1
     */
    private int renderCount = 1;

    public static final String KEY_RENDER_COUNT = "renderCount";

    @Save
    private DecisionResult decisionResult;
    private String lastAdapterType = "";
    private AbstractRouterAdapter curRouterAdapter;

    public RouterAdapterManager(RouterContext context, RouterData data) {
        this.routerContext = context;
        this.businessData = data.businessData();
        this.routerContext.observable(OnLoadFinished.class).subscribe(this);
    }

    public void onCreate(Bundle savedInstanceState) {
        SaveInstanceHelper.restore(savedInstanceState, this, getClass());
        restore();
    }

    @Override
    public void onSaveInstanceState(Bundle state) {
        SaveInstanceHelper.save(state, this, getClass());
    }

    public void onDestroy() {
        performAdapterOnDestroy();
    }

    public boolean isDecisionResultValid(DecisionResult result) {
        this.decisionResult = result;
        CheckResult checkResult = newAdapterAndCheck(result.getDestAdapterType());
        if (!checkResult.isValid()) {
            RouterReporter.reporter().reportError(trace(), MapUtils.builder()
                    .add("errorCode", checkResult.getErrorCode())
                    .add("errorMessage", checkResult.getErrorMessage()).build());
            return false;
        }
        return true;
    }

    /**
     * 在没有找到下一个可以替换的Adapter时，不销毁之前的Adapter。
     * @param adapterType 适配器类型
     * @return 检查结果
     */
    private CheckResult newAdapterAndCheck(String adapterType) {
        if (!RouterImplManager.isValidRouterAdapter(adapterType)) {
            return CheckResult.fail("001", "adapterType is invalid");
        }
        AbstractRouterAdapter routerAdapter = RouterImplManager.loadRouterAdapter(adapterType);
        if (routerAdapter == null) {
            return CheckResult.fail("002", "routerAdapter init fail");
        }
        // 如果当前存在可用的Adapter，那么执行销毁逻辑
        performAdapterOnDestroy();
        // 使用新的Adapter
        curRouterAdapter = routerAdapter;
        // 指定初始化逻辑
        performAdapterOnCreate();
        return curRouterAdapter.check();
    }

    public void invoke() {
        if (curRouterAdapter != null) {
            InvokeInfo invokeInfo = new InvokeInfo();
            invokeInfo.setDowngradeFrom(lastAdapterType);
            lastAdapterType = decisionResult.getDestAdapterType();

            curRouterAdapter.invoke(invokeInfo);
            RouterReporter.reporter().increaseLoadTimes(trace());
            RouterReporter.reporter().loadStart(trace(), decisionResult);
        }
    }

    public void restore() {
        if (curRouterAdapter == null && decisionResult != null) {
            String adapterType = decisionResult.getDestAdapterType();
            if (!RouterImplManager.isValidRouterAdapter(adapterType)) {
                request().downgrade(decisionResult);
                return;
            }
            AbstractRouterAdapter routerAdapter = RouterImplManager.loadRouterAdapter(adapterType);
            if (routerAdapter == null) {
                request().downgrade(decisionResult);
                return;
            }
            // 如果当前存在可用的Adapter，那么执行销毁逻辑
            performAdapterOnDestroy();
            // 使用新的Adapter
            curRouterAdapter = routerAdapter;
            // 指定初始化逻辑
            performAdapterOnCreate();
            curRouterAdapter.restore();
        }
    }

    @Override
    public void onLoadFinished(boolean success) {
        RouterReporter.reporter().getExtras(trace()).put(KEY_RENDER_COUNT, renderCount++);
        RouterReporter.reporter().loadEnd(trace(), decisionResult, success);
    }

    private void performAdapterOnCreate() {
        if (curRouterAdapter != null) {
            curRouterAdapter.init(this, businessData);
            curRouterAdapter.onCreate(null);
        }
    }

    private void performAdapterOnDestroy() {
        if (curRouterAdapter != null) {
            curRouterAdapter.onDestroy();
            curRouterAdapter = null;
            RouterObservableManager.with(routerContext).unsubscribe(TAG_ADAPTER_OBSERVER);
        }
    }

    @Override
    public AbstractRouterAdapter adapter() {
        return curRouterAdapter;
    }

    @Override
    public RouterRequest request() {
        return routerContext.request();
    }

    /**
     * adapter 生命周期范围内使用，如果生命周期超过 adapter 的生命周期，比如decision、adapterManager等
     */
    @Override
    public <T> RouterObservable<T> observable(Class<T> clz) {
        return routerContext.observable(clz).proxy(TAG_ADAPTER_OBSERVER);
    }

    @Override
    public Activity getActivity() {
        return routerContext.getActivity();
    }

    @Override
    public Context getContext() {
        return routerContext.getContext();
    }

    @Override
    public String trace() {
        return routerContext.trace();
    }

    protected RouterContext routerContext() {
        return routerContext;
    }
}
