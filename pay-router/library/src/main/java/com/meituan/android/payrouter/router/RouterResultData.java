package com.meituan.android.payrouter.router;

import android.content.Intent;
import android.support.annotation.Keep;

import java.io.Serializable;
import java.util.HashMap;
@Keep
public class RouterResultData implements Serializable {
    private static final long serialVersionUID = 8053413250724664849L;
    int code;
    HashMap<String, Serializable> data = new HashMap<>();
    Intent intent;

    public RouterResultData(int code, HashMap<String, Serializable> resultData, Intent intent) {
        this.code = code;
        this.data = resultData;
        this.intent = intent;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public HashMap<String, Serializable> getData() {
        return data;
    }

    public void setData(HashMap<String, Serializable> data) {
        this.data = data;
    }

    public Intent getIntent() {
        return intent;
    }

    public void setIntent(Intent intent) {
        this.intent = intent;
    }
}
