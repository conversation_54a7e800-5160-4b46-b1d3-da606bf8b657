package com.meituan.android.payrouter.decision;


import android.support.annotation.Keep;

import com.meituan.android.paybase.payrouter.constants.RouterAdapterConstants;

import java.io.Serializable;

/**
 * 决策结果数据结构
 */
@Keep
public class RouterDecisionResultData implements Serializable {

    private static final long serialVersionUID = 7335770257927081037L;
    private  String msg;

    //决策的适配器类型
    @RouterAdapterConstants.AdapterType
    String destAdapterType;

    //决策的产品类型
    String productType;

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    DecisionResultCode resultCode;

    public RouterDecisionResultData(String destAdapterType, DecisionResultCode resultCode) {
        this.destAdapterType = destAdapterType;
        this.resultCode =resultCode;
    }

    public RouterDecisionResultData(String destAdapterType, DecisionResultCode resultCode, String msg) {
        this.destAdapterType = destAdapterType;
        this.resultCode = resultCode;
        this.msg = msg;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getDestAdapterType() {
        return destAdapterType;
    }

    public void setDestAdapterType(String destAdapterType) {
        this.destAdapterType = destAdapterType;
    }

    @Keep
    public enum DecisionResultCode implements Serializable {
        //决策成功
        ROUTER_DECISION_SUCCESS,
        //决策失败
        ROUTER_DECISION_FAIL,
    }

    public DecisionResultCode getResultCode() {
        return resultCode;
    }

    public boolean isDecisionSuccess() {
        return  resultCode == DecisionResultCode.ROUTER_DECISION_SUCCESS ;
    }
}
