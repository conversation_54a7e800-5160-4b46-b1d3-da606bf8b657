package com.meituan.android.payrouter.remake.router.adapter;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import com.meituan.android.payrouter.remake.base.OnActivityResult;
import com.meituan.android.payrouter.remake.base.OnLoadFinished;
import com.meituan.android.payrouter.remake.base.RouterRestore;
import com.meituan.android.payrouter.remake.base.Traceable;
import com.meituan.android.payrouter.remake.modules.decision.data.DowngradeData;
import com.meituan.android.payrouter.remake.modules.load.data.LoadData;
import com.meituan.android.payrouter.remake.result.RouterResult;
import com.meituan.android.payrouter.remake.router.context.AdapterContext;
import com.meituan.android.payrouter.remake.router.context.RouterRequest;
import com.meituan.android.payrouter.remake.router.data.CheckResult;
import com.meituan.android.payrouter.remake.router.data.InvokeInfo;
import com.meituan.android.payrouter.utils.save.SaveInstanceHelper;

/**
 * 具体业务需要实现的 adapter， 用于管理独立业务场景的业务逻辑
 */
public abstract class AbstractRouterAdapter implements Traceable, RouterRestore, RouterRequest, OnActivityResult, OnLoadFinished {
    private static final String ROUTER_ADAPTER_STATUS_CREATING = "creating";
    private static final String ROUTER_ADAPTER_STATUS_EXECUTING = "executing";
    private static final String ROUTER_ADAPTER_STATUS_LOADING = "loading";
    private static final String ROUTER_ADAPTER_STATUS_DOWNGRADING = "downgrading";
    private static final String ROUTER_ADAPTER_STATUS_RESULTING = "resulting";

    private AdapterContext adapterContext;
    private Bundle businessData;
    private String routerAdapterStatus = ROUTER_ADAPTER_STATUS_CREATING;

    public final void init(AdapterContext adapterContext, Bundle businessData) {
        this.adapterContext = adapterContext;
        this.businessData = businessData != null ? businessData : new Bundle();
        adapterContext.observable(OnActivityResult.class).subscribe(this);
        adapterContext.observable(OnLoadFinished.class).subscribe(this);
    }

    public void onCreate(Bundle savedInstanceState) {
    }

    public void onDestroy() {
    }

    /**
     * 业务层再进行判断是否可用，如果不可用，则会进行下一级决策/降级
     * 【注】check是同步方法，不可执行耗时操作，只用于进行判定。
     * @return true 可用，false 不可用
     */
    public CheckResult check() {
        return CheckResult.fail("000", "default error");
    }

    /**
     * 【通常】当check通过时，执行invoke方法。
     * 【配置决策】当check不通过时，会强制进入对应收银台
     * 【注】该方法中可以执行异步操作，如网络请求等，待需要进行页面加载时，调用{@link #load(LoadData)}
     */
    public void invoke(InvokeInfo info) {
        routerAdapterStatus = ROUTER_ADAPTER_STATUS_EXECUTING;
    }

    public void restore() {
    }

    @Override
    public void onLoadFinished(boolean success) {
    }

    @Override
    public void load(LoadData loadData) {
        adapterContext.request().load(loadData);
        routerAdapterStatus = ROUTER_ADAPTER_STATUS_LOADING;
    }

    @Override
    public void downgrade(DowngradeData downgradeData) {
        adapterContext.request().downgrade(downgradeData);
        routerAdapterStatus = ROUTER_ADAPTER_STATUS_DOWNGRADING;
    }

    @Override
    public void finish(RouterResult result) {
        adapterContext.request().finish(result);
        routerAdapterStatus = ROUTER_ADAPTER_STATUS_RESULTING;
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        SaveInstanceHelper.save(outState, this, getClass());
    }

    @Override
    public void onRestoreInstanceState(Bundle outState) {
        SaveInstanceHelper.restore(outState, this, getClass());
    }

    @Override
    public boolean onActivityResult(int requestCode, int resultCode, Intent data) {
        return false;
    }

    public Activity getActivity() {
        return adapterContext.getActivity();
    }

    public Bundle getBusinessData() {
        return businessData;
    }

    public AdapterContext adapterContext() {
        return adapterContext;
    }

    @Override
    public String trace() {
        return adapterContext.trace();
    }
}
