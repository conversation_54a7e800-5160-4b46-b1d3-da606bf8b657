package com.meituan.android.payrouter.decision.common;

import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;

import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.payrouter.RouterActivityProxy;
import com.meituan.android.paybase.payrouter.constants.RouterAdapterConstants;
import com.meituan.android.paybase.payrouter.constants.RouterDecisionConstant;
import com.meituan.android.paybase.payrouter.constants.RouterRegisterManager;
import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.paybase.utils.MTPayNeedToPersist;
import com.meituan.android.payrouter.adapter.PayRouterAdapterInterface;
import com.meituan.android.payrouter.data.RouterReportModule;
import com.meituan.android.payrouter.decision.DecisionCallback;
import com.meituan.android.payrouter.decision.RouterDecisionInterface;
import com.meituan.android.payrouter.decision.RouterDecisionResultData;
import com.meituan.android.payrouter.load.RouterDowngradeData;
import com.meituan.android.payrouter.router.RouterData;
import com.sankuai.meituan.serviceloader.ServiceLoader;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@ServiceLoaderInterface(key = RouterDecisionConstant.ROUTER_DECISION_TYPE_COMMON, interfaceClass = RouterDecisionInterface.class)
public class CommonDecisionModule extends RouterDecisionInterface {
    //保存已经降级过的元素，防止出现环
    @MTPayNeedToPersist
    private final Set<String> alreadyDowngradeList = new HashSet<>();
    //业务方返回的降级列表
    @MTPayNeedToPersist
    private List<String> downgradeList;
    //业务方传入的决策数据
    private CommonDecideData decideInData;
    @MTPayNeedToPersist
    private String currentAdapterType;
    @MTPayNeedToPersist
    private boolean isGetData = false;
    @MTPayNeedToPersist
    private String productType;

    private RouterData routerData;
    private DecisionCallback decideCallback;
    private CommonDecideDataInterface decisionDataModule = null;
    private final Handler handler = new Handler();

    private final CommonDecideDataCallback callback = (isSuccess, decideData) -> {
        isGetData = true;
        executeDecision(isSuccess, decideData);
    };

    private void executeDecision(boolean isSuccess, CommonDecideData decideData) {
        if (isDecisionDataIllegal(decideData) || !isSuccess) {
            decisionFail("decideData is illegal by decision");
            return;
        }
        decideInData = decideData;
        this.productType = decideInData.getProductType();
        String chooseAdapter = getDecisionAdapter(decideData.getProductType(), null);
        if (TextUtils.isEmpty(chooseAdapter)) {
            decisionFail("chooseAdapter is empty by decision");
        } else {
            decisionSuccess(chooseAdapter);
        }
    }

    @Override
    public void decide(@RouterAdapterConstants.AdapterType String currentAdapterType, RouterData data, DecisionCallback decisionCallback) {
        this.decideCallback = decisionCallback;
        this.routerData = data;
        this.currentAdapterType = currentAdapterType;
        if (!isDecisionDataIllegal(decideInData)) {
            executeDecision(true, decideInData);
            return;
        }
        decisionDataModule = getDecideDataModule(routerData.getRouterType());
        isGetData = false;
        if (decisionDataModule != null) {
            decisionDataModule.getBusinessData(data, callback, getRouterProxy());
        }

        //延时消息来防止获取decisionDataModule 模块卡住没有返回数据
        handler.postDelayed(() -> {
            if (isGetData) {
                return;
            }
            callback.getDataResult(false, null);
        }, 30000);
    }

    @Override
    public void downgrade(RouterDowngradeData downGradeData, RouterData data, DecisionCallback decisionCallback) {
        this.decideCallback = decisionCallback;
        this.routerData = data;
        if (isDecisionDataIllegal(this.decideInData)) {
            decisionFail("decideData is illegal  by downgrade");
            return;
        }
        String destProductType = downGradeData == null ? null : downGradeData.getDestProductType();
        String destAdapterType = downGradeData == null ? null : downGradeData.getDestAdapterType();
        if (!TextUtils.isEmpty(destProductType)) {
            this.productType = destProductType;
        }
        String chooseAdapter = getDecisionAdapter(destProductType, destAdapterType);
        if (TextUtils.isEmpty(chooseAdapter)) {
            decisionFail("chooseAdapter is empty  by downgrade");
        } else {
            //更新降级数据，表示这个adapter下次不再进入
            decisionSuccess(chooseAdapter);
        }
    }

    @Override
    public void onRestore(Bundle bundle, RouterActivityProxy routerActivityProxy, RouterData routerData, PayRouterAdapterInterface routerAdapter, DecisionCallback decisionCallback) {
        super.onRestore(bundle, routerActivityProxy, routerData, routerAdapter, decisionCallback);
        String routerDecideInData = bundle.getString("routerDecideInData");
        if (!TextUtils.isEmpty(routerDecideInData)) {
            decideInData = GsonProvider.getInstance().fromJson(routerDecideInData, CommonDecideData.class);
        }
        this.decideCallback = decisionCallback;
        this.routerData = routerData;
    }

    @Override
    public void onSaveState(Bundle bundle) {
        if (decideInData != null) {
            bundle.putString("routerDecideInData", GsonProvider.getInstance().toJson(decideInData));
        }
        super.onSaveState(bundle);
    }

    private void decisionSuccess(String chooseAdapter) {
        if (this.decideCallback == null) {
            return;
        }
        RouterDecisionResultData routerDecisionResultData = new RouterDecisionResultData(chooseAdapter, RouterDecisionResultData.DecisionResultCode.ROUTER_DECISION_SUCCESS);
        routerDecisionResultData.setProductType(productType);
        this.decideCallback.decisionResult(routerDecisionResultData);
    }

    private void decisionFail(String msg) {
        if (this.decideCallback == null) {
            reportCommonError(msg);
            return;
        }
        RouterDecisionResultData routerDecisionResultData = new RouterDecisionResultData(null, RouterDecisionResultData.DecisionResultCode.ROUTER_DECISION_FAIL, msg);
        routerDecisionResultData.setProductType(productType);
        this.decideCallback.decisionResult(routerDecisionResultData);
    }

    /**
     * 获取决策数据中合适的adapter，已经降级过的adapter保存在downgradeList 中，不再重复使用
     *
     * @param destProductType 指定要降级到哪个产品类型
     * @param destAdapterType 指定要降级到哪个适配器
     * @return 决策出的adapter
     */
    private String getDecisionAdapter(String destProductType, @RouterAdapterConstants.AdapterType String destAdapterType) {
        String dateInnerAdapter = null;
        String finalAdapter = null;
        //根据指定的产品类型，找到对应的adapter 和downgradeList
        if (!TextUtils.isEmpty(destProductType) && this.decideInData != null) {
            //找到这个产品对应的业务决策数据决策
            CommonDecideData.ProductData adapterData = this.decideInData.getProductData(destProductType);
            if (adapterData != null) {
                //更新降级列表
                downgradeList = adapterData.getDowngradeList();
                dateInnerAdapter = adapterData.getDestAdapter();
            }
        }

        //业务方降级里有指定的目标类型且没有加载过，则选择它
        if (!TextUtils.isEmpty(destAdapterType) && !alreadyDowngradeList.contains(destAdapterType)) {
            finalAdapter = destAdapterType;
        } else if (!TextUtils.isEmpty(dateInnerAdapter) && !alreadyDowngradeList.contains(dateInnerAdapter)) { //内部数据里有destAdapter且没有加载过，则选择它
            finalAdapter = dateInnerAdapter;
        } else {
            //什么都没指定，所以根据降级列表轮回
            if (downgradeList != null && downgradeList.size() > 0) {
                for (String adapter : downgradeList) {
                    if (!TextUtils.isEmpty(adapter) && !alreadyDowngradeList.contains(adapter)) {
                        finalAdapter = adapter;
                        break;
                    }
                }
            }
        }


        //finalAdapter 为空，则继续从降级列表中读取适配器,防止配置了一个无法进入的adapter
        if (TextUtils.isEmpty(finalAdapter) || !RouterAdapterConstants.contain(finalAdapter)) {
            if (downgradeList != null && downgradeList.size() > 0) {
                for (String adapter : downgradeList) {
                    if (!TextUtils.isEmpty(adapter) && !alreadyDowngradeList.contains(adapter)) {
                        finalAdapter = adapter;
                        break;
                    }
                }
            }
        }
        //更新降级数据，表示这个adapter下次不再进入
        this.alreadyDowngradeList.add(finalAdapter);
        this.currentAdapterType = finalAdapter;
        return finalAdapter;
    }

    /**
     * 校验业务方返回的决策数据
     *
     * @param decideData
     * @return true 标识校验不通过
     */
    private boolean isDecisionDataIllegal(CommonDecideData decideData) {
        //没有决策数据
        if (decideData == null) {
            return true;
        }
        //没有目标产品类型
        if (TextUtils.isEmpty(decideData.getProductType())) {
            return true;
        }
        this.productType = decideData.getProductType();
        //没有任何产品类型对应的适配器数据
        if (decideData.getAllData() == null) {
            return true;
        }
        CommonDecideData.ProductData adapterData = decideData.getProductData(decideData.getProductType());
        //目标产品类型没有 适配器数据
        return adapterData == null;
    }

    /**
     * 加载该业务对应的获取决策数据的模块
     *
     * @param routerType
     * @return
     */
    CommonDecideDataInterface getDecideDataModule(String routerType) {
        if (this.decisionDataModule != null) {
            return this.decisionDataModule;
        }
        String routerDecisionData = RouterRegisterManager.getRouterDecisionData(routerType);
        List<CommonDecideDataInterface> load = ServiceLoader.load(CommonDecideDataInterface.class, routerDecisionData);
        if (load == null || load.size() == 0 || load.get(0) == null) {
            decisionFail("CommonDecideDataInterface is null");
        } else {
            this.decisionDataModule = load.get(0);
        }
        return this.decisionDataModule;
    }

    private void reportCommonError(String errorMessage) {
        Map<String, Object> errorData = new AnalyseUtils.MapBuilder().add("errorMsg", errorMessage).build();
        RouterReportModule.routerCommonError(routerData, errorData);
    }
}
