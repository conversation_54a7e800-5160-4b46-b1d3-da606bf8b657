package com.meituan.android.payrouter.utils.bus;

import com.meituan.android.payrouter.remake.base.Restorable;

import java.util.Map;
import java.util.WeakHashMap;

/**
 * <AUTHOR>
 *
 * routerBus可以和其他routerBus通信的关键，routerBus的设计是需要进行信息保存的，当回调收不到信息时，需要将当前的信息进行保存。
 * 但是直接保存到storage中风险较大，业务信息也不建议直接使用storage。
 * routerBus最适合通过Activity的saveInstanceState方法进行数据保存，当信息没有消费者时，需要将信息保存在bundle中，当消费者重建时或重新链接时，就能够收到回调。
 * 通过RouterManager和其他RouterManager中的routerBus进行通信。
 *
 * 当通过Context调起时，没有存储信息的逻辑，则无法保存对应的信息。
 */

public class RouterBusManager implements RouterMessageHandler {
    private static final Map<Object, RouterBusImpl> busCenter = new WeakHashMap<>();

    public boolean onThrowMessage(RouterBus bus, Message message) {
        for (Map.Entry<Object, RouterBusImpl> entry : busCenter.entrySet()) {
            if (bus != entry.getValue() && entry.getValue().flushMessage(message)) {
                return true;
            }
        }
        return false;
    }

    private synchronized RouterBusImpl bus(Object traceable) {
        if (!busCenter.containsKey(traceable)) {
            busCenter.put(traceable, new RouterBusImpl(this));
        }
        return busCenter.get(traceable);
    }

    private RouterBusManager() {
    }

    private static class InnerSingleHolder {
        private static final RouterBusManager INSTANCE = new RouterBusManager();
    }

    public static RouterBusManager get() {
        return InnerSingleHolder.INSTANCE;
    }

    public static RouterBus getBus(Object traceable) {
        return get().bus(traceable);
    }

    public static Restorable getRestorable(Object traceable) {
        return get().bus(traceable);
    }
}
