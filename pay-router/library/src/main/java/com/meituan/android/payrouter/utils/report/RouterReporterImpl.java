package com.meituan.android.payrouter.utils.report;

import static com.meituan.android.paybase.payrouter.constants.RouterAdapterConstants.ROUTER_ADAPTER_HYBRID_STANDARD_CASHIER;
import static com.meituan.android.paybase.payrouter.constants.RouterAdapterConstants.ROUTER_ADAPTER_NATIVE_STANDARD_CASHIER;

import android.text.TextUtils;

import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.paybase.utils.StatisticsUtils;
import com.meituan.android.payrouter.remake.modules.decision.data.DecisionData;
import com.meituan.android.payrouter.remake.modules.decision.data.DecisionResult;
import com.meituan.android.payrouter.remake.modules.decision.data.DowngradeData;
import com.meituan.android.payrouter.remake.result.RouterResult;
import com.meituan.android.payrouter.remake.router.adapter.RouterAdapterManager;
import com.meituan.android.payrouter.remake.router.data.RouterData;
import com.meituan.android.payrouter.remake.router.manager.RouterManager;
import com.meituan.android.payrouter.utils.CachedParamCenter;

import java.util.HashMap;
import java.util.Map;

public class RouterReporterImpl extends RouterReporter {

    public static final String KEY_ROUTER_STATUS = "pay_router_status";
    public static final String KEY_ROUTER_TYPE = "routerType";
    public static final String KEY_DECISION_ORIGIN_ADAPTER_TYPE = "currentType";
    public static final String KEY_DECISION_ADAPTER_TYPE = "destType";
    public static final String KEY_DECISION_PRODUCT_TYPE = "productType";
    public static final String KEY_DECISION_ERROR_MESSAGE = "errorMessage";
    public static final String KEY_LOAD_TIMES = "loadTimes";
    public static final String KEY_ROUTER_START_TIME = "routerStartTime";
    public static final String KEY_DECISION_START_TIME = "decisionStartTime";
    public static final String KEY_DOWNGRADE_START_TIME = "downgradeStartTime";
    public static final String KEY_LOAD_START_TIME = "loadStartTime";
    public static final String KEY_ROUTER_END_SUCCESS_DATA = "successData";
    public static final String KEY_ROUTER_END_ERROR_DATA = "errorData";
    public static final String KEY_CODE = "code";
    public static final String KEY_DURATION = "duration";
    public static final String KEY_ROUTER_DURATION = "routerDuration";
    public static final String KEY_CASHIER_TYPE = "cashier_type";
    public static final String KEY_RENDER_COUNT = "render_count";
    public static final String KEY_START_TIME = "start_time";
    public static final String KEY_END_TIME = "end_time";


    public static final String VAL_UNKNOWN = "unknown";
    public static final String VAL_ROUTER_STATUS_START = "start";
    public static final String VAL_ROUTER_STATUS_SUCCESS = "success";
    public static final String VAL_ROUTER_STATUS_FAIL = "fail";

    public static final String COMMAND_ROUTER_START = "routerStart";
    public static final String COMMAND_ROUTER_END = "routerEnd";
    public static final String COMMAND_DECISION_START = "decisionStart";
    public static final String COMMAND_DECISION_END = "decisionEnd";
    public static final String COMMAND_DOWNGRADE_START = "downgradeStart";
    public static final String COMMAND_DOWNGRADE_END = "downgradeEnd";
    public static final String COMMAND_LOAD_START = "loadStart";
    public static final String COMMAND_LOAD_END = "loadEnd";
    public static final String COMMAND_ROUTER_COMMON_ERROR = "routerCommonError";

    public static final String BID_ROUTER_START_OR_END = "b_pay_238i0hz9_sc";
    public static final String BID_DECISION_START_OR_END = "b_pay_o1o6rio1_sc";
    public static final String BID_DOWNGRADE_START_OR_END = "b_pay_mxqo2awl_sc";
    public static final String BID_LOAD_START_OR_END = "b_pay_u8aaaw30_sc";
    public static final String BID_ROUTER_COMMON_ERROR = "b_pay_1hjjlbf1_sc";


    /** @noinspection rawtypes*/
    private final CachedParamCenter<String, Map> cachedExtras = CachedParamCenter.withClz(Map.class, key -> new HashMap<>());

    public Map<String, Object> getExtras(String trace) {
        //noinspection unchecked
        return cachedExtras.get(trace);
    }

    public void routerStart(String trace, RouterData routerData) {
        Map<String, Object> extras = getExtras(trace);
        extras.put(KEY_ROUTER_START_TIME, System.currentTimeMillis());
        report(RouterReportData.create()
                .setLxData(LXData.bid(BID_ROUTER_START_OR_END).build())
                .setRaptorData(RaptorData.command(COMMAND_ROUTER_START).build())
                .setExtra(KEY_ROUTER_STATUS, VAL_ROUTER_STATUS_START)
                .setExtra(KEY_ROUTER_TYPE, routerData != null ? routerData.getRouterType() : VAL_UNKNOWN)
                .setExtras(extras));
    }

    public void routerEnd(String trace, RouterData routerData, RouterResult result) {
        Map<String, Object> extras = getExtras(trace);
        String valRouterStatus = result != null && result.isSuccess() ? VAL_ROUTER_STATUS_SUCCESS : VAL_ROUTER_STATUS_FAIL;
        String keyRouterEndData = result != null && result.isSuccess() ? KEY_ROUTER_END_SUCCESS_DATA : KEY_ROUTER_END_ERROR_DATA;
        Object startTime = extras.get(KEY_ROUTER_START_TIME);
        long duration = startTime != null ? (System.currentTimeMillis() - (long) startTime) : -1;
        report(RouterReportData.create()
                .setLxData(LXData.bid(BID_ROUTER_START_OR_END).build())
                .setRaptorData(RaptorData.command(COMMAND_ROUTER_END).build())
                .setExtra(KEY_ROUTER_STATUS, valRouterStatus)
                .setExtra(KEY_ROUTER_TYPE, routerData != null ? routerData.getRouterType() : VAL_UNKNOWN)
                .setExtra(keyRouterEndData, result != null ? GsonProvider.getInstance().toJson(result.toReport()) : VAL_UNKNOWN)
                .setExtra(KEY_DURATION, duration)
                .setExtras(extras));
    }

    public void decisionStart(String trace, DecisionData decisionData) {
        Map<String, Object> extras = getExtras(trace);
        extras.put(KEY_DECISION_START_TIME, System.currentTimeMillis());
        report(RouterReportData.create()
                .setLxData(LXData.bid(BID_DECISION_START_OR_END).build())
                .setRaptorData(RaptorData.command(COMMAND_DECISION_START).build())
                .setExtra(KEY_ROUTER_STATUS, VAL_ROUTER_STATUS_START)
                .setExtra(KEY_ROUTER_TYPE, decisionData != null ? decisionData.routerType() : VAL_UNKNOWN)
                .setExtra(KEY_DECISION_ORIGIN_ADAPTER_TYPE, decisionData != null ? decisionData.getOriginProductType() : VAL_UNKNOWN)
                .setExtra(KEY_DECISION_PRODUCT_TYPE, decisionData != null ? decisionData.getDestProductType() : VAL_UNKNOWN)
                .setExtra(KEY_DECISION_ADAPTER_TYPE, decisionData != null ? decisionData.getDestAdapterType() : VAL_UNKNOWN)
                .setExtras(extras));
    }

    public void decisionEnd(String trace, DecisionData decisionData, DecisionResult result) {
        Map<String, Object> extras = getExtras(trace);
        String valRouterStatus = result != null && result.isSuccess() ? VAL_ROUTER_STATUS_SUCCESS : VAL_ROUTER_STATUS_FAIL;
        Object startTime = extras.get(KEY_DECISION_START_TIME);
        long duration = startTime != null ? (System.currentTimeMillis() - (long) startTime) : -1;
        report(RouterReportData.create()
                .setLxData(LXData.bid(BID_DECISION_START_OR_END).build())
                .setRaptorData(RaptorData.command(COMMAND_DECISION_END).build())
                .setExtra(KEY_ROUTER_STATUS, valRouterStatus)
                .setExtra(KEY_ROUTER_TYPE, decisionData != null ? decisionData.routerType() : VAL_UNKNOWN)
                .setExtra(KEY_DECISION_ORIGIN_ADAPTER_TYPE, decisionData != null ? decisionData.getOriginProductType() : VAL_UNKNOWN)
                .setExtra(KEY_DECISION_PRODUCT_TYPE, decisionData != null ? decisionData.getDestProductType() : VAL_UNKNOWN)
                .setExtra(KEY_DECISION_ADAPTER_TYPE, decisionData != null ? decisionData.getDestAdapterType() : VAL_UNKNOWN)
                .setExtra(KEY_DECISION_ERROR_MESSAGE, result != null ? result.getMessage() : VAL_UNKNOWN)
                .setExtra(KEY_DURATION, duration)
                .setExtras(extras));
    }

    public void downgradeStart(String trace, DecisionData decisionData, DowngradeData downgradeData) {
        Map<String, Object> extras = getExtras(trace);
        extras.put(KEY_DOWNGRADE_START_TIME, System.currentTimeMillis());
        report(RouterReportData.create()
                .setLxData(LXData.bid(BID_DOWNGRADE_START_OR_END).build())
                .setRaptorData(RaptorData.command(COMMAND_DOWNGRADE_START).build())
                .setExtra(KEY_ROUTER_STATUS, VAL_ROUTER_STATUS_START)
                .setExtra(KEY_ROUTER_TYPE, decisionData != null ? decisionData.routerType() : VAL_UNKNOWN)
                .setExtra(KEY_DECISION_PRODUCT_TYPE, downgradeData != null ? downgradeData.getDestProductType() : VAL_UNKNOWN)
                .setExtra(KEY_DECISION_ADAPTER_TYPE, downgradeData != null ? downgradeData.getDestAdapterType() : VAL_UNKNOWN)
                .setExtras(extras));
    }

    public void downgradeEnd(String trace, DecisionData decisionData, DowngradeData downgradeData, DecisionResult result) {
        Map<String, Object> extras = getExtras(trace);
        String valRouterStatus = result != null && result.isSuccess() ? VAL_ROUTER_STATUS_SUCCESS : VAL_ROUTER_STATUS_FAIL;
        Object startTime = extras.get(KEY_DOWNGRADE_START_TIME);
        long duration = startTime != null ? (System.currentTimeMillis() - (long) startTime) : -1;
        report(RouterReportData.create()
                .setLxData(LXData.bid(BID_DOWNGRADE_START_OR_END).build())
                .setRaptorData(RaptorData.command(COMMAND_DOWNGRADE_END).build())
                .setExtra(KEY_ROUTER_STATUS, valRouterStatus)
                .setExtra(KEY_ROUTER_TYPE, decisionData != null ? decisionData.routerType() : VAL_UNKNOWN)
                .setExtra(KEY_DECISION_ORIGIN_ADAPTER_TYPE, downgradeData != null ? downgradeData.getDestProductType() : VAL_UNKNOWN)
                .setExtra(KEY_DECISION_ADAPTER_TYPE, result != null ? result.getDestAdapterType() : VAL_UNKNOWN)
                .setExtra(KEY_DECISION_PRODUCT_TYPE, result != null ? result.getDestProductType() : VAL_UNKNOWN)
                .setExtra(KEY_DECISION_ERROR_MESSAGE, result != null ? result.getMessage() : VAL_UNKNOWN)
                .setExtra(KEY_DURATION, duration)
                .setExtras(extras));
    }

    public void loadStart(String trace, DecisionResult decisionResult) {
        Map<String, Object> extras = getExtras(trace);
        extras.put(KEY_LOAD_START_TIME, System.currentTimeMillis());
        String adapterType = decisionResult != null ? decisionResult.getDestAdapterType() : VAL_UNKNOWN;
        String productType = decisionResult != null ? decisionResult.getDestProductType() : VAL_UNKNOWN;
        report(RouterReportData.create()
                .setLxData(LXData.bid(BID_LOAD_START_OR_END).build())
                .setRaptorData(RaptorData.command(COMMAND_LOAD_START).build())
                .setExtra(KEY_ROUTER_STATUS, VAL_ROUTER_STATUS_START)
                .setExtra(KEY_ROUTER_TYPE, RouterManager.helper(trace).type())
                .setExtra(KEY_DECISION_ADAPTER_TYPE, adapterType)
                .setExtra(KEY_DECISION_PRODUCT_TYPE, productType)
                .setExtras(extras));
    }

    public void loadEnd(String trace, DecisionResult decisionResult, boolean success) {
        Map<String, Object> extras = getExtras(trace);
        String adapterType = decisionResult != null ? decisionResult.getDestAdapterType() : VAL_UNKNOWN;
        String productType = decisionResult != null ? decisionResult.getDestProductType() : VAL_UNKNOWN;
        String valRouterStatus = success ? VAL_ROUTER_STATUS_SUCCESS : VAL_ROUTER_STATUS_FAIL;
        int code = success ? 0 : 1;
        Object startTime = extras.get(KEY_LOAD_START_TIME);
        long duration = startTime != null ? (System.currentTimeMillis() - (long) startTime) : -1;
        Object routerStartTime = extras.get(KEY_ROUTER_START_TIME);
        long routerDuration = routerStartTime != null ? (System.currentTimeMillis() - (long) routerStartTime) : -1;
        report(RouterReportData.create()
                .setLxData(LXData.bid(BID_LOAD_START_OR_END).build())
                .setRaptorData(RaptorData.command(COMMAND_LOAD_END).build())
                .setExtra(KEY_ROUTER_STATUS, valRouterStatus)
                .setExtra(KEY_ROUTER_TYPE, RouterManager.helper(trace).type())
                .setExtra(KEY_DECISION_ADAPTER_TYPE, adapterType)
                .setExtra(KEY_DECISION_PRODUCT_TYPE, productType)
                .setExtra(KEY_CODE, code)
                .setExtra(KEY_DURATION, duration)
                .setExtra(KEY_ROUTER_DURATION, routerDuration)
                .setExtras(extras));

        reportCashierLoadFinished(adapterType, productType, extras);
    }

    /**
     * 体验指标完善需求 - 上报收银台唤起耗时相关参数
     * <a href="https://ocean.sankuai.com/config/#/demand/program/edit?flowId=48923&action=detail">...</a>
     *
     * @param adapterType 路由类型
     * @param productType 产品类型
     * @param extras 参数
     */
    public void reportCashierLoadFinished(String adapterType, String productType, Map<String, Object> extras) {
        HashMap<String, Object> cashierLoadFinishedMap = new HashMap<>();
        cashierLoadFinishedMap.put(KEY_CASHIER_TYPE, isStandardCashier(adapterType) ? adapterType : productType); // 收银台类型，完整枚举值详见文档 https://km.sankuai.com/collabpage/2283805784
        cashierLoadFinishedMap.put(KEY_RENDER_COUNT, extras.get(RouterAdapterManager.KEY_RENDER_COUNT)); // 支付组件渲染次数，从1开始
        cashierLoadFinishedMap.put(KEY_START_TIME, extras.get(KEY_ROUTER_START_TIME)); // 进入路由的13位时间戳
        cashierLoadFinishedMap.put(KEY_END_TIME, System.currentTimeMillis()); // 收银台成功渲染的13位时间戳，等于当前时间
        StatisticsUtils.reportModelEvent("c_PJmoK", "b_pay_6xrqdx16_mv", "收银台首页-收银台唤起耗时", cashierLoadFinishedMap,
                StatisticsUtils.EventType.VIEW, 0, StatisticsUtils.getBusinessKey(), true);
    }

    public boolean isStandardCashier(String adapterType) {
        return TextUtils.equals(adapterType, ROUTER_ADAPTER_HYBRID_STANDARD_CASHIER) || TextUtils.equals(adapterType, ROUTER_ADAPTER_NATIVE_STANDARD_CASHIER);
    }

    public void reportError(String trace, Map<String, Object> errorData) {
        Map<String, Object> extras = getExtras(trace);
        report(RouterReportData.create()
                .setLxData(LXData.bid(BID_ROUTER_COMMON_ERROR).build())
                .setRaptorData(RaptorData.command(COMMAND_ROUTER_COMMON_ERROR).build())
                .setExtra(KEY_ROUTER_TYPE, RouterManager.helper(trace).type())
                .setExtras(errorData)
                .setExtras(extras));
    }

    public void increaseLoadTimes(String trace) {
        Map<String, Object> extras = getExtras(trace);
        Integer loadTimes = (Integer) extras.get(KEY_LOAD_TIMES);
        if (loadTimes == null) {
            loadTimes = 1;
        }
        loadTimes++;
        extras.put(KEY_LOAD_TIMES, loadTimes);
    }

}
