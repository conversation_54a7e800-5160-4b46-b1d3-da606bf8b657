package com.meituan.android.payrouter.remake.modules.decision;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;

import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.payrouter.remake.base.OnDestroy;
import com.meituan.android.payrouter.remake.base.Restorable;
import com.meituan.android.payrouter.remake.base.Traceable;
import com.meituan.android.payrouter.remake.config.RouterDecisionConfigManager;
import com.meituan.android.payrouter.remake.router.data.RouterData;
import com.meituan.android.payrouter.remake.modules.decision.data.DecisionData;
import com.meituan.android.payrouter.remake.modules.decision.data.DecisionProductTypeData;
import com.meituan.android.payrouter.remake.modules.decision.data.DecisionResult;
import com.meituan.android.payrouter.remake.modules.decision.data.DowngradeData;
import com.meituan.android.payrouter.remake.router.context.RouterContext;
import com.meituan.android.payrouter.remake.router.manager.RouterImplManager;
import com.meituan.android.payrouter.remake.router.manager.RouterManager;
import com.meituan.android.payrouter.utils.report.RouterReporter;
import com.meituan.android.payrouter.utils.save.Save;
import com.meituan.android.payrouter.utils.save.SaveInstanceHelper;

import java.util.List;

/**
 * 抽象决策逻辑：
 * 中间状态保存在DecisionData中
 * 输出 DecisionResult，并通知 DecisionObserver
 * 逻辑：
 * 1）业务层调用业务决策的相关逻辑，如接口请求等，输出DecisionResult：
 * onStart -> notifyDecisionResultFromProductType -> notifyDecisionResult -> DecisionObserver
 * 2）路由层调用降级的相关逻辑，触发降级规则，输出DecisionResult：
 * downgrade -> notifyDecisionResult -> DecisionObserver
 * 3) 比较危险的逻辑操作是业务方传入了不符合预期的降级信息，再次降级又获取了不符合预期的降级信息，导致循环降级。
 * 需要设置最大降级次数，超过最大降级次数，则直接进入finalDowngrade
 */
public class RouterDecisionManager implements Traceable, Restorable, OnDestroy, DecisionProxy {
    private final RouterContext routerContext;

    private final Bundle businessData;
    @Save
    private String routerType;
    @Save
    private DecisionData decisionData;
    @Save
    private boolean hasFinalDowngrade;

    private final DecisionTemplate decisionTemplate;

    public RouterDecisionManager(RouterContext context, RouterData data) {
        this.routerContext = context;
        this.businessData = data.businessData();
        this.routerType = data.getRouterType();
        this.decisionTemplate = RouterImplManager.loadDecision(routerType);
        this.decisionData = new DecisionData(routerType, decisionTemplate.finalProductData());
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        SaveInstanceHelper.restore(savedInstanceState, this, getClass());
    }

    public void onStart() {
        decisionTemplate.onStart(businessData);
        DecisionResult result = decisionData.getCurrentDecisionResult();
        if (result == null) { // 如果数据中不存在已有的决策结果，则决策，否则不再进行决策，使用已有的决策逻辑
            decisionTemplate.onDecisionStart(this);
            RouterReporter.reporter().decisionStart(trace(), decisionData);
        }
    }

    @Override
    public void onSaveInstanceState(Bundle state) {
        SaveInstanceHelper.save(state, this, getClass());
    }

    @Override
    public void onDestroy() {
    }

    @Override
    public void updateProductData(String productData) {
        decisionData.setProductData(RouterDecisionConfigManager.parseConfigString(productData));
    }

    @Override
    public void notifyProductType(String productType) {
        decisionData.setOriginProductType(productType); // 记录最初的产品类型
        decisionData.setDestProductType(productType); // 记录当前的产品类型
        DecisionProductTypeData productData = decisionData.getProductTypeData(productType);
        DecisionResult result = productData != null ? DecisionResult.success(productType, productData.getDecisionType())
                : DecisionResult.fail("productData is null");

        RouterReporter.reporter().decisionEnd(trace(), decisionData, result);

        onDecisionResult(result);
    }

    @Override
    public void notifyDecisionError(String message) {

    }

    @Override
    public boolean isAvailableProductType(String productType) {
        return decisionData.isAvailableProductType(productType);
    }

    /**
     * 通知决策结果
     *
     * @param result 决策结果
     */
    private void onDecisionResult(DecisionResult result) {
        // 决策成功，则需要更新数据；决策失败，不会更新数据，但是会通知Observer(Router)
        if (DecisionResult.isValid(result)) {
            decisionData.addDowngradeTrace(result.getDestAdapterType(), decisionData.getDestAdapterType());
        }
        if (DecisionResult.isSuccess(result)) {
            // 记录降级数据
            decisionData.setDestProductType(result.getDestProductType());
            decisionData.setDestAdapterType(result.getDestAdapterType());
            decisionData.setCurrentDecisionResult(result);
            decisionTemplate.onDecisionResult(result);
        }
        routerContext.observable(DecisionObserver.class).dispatch().onDecisionResult(result);
    }

    // ********************降级相关逻辑********************
    public final void downgrade(DowngradeData downgradeData) {
        RouterReporter.reporter().downgradeStart(trace(), decisionData, downgradeData);
        DecisionResult decisionResult = executeDowngrade(downgradeData);
        RouterReporter.reporter().downgradeEnd(trace(), decisionData, downgradeData, decisionResult);
        onDecisionResult(decisionResult);
    }

    /**
     * 降级逻辑核心流程：
     * 1. 判断降级参数是否合法，不合法则会在当前的降级列表中查找，nextDowngrade();
     * 2. 判断目标降级的 AdapterType 是否合法：1）是否存在，2）是否已经降级。合法则直接返回，不合法则执行步骤3
     * 3. 判断目标降级的 ProductType 下的首个 AdapterType 是否合法：1）是否存在，2）是否已经降级。合法则直接返回，不合法则执行步骤4
     * 4. 兜底逻辑：根据降级列表进行循环查找, nextDowngrade();
     *
     * @param downgradeData 输入的降级信息，包括目标产品类型，目标Adapter类型等。
     * @return 决策结果，包括目标产品类型，目标Adapter类型等。
     */
    private DecisionResult executeDowngrade(DowngradeData downgradeData) {
        if (!DowngradeData.isValid(downgradeData)) { // 降级信息不完整，从当前的ProductType进行查找降级
            return DecisionResult.getWithDowngradeExtras(nextDowngrade(null), downgradeData);
        }
        String downgradeAdapterType = downgradeData.getDestAdapterType();
        String downgradeProductType = downgradeData.getDestProductType();
        // ProductType & AdapterType 均不为空，正常降级
        if (!TextUtils.isEmpty(downgradeAdapterType) && decisionData.hasNotDowngraded(downgradeAdapterType)) {
            DecisionResult result = DecisionResult.success(downgradeProductType, downgradeAdapterType, downgradeData.getDowngradeExtras());
            result.setFromBusiness(downgradeData.isFromBusiness());
            return result;
        }
        // 从传入的ProductType进行查找降级
        return DecisionResult.getWithDowngradeExtras(nextDowngrade(downgradeProductType), downgradeData);
    }

    /**
     * 降级逻辑：获取目标产品类型的下一个可用降级决策结果
     *
     * @param destProductType 目标产品类型
     * @return 下一个降级的决策结果
     */
    private DecisionResult nextDowngrade(String destProductType) {
        if (TextUtils.isEmpty(destProductType)) { // 兜底，如果传入的ProductType为空，则从当前记录的产品类型开始进行降级
            destProductType = decisionData.getDestProductType();
        }
        DecisionProductTypeData productData = decisionData.getProductTypeData(destProductType);
        if (productData == null) { // 如果没有任何可用的降级策略，那么进行最终的兜底的降级
            return executeFinalDowngrade();
        }
        List<String> downgradeList = productData.getDowngradeList();
        for (String downgradeType : downgradeList) {
            if (decisionData.hasNotDowngraded(downgradeType)) {
                return DecisionResult.success(destProductType, downgradeType);
            }
        }
        return executeFinalDowngrade();
    }

    private DecisionResult executeFinalDowngrade() {
        if (!hasFinalDowngrade) {
            hasFinalDowngrade = true;
            DecisionResult result = decisionTemplate.finalDowngrade();
            if (result != null) {
                return result;
            }
        }
        return DecisionResult.fatalError("final downgrade has execute");
    }

    @Override
    public RouterContext routerContext() {
        return routerContext;
    }

    @Override
    public String trace() {
        return routerContext.trace();
    }
}
