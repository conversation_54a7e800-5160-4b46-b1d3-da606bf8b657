package com.meituan.android.payrouter.remake.config;

import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.meituan.android.paybase.helper.ConfigHelper;
import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.payrouter.remake.modules.decision.data.DecisionProductTypeData;

import java.util.HashMap;
import java.util.Map;

public class RouterDecisionConfigManager {
    private static final Map<String, Map<String, DecisionProductTypeData>> defaultConfigMap = new HashMap<>();

    public static final String DECISION_TYPE_CASHIER = "finance_cashier";
    public static final Map<String, String> decisionMap = new HashMap<>();

    static {
        decisionMap.put(DECISION_TYPE_CASHIER, RouterConfigManager.ROUTER_CONFIG_CASHIER);
    }

    public static Map<String, DecisionProductTypeData> getDynamicConfig(String key) {
        // 需要做一层decisionType和configName的映射
        String configName = decisionMap.get(key);
        if (TextUtils.isEmpty(configName)) {
           return new HashMap<>();
        }
        return ConfigHelper.of(configName)
                .pubConfig(new TypeToken<Map<String, DecisionProductTypeData>>(){});
    }

    public static Map<String, DecisionProductTypeData> parseDefaultConfigString(String key, String config) {
        Map<String, DecisionProductTypeData> defaultDecisionData = defaultConfigMap.get(key);
        if (defaultDecisionData == null) {
            defaultDecisionData = parseConfigString(config);
            defaultConfigMap.put(key, defaultDecisionData);
        }
        return defaultDecisionData;
    }

    public static Map<String, DecisionProductTypeData> parseConfigString(String config) {
        try {
            return GsonProvider.getInstance().fromJson(config,
                    new TypeToken<HashMap<String, DecisionProductTypeData>>() {
                    }.getType());
        } catch (Exception e) {
            return new HashMap<>();
        }
    }
}
