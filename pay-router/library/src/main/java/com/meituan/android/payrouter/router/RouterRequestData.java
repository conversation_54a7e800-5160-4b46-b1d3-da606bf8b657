package com.meituan.android.payrouter.router;

import android.support.annotation.Keep;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Keep
public class RouterRequestData implements Serializable {
    private static final long serialVersionUID = 3864431384343342005L;
    private int requestCode;

    private Map<String, Serializable> businessInputData;   //业务方传入数据

    private Map<String, Object> adapterExtraData =new HashMap<>();   //业务方各个adapter之间共享的数据，需要业务方自己确认是否要进行覆盖

    public Map<String, Object> getAdapterExtraData() {
        return adapterExtraData;
    }

    public void setAdapterExtraData(Map<String, Object> adapterExtraData) {
        if (adapterExtraData ==null){
            return;
        }
        this.adapterExtraData.putAll(adapterExtraData);
    }

    public RouterRequestData(Map<String, Serializable> data) {
        this.businessInputData = data;
    }

    public RouterRequestData() {
    }

    public int getRequestCode() {
        return requestCode;
    }

    public void setRequestCode(int requestCode) {
        this.requestCode = requestCode;
    }

    public Map<String, Serializable> getBusinessData() {
        return businessInputData;
    }

    public void setBusinessData(Map<String, Serializable> businessData) {
        this.businessInputData = businessData;
    }
}
