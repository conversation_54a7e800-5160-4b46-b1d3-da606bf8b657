package com.meituan.android.payrouter.data;

import com.meituan.android.paybase.common.analyse.cat.CatUtils;
import com.meituan.android.paybase.payrouter.callback.RouterCallback;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.StatisticsUtils;
import com.meituan.android.payrouter.adapter.PayRouterAdapterInterface;
import com.meituan.android.payrouter.decision.RouterDecisionResultData;
import com.meituan.android.payrouter.load.RouterLoadResultData;
import com.meituan.android.payrouter.router.RouterData;
import com.meituan.android.payrouter.router.RouterRequestData;

import java.util.HashMap;
import java.util.Map;

public class RouterReportModule {
    private static final String routerDataState = "pay_router_status";
    private static final String duration = "duration";
    private static final String errorMessage = "errorMessage";
    private static final String currentType = "currentType";
    private static final String destType = "destType";
    private static final String productType = "productType";
    private static final String routerType = "routerType";
    private static final String loadTimes = "loadTimes";
    private static final String start = "start";
    private static final String success = "success";
    private static final String failure = "failure";

    static public void routerStart(RouterData data) {
        RouterData notNullData = getNotNullData(data);
        Map<String, Object> map = new HashMap<>(notNullData.getAnalysisData());
        notNullData.getRouterReportData().setRouterStartTimeStamp(System.currentTimeMillis());
        map.put(routerDataState, start);
        LoganUtils.log("路由模块", map, LoganUtils.TAGS_ROUTER);
        CatUtils.logCustom("routerStart", map, null, null);
    }


    static public void routerEnd(RouterData data, RouterCallback.Result result) {
        RouterData notNullData = getNotNullData(data);
        Map<String, Object> map = new HashMap<>(notNullData.getAnalysisData());
        map.put(routerDataState, success);
        map.put(duration, getDuration(data.getRouterReportData().getRouterStartTimeStamp()));
        map.put("successData", result.getResultData());
        LoganUtils.log("路由模块", map, LoganUtils.TAGS_ROUTER);
        CatUtils.logCustom("routerEnd", map, null, null);
    }

    static public void routerEndFail(RouterData data, Map errorMsg) {
        RouterData notNullData = getNotNullData(data);
        Map<String, Object> map = new HashMap<>(notNullData.getAnalysisData());
        map.put(routerDataState, failure);
        map.put(duration, getDuration(notNullData.getRouterReportData().getRouterStartTimeStamp()));
        map.put("errorData", errorMsg);
        LoganUtils.log("路由模块", map, LoganUtils.TAGS_ROUTER);
        CatUtils.logCustom("routerEndFail", map, null, null);
    }


    static public void decisionStart(RouterData data) {
        RouterData notNullData = getNotNullData(data);
        notNullData.getRouterReportData().setDecisionStartTimeStamp(System.currentTimeMillis());
        Map<String, Object> map = new HashMap<>(notNullData.getAnalysisData());
        map.put(routerDataState, start);
        map.put(currentType, notNullData.getCurrentAdapterType());
        map.put(productType, notNullData.getRouterDecisionResultData().getProductType());
        LoganUtils.log("路由决策", map, LoganUtils.TAGS_ROUTER);
        CatUtils.logCustom("decisionStart", map, null, null);
    }

    static public void decisionEnd(RouterData data) {
        RouterData notNullData = getNotNullData(data);
        Map<String, Object> map = new HashMap<>(notNullData.getAnalysisData());
        map.put(routerDataState, success);
        map.put(currentType, notNullData.getCurrentAdapterType());
        map.put(destType, notNullData.getRouterDecisionResultData().getDestAdapterType());
        map.put(productType, notNullData.getRouterDecisionResultData().getProductType());
        map.put(duration, getDuration(notNullData.getRouterReportData().getDecisionStartTimeStamp()));
        LoganUtils.log("路由决策", map, LoganUtils.TAGS_ROUTER);
        CatUtils.logCustom("decisionEnd", map, null, null);
    }

    static public void decisionEndFail(RouterData data, String message) {
        RouterData notNullData = getNotNullData(data);
        Map<String, Object> map = new HashMap<>(notNullData.getAnalysisData());
        map.put(errorMessage, message);
        map.put(routerDataState, failure);
        map.put(currentType, notNullData.getCurrentAdapterType());
        map.put(destType, notNullData.getRouterDecisionResultData().getDestAdapterType());
        map.put(productType, notNullData.getRouterDecisionResultData().getProductType());
        map.put(duration, getDuration(notNullData.getRouterReportData().getDecisionStartTimeStamp()));
        LoganUtils.log("路由决策", map, LoganUtils.TAGS_ROUTER);
        CatUtils.logCustom("decisionEndFail", map, null, null);
    }

    static public void downgradeStart(RouterData data) {
        RouterData notNullData = getNotNullData(data);
        notNullData.getRouterReportData().setDowngradeStartTimeStamp(System.currentTimeMillis());
        Map<String, Object> map = new HashMap<>(notNullData.getAnalysisData());
        map.put(routerDataState, start);
        map.put(productType, notNullData.getRouterDecisionResultData().getProductType());
        map.put(currentType, notNullData.getCurrentAdapterType());
        ReportSc("b_pay_mxqo2awl_sc", map, notNullData);
        CatUtils.logCustom("downgradeStart", map, null, null);
    }

    static public void downgradeEnd(RouterData data) {
        RouterData notNullData = getNotNullData(data);
        Map<String, Object> map = new HashMap<>(notNullData.getAnalysisData());
        map.put(currentType, notNullData.getCurrentAdapterType());
        map.put(destType, notNullData.getRouterDecisionResultData().getDestAdapterType());
        map.put(productType, notNullData.getRouterDecisionResultData().getProductType());
        map.put(duration, getDuration(notNullData.getRouterReportData().getDowngradeStartTimeStamp()));
        map.put(routerDataState, success);
        ReportSc("b_pay_mxqo2awl_sc", map, notNullData);
        CatUtils.logCustom("downgradeEnd", map, null, null);
    }

    static public void downgradeEndFail(RouterData data, String message) {
        RouterData notNullData = getNotNullData(data);
        Map<String, Object> map = new HashMap<>(notNullData.getAnalysisData());
        map.put(currentType, notNullData.getCurrentAdapterType());
        map.put(destType, notNullData.getRouterDecisionResultData().getDestAdapterType());
        map.put(errorMessage, message);
        map.put(routerDataState, failure);
        map.put(productType, notNullData.getRouterDecisionResultData().getProductType());
        map.put(duration, getDuration(notNullData.getRouterReportData().getDowngradeStartTimeStamp()));
        ReportSc("b_pay_mxqo2awl_sc", map, notNullData);
        CatUtils.logCustom("downgradeEndFail", map, null, null);
    }

    static public void loadStart(RouterData data) {
        RouterData notNullData = getNotNullData(data);
        notNullData.getRouterReportData().resetLoadCount();
        notNullData.getRouterReportData().setLoadStartTimeStamp(System.currentTimeMillis());
        Map<String, Object> map = new HashMap<>(notNullData.getAnalysisData());
        map.put(routerDataState, start);
        map.put(currentType, notNullData.getCurrentAdapterType());
        map.put(productType, notNullData.getRouterDecisionResultData().getProductType());
        LoganUtils.log("路由加载", map, LoganUtils.TAGS_ROUTER);
        CatUtils.logCustom("loadStart", map, null, null);
    }

    static public void loadEnd(RouterData data, RouterLoadResultData loadResultData) {
        RouterData notNullData = getNotNullData(data);
        //多次load，记录load次数，第一次作为抵达率终点
        Map<String, Object> map = new HashMap<>(notNullData.getAnalysisData());
        map.put("code", revertCode(loadResultData.getResultCode()));
        map.put(routerDataState, "end");
        map.put(currentType, notNullData.getCurrentAdapterType());
        map.put(loadTimes, notNullData.getRouterReportData().incrementLoadCount());
        map.put("routerDuration", getDuration(notNullData.getRouterReportData().getLoadStartTimeStamp()));
        map.put(duration, getDuration(notNullData.getRouterReportData().getRouterStartTimeStamp()));
        map.put(productType, notNullData.getRouterDecisionResultData().getProductType());
        LoganUtils.log("路由加载", map, LoganUtils.TAGS_ROUTER);
        CatUtils.logCustom("loadEnd", map, null, null);
    }

    private static int revertCode(RouterLoadResultData.LoadResultCode code) {
        switch (code) {
            case ROUTER_LOAD_SUCCESS:
                return 0;
            case ROUTER_LOAD_FAIL:
                return 1;
            case ROUTER_LOAD_DOWNGRADE:
                return 2;
        }
        return -1;
    }

    static public void routerCommonError(RouterData data, Map<String, Object> errorData) {
        RouterData notNullData = getNotNullData(data);
        ReportSc("b_pay_1hjjlbf1_sc", errorData, notNullData);
        CatUtils.logCustom("routerCommonError", errorData, null, null);
    }

    static private void ReportSc(String bid, Map<String, Object> map, RouterData notNullData) {
        map.put(routerType, notNullData.getRouterType());
        StatisticsUtils.reportSystemCheck(null, bid, map, StatisticsUtils.CID_DEFAULT, "com.meituan.android.cashier.common.CashierStaticsUtils", true);
    }


    private static RouterData getNotNullData(RouterData data) {
        if (data == null) {
            data = new RouterData(null, new RouterRequestData());
        }
        if (data.getAnalysisData() == null) {
            data.setAnalysisData(new HashMap<>());
        }
        if (data.getRouterRequestData() == null) {
            data.setRouterRequestData(new RouterRequestData());
        }
        if (data.getRouterDecisionResultData() == null) {
            data.setRouterDecisionResultData(new RouterDecisionResultData(null, RouterDecisionResultData.DecisionResultCode.ROUTER_DECISION_FAIL, "decisionResult is null"));
        }
        if (data.getRouterReportData() == null) {
            data.setRouterReportData(new RouterReportData());
        }
        return data;
    }

    private static long getDuration(long time) {
        if (time <= 0) {
            return -1;
        }
        return System.currentTimeMillis() - time;
    }

    public static void adapterConsumeResult(RouterData data, PayRouterAdapterInterface.ConsumeResult result) {
        RouterData notNullData = getNotNullData(data);
        notNullData.getRouterReportData().setLoadStartTimeStamp(System.currentTimeMillis());
        Map<String, Object> map = new HashMap<>(notNullData.getAnalysisData());
        map.put(routerDataState, start);
        map.put("isConsume", result.isConsumed());
        map.put("message", result.getMessage());
        map.put(currentType, notNullData.getCurrentAdapterType());
        ReportSc("b_pay_1hjjlbf1_sc", map, notNullData);
        CatUtils.logCustom("adapterConsume", map, null, null);
    }
}
