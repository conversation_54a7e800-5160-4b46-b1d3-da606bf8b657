package com.meituan.android.payrouter.utils.report;

import com.meituan.android.paybase.common.analyse.cat.CatUtils;
import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.paybase.utils.StatisticsUtils;
import com.meituan.android.payrouter.remake.RouterEnv;

public class RouterReporter {
    private static RouterReporterImpl routerReporter;

    public static RouterReporterImpl reporter() {
        if (routerReporter == null) {
            routerReporter = new RouterReporterImpl();
        }
        return routerReporter;
    }

    public void report(RouterReportData reportData) {
        if (reportData == null) {
            return;
        }
        LXData lxData = reportData.getLxData();
        if (lxData != null) {
            StatisticsUtils.reportSystemCheck(
                    lxData.getPageInfoKey(),
                    lxData.getBid(),
                    lxData.getValLab(),
                    lxData.getCid(),
                    lxData.getBusinessKey(),
                    lxData.isMis());
        }
        RaptorData raptorData = reportData.getRaptorData();
        if (raptorData != null) {
            CatUtils.logCustom(raptorData.getCommand(), raptorData.getCustom(), null, null);
        }
        log(reportData);
    }

    public void log(RouterReportData reportData) {
        if (reportData == null || !RouterEnv.isDebug()) {
            return;
        }
        LXData lxData = reportData.getLxData();
        if (lxData != null) {
            android.util.Log.d("pay.router", GsonProvider.getInstance().toJson(lxData));
        }
    }
}
