package com.meituan.android.payrouter.remake.modules.load;

import android.os.Bundle;

import com.meituan.android.payrouter.remake.base.OnDestroy;
import com.meituan.android.payrouter.remake.base.Restorable;
import com.meituan.android.payrouter.remake.modules.load.data.LoadData;
import com.meituan.android.payrouter.remake.modules.load.data.LoadResult;
import com.meituan.android.payrouter.remake.router.context.RouterContext;

/**
 *  加载器抽象类，理论上Loader负责启动新页面/新组件,则新页面/新组件会直接和Loader负责交互，
 *  一次Load过程中，最多会有2次通信：
 *  1. 加载失败/降级，不会触发后续的逻辑，同时Loader会被销毁。
 *  2. 加载成功，Loader对象不销毁。如果再次触发降级，则Loader对象销毁，重新决策。如果业务结束，则Loader对象销毁。
 *  一次Load过程的抵达率 = 第一次通信为成功数 / Load 起点。
 */
public abstract class AbstractLoader implements Restorable, OnDestroy {
    protected final RouterContext routerContext;

    public AbstractLoader(RouterContext context) {
        this.routerContext = context;
    }

    public void onCreate(Bundle savedInstanceState) {
    }

    @Override
    public void onSaveInstanceState(Bundle state) {
    }

    @Override
    public void onDestroy() {
    }

    public void onLoad(LoadData loadData) {
    }

    public void onResult(LoadResult result) {
    }
}
