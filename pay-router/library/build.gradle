apply plugin: 'com.android.library'

buildscript {
    def gradle_version = '7.2.2'
    def kotlin_version = '1.3.61'
    repositories {
        maven {
            url "http://mvn.dianpingoa.com/android-nova"
            allowInsecureProtocol = true
        }
        maven {
            url "http://mvn.dianpingoa.com/android-nova-snapshot"
            allowInsecureProtocol = true
        }
        maven {
            url 'http://depot.sankuai.com/nexus/content/groups/public/'
            allowInsecureProtocol = true
        }
    }

    dependencies {
        classpath "com.android.tools.build:gradle:${gradle_version}"
        classpath 'com.github.dcendents:android-maven-gradle-plugin:1.4.1'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:${kotlin_version}"
    }
}

android {
    compileSdkVersion Integer.parseInt(project.ANDROID_BUILD_SDK_VERSION)
    buildToolsVersion project.ANDROID_BUILD_TOOLS_VERSION
    defaultConfig {
        minSdkVersion Integer.parseInt(project.ANDROID_BUILD_MIN_SDK_VERSION)
        targetSdkVersion Integer.parseInt(project.ANDROID_BUILD_TARGET_SDK_VERSION)
        versionCode Integer.parseInt(project.VERSION_CODE)
        versionName project.VERSION_NAME

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

configurations {
    developCompile {}
    all*.exclude group: 'com.squareup.okhttp', module: 'okhttp'
}

configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    resolutionStrategy.eachDependency { DependencyResolveDetails details ->
        def requested = details.requested
        if (requested.group == 'com.android.support') {
            if (!requested.name.startsWith("multidex")) {
                details.useVersion '26.0.2'
            }
        }
    }
}

repositories {
    def useNewGradle = gradle.gradleVersion >= '7.0.0'
    maven {
        url "http://mvn.dianpingoa.com/android-nova"
        allowInsecureProtocol = true
    }
    maven {
        url "http://mvn.dianpingoa.com/android-nova-snapshot"
        allowInsecureProtocol = true
    }
    maven {
        url 'http://depot.sankuai.com/nexus/content/groups/public/'
        allowInsecureProtocol = true
    }
}

dependencies {
    api 'com.android.support:appcompat-v7:26.0.2'
    api 'com.android.support:support-v4:26.0.2'

    // 删除灵犀、Metrics、pay-base依赖；添加serviceloader注解、pay-common依赖
    api 'com.meituan.android.common.analyse:library:4.9.0'
    api('com.sankuai.meituan.serviceloader:serviceloader:2.2.27')
    annotationProcessor 'com.sankuai.meituan.serviceloader:processor:2.2.27'

    api('com.meituan.android.wallet:pay-base:1335.0.3')
}