package com.meituan.android.cashier.common;

import android.content.Intent;
import android.os.Bundle;
import android.support.v4.app.FragmentActivity;
import android.text.TextUtils;

import com.meituan.android.cashier.bean.CashierParams;
import com.meituan.android.paybase.common.activity.PayBaseActivity;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.utils.CollectionUtils;

import java.util.HashMap;
import java.util.Map;

public abstract class ICashierAdapter implements ICashier {
    // SLA 埋点需要的参数
    private long mStartTime;
    private String mFlowSource;
    private String mCashierFrom;
    private String mProductFrom;
    // 路由记录的开始时间
    private Object mRouterStartTime;
    // 记录SLA成功点的上报次数
    private int mSlAReportCount;
    private String mUniqueId;

    /**
     * 根据条件判断是否进入当前收银台
     *
     * @param t
     * @param cashierParams
     * @param <T>
     * @return
     */
    @Override
    public abstract <T extends FragmentActivity & CashierListener & IRequestCallback> ConsumeResult consume(T t, CashierParams cashierParams);


    private String getOrEmptyStr(Map<String, Object> map, String key) {
        if (CollectionUtils.isEmpty(map)) {
            return "";
        }
        Object value = map.get(key);
        if (value instanceof String) {
            return (String) value;
        }
        return "";
    }


    /**
     * 打开当前收银台
     *
     * @param cashierFrom
     * @param cashierParams 提单页params，为保证参数准确性，从上至下进行传递
     */
    @Override
    public final void start(String cashierFrom, Map<String, Object> cashierParams) {
        mSlAReportCount = 0;
        mStartTime = System.currentTimeMillis();
        mRouterStartTime = cashierParams.get("cashier_router_start_time");
        Map<String, Object> tag = new HashMap<>();
        tag.put("cashier_type", getCashierType());
        tag.put("scene", "start");
        this.mFlowSource = getOrEmptyStr(cashierParams, "flow_source");
        this.mCashierFrom = getOrEmptyStr(cashierParams, "from_cashier");
        this.mProductFrom = getOrEmptyStr(cashierParams, "from_product_type");
        this.mUniqueId = getOrEmptyStr(cashierParams, "uniqueId");
        appendSLASourceInfo(tag);
        CashierStaticsUtils.reportSystemCheck("c_PJmoK","b_pay_8ou0rbhz_mv", tag, getUniqueId());
        CashierStaticsUtils.logCustom("pay_sla_start_recorded_by_cashier_router", tag, null, getUniqueId());
        invoke(cashierFrom, cashierParams);
    }
    /**
     * 只有在 invoke 方法之后才能获取到该值
     *
     * @return
     */
    protected String getUniqueId() {
        return this.mUniqueId;
    }

    /**
     * 打开当前收银台
     *
     * @param cashierFrom
     * @param cashierParams 提单页params，为保证参数准确性，从上至下进行传递
     */
    public abstract void invoke(String cashierFrom, Map<String, Object> cashierParams);

    @Override
    public abstract String getCashierType();

    @Override
    public PayBaseActivity.ProcessType getRequestProgressType(int tag) {
        return null;
    }

    @Override
    public boolean onBackPressed() {
        return false;
    }

    @Override
    public abstract void onSaveInstanceState(Bundle outState);

    @Override
    public abstract void onRestoreInstanceState(Bundle savedInstanceState);

    @Override
    public void onDestroy(boolean release) {

    }

    @Override
    public abstract void onActivityResult(int requestCode, int resultCode, Intent data);

    @Override
    public void onStart() {

    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {

    }

    @Override
    public void onRequestSucc(int tag, Object obj) {

    }

    @Override
    public void onRequestException(int tag, Exception e) {

    }

    @Override
    public void onRequestFinal(int tag) {

    }

    @Override
    public void onRequestStart(int tag) {

    }

    private void appendSLASourceInfo(Map<String, Object> map) {
        if (map == null) {
            return;
        }
        if (!TextUtils.isEmpty(mFlowSource)) {
            map.put("flow_source", mFlowSource);
        }
        if (!TextUtils.isEmpty(mCashierFrom)) {
            map.put("from_cashier", mCashierFrom);
        }
        if (!TextUtils.isEmpty(mCashierFrom)) {
            map.put("from_product_type", mProductFrom);
        }
    }

    public void setUniqueId(String uniqueId) {
        this.mUniqueId = uniqueId;
    }

    /**
     * 收银台加载成功或者失败后回调此方法
     *
     * @param success
     * @param tag
     */
    public final void openStatus(boolean success, Map<String, Object> tag) {
        if (tag == null) {
            tag = new HashMap<>();
        }
        tag.put("cashier_type", getCashierType());
        tag.put("duration", (System.currentTimeMillis() - mStartTime));
        Object hybridCashierTTI = tag.get("hybrid_cashier_tti");
        long hybridCashierDuration = hybridCashierTTI == null ? 0 : ((Long) hybridCashierTTI);
        if (hybridCashierDuration > 0 && mRouterStartTime instanceof Long && (long) mRouterStartTime > 0) {
            tag.put("hybrid_cashier_duration", hybridCashierDuration - mStartTime);
            tag.put("hybrid_cashier_duration_route", hybridCashierDuration - (long) mRouterStartTime);
        }
        if ((TextUtils.isEmpty(mCashierFrom) || TextUtils.equals("empty", mCashierFrom)) && mRouterStartTime != null && mRouterStartTime instanceof Long) {
            tag.put("duration_from_router", System.currentTimeMillis() - (long) mRouterStartTime);
        }
        tag.put("scene", success ? "success" : "fail");
        tag.put("sla_end_count", ++mSlAReportCount);
        appendSLASourceInfo(tag);
        CashierStaticsUtils.reportSystemCheck("c_PJmoK","b_pay_8ou0rbhz_mv", tag, getUniqueId());
        if (success) {
            CashierStaticsUtils.logCustom("pay_sla_success_recorded_by_cashier_router", tag, null, getUniqueId());
        } else {
            CashierStaticsUtils.logCustom("pay_sla_failed_recorded_by_cashier_router", tag, null, getUniqueId());
        }
        if (success) {
            onSLASuccess();
        }
    }

    protected void onSLASuccess() {
    }

}
