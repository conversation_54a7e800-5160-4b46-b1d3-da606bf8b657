package com.meituan.android.cashier.bean;

import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;

@JsonBean
public class CashierParamRuleBean implements Serializable {
    private static final long serialVersionUID = 287057501028745363L;
    private CashierParamRuleDetailBean extraData;
    private CashierParamRuleDetailBean extraStatics;

    public CashierParamRuleDetailBean getExtraData() {
        return extraData;
    }

    public void setExtraData(CashierParamRuleDetailBean extraData) {
        this.extraData = extraData;
    }

    public CashierParamRuleDetailBean getExtraStatics() {
        return extraStatics;
    }

    public void setExtraStatics(CashierParamRuleDetailBean extraStatics) {
        this.extraStatics = extraStatics;
    }
}
