package com.meituan.android.cashier.bean;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;
import java.util.Map;

/**
 * 前置引导 Horn 配置
 * 应用场景：先享后付
 * https://km.sankuai.com/page/1110737342#id-b.Horn配置格式
 */
@JsonBean
public class CashierRouterPreGuideHornConfig implements Serializable {
    private static final long serialVersionUID = 7232133225596412389L;
    @SerializedName("cashier_type")
    private String cashierType;
    @SerializedName("render_error_action")
    private String renderErrorAction;
    private String url;
    @SerializedName("request_predispatcher")
    private String requestPreDispatcher;
    @SerializedName("nsf_url")
    private String nsfUrl;
    @SerializedName("nsf_params")
    private Map<String, String> nsfParams;
    private String backgroundColor;
    @SerializedName("loading_timeout")
    private long loadingTimeOut;
    @SerializedName("render_error_toast")
    private String renderErrorToast;
    private boolean nsf;
    @SerializedName("cover_url")
    private boolean coverUrl;

    public String getCashierType() {
        return cashierType;
    }

    public void setCashierType(String cashierType) {
        this.cashierType = cashierType;
    }

    public String getRenderErrorAction() {
        return renderErrorAction;
    }

    public void setRenderErrorAction(String renderErrorAction) {
        this.renderErrorAction = renderErrorAction;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getRequestPreDispatcher() {
        return requestPreDispatcher;
    }

    public void setRequestPreDispatcher(String requestPreDispatcher) {
        this.requestPreDispatcher = requestPreDispatcher;
    }

    public String getNsfUrl() {
        return nsfUrl;
    }

    public void setNsfUrl(String nsfUrl) {
        this.nsfUrl = nsfUrl;
    }

    public Map<String, String> getNsfParams() {
        return nsfParams;
    }

    public void setNsfParams(Map<String, String> nsfParams) {
        this.nsfParams = nsfParams;
    }

    public String getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    public long getLoadingTimeOut() {
        return loadingTimeOut;
    }

    public void setLoadingTimeOut(long loadingTimeOut) {
        this.loadingTimeOut = loadingTimeOut;
    }

    public String getRenderErrorToast() {
        return renderErrorToast;
    }

    public void setRenderErrorToast(String renderErrorToast) {
        this.renderErrorToast = renderErrorToast;
    }

    public boolean isNsf() {
        return nsf;
    }

    public void setNsf(boolean nsf) {
        this.nsf = nsf;
    }

    public boolean isCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(boolean coverUrl) {
        this.coverUrl = coverUrl;
    }
}
