package com.meituan.android.cashier.bean;

import android.text.TextUtils;

import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.JsonBean;

import android.support.annotation.NonNull;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

@JsonBean
public class CashierParamRuleDetailBean implements Serializable {
    private static final long serialVersionUID = 2698919864978102951L;
    private long maxSize;
    private boolean abandon;
    private List<CashierParamRuleDetailItemBean> params;

    public long getMaxSize() {
        return maxSize;
    }

    public void setMaxSize(long maxSize) {
        this.maxSize = maxSize;
    }

    public boolean isAbandon() {
        return abandon;
    }

    public void setAbandon(boolean abandon) {
        this.abandon = abandon;
    }

    public List<CashierParamRuleDetailItemBean> getParams() {
        return params;
    }

    public void setParams(List<CashierParamRuleDetailItemBean> params) {
        this.params = params;
    }

    public @NonNull
    HashMap<String, CashierParamRuleDetailItemBean> getParamsRuleMap() {
        HashMap<String, CashierParamRuleDetailItemBean> hashMap = new HashMap<>();
        if (CollectionUtils.isEmpty(params)) {
            return hashMap;
        }
        for (CashierParamRuleDetailItemBean cashierParamRuleDetailItemBean : params) {
            if (!TextUtils.isEmpty(cashierParamRuleDetailItemBean.getParamName())) {
                hashMap.put(cashierParamRuleDetailItemBean.getParamName(), cashierParamRuleDetailItemBean);
            }
        }
        return hashMap;
    }
}
