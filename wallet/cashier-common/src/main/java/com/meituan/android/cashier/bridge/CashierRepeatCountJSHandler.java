package com.meituan.android.cashier.bridge;

import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.paybase.moduleinterface.FinanceJsHandler;
import com.meituan.android.paybase.webview.jshandler.PayBaseJSHandler;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 桥参数说明 https://km.sankuai.com/page/1287566342
 */
@ServiceLoaderInterface(interfaceClass = FinanceJsHandler.class, key = "pay.cashierRepeatCount")
public class CashierRepeatCountJSHandler extends PayBaseJSHandler implements FinanceJsHandler {
    private static final String NAME = "pay.cashierRepeatCount";

    @Override
    public void exec() {
        super.exec();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("count", MTCashierActivity.cashierRepeatCount);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        jsCallback(jsonObject);
    }

    @Override
    public Class<?> getHandlerClass() {
        return this.getClass();
    }

    @Override
    public String getMethodName() {
        return NAME;
    }

    @Override
    public String getBridgeName() {
        return NAME;
    }

    @Override
    public String getSignature() {
        return "oX722/40AglBzTyNLt2P0MN6CvHHJ6iTqe7a557i6SpenttslEyINK+zYT/D1yh9+Dskk6RdOyGknx0CNwK7Hg==";
    }
}
