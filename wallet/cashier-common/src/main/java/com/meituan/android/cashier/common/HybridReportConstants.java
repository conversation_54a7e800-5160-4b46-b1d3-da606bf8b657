package com.meituan.android.cashier.common;

/**
 * <AUTHOR>
 * @date
 */
public class HybridReportConstants {
    public static final String CID_HYBRID_CASHIER_COMMON = "c_pay_7c9fc4b4";

    public static final String HYBRID_CASHIER_PAY_CLICK = "b_pay_hybrid_cashier_pay_click_mc";
    public static final String HYBRID_CASHIER_GOHELLOPAY_REQUEST_START = "b_pay_hybrid_cashier_gohellopay_request_start_mv";
    public static final String HYBRID_CASHIER_GOHELLOPAY_REQUEST_SUCC = "b_pay_hybrid_cashier_gohellopay_request_succ_mv";
    public static final String HYBRID_CASHIER_GOHELLOPAY_REQUEST_FAIL = "b_pay_hybrid_cashier_gohellopay_request_fail_mv";
    public static final String HYBRID_CASHIER_MAPAY_SUCC = "b_pay_hybrid_cashier_mtpay_succ_mv";

    public static final String MTPAY_BRIDGE_RETURN_RESULT_TO_ICASHIER = "b_pay_n3s0fgth_sc";
    public static final String MTPAY_BRIDGE_RETURN_RESULT_SUCC = "b_pay_o9mas1is_sc";
    public static final String MTPAY_BRIDGE_RETURN_RESULT_FAIL = "b_pay_8x7od2op_sc";
    public static final String MTPAY_BRIDGE_RETURN_RESULT = "b_pay_b1w0hzjh_sc";
    public static final String MTPAY_BRIDGE_RETURN_FREQUENCY_CONTROLLER = "b_pay_ki1dsw33_sc";
    public static final String MTPAY_BRIDGE_RETURN_AND_PAY = "b_pay_amw28c23_sc";
    public static final String MTPAY_BRIDGE_REQUEST = "b_pay_srn4qt5c_sc";
    public static final String MTPAY_BRIDGE_REQUEST_FAIL = "b_pay_v2gbavsa_sc";
    public static final String MTPAY_BRIDGE_CUT_POP_WINDOW = "b_pay_hd17hnjg_sc";
    public static final String MTPAY_BRIDGE_CUT_POP_WINDOW_SELECT_PAY_TYPE = "b_pay_4jv3tp2s_sc";
    public static final String MTPAY_BRIDGE_CUT_POP_WINDOW_CANCELLATION = "b_pay_epykl897_sc";
    public static final String MTPAY_BRIDGE_PARAMETER_VERIFICATION = "b_pay_2hdickb8_sc";
    public static final String MTPAY_BRIDGE_ACTION_DETERMINATION = "b_pay_i0q5t1fy_sc";
    public static final String MTPAY_BRIDGE_MORE_PAY_TYPE_POP_WINDOW = "b_pay_euobxq7v_sc";
    public static final String MTPAY_BRIDGE_GOHELLOPAY = "b_pay_rcywdj1v_sc";

}
