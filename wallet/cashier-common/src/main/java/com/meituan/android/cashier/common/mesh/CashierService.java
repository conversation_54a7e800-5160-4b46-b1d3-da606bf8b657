package com.meituan.android.cashier.common.mesh;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.support.annotation.Keep;
import android.text.TextUtils;

import com.google.gson.JsonObject;
import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.common.CashierConstants;
import com.sankuai.mesh.bean.MeshBaseUrl;
import com.sankuai.mesh.core.MeshBaseService;
import com.sankuai.mesh.core.MeshMethod;
import com.sankuai.mesh.core.MeshSdk;

/**
 * created by xingweike
 * 2019/6/28
 */
@Keep
public class CashierService extends MeshBaseService {

    private static final int REQUEST_FOR_CASHIER = 100;
    private static final String KEY_ERROR_CODE = "errorCode";
    private static final String KEY_ERROR_MSG = "errorMsg";


    private static final String PARAM_URL = "url";
    private static final String PARAM_REQUEST_CODE = "requestCode";


    private static final String SERVICE_MESH = "mesh";
    private static final String API_OPEN_URL_FOR_RESULT = "openUrlForResult";
    private static final String API_OPEN_URL = "openUrl";


    /**
     * 打开收银台
     */
    @MeshMethod("openCashier")
    public void openCashier() {
        if (this.mMeshBaseUrl == null) {
            callBackError("meshBaseUrl is null");
            return;
        }
        JsonObject params;
        if ((params = this.mMeshBaseUrl.getParameters()) != null) {
            // 先解析出跳转收银台需要的参数
            String tradeNum = params.get(CashierConstants.ARG_TRADE_NO) != null ? params.get(CashierConstants.ARG_TRADE_NO).getAsString() : null;
            String payToken = params.get(CashierConstants.ARG_PAY_TOKEN) != null ? params.get(CashierConstants.ARG_PAY_TOKEN).getAsString() : null;
            String callbackUrl = params.get(CashierConstants.ARG_CALLBACK_URL) != null ? params.get(CashierConstants.ARG_CALLBACK_URL).getAsString() : null;
            String requestCode = params.get(CashierConstants.QUERY_PARAM_REQUEST_CODE) != null ? params.get(CashierConstants.QUERY_PARAM_REQUEST_CODE).getAsString() : null;
            String merchantNo = params.get(CashierConstants.ARG_MERCHANT_NO) != null ? params.get(CashierConstants.ARG_MERCHANT_NO).getAsString() : null;
            String extraData = params.get(CashierConstants.ARG_EXTRA_DATA) != null ? params.get(CashierConstants.ARG_EXTRA_DATA).getAsString() : null;
            String extraStatics = params.get(CashierConstants.ARG_EXTRA_STATICS) != null ? params.get(CashierConstants.ARG_EXTRA_STATICS).getAsString() : null;
            String cif = params.get(CashierConstants.ARG_CIF) != null ? params.get(CashierConstants.ARG_CIF).getAsString() : null;
            if (TextUtils.isEmpty(tradeNum) || TextUtils.isEmpty(payToken)) {
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("msg", "trade number和pay_token不能为空");
                callBackError(jsonObject);
                return;
            }
            // 拼接跳转收银台的scheme协议
            Uri.Builder builder = Uri.parse("meituanpayment://cashier/launch").buildUpon();
            addParam(builder, CashierConstants.ARG_TRADE_NO, tradeNum);
            addParam(builder, CashierConstants.ARG_PAY_TOKEN, payToken);
            addParam(builder, CashierConstants.ARG_CALLBACK_URL, callbackUrl);
            addParam(builder, CashierConstants.ARG_MERCHANT_NO, merchantNo);
            addParam(builder, CashierConstants.ARG_EXTRA_DATA, extraData);
            addParam(builder, CashierConstants.ARG_EXTRA_STATICS, extraStatics);
            addParam(builder, CashierConstants.ARG_CIF, cif);

            JsonObject requestParams = new JsonObject();
            requestParams.addProperty("url", builder.toString());
            if (!TextUtils.isEmpty(requestCode)) {
                requestParams.addProperty(CashierConstants.QUERY_PARAM_REQUEST_CODE, requestCode);
            }

            // 这里使用Mesh内置的跳转Api进行scheme跳转
            MeshSdk.call((Activity) mContext, new MeshBaseUrl.Builder()
                    .businessId(mMeshBaseUrl.getBusinessId())
                    .service(SERVICE_MESH)
                    .api(API_OPEN_URL)
                    .parameters(requestParams.toString())
                    .build(), meshBaseUrl -> {
                // 通知调用方的执行结果.
                if (meshBaseUrl.isRequestSuccess()) {
                    callBackOk("");
                } else {
                    callBackError("");
                }
            });

        } else {
            this.callBackError("");
        }
    }

    private void addParam(Uri.Builder builder, String key, String param) {
        if (!TextUtils.isEmpty(param) && !TextUtils.isEmpty(key)) {
            builder.appendQueryParameter(key, param);
        }
    }


    @MeshMethod("openCashierForResult")
    public void openCashierForResult() {
        if (this.mMeshBaseUrl == null) {
            callBackError("meshBaseUrl is null");
            return;
        }
        JsonObject params = this.mMeshBaseUrl.getParameters();
        if (params != null) {
            String tradeNum = params.get(CashierConstants.ARG_TRADE_NO) != null ? params.get(CashierConstants.ARG_TRADE_NO).getAsString() : null;
            String payToken = params.get(CashierConstants.ARG_PAY_TOKEN) != null ? params.get(CashierConstants.ARG_PAY_TOKEN).getAsString() : null;
            String callbackUrl = params.get(CashierConstants.ARG_CALLBACK_URL) != null ? params.get(CashierConstants.ARG_CALLBACK_URL).getAsString() : null;
            String merchantNo = params.get(CashierConstants.ARG_MERCHANT_NO) != null ? params.get(CashierConstants.ARG_MERCHANT_NO).getAsString() : null;
            String extraData = params.get(CashierConstants.ARG_EXTRA_DATA) != null ? params.get(CashierConstants.ARG_EXTRA_DATA).getAsString() : null;
            String extraStatics = params.get(CashierConstants.ARG_EXTRA_STATICS) != null ? params.get(CashierConstants.ARG_EXTRA_STATICS).getAsString() : null;
            String cif = params.get(CashierConstants.ARG_CIF) != null ? params.get(CashierConstants.ARG_CIF).getAsString() : null;
            if (TextUtils.isEmpty(tradeNum) || TextUtils.isEmpty(payToken)) {
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty(KEY_ERROR_MSG, "trade number和pay_token不能为空");
                jsonObject.addProperty(KEY_ERROR_CODE, 11);
                callBackError(jsonObject);
                return;
            }

            Uri.Builder builder = Uri.parse("meituanpayment://cashier/launch").buildUpon();
            addParam(builder, CashierConstants.ARG_TRADE_NO, tradeNum);
            addParam(builder, CashierConstants.ARG_PAY_TOKEN, payToken);
            addParam(builder, CashierConstants.ARG_CALLBACK_URL, callbackUrl);
            addParam(builder, CashierConstants.ARG_MERCHANT_NO, merchantNo);
            addParam(builder, CashierConstants.ARG_EXTRA_DATA, extraData);
            addParam(builder, CashierConstants.ARG_EXTRA_STATICS, extraStatics);
            addParam(builder, CashierConstants.ARG_CIF, cif);

            JsonObject requestParams = new JsonObject();
            requestParams.addProperty(PARAM_URL, builder.toString());
            requestParams.addProperty(PARAM_REQUEST_CODE, REQUEST_FOR_CASHIER);

            MeshBaseUrl newMeshBaseUrl = new MeshBaseUrl.Builder()
                    .businessId(mMeshBaseUrl.getBusinessId())
                    .service(SERVICE_MESH)
                    .api(API_OPEN_URL_FOR_RESULT)
                    .parameters(requestParams.toString())
                    .build();

            MeshSdk.call((Activity) mContext, newMeshBaseUrl, mIMeshApiCallBack);
        } else {
            callBackError("cannot find params");
        }
    }


    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == REQUEST_FOR_CASHIER) {
            JsonObject result = new JsonObject();
            if (resultCode == Activity.RESULT_CANCELED) {
                result.addProperty(KEY_ERROR_CODE, 12);
                callBackError(result);
            } else if (resultCode == Activity.RESULT_OK) {
                int payStatus = data.getIntExtra(MTCashierActivity.RESULT, -1);
                if (payStatus == MTCashierActivity.STATUS_FINISH) {
                    callBackOk("");
                } else if (payStatus == MTCashierActivity.STATUS_THIRD_PARTY_FAIL) {
                    result.addProperty(KEY_ERROR_CODE, 11);
                    callBackError(result);
                }
            }
        }
    }


    public boolean consumeActivityResult() {
        return true;
    }
}