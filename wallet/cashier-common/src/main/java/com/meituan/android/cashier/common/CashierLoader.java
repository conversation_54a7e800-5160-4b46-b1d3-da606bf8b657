package com.meituan.android.cashier.common;

import android.text.TextUtils;

import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.bean.CashierParams;
import com.meituan.android.paybase.common.analyse.cat.CatUtils;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.sankuai.meituan.serviceloader.ServiceLoader;

import java.util.HashMap;
import java.util.List;

class CashierLoader {
    private final CashierParams mCashierParams;
    private final MTCashierActivity mtCashierActivity;

    public CashierLoader(CashierParams cashierParams, MTCashierActivity cashierActivity) {
        this.mCashierParams = cashierParams;
        this.mtCashierActivity = cashierActivity;
    }

    /**
     * 根据 cashierType 强制更新当前收银台的类型，可以参考 CashierTypeConstant 来获取 cashierType
     * 该方法与 loadCashier 不同的是，不会根据 init 方法中传递进入的参数来寻找合适的收银台
     *
     * @param sourceCashierType
     * @param destCashierType
     */
    public ICashier loadCashierByCashierType(String sourceCashierType, String destCashierType) {
        String[] cashierTypes = new String[]{destCashierType, CashierTypeConstant.CASHIERTYPE_NATIVE_STANDARD_CASHIER};
        for (String cashierType : cashierTypes) {
            if (TextUtils.equals(sourceCashierType, cashierType)) {
                continue;
            }
            ICashier iCashier = initCashier(cashierType);
            if (iCashier == null) {
                continue;
            }
            ICashier.ConsumeResult consumeResult = iCashier.consume(mtCashierActivity, mCashierParams);
            reportWhenCashierNotConsume(iCashier, consumeResult);
            if (consumeResult != null && consumeResult.isConsume()) {
                return iCashier;
            }
        }
        return null;
    }

    private String getUniqueId() {
        if (mtCashierActivity == null) {
            return "";
        }
        return mtCashierActivity.getUniqueId();
    }

    ICashier loadCashiers(String[] cashierTypes) {
        if (cashierTypes == null || cashierTypes.length == 0) {
            return null;
        }
        for (String cashierType : cashierTypes) {
            ICashier iCashier = initCashier(cashierType);
            if (iCashier == null) {
                continue;
            }
            ICashier.ConsumeResult consumeResult = iCashier.consume(mtCashierActivity, mCashierParams);
            reportWhenCashierNotConsume(iCashier, consumeResult);
            if (consumeResult != null && consumeResult.isConsume()) {
                return iCashier;
            }
        }
        return null;
    }

    private void reportWhenCashierNotConsume(ICashier iCashier, ICashier.ConsumeResult consumeResult) {
        if (iCashier ==null || consumeResult == null || consumeResult.isConsume()) {
            return;
        }
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("error_code", consumeResult.getErrorCode());
        hashMap.put("error_msg", consumeResult.getErrorReason());
        hashMap.put("cashier_type", iCashier.getCashierType());
        CashierStaticsUtils.reportSystemCheck("b_pay_9bl5zxok_sc", hashMap, getUniqueId());
        CashierStaticsUtils.logCustom("cashier_downgrade", hashMap, null, getUniqueId());
    }

    public ICashier loadStandardCashier() {
        ICashier iCashier = initCashier(CashierTypeConstant.CASHIERTYPE_NATIVE_STANDARD_CASHIER);
        if (iCashier == null) {
            return null;
        }
        ICashier.ConsumeResult consumeResult = iCashier.consume(mtCashierActivity, mCashierParams);
        if (consumeResult != null && consumeResult.isConsume()) {
            return iCashier;
        }
        return null;
    }

    /**
     * 初始化收银台，并缓存收银台对象
     *
     * @param cashierType
     * @return
     */
    private ICashier initCashier(String cashierType) {
        List<ICashier> cashiers = ServiceLoader.load(ICashier.class, cashierType);
        if (CollectionUtils.isEmpty(cashiers)) {
            return null;
        }
        return cashiers.get(0);
    }
}
