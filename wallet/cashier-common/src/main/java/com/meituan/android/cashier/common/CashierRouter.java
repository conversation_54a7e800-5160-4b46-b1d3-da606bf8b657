package com.meituan.android.cashier.common;

import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;

import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.bean.CashierParams;
import com.meituan.android.cashier.bean.CashierRouterInfo;
import com.meituan.android.cashier.bean.CashierScopeBean;
import com.meituan.android.cashier.retrofit.CashierRouterReqTagConstant;
import com.meituan.android.cashier.util.CashierRouterStatics;
import com.meituan.android.neohybrid.util.SavedStateUtils;
import com.meituan.android.paybase.common.activity.PayBaseActivity;
import com.meituan.android.paybase.common.analyse.cat.CatUtils;
import com.meituan.android.paybase.downgrading.PayHornConfigBean;
import com.meituan.android.paybase.downgrading.PayHornConfigService;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.retrofit.PayException;
import com.meituan.android.paybase.utils.CashierRepeatDownGradeSwitchManager;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.MTPaySuppressFBWarnings;
import com.meituan.android.paybase.utils.SdkDataStorageUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 路由模块，负责路由信息的解析和收银台对象的构建。
 * 流程如下：路由信息 -> 解析出对应的收银台类型 -> 初始化收银台
 * CashierRouterListener 用于事件回调
 */
public class CashierRouter implements IRequestCallback {
    private final static int ERROR_CODE_CASHIER_ROUTER_LISTENER_IS_NULL = 1140002;
    private static final String KEY_CASHIER_ROUTER_INFO = "key_cashier_router_info";
    private static final String ROUTE_INFO_SAVE_TYPE = "route_info_save_type";
    private static final String JSON_STRING_TYPE = "jsonString";
    // 管理收银台加载顺序
    private final CashierLoaderRecorder mCashierLoaderRecorder = new CashierLoaderRecorder();
    private CashierParams mCashierParams;
    private MTCashierActivity mtCashierActivity;
    // 处理 dispatcher 网络请求
    private CashierInfoService mCashierInfoService;
    // 处理收银台的加载
    private CashierLoader mCashierLoader;
    // 收银台准备就绪后给外部回调
    private CashierRouterListener mCashierRouterListener;
    private long durationPreDispatcher;

    @Override
    public void onRequestSucc(int tag, Object obj) {
        if (mCashierInfoService != null) {
            mCashierInfoService.onRequestSucc(tag, obj);
        }
    }

    private void appendDowngradeInfo(String errorInfo) {
        if (mCashierParams != null) {
            mCashierParams.setDowngradeInfo(errorInfo);
        }
    }

    @Override
    public void onRequestException(int tag, Exception e) {
        if (mCashierInfoService != null) {
            mCashierInfoService.onRequestException(tag, e);
        }
    }

    private String getExtParam(String errorMessage, int errorCode) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("jump_from_product", "preposed-mtcashier");
            if (errorCode != -1) {
                jsonObject.put("pay_err_code", errorCode);
                if (!TextUtils.isEmpty(errorMessage)) {
                    jsonObject.put("pay_err_msg", errorMessage);
                }
            }
        } catch (JSONException e) {
            CatUtils.logError("CashierRouter", "getExtParam");
        }
        return jsonObject.toString();
    }


    @Override
    public void onRequestFinal(int tag) {
        if (mCashierInfoService != null) {
            mCashierInfoService.onRequestFinal(tag);
        }
    }

    @Override
    public void onRequestStart(int tag) {
        if (mCashierInfoService != null) {
            mCashierInfoService.onRequestStart(tag);
        }
    }

    public void init(MTCashierActivity mtCashierActivity, final CashierParams cashierParams, String uniqueId) {
        PayHornConfigService.get().load(mtCashierActivity.getApplicationContext());
        CashierArrangeHornService.getInstance().load(mtCashierActivity.getApplicationContext());
        this.mtCashierActivity = mtCashierActivity;
        this.mCashierParams = cashierParams;
        this.mCashierInfoService = new CashierInfoService(cashierParams, mtCashierActivity);
        this.mCashierLoader = new CashierLoader(cashierParams, mtCashierActivity);
        reportOnCashierRouterStart(cashierParams, uniqueId);
    }

    private void reportOnCashierRouterStart(final CashierParams cashierParams, String uniqueId) {
        HashMap<String, Object> hashMap = new HashMap<>();
        final String vRoot = SdkDataStorageUtils.getDataStorageCenter(mtCashierActivity).getString(MTCashierActivity.KEY_IS_ROOT, "0");
        hashMap.put("device_rooted", vRoot);
        hashMap.put("unique_id", uniqueId);
        hashMap.put("trade_no", cashierParams.getTradeNo());
        hashMap.put("tradeNo", cashierParams.getTradeNo());
        hashMap.put("merchant_no", cashierParams.getMerchantNo());
        PayHornConfigBean payHornConfigBean = PayHornConfigService.get().getPayCashierHornConfigBean();
        if (payHornConfigBean != null) {
            hashMap.put("android_router_back_enabled", payHornConfigBean.isAndroidRouterLoadingBackEnabled());
        }
        hashMap.put("use_new_cashier_callback", !CashierRepeatDownGradeSwitchManager.downGrade());
        // 记录调起收银台的页面
        if (mtCashierActivity.getCallingActivity() != null) {
            hashMap.put("last_resumed_page", mtCashierActivity.getCallingActivity().getClassName());
        }
        CashierRouterStatics.registerCommonBusinessParams(hashMap, getUniqueId());
    }

    /**
     * 根据 init 方法传递进入的参数加载合适的收银台
     */
    public void loadCashier(CashierRouterListener cashierRouterListener) {
        this.mCashierRouterListener = cashierRouterListener;
        loadCashierInternal(cashierRouterListener);
    }

    private String getProductTypeFromCif() {
        if (!TextUtils.isEmpty(mCashierParams.getCif())) {
            try {
                JSONObject cifJson = new JSONObject(mCashierParams.getCif());
                return cifJson.optString("ct");
            } catch (Exception e) {
                LoganUtils.logError("CashierRouter_getProductTypeFromCif", e.getMessage());
            }
        }
        return "";
    }

    private void loadCashierInternal(CashierRouterListener cashierRouterListener) {
        CashierRouterStatics.onCashierRouterEnterStart(mCashierParams, getUniqueId());
        long durationRouterEnter = System.currentTimeMillis();
        String productType = getProductTypeFromCif();
        if (!TextUtils.isEmpty(productType)) {
            List<CashierScopeBean> cashierScopeBeans = CashierArrangeManager.getCashierScopeBean(mCashierParams, productType);
            if (CollectionUtils.isEmpty(cashierScopeBeans)) {
                // c if 字段中的 productType 没有匹配的编排配置，则降级到标准收银台
                productType = ProductTypeConstant.STANDARD_CASHIER;
                cashierScopeBeans = CashierArrangeManager.getCashierScopeBean(mCashierParams, productType);
            }
            mCashierParams.setProductType(productType);
            ICashier iCashier = loadCashier(cashierScopeBeans);
            CashierRouterStatics.onCashierRouterEnterResult(iCashier, mCashierParams, getUniqueId(), true, durationRouterEnter);
            if (iCashier != null) {
                // 存储当前加载的收银台
                mCashierLoaderRecorder.store(mCashierParams.getProductType(), cashierScopeBeans, iCashier.getCashierType());
                onCashierRouteInfoReady(iCashier, getEnterStaticsInfo());
            }
            return;
        }

        // 获取具体的编排配置
        mCashierParams.setProductType(mCashierParams.getBusinessInputCashierType());
        List<CashierScopeBean> cashierScopeBeans = CashierArrangeManager.getCashierScopeBean(mCashierParams, mCashierParams.getBusinessInputCashierType());
        if (CollectionUtils.isEmpty(cashierScopeBeans)) { // 如果没有匹配的数据，则请求 predispatcher 接口
            CashierRouterStatics.onCashierRouterEnterResult(null, mCashierParams, getUniqueId(), false, durationRouterEnter);
            requestPreDispatcher(cashierRouterListener);
        } else {
            ICashier iCashier = loadCashier(cashierScopeBeans);
            CashierRouterStatics.onCashierRouterEnterResult(iCashier, mCashierParams, getUniqueId(), false, durationRouterEnter);
            if (iCashier != null) {
                // 存储当前加载的收银台
                mCashierLoaderRecorder.store(mCashierParams.getProductType(), cashierScopeBeans, iCashier.getCashierType());
                onCashierRouteInfoReady(iCashier, getEnterStaticsInfo());
            }
        }
    }

    private ICashier loadCashier(List<CashierScopeBean> cashierScopeBeans) {
        // 编排配置转为 cashierType 数组
        String[] cashierTypes = CashierArrangeManager.getCashierTypes(cashierScopeBeans);
        // 根据 cashierType 数据加载收银台
        return mCashierLoader.loadCashiers(cashierTypes);
    }

    private Map<String, Object> getEnterStaticsInfo() {
        Map<String, Object> map = new HashMap<>();
        map.put("flow_source", "enter");
        return map;
    }

    private Map<String, Object> getPreDispatcherStaticsInfo(boolean requestSuccess) {
        Map<String, Object> map = new HashMap<>();
        map.put("flow_source", requestSuccess ? "predispatcher_success" : "predispatcher_failed");
        return map;
    }

    private void onRequestPreDispatcherSuccess(CashierRouterInfo cashierRouterInfo) {
        mCashierParams.setCashierRouterInfo(cashierRouterInfo); // 存储进入 CashierParams，方便传给各个收银台
        mCashierParams.setProductType(cashierRouterInfo.getProductType());
        // 优先使用 predispatcher 接口下发的 merchantNo 代替，如果下发为空，则用业务方传递的参数，既不重新设置值
        String merchantNo = mCashierParams.getPreDispatcherMerchantNo();
        if (!TextUtils.isEmpty(merchantNo)) {
            mCashierParams.setMerchantNo(merchantNo);
            Uri uri;
            if (mCashierParams.getUri() != null &&
                    mCashierParams.getUri().getQueryParameterNames() != null &&
                    mCashierParams.getUri().getQueryParameterNames().contains(CashierConstants.ARG_MERCHANT_NO)) {
                uri = BusinessParamsFilter.replaceUriParameter(mCashierParams.getUri(),
                        CashierConstants.ARG_MERCHANT_NO, merchantNo);
            } else {
                uri = BusinessParamsFilter.addUriParameter(mCashierParams.getUri(),
                        CashierConstants.ARG_MERCHANT_NO, merchantNo);
            }
            mCashierParams.setUri(uri);
            String type = mtCashierActivity.getIntent().getType();
            mtCashierActivity.getIntent().setDataAndType(uri, type); // 替换 uri、type
        }
        List<CashierScopeBean> cashierScopeBeans = CashierArrangeManager.getCashierScopeBean(mCashierParams, cashierRouterInfo.getProductType());
        String[] cashierTypes = CashierArrangeManager.getCashierTypes(cashierScopeBeans);
        ICashier iCashier = mCashierLoader.loadCashiers(cashierTypes);
        CashierRouterStatics.onCashierRouterPreDispatcherResult(true, iCashier, cashierRouterInfo.getProductType(),
                mCashierParams, getUniqueId(), durationPreDispatcher);
        if (iCashier != null) {
            // 存储当前的收银台
            mCashierLoaderRecorder.store(cashierRouterInfo.getProductType(), cashierScopeBeans, iCashier.getCashierType());
            onCashierRouteInfoReady(iCashier, getPreDispatcherStaticsInfo(true));
        }
    }

    private void onRequestPreDispatcherFail(Exception exception) {
        // predispatcher 接口请求失败，按照标准收银台处理
        final String productType = ProductTypeConstant.STANDARD_CASHIER;
        mCashierParams.setProductType(productType);
        List<CashierScopeBean> cashierScopeBeans = CashierArrangeManager.getCashierScopeBean(mCashierParams, productType);
        String[] cashierTypes = CashierArrangeManager.getCashierTypes(cashierScopeBeans);
        ICashier iCashier = mCashierLoader.loadCashiers(cashierTypes);
        if (exception instanceof PayException) {
            appendDowngradeInfo(getExtParam(exception.getMessage(), ((PayException) exception).getCode()));
        } else {
            appendDowngradeInfo(null);
        }
        CashierRouterStatics.onCashierRouterPreDispatcherResult(false, iCashier, productType, mCashierParams,
                getUniqueId(), durationPreDispatcher);
        if (iCashier != null) {
            mCashierLoaderRecorder.store(productType, cashierScopeBeans, iCashier.getCashierType());
            onCashierRouteInfoReady(iCashier, getPreDispatcherStaticsInfo(false));
        }
    }

    private void requestPreDispatcherInternal() {
        CashierRouterStatics.onCashierRouterPreDispatcherStart(getUniqueId());
        durationPreDispatcher = System.currentTimeMillis();
        mCashierInfoService.requestPreDispatcher(new CashierInfoService.CashierInfoServiceListener() {
            @Override
            public void onSuccess(CashierRouterInfo cashierRouterInfo) {
                onRequestPreDispatcherSuccess(cashierRouterInfo);
            }

            @Override
            public void onFail(Exception exception) {
                onRequestPreDispatcherFail(exception);
            }
        });
    }

    // 部分前置引导界面在降级时可能需要请求 predispatcher 接口，由后端决策降级到哪个收银台。
    public void requestPreDispatcher(CashierRouterListener cashierRouterListener) {
        this.mCashierRouterListener = cashierRouterListener;
        requestPreDispatcherInternal();
    }

    private void onCashierRouteInfoReady(ICashier iCashier, Map<String, Object> staticsInfo) {
        if (mCashierRouterListener != null) {
            mCashierRouterListener.onCashierRouteInfoReady(iCashier, staticsInfo);
        }
    }

    public void onDestroy() {
        if (mCashierInfoService != null) {
            mCashierInfoService.onDestroy();
        }
        CashierRouterStatics.unRegisterCommonBusinessParams(getUniqueId());
    }


    /**
     * 获取请求过程中的 loading 动画类型
     * 如果不确定具体类似，使用 PayBaseActivity.ProcessType.CASHIER 即可
     *
     * @param tag
     * @return
     */
    public PayBaseActivity.ProcessType getRequestProgressType(int tag) {
        if (tag != CashierRouterReqTagConstant.REQ_TAG_ROUTER_INFO) {
            return null;
        }
        return PayBaseActivity.ProcessType.CASHIER;
    }

    public ICashier loadCashierByCashierType(String sourceCashierType, String destCashierType) {
        return mCashierLoader.loadCashierByCashierType(sourceCashierType, destCashierType);
    }

    public ICashier loadStandardCashier() {
        return mCashierLoader.loadStandardCashier();
    }

    private String getUniqueId() {
        if (mtCashierActivity == null) {
            return "";
        }
        return mtCashierActivity.getUniqueId();
    }

    /**
     * 技术降级
     *
     * @param sourceCashierType
     * @param destCashierType
     * @param info
     * @return
     */
    public ICashier onCashierTechDowngrade(@CashierTypeConstant.CashierType String sourceCashierType, @CashierTypeConstant.CashierType String destCashierType, String info) {
        CashierRouterStatics.onCashierRouterTechDegradeStart(sourceCashierType, mCashierParams.getProductType(), getUniqueId());
        long durationTechDegrade = System.currentTimeMillis();
        // 技术降级时，从收银台列表中取出余下的部分
        mCashierParams.setDowngradeInfo(refreshInfo(sourceCashierType, info));
        ICashier iCashier;
        if (!TextUtils.isEmpty(destCashierType)) { // 如果指定了收银台类型，则必进
            CashierScopeBean cashierScopeBean = mCashierParams.getCashierScope(destCashierType, getUniqueId());
            if (cashierScopeBean != null) {
                cashierScopeBean.setDowngradeAvailable(false);
            }
            iCashier = mCashierLoader.loadCashiers(new String[]{destCashierType});
        } else {
            iCashier = mCashierLoader.loadCashiers(mCashierLoaderRecorder.getRemainCashierTypes());
            if (iCashier != null) {
                mCashierParams.setProductType(mCashierLoaderRecorder.getProductType());
                mCashierLoaderRecorder.store(mCashierLoaderRecorder.getProductType(), mCashierLoaderRecorder.getCashierScopeBeans(), iCashier.getCashierType());
            }
        }
        CashierRouterStatics.onCashierRouterTechDegradeResult(iCashier, mCashierParams.getProductType(),
                mCashierParams, getUniqueId(), durationTechDegrade);
        return iCashier;
    }

    /**
     * 业务降级，一般都跨产品类型进行降级
     *
     * @param sourceCashierType
     * @param destProductType
     * @param info
     * @return
     */
    public ICashier onCashierBusinessDowngrade(@CashierTypeConstant.CashierType String sourceCashierType,
                                               @ProductTypeConstant.ProductType String destProductType, String info) {
        CashierRouterStatics.onCashierRouterBusinessDegradeStart(sourceCashierType, mCashierParams.getProductType(), getUniqueId());
        long durationBusinessDegrade = System.currentTimeMillis();
        mCashierParams.setProductType(destProductType);
        mCashierParams.setDowngradeInfo(refreshInfo(sourceCashierType, info));
        if (TextUtils.equals(mCashierLoaderRecorder.getProductType(), destProductType)) {
            // 产品类型相同的降级，从收银台列表中取出余下的部分
            ICashier iCashier = mCashierLoader.loadCashiers(mCashierLoaderRecorder.getRemainCashierTypes());
            if (iCashier != null) {
                mCashierLoaderRecorder.store(destProductType, mCashierLoaderRecorder.getCashierScopeBeans(), iCashier.getCashierType());
            }
            CashierRouterStatics.onCashierRouterBusinessDegradeResult(iCashier, mCashierParams.getProductType(),
                    mCashierParams, getUniqueId(), durationBusinessDegrade);
            return iCashier;
        } else {
            List<CashierScopeBean> cashierScopeBeans = CashierArrangeManager.getCashierScopeBean(mCashierParams, destProductType);
            String[] cashierTypes = CashierArrangeManager.getCashierTypes(cashierScopeBeans);
            ICashier iCashier = mCashierLoader.loadCashiers(cashierTypes);
            if (iCashier != null) {
                mCashierLoaderRecorder.store(destProductType, cashierScopeBeans, iCashier.getCashierType());
            }
            CashierRouterStatics.onCashierRouterBusinessDegradeResult(iCashier, mCashierParams.getProductType(),
                    mCashierParams, getUniqueId(), durationBusinessDegrade);
            return iCashier;
        }
    }

    public boolean isUrlLink(String url) {
        if (TextUtils.isEmpty(url)) {
            return false;
        }
        return url.startsWith("http://") || url.startsWith("https://") || url.startsWith("meituanpayment://");
    }

    private String refreshInfo(String sourceCashierType, String info) {
        if (!isUrlLink(info)) {
            if (TextUtils.isEmpty(info)) {
                info = "{\"jump_from_product\":\"" + sourceCashierType + "\"}";
                return info;
            } else if (info.contains("jump_from_product")) {
                return info;
            } else {
                try {
                    JSONObject jsonObject = new JSONObject(info);
                    jsonObject.put("jump_from_product", sourceCashierType);
                    info = jsonObject.toString();
                } catch (Exception e) {
                    CatUtils.logError("refreshInfo_error", e.getMessage());
                    return info;
                }
            }
        }
        return info;
    }

    public void onSaveInstanceState(Bundle outState) {
        if (outState == null) {
            return;
        }
        mCashierLoaderRecorder.onSaveInstanceState(outState);
        if (mCashierParams.getCashierRouterInfo() != null) {
            PayHornConfigBean configBean = PayHornConfigService.get().getPayCashierHornConfigBean();
            //horn开关控制是否把bean转json在转string保存
            if (configBean != null && configBean.isRouteInfoSaveSwitch() && Build.VERSION.SDK_INT == 29) {
                SavedStateUtils.saveToBundle(outState, KEY_CASHIER_ROUTER_INFO, mCashierParams.getCashierRouterInfo());
                //新增一个变量保存存储方式，防止存和取的方式不一致
                outState.putString(ROUTE_INFO_SAVE_TYPE, JSON_STRING_TYPE);
            } else {
                outState.putSerializable(KEY_CASHIER_ROUTER_INFO, mCashierParams.getCashierRouterInfo());
            }
        }
    }

    public @ProductTypeConstant.ProductType
    String getCurrentProductType() {
        if (mCashierParams == null) {
            return "";
        }
        return mCashierParams.getProductType();
    }

    @MTPaySuppressFBWarnings("RCN_REDUNDANT_NULLCHECK_WOULD_HAVE_BEEN_A_NPE")
    public void onRestoreInstanceState(Bundle savedInstanceState) {
        if (savedInstanceState == null) {
            return;
        }
        mCashierLoaderRecorder.onRestoreInstanceState(savedInstanceState);
        if (mCashierParams != null) {
            mCashierParams.setProductType(mCashierLoaderRecorder.getProductType());
        }
        String saveType = savedInstanceState.getString(ROUTE_INFO_SAVE_TYPE);
        if (TextUtils.equals(JSON_STRING_TYPE, saveType)) {
            //删除标志位
            savedInstanceState.remove(ROUTE_INFO_SAVE_TYPE);
            //安卓10在bundle中存对象会崩溃,改完存对象转json后的string
            CashierRouterInfo routerInfo = SavedStateUtils.restoreFromBundle(savedInstanceState, KEY_CASHIER_ROUTER_INFO, CashierRouterInfo.class);
            if (routerInfo != null && mCashierParams != null) {
                mCashierParams.setCashierRouterInfo(routerInfo);
            }
        } else {
            Serializable serializable = savedInstanceState.getSerializable(KEY_CASHIER_ROUTER_INFO);
            if (serializable instanceof CashierRouterInfo && mCashierParams != null) {
                mCashierParams.setCashierRouterInfo((CashierRouterInfo) serializable);
            }
        }
    }

    public interface CashierRouterListener {
        /**
         * 收银台对象初始化完成
         *
         * @param iCashier
         * @param staticsInfo 埋点信息
         */
        void onCashierRouteInfoReady(ICashier iCashier, Map<String, Object> staticsInfo);
    }

}