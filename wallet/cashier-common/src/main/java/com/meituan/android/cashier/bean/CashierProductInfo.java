package com.meituan.android.cashier.bean;

import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;

@JsonBean
public class CashierProductInfo implements Serializable {

    private static final long serialVersionUID = -7808455358336284042L;
    // preposed-mtcashier:前置美团支付组件
    //standard-cashier：标准收银台
    private String type;
    // 下个请求的路径
    private String path;
    // 下个请求的参数
    private String nextReqParams;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getNextReqParams() {
        return nextReqParams;
    }

    public void setNextReqParams(String nextReqParams) {
        this.nextReqParams = nextReqParams;
    }
}
