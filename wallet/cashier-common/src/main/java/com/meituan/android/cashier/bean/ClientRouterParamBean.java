package com.meituan.android.cashier.bean;

import android.os.Build;
import android.text.TextUtils;

import com.dianping.titans.offline.OfflineCenter;
import com.google.gson.annotations.SerializedName;
import com.meituan.android.cashier.common.CashierUtil;
import com.meituan.android.neohybrid.init.HybridSDK;
import com.meituan.android.neohybrid.neo.offline.NeoOffline;
import com.meituan.android.neohybrid.util.WebViewUtils;
import com.meituan.android.paybase.config.PayBaseConfig;
import com.meituan.android.paybase.utils.AppUtils;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.JsonBean;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paycommon.lib.BaseConfig;
import com.meituan.metrics.util.DeviceUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@JsonBean
public class ClientRouterParamBean implements Serializable {
    private static final long serialVersionUID = -7703098101040821743L;
    @SerializedName("config_debug")
    private String configDebug;
    @SerializedName("net_status")
    private String netStatus;
    @SerializedName("user_id")
    private String userId;
    @SerializedName("device_type")
    private String deviceType;
    @SerializedName("global_offline_neo")
    private List<String> globalOfflineNeo;
    @SerializedName("global_offline_hybrid_mtp")
    private List<String> globalOfflineHybridMtp;
    @SerializedName("global_offline_hybrid_cashier")
    private List<String> globalOfflineHybridCashier;
    @SerializedName("packageName")
    private String packageName;
    @SerializedName("app_display_type")
    private String appDisplayType;
    @SerializedName("token")
    private String token;
    @SerializedName("pay_token")
    private String payToken;
    @SerializedName("debug")
    private String debug;
    @SerializedName("tradeno")
    private String tradeno;
    @SerializedName("device_level")
    private String deviceLevel;
    @SerializedName("chrome_version")
    private String chromeVersion;
    @SerializedName("chrome_version_core")
    private String chromeVersionCore;
    @SerializedName("app_version")
    private String appVersion;
    @SerializedName("device_model")
    private String deviceModel;
    @SerializedName("device_id")
    private String deviceID;
    @SerializedName("os_version")
    private String osVersion;
    @SerializedName("package_name")
    private String newPackageName;
    @SerializedName("use_new_router")
    private String useNewRouter;
    @SerializedName("projectIds")
    private Map<String, Object> projectIds;
    @SerializedName("os")
    private String os;
    @SerializedName("pay_sdk_version")
    private String paySdkVersion;
    @SerializedName("platform")
    private String platform;

    public String getUseNewRouter() {
        return useNewRouter;
    }

    public void setUseNewRouter(String useNewRouter) {
        this.useNewRouter = useNewRouter;
    }

    public Map<String, Object> getProjectIds() {
        return projectIds;
    }

    public void setProjectIds(Map<String, Object> projectIds) {
        this.projectIds = projectIds;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getConfigDebug() {
        return configDebug;
    }

    public void setConfigDebug(String configDebug) {
        this.configDebug = configDebug;
    }

    public String getNetStatus() {
        return netStatus;
    }

    public void setNetStatus(String netStatus) {
        this.netStatus = netStatus;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public List<String> getGlobalOfflineNeo() {
        return globalOfflineNeo;
    }

    public void setGlobalOfflineNeo(List<String> globalOfflineNeo) {
        this.globalOfflineNeo = globalOfflineNeo;
    }

    public List<String> getGlobalOfflineHybridMtp() {
        return globalOfflineHybridMtp;
    }

    public void setGlobalOfflineHybridMtp(List<String> globalOfflineHybridMtp) {
        this.globalOfflineHybridMtp = globalOfflineHybridMtp;
    }

    public List<String> getGlobalOfflineHybridCashier() {
        return globalOfflineHybridCashier;
    }

    public void setGlobalOfflineHybridCashier(List<String> globalOfflineHybridCashier) {
        this.globalOfflineHybridCashier = globalOfflineHybridCashier;
    }

    public String getAppDisplayType() {
        return appDisplayType;
    }

    public void setAppDisplayType(String appDisplayType) {
        this.appDisplayType = appDisplayType;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getPayToken() {
        return payToken;
    }

    public void setPayToken(String payToken) {
        this.payToken = payToken;
    }

    public String getTradeno() {
        return tradeno;
    }

    public void setTradeno(String tradeno) {
        this.tradeno = tradeno;
    }

    public String getDebug() {
        return debug;
    }

    public void setDebug(String debug) {
        this.debug = debug;
    }

    public String getDeviceLevel() {
        return deviceLevel;
    }

    public void setDeviceLevel(String deviceLevel) {
        this.deviceLevel = deviceLevel;
    }

    public String getChromeVersion() {
        return chromeVersion;
    }

    public void setChromeVersion(String chromeVersion) {
        this.chromeVersion = chromeVersion;
    }

    public String getChromeVersionCore() {
        return chromeVersionCore;
    }

    public void setChromeVersionCore(String chromeVersionCore) {
        this.chromeVersionCore = chromeVersionCore;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }

    public String getDeviceID() {
        return deviceID;
    }

    public void setDeviceID(String deviceID) {
        this.deviceID = deviceID;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public String getNewPackageName() {
        return newPackageName;
    }

    public void setNewPackageName(String newPackageName) {
        this.newPackageName = newPackageName;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getPaySdkVersion() {
        return paySdkVersion;
    }

    public void setPaySdkVersion(String paySdkVersion) {
        this.paySdkVersion = paySdkVersion;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    private static List<String> getAvailableOffline(String scope, List<String> offlines) {
        // 进行列表拷贝，防止遍历时，离线包同时存在更新，导致出现 ConcurrentModificationException
        if (CollectionUtils.isEmpty(offlines)) {
            return new ArrayList<>();
        }
        List<String> copyOfflines = new ArrayList<>(offlines);
        List<String> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(copyOfflines)) {
            return result;
        }
        for (String offline : copyOfflines) {
            if (NeoOffline.isOfflineResourceExist(scope, offline)) {
                result.add(offline);
            }
        }
        return result;
    }

    public static ClientRouterParamBean createClientRouterParamBean() {
        ClientRouterParamBean clientRouterParamBean = new ClientRouterParamBean();
        // 离线包获取、处理方法皆不可信，全部进行 try-catch 处理
        try {
            // 离线包名称和离线包配置平台（https://bundle.sankuai.com/）上的名称保持一致
            if (OfflineCenter.getInstance() != null) {
                clientRouterParamBean.setGlobalOfflineNeo(getAvailableOffline("global_offline_neo", OfflineCenter.getInstance().getOfflineUrlFromScope("global_offline_neo")));
                clientRouterParamBean.setGlobalOfflineHybridCashier(getAvailableOffline("global_offline_hybrid_cashier", OfflineCenter.getInstance().getOfflineUrlFromScope("global_offline_hybrid_cashier")));
                clientRouterParamBean.setGlobalOfflineHybridMtp(getAvailableOffline("global_offline_hybrid_mtp", OfflineCenter.getInstance().getOfflineUrlFromScope("global_offline_hybrid_mtp")));
            }
        } catch (Exception exception) {
            LoganUtils.logError("ClientRouterParamBean_createClientRouterParamBean", exception.getMessage());
        }
        clientRouterParamBean.setDeviceType(Build.MODEL);
        clientRouterParamBean.setAppDisplayType(CashierUtil.getOrientedElderlyType());
        clientRouterParamBean.setPackageName(PayBaseConfig.getProvider().getApplicationContext().getPackageName());
        clientRouterParamBean.setNetStatus(AppUtils.getNetWorkType(PayBaseConfig.getProvider().getApplicationContext()));
        clientRouterParamBean.setUserId(PayBaseConfig.getProvider().getUserId());
        clientRouterParamBean.setConfigDebug("0");
        clientRouterParamBean.setDebug(HybridSDK.isDebug() ? "1" : "0");
        clientRouterParamBean.setDeviceLevel(String.valueOf(DeviceUtil.getDeviceLevel(HybridSDK.getApplicationContext())));
        clientRouterParamBean.setChromeVersion(WebViewUtils.getChromeVersion(HybridSDK.getApplicationContext()));
        clientRouterParamBean.setChromeVersionCore(WebViewUtils.getChromeVersionCore(HybridSDK.getApplicationContext()));

        // 刻晴所需参数
        clientRouterParamBean.setAppVersion(PayBaseConfig.getProvider().getAppVersionName());
        clientRouterParamBean.setDeviceModel(Build.MODEL);
        clientRouterParamBean.setDeviceID(PayBaseConfig.getProvider().getDeviceId());
        clientRouterParamBean.setOsVersion(PayBaseConfig.getProvider().getOsVersion());
        clientRouterParamBean.setNewPackageName(PayBaseConfig.getProvider().getApplicationContext().getPackageName());
        if (HybridSDK.isDebug() && TextUtils.equals(PayBaseConfig.getProvider().getApplicationContext().getPackageName(), "com.sankuai.meituan.paydemo")) {
            clientRouterParamBean.setNewPackageName("com.sankuai.meituan");
        }
        clientRouterParamBean.setOs("android");
        clientRouterParamBean.setPaySdkVersion(BaseConfig.VERSION);
        clientRouterParamBean.setPlatform("android");
//        if (SystemInfoUtils.isApkDebuggable(MTPayConfig.getProvider().getApplicationContext())) {
//            clientRouterParamBean.setConfigDebug(String.valueOf(PaySettingStatus.getHornConfigDebug()));
//        }
        return clientRouterParamBean;
    }
}
