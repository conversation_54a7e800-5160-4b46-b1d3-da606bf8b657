package com.meituan.android.cashier.common;

import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.android.neohybrid.util.gson.GsonProvider;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.common.analyse.cat.CatUtils;
import com.meituan.android.paybase.config.PayBaseConfig;
import com.meituan.android.paybase.retrofit.PayException;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.NetRequestStatisticsUtils;
import com.meituan.android.paybase.utils.PayBaseStatisticsUtils;
import com.meituan.android.paybase.utils.ReportParamsManager;
import com.meituan.android.paybase.utils.StatisticsUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public final class CashierStaticsUtils {

    private static final String BUSINESS_KEY = "com.meituan.android.cashier.common.CashierStaticsUtils";

    private CashierStaticsUtils() {
    }

    public static String getBusinessKeyWithUniqueId(String uniqueId) {
        if (TextUtils.isEmpty(uniqueId)) {
            return BUSINESS_KEY;
        }
        return BUSINESS_KEY + "_" + uniqueId;
    }

    private static String getBusinessKey() {
        return BUSINESS_KEY;
    }

    public static void updateParameters(HashMap<String, Object> params, String uniqueId) {
        if (!CollectionUtils.isEmpty(params)) {
            ReportParamsManager.updateParametersWithBusinessKey(getBusinessKey(), params);
            ReportParamsManager.updateParametersWithBusinessKey(getBusinessKeyWithUniqueId(uniqueId), params);
        }
    }

    public static void registerCommonBusinessParams(HashMap<String, Object> params, String uniqueId) {
        unRegisterCommonBusinessParams(uniqueId);
        StatisticsUtils.registerParametersWithBusinessKey(getBusinessKey(), params);
        StatisticsUtils.registerParametersWithBusinessKey(getBusinessKeyWithUniqueId(uniqueId), params);
    }

    public static void unRegisterCommonBusinessParams(String uniqueId) {
        StatisticsUtils.unRegisterParametersWithBusinessKey(getBusinessKeyWithUniqueId(uniqueId));
        StatisticsUtils.unRegisterParametersWithBusinessKey(getBusinessKey());
    }

    public static void updateParametersWithBusinessKey(@NonNull final HashMap<String, Object> newParameters, String uniqueId) {
        StatisticsUtils.updateParametersWithBusinessKey(getBusinessKeyWithUniqueId(uniqueId), newParameters);
        StatisticsUtils.updateParametersWithBusinessKey(getBusinessKey(), newParameters);
    }

    public static HashMap<String, Object> getTechnologyParameters() {
        return StatisticsUtils.getTechnologyParameters();
    }

    public static void techMis(String bid, Map<String, Object> lab, String uniqueId) {
        StatisticsUtils.techMis(bid, lab, getBusinessKeyWithUniqueId(uniqueId));
    }

    public static void techMis(String bid, Map<String, Object> lab, String cid, String uniqueId) {
        StatisticsUtils.techMis(bid, lab, cid, getBusinessKeyWithUniqueId(uniqueId));
    }

    public static void logModelEvent(String cid, String bid, String act, Map<String, Object> lab,
                                     StatisticsUtils.EventType eventType, String uniqueId) {
        StatisticsUtils.logModelEvent(cid, bid, act, lab, eventType, -1, getBusinessKeyWithUniqueId(uniqueId));
    }

    public static void logModelEvent(String cid, String bid, String act, Map<String, Object> lab,
                                     StatisticsUtils.EventType eventType, int index, String uniqueId) {
        StatisticsUtils.logModelEvent(cid, bid, act, lab, eventType, index, getBusinessKeyWithUniqueId(uniqueId));
    }

    public static void reportModelEvent(@NonNull String cid, String bid, String act, Map<String, Object> lab,
                                        StatisticsUtils.EventType eventType, String uniqueId) {
        StatisticsUtils.reportModelEvent(cid, bid, act, lab, eventType, 0, getBusinessKeyWithUniqueId(uniqueId), true);
    }

    public static void reportRequestStart(String requestPath, String bid, Map<String, Object> lab, String uniqueId) {
        NetRequestStatisticsUtils.requestStart(requestPath, bid, lab, getBusinessKeyWithUniqueId(uniqueId));
    }

    public static void reportRequestSuccess(String requestPath, String bid, Map<String, Object> lab, String uniqueId) {
        NetRequestStatisticsUtils.requestSuccess(requestPath, bid, lab, getBusinessKeyWithUniqueId(uniqueId));
    }

    public static void reportRequestFailed(String requestPath, String bid, Exception exception, String uniqueId) {
        NetRequestStatisticsUtils.requestFailed(requestPath, bid, exception, getBusinessKeyWithUniqueId(uniqueId));
    }

    public static void reportModelEventWithViewEvent(@NonNull String cid, String bid, String act, Map<String, Object> lab, String uniqueId) {
        StatisticsUtils.reportModelEvent(cid, bid, act, lab, StatisticsUtils.EventType.VIEW, 0, getBusinessKeyWithUniqueId(uniqueId), true);
    }

    public static void reportModelEventWithViewEvent(@NonNull String cid, String bid, String act, Map<String, Object> lab, int index, String uniqueId) {
        StatisticsUtils.reportModelEvent(cid, bid, act, lab, StatisticsUtils.EventType.VIEW, index, getBusinessKeyWithUniqueId(uniqueId), true);
    }


    public static void reportModelEventWithNoneEvent(@NonNull String cid, String bid, String act, Map<String, Object> lab, String uniqueId) {
        StatisticsUtils.reportModelEvent(cid, bid, act, lab, null, 0, getBusinessKeyWithUniqueId(uniqueId), true);
    }


    public static void reportModelEventWithClickEvent(@NonNull String cid, String bid, String act, Map<String, Object> lab, String uniqueId) {
        StatisticsUtils.reportModelEvent(cid, bid, act, lab, StatisticsUtils.EventType.CLICK, 0, getBusinessKeyWithUniqueId(uniqueId), true);
    }

    public static void reportSystemCheck(String val_bid, Map<String, Object> val_lab, String uniqueId) {
        StatisticsUtils.reportSystemCheck(null, val_bid, val_lab, getVirtualCid(), getBusinessKeyWithUniqueId(uniqueId), true);
    }

    public static void reportSystemCheck(String val_cid, String val_bid, Map<String, Object> val_lab, String uniqueId) {
        StatisticsUtils.reportSystemCheck(null, val_bid, val_lab, val_cid, getBusinessKeyWithUniqueId(uniqueId), true);
    }

    public static void logPV(String pageInfoKey, @NonNull String cid, Map<String, Object> valLab, String uniqueId) {
        if (valLab == null) {
            valLab = new HashMap<>();
        }
        valLab.put("tradeNo", AnalyseUtils.getCashierTradeNo());
        valLab.put("nb_version", PayBaseConfig.getProvider().getPayVersion());
        StatisticsUtils.logPV(pageInfoKey, cid, valLab, getBusinessKeyWithUniqueId(uniqueId));
    }

    /**
     * cat 埋点
     *
     * @param customCommand
     */
    public static void logCustomRequestStart(String customCommand, String uniqueId) {
        CatUtils.logCustom(customCommand, null, null, getBusinessKeyWithUniqueId(uniqueId));
    }

    /**
     * cat 埋点
     *
     * @param customCommand
     * @param customTags
     */
    public static void logCustomRequestSuccess(String customCommand, Map<String, Object> customTags, String uniqueId) {
        CatUtils.logCustom(customCommand, customTags, null, getBusinessKeyWithUniqueId(uniqueId));
    }

    /**
     * cat 埋点
     *
     * @param customCommand
     */
    public static void logCustomRequestFailed(String customCommand, Exception exception, String uniqueId) {
        Map<String, Object> customTags = new HashMap<>();
        if (exception instanceof PayException) {
            customTags.put("error_code", ((PayException) exception).getCode());
        }
        CatUtils.logCustom(customCommand, customTags, null, getBusinessKeyWithUniqueId(uniqueId));
    }

    public static void logCustom(String customCommand, Map<String, Object> customTags, List<Float> value, String uniqueId) {
        CatUtils.logCustom(customCommand, customTags, value, getBusinessKeyWithUniqueId(uniqueId));
    }

    public static JSONObject convertMapToObject(Map<String, ?> toBeConverted) {
        if (CollectionUtils.isEmpty(toBeConverted)) {
            return new JSONObject();
        }
        try {
            String jsonString = GsonProvider.getInstance().toJson(toBeConverted);
            return new JSONObject(jsonString);
        } catch (Exception e) {
            return new JSONObject();
        }
    }

    public static JSONArray convertListToArray(List<? extends Serializable> toBeConverted) {
        if (CollectionUtils.isEmpty(toBeConverted)) {
            return new JSONArray();
        }
        try {
            String jsonString = GsonProvider.getInstance().toJson(toBeConverted);
            return new JSONArray(jsonString);
        } catch (Exception e) {
            return new JSONArray();
        }
    }

    /**
     * sc 埋点专用虚拟 cid
     *
     * @return
     */
    public static String getVirtualCid() {
        return PayBaseStatisticsUtils.getVirtualCid();
    }
}
