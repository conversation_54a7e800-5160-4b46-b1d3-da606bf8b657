package com.meituan.android.cashier.common;

import android.content.Context;

import com.meituan.android.paybase.config.PayBaseConfig;
import com.meituan.android.paycommon.lib.utils.CommonHornServiceUtil;

public class HybridPrePosedMTCashierHornService {
    private static final String HORN_NAME = "hybrid_meituanpay_individual";
    private volatile static HybridPrePosedMTCashierHornService mHybridPrePosedMTCashierHornService;
    private String mHybridPrePosedMTCashierConfig;

    private HybridPrePosedMTCashierHornService() {
    }

    public static HybridPrePosedMTCashierHornService getInstance() {
        if (mHybridPrePosedMTCashierHornService == null) {
            synchronized (HybridPrePosedMTCashierHornService.class) {
                if (mHybridPrePosedMTCashierHornService == null) {
                    mHybridPrePosedMTCashierHornService = new HybridPrePosedMTCashierHornService();
                }
            }
        }
        return mHybridPrePosedMTCashierHornService;
    }

    public void init() {
        Context context = PayBaseConfig.getProvider().getApplicationContext();
        CommonHornServiceUtil.load(context, null, HORN_NAME, (enable, result) -> {
            mHybridPrePosedMTCashierConfig = result;
        });
    }

    public String getHybridPrePosedMTCashierConfig() {
        return mHybridPrePosedMTCashierConfig;
    }
}
