package com.meituan.android.cashier.common;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.meituan.android.cashier.bean.CashierRouterPreGuideHornConfig;
import com.meituan.android.common.horn.Horn;
import com.meituan.android.common.horn.HornCallback;
import com.meituan.android.paybase.config.PayBaseConfig;
import com.meituan.android.paybase.downgrading.DowngradingService;
import com.meituan.android.paybase.utils.AppUtils;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.MTPaySuppressFBWarnings;
import com.meituan.android.paybase.utils.SystemInfoUtils;
import com.meituan.android.paycommon.lib.BaseConfig;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class CashierRouterHornService {
    private volatile static CashierRouterHornService cashierRouterHornService;
    @MTPaySuppressFBWarnings("IS2_INCONSISTENT_SYNC")
    private List<CashierRouterPreGuideHornConfig> cashierRouterPreGuideHornConfigList;
    private static final String HORN_CONFIG_NAME = "cashier_router";
    private static boolean isUsingOnline = true;

    private CashierRouterHornService() {
    }

    public static CashierRouterHornService getInstance() {
        if (cashierRouterHornService == null) {
            synchronized (CashierRouterHornService.class) {
                if (cashierRouterHornService == null) {
                    cashierRouterHornService = new CashierRouterHornService();
                }
            }
        }
        return cashierRouterHornService;
    }

    private final HornCallback callback = (enable, result) -> {
        LoganUtils.logHornConfig(HORN_CONFIG_NAME, enable, result);
        if (!enable) {
            return;
        }
        try {
            if (TextUtils.isEmpty(result)) {
                return;
            }
            synchronized (CashierRouterHornService.class) {
                JSONObject jsonObject = new JSONObject(result);
                Iterator<String> keys = jsonObject.keys();
                List<CashierRouterPreGuideHornConfig> cashierRouterPreGuideHornConfigs = new ArrayList<>();
                while (keys.hasNext()) {
                    try {
                        String key = keys.next();
                        String cashierRouterPreGuide = jsonObject.optString(key);
                        cashierRouterPreGuideHornConfigs.add(GsonProvider.getInstance().fromJson(cashierRouterPreGuide, CashierRouterPreGuideHornConfig.class));
                    } catch (Exception e) {
                        LoganUtils.logError("CashierRouterHornService_callback", e.getMessage());
                    }
                }
                this.cashierRouterPreGuideHornConfigList = cashierRouterPreGuideHornConfigs;
            }
        } catch (Exception e) {
            LoganUtils.logError("CashierRouterHornService_callback", e.getMessage());
        }
    };

    public synchronized List<CashierRouterPreGuideHornConfig> getCashierRouterPreGuideHornConfigList() {
        if (CollectionUtils.isEmpty(cashierRouterPreGuideHornConfigList)) {
            cashierRouterPreGuideHornConfigList = GsonProvider.getInstance().fromJson(CommonHalfPageCashierDefaultConfig.getDefaultHornConfig(), new TypeToken<List<CashierRouterPreGuideHornConfig>>() {
            }.getType());
        }
        return cashierRouterPreGuideHornConfigList;
    }

    public void load(Context context) {
        if (SystemInfoUtils.isApkDebuggable(context)) {
            Horn.debug(context, HORN_CONFIG_NAME, !DowngradingService.isUsingOnline());
        }
        Map<String, Object> query = new HashMap<>();
        query.put("channel", PayBaseConfig.getProvider().getChannel());
        query.put("cityid", PayBaseConfig.getProvider().getCityId());
        query.put("net_status", AppUtils.getNetWorkType(PayBaseConfig.getProvider().getApplicationContext()));
        query.put("userid", PayBaseConfig.getProvider().getUserId());
        query.put("pay_sdk_version", BaseConfig.VERSION);
        query.put("env", isUsingOnline ? "online" : "debug");
        Horn.register(HORN_CONFIG_NAME, callback, query);
    }

    public static boolean isUsingOnline() {
        return isUsingOnline;
    }

    public static void setUsingOnline(boolean mIsUsingOnline) {
        isUsingOnline = mIsUsingOnline;
    }
}