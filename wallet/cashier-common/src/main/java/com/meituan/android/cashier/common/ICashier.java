package com.meituan.android.cashier.common;

import android.support.v4.app.FragmentActivity;

import com.meituan.android.cashier.bean.CashierParams;
import com.meituan.android.neohybrid.util.NeoLogUtils;
import com.meituan.android.paybase.common.activity.PayBaseActivity;
import com.meituan.android.paybase.retrofit.IRequestCallback;

import java.util.Map;

public interface ICashier extends IRequestCallback, CashierLifecycleCallbacks, WindowFocusChangeListener {
    /**
     * 判断收银台类型是否匹配
     *
     * @param cashierParams
     * @return true 表示收银台类型匹配
     */
    <T extends FragmentActivity & CashierListener & IRequestCallback> ConsumeResult consume(T t, CashierParams cashierParams);

    /**
     * 打开收银台，一般是判断完收银台是否匹配后再调用此方法
     *
     * @param cashierFrom
     * @param cashierParams 提单页params，为保证参数准确性，从上至下进行传递
     */
    void start(String cashierFrom, Map<String, Object> cashierParams);

    /**
     * 返回当前收银台的类型，可以参考 CashierTypeConstant
     *
     * @return
     */
    String getCashierType();

    /**
     * 获取请求过程中的 loading 动画类型
     * 如果不确定具体类似，使用 PayBaseActivity.ProcessType.CASHIER 即可
     *
     * @param tag
     * @return
     */
    PayBaseActivity.ProcessType getRequestProgressType(int tag);

    class ConsumeResult {
        private boolean consume; // 表示此收银台是否可以打开
        private String errorCode; // 无法进入的错误码
        private String errorReason; // 无法进入的原因
        private Map<String, Object> extra; // 其他需要上报的埋点数据

        public ConsumeResult(boolean consume, String errorCode, String errorReason) {
            NeoLogUtils.logd("ICashier_ConsumeResult:consume=" + consume + ";errorCode=" + errorCode + ";errorReason=" + errorReason);
            this.consume = consume;
            this.errorCode = errorCode;
            this.errorReason = errorReason;
        }

        public ConsumeResult(boolean consume) {
            this.consume = consume;
        }

        public boolean isConsume() {
            return consume;
        }

        public void setConsume(boolean consume) {
            this.consume = consume;
        }

        public String getErrorReason() {
            return errorReason;
        }

        public void setErrorReason(String errorReason) {
            this.errorReason = errorReason;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public void setErrorCode(String errorCode) {
            this.errorCode = errorCode;
        }

        public Map<String, Object> getExtra() {
            return extra;
        }

        public void setExtra(Map<String, Object> extra) {
            this.extra = extra;
        }
    }
}
