package com.meituan.android.cashier.bean;

import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;

@JsonBean
public class CashierParamRuleDetailItemBean implements Serializable {
    public static String TYPE_STRING = "string";
    public static String TYPE_JSON_STRING = "jsonString";
    private static final long serialVersionUID = -2995850630598845395L;
    private String paramName;
    private long maxSize;
    private boolean abandon;
    // 类型：string 或者 jsonString
    private String type;

    public String getParamName() {
        return paramName;
    }

    public void setParamName(String paramName) {
        this.paramName = paramName;
    }

    public long getMaxSize() {
        return maxSize;
    }

    public void setMaxSize(long maxSize) {
        this.maxSize = maxSize;
    }

    public boolean isAbandon() {
        return abandon;
    }

    public void setAbandon(boolean abandon) {
        this.abandon = abandon;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
