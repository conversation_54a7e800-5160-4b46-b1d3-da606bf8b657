package com.meituan.android.cashier.common;

import android.support.annotation.RestrictTo;
import android.support.annotation.StringDef;

/**
 * 客户端自定义的 cashierType，和微服务下发的 destination_cashier 是对应的
 */
public final class CashierTypeConstant {
    private CashierTypeConstant() {
    }

    /**
     * 极速支付收银台
     */
    public static final String CASHIERTYPE_ONE_CLICK = "oneclickpay";

    /**
     * 周卡收银台
     */
    public static final String CASHIERTYPE_WEEKPAY = "weekpay";

    /**
     * Native 标准收银台
     */
    public static final String CASHIERTYPE_NATIVE_STANDARD_CASHIER = "native_standard_cashier";

    /**
     * i版收银台
     */
    public static final String CASHIERTYPE_ICASHIER = "web_cashier";

    /**
     * hybrid 标准收银台
     */
    public static final String CASHIERTYPE_HYBRID_STANDARD_CASHIER = "hybrid_standard_cashier";
    /**
     * hybrid 长辈版收银台
     */
    public static final String CASHIERTYPE_HYBRID_ELDERLY_CASHIER = "hybrid_elderly_cashier";
    /**
     * 美团支付 Hybrid 独立收银台
     */
    public static final String CASHIERTYPE_MT_HYBRID_HALFPAGE_CASHIER = "hybrid_preposed_mtcashier";

    /**
     * 前置引导
     */
    public static final String CASHIERTYPE_HYBRID_PRE_GUIDE = "hybrid_pre_guide_cashier";

    /**
     * 先用后付-签约流程
     */
    public static final String CASHIERTYPE_PAY_DEFER_SIGN = "pay_defer_sign";

    /**
     * 美支前置-验证收银台
     */
    public static final String CASHIERTYPE_PREORDER_CASHIER = "preorder_cashier";

    /**
     * 重新请求 predispatcher
     */
    public static final String CASHIERTYPE_REQUEST_PREDISPATCHER = "request_predispatcher";

    /**
     * 美团支付组件
     */
    public static final String CASHIERTYPE_MT_COMPONENT_CASHIER = "meituanpay_component";


    @RestrictTo(RestrictTo.Scope.LIBRARY)
    @StringDef({CASHIERTYPE_ONE_CLICK, CASHIERTYPE_WEEKPAY, CASHIERTYPE_NATIVE_STANDARD_CASHIER,
            CASHIERTYPE_ICASHIER, CASHIERTYPE_HYBRID_STANDARD_CASHIER,
            CASHIERTYPE_HYBRID_ELDERLY_CASHIER,
            CASHIERTYPE_MT_HYBRID_HALFPAGE_CASHIER,
            CASHIERTYPE_HYBRID_PRE_GUIDE, CASHIERTYPE_PAY_DEFER_SIGN,
            CASHIERTYPE_REQUEST_PREDISPATCHER, CASHIERTYPE_MT_COMPONENT_CASHIER,
            CASHIERTYPE_PREORDER_CASHIER, ""})
    public @interface CashierType {
    }
}