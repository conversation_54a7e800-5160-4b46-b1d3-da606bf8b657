package com.meituan.android.cashier.bean;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.cashier.common.CashierTypeConstant;
import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;
import java.util.List;

@JsonBean
public class CashierScopeBean implements Serializable {
    private static final long serialVersionUID = 3061989667081264560L;
    private String minVersion;
    private String maxVersion;
    private List<String> blackList;
    @SerializedName("downgrade_available")
    private boolean downgradeAvailable = true;
    @SerializedName("dest_cashier")
    private String destCashier;

    public String getMinVersion() {
        return minVersion;
    }

    public void setMinVersion(String minVersion) {
        this.minVersion = minVersion;
    }

    public String getMaxVersion() {
        return maxVersion;
    }

    public void setMaxVersion(String maxVersion) {
        this.maxVersion = maxVersion;
    }

    public List<String> getBlackList() {
        return blackList;
    }

    public void setBlackList(List<String> blackList) {
        this.blackList = blackList;
    }

    public boolean isDowngradeAvailable() {
        return downgradeAvailable;
    }

    public void setDowngradeAvailable(boolean downgradeAvailable) {
        this.downgradeAvailable = downgradeAvailable;
    }

    public String getDestCashier() {
        return destCashier;
    }

    public void setDestCashier(String destCashier) {
        this.destCashier = destCashier;
    }

    /**
     * 用于异常情况兜底
     * @return
     */
    public static CashierScopeBean getDefault(@CashierTypeConstant.CashierType String cashierType) {
        CashierScopeBean cashierScopeBean = new CashierScopeBean();
        cashierScopeBean.setDestCashier(cashierType);
        return cashierScopeBean;
    }
}
