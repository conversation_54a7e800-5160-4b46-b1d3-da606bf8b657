package com.meituan.android.cashier.common;

import android.support.annotation.RestrictTo;
import android.support.annotation.StringDef;

/**
 * 收银台的产品类型，新旧路由都需要用到
 */
public class ProductTypeConstant {
    private ProductTypeConstant() {
    }

    /**
     * 标准收银台
     */
    public static final String STANDARD_CASHIER = "standard-cashier";
    /**
     * 长辈版收银台
     */
    public static final String ELDERLY_CASHIER = "elderly-cashier";
    /**
     * 独立收银台
     */
    public static final String PREPOSED_MTCASHIER = "preposed-mtcashier";
    /**
     * 极速支付
     */
    public static final String ONECLICKPAY = "oneclickpay";
    /**
     * 先用后付
     */
    public static final String PAY_DEFER_SIGN = "pay_defer_sign";
    /**
     * 周卡支付
     */
    public static final String WEEK_PAY = "weekpay";

    /**
     * 重新请求 predispatcher 接口
     */
    public static final String OTHER = "request_predispatcher";

    /**
     * 美团支付组件
     */
    public static final String CASHIERTYPE_MT_COMPONENT_CASHIER = "meituanpay_component";


    /**
     * 美支前置-验证收银台
     */
    public static final String PREORDER_CASHIER = "preorder_cashier";

    @RestrictTo(RestrictTo.Scope.LIBRARY)
    @StringDef({STANDARD_CASHIER, PREPOSED_MTCASHIER, ONECLICKPAY, PAY_DEFER_SIGN, WEEK_PAY, CASHIERTYPE_MT_COMPONENT_CASHIER, PREORDER_CASHIER, OTHER, ""})
    public @interface ProductType {
    }

    public static final boolean contains(String product) {
        return STANDARD_CASHIER.equals(product) ||
                PREPOSED_MTCASHIER.equals(product) ||
                ONECLICKPAY.equals(product) ||
                PAY_DEFER_SIGN.equals(product) ||
                WEEK_PAY.equals(product) ||
                CASHIERTYPE_MT_COMPONENT_CASHIER.equals(product) ||
                PREORDER_CASHIER.equals(product) ||
                OTHER.equals(product);

    }
}
