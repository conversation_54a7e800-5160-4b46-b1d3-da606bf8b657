package com.meituan.android.cashier.bean;

import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;

@JsonBean
public class CashierRouterInfo implements Serializable {
    public static final String DECISION_TYPE_CASHIER = "cashierProduct";
    private static final long serialVersionUID = -1571888408693264010L;
    /**
     * 推荐产品类型
     * 不需要降级的情况下有值：
     * cashierProduct:收银台产品
     * guideProduct：引导产品
     */
    private String decisionType;
    // 产品信息
    private CashierProductInfo productInfo;

    private String clientRouterInfo;

    private String cashierSessionId;

    public String getCashierSessionId() {
        return cashierSessionId;
    }

    public void setCashierSessionId(String cashierSessionId) {
        this.cashierSessionId = cashierSessionId;
    }

    public String getDecisionType() {
        return decisionType;
    }

    public void setDecisionType(String decisionType) {
        this.decisionType = decisionType;
    }

    public CashierProductInfo getProductInfo() {
        return productInfo;
    }

    public void setProductInfo(CashierProductInfo productInfo) {
        this.productInfo = productInfo;
    }

    public String getClientRouterInfo() {
        return clientRouterInfo;
    }

    public void setClientRouterInfo(String clientRouterInfo) {
        this.clientRouterInfo = clientRouterInfo;
    }

    public String getProductType() {
        CashierProductInfo cashierProductInfo = getProductInfo();
        if (cashierProductInfo == null) {
            return null;
        }
        return cashierProductInfo.getType();
    }

}
