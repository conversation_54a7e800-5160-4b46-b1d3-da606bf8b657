package com.meituan.android.cashier.common;

/**
 * 通用半页收银台兜底horn配置，horn配置链接 https://horn.sankuai.com/file/3993/edit/PROD/content
 * 目前支持收银台类型
 * 1、pay_defer_sign：先用后付签约收银台，不是当前线上主要使用的收银台
 * 2、pay_defer_collection：先用后付-催收流程配置，不是当前线上主要使用的收银台
 * 3、paydefer-cashier：先用后付收银台，当前使用的业务方是打车和单车
 * 4、delaypay：前置先用后付收银台
 * 5、preorder_cashier：前置美团支付收银台，目前线上horn配置使用的是下划线的"preorder_cashier"，但后端希望后面改成横杠的"preorder-cashier"，为减少后续发版，兜底horn配置同时保留两份，待后续更新后删除不迭代的
 * 6、preorder-guide：前置月付拉新收银台
 * 7、deduction-precashier：极速支付引导前置收银台
 */
class CommonHalfPageCashierDefaultConfig {
    /**
     * 预置 Horn 配置，没有放在文件里，是因为文件需要读取，比较耗时
     */

    private static final String DEFAULT_HORN_CONFIG = "[\n" +
            "  {\n" +
            "    \"cashier_type\": \"pay_defer_sign\",\n" +
            "    \"render_error_action\": \"hybrid_standard_cashier\",\n" +
            "    \"render_error_toast\": \"美团先用后付暂时无法开启，为不影响您的体验，请先完成支付\",\n" +
            "    \"url\": \"/pay-defer/index.html\",\n" +
            "    \"nsf\": false,\n" +
            "    \"loading_timeout\": 6000\n" +
            "  },\n" +
            "  {\n" +
            "    \"cashier_type\": \"pay_defer_collection\",\n" +
            "    \"render_error_action\": \"hybrid_standard_cashier\",\n" +
            "    \"render_error_toast\": \"当前网络环境不稳定，请稍后再试\",\n" +
            "    \"url\": \"/pay-defer/payment.html\",\n" +
            "    \"nsf\": false,\n" +
            "    \"loading_timeout\": 6000\n" +
            "  },\n" +
            "  {\n" +
            "    \"cashier_type\": \"paydefer-cashier\",\n" +
            "    \"render_error_action\": \"hybrid_standard_cashier\",\n" +
            "    \"render_error_toast\": \"美团先用后付暂时无法开启，为不影响您的体验，请先完成支付\",\n" +
            "    \"url\": \"https://npay.meituan.com/pay-defer/indexv2.html\",\n" +
            "    \"nsf\": false,\n" +
            "    \"loading_timeout\": 6000\n" +
            "  },\n" +
            "  {\n" +
            "    \"cashier_type\": \"delaypay\",\n" +
            "    \"render_error_action\": \"hybrid_standard_cashier\",\n" +
            "    \"render_error_toast\": \"美团先用后付暂时无法开启，为不影响您的体验，请先完成支付\",\n" +
            "    \"url\": \"/pay-defer/paydelay.html\",\n" +
            "    \"nsf\": false,\n" +
            "    \"loading_timeout\": 6000,\n" +
            "    \"backgroundColor\": \"#00000000\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"cashier_type\": \"preorder_cashier\",\n" +
            "    \"render_error_action\": \"\",\n" +
            "    \"render_error_toast\": \"\",\n" +
            "    \"url\": \"https://npay.meituan.com/preorder-cashier/index.html\",\n" +
            "    \"nsf\": false,\n" +
            "    \"loading_timeout\": 6000,\n" +
            "    \"backgroundColor\": \"#B3000000\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"cashier_type\": \"preorder-cashier\",\n" +
            "    \"render_error_action\": \"\",\n" +
            "    \"render_error_toast\": \"\",\n" +
            "    \"url\": \"https://npay.meituan.com/preorder-cashier/index.html\",\n" +
            "    \"nsf\": false,\n" +
            "    \"loading_timeout\": 6000,\n" +
            "    \"backgroundColor\": \"#B3000000\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"cashier_type\": \"preorder-guide\",\n" +
            "    \"render_error_action\": \"\",\n" +
            "    \"render_error_toast\": \"\",\n" +
            "    \"url\": \"/preorder-cashier-guide/index.html\",\n" +
            "    \"nsf\": true,\n" +
            "    \"nsf_url\":  \"/cashier/displayconsult\",\n" +
            "    \"loading_timeout\": 6000,\n" +
            "    \"backgroundColor\": \"#B3000000\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"cashier_type\": \"deduction-precashier\",\n" +
            "    \"render_error_action\": \"\",\n" +
            "    \"render_error_toast\": \"\",\n" +
            "    \"url\": \"/deduction-precashier/index.html\",\n" +
            "    \"nsf\": false,\n" +
            "    \"loading_timeout\": 6000,\n" +
            "    \"backgroundColor\": \"#B3000000\"\n" +
            "  }\n" +
            "]";

    public static String getDefaultHornConfig() {
        return DEFAULT_HORN_CONFIG;
    }
}