package com.meituan.android.cashier.common;

import com.google.gson.JsonObject;
import com.meituan.android.cashier.bean.CashierRouterInfo;
import com.meituan.android.cashier.newrouter.predisplay.data.PreDisplayInfo;
import com.meituan.android.pay.base.utils.exception.Getter;
import com.meituan.android.pay.base.utils.serialize.GsonUtils;
import com.meituan.android.paybase.utils.CatchException;
import com.meituan.android.paybase.utils.GsonProvider;

import java.util.concurrent.Callable;

/**
 * <AUTHOR>
 */
public class PreDisplayInfoParser {
    private final PreDisplayInfo preDisplayInfo;

    public PreDisplayInfoParser(PreDisplayInfo preDisplayInfo) {
        this.preDisplayInfo = preDisplayInfo;
    }

    public String getMerchantNo() {
        return CatchException.runForNonNullReturned(() -> String.valueOf(preDisplayInfo.getBasicInfo().get("merchantNo")), "").value();
    }

    public String getCashierConfig(String cashierType) {
        return CatchException.runForNonNullReturned(() -> preDisplayInfo.getConfig(cashierType).toString(), "").value();
    }

    public JsonObject getCashierJsonConfig(String cashierType) {
        return Getter.get(() -> GsonUtils.toJsonObject(preDisplayInfo.getConfig(cashierType)));
    }

    public String getCashierSessionId() {
        return CatchException.runForNonNullReturned(() -> preDisplayInfo.getCashierSessionId(), "").value();
    }

    public JsonObject getNSFInfo() {
        if (preDisplayInfo == null) {
            return null;
        }
        JsonObject root = new JsonObject();
        root.addProperty("status", "success");
        JsonObject preDisplay = GsonProvider.getInstance().toJsonTree(preDisplayInfo).getAsJsonObject();
        preDisplay.remove("clientRouterInfo");
        root.add("data", preDisplay);
        return root;
    }

}
