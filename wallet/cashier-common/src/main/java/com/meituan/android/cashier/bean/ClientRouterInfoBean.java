package com.meituan.android.cashier.bean;

import com.google.gson.annotations.SerializedName;

import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;
import java.util.HashMap;

@JsonBean
public class ClientRouterInfoBean implements Serializable {
    private static final long serialVersionUID = 181763860133468820L;
    @SerializedName("merchantNo")
    private String merchantNo;
    @SerializedName("cache_configurations")
    private HashMap<String, Object> cacheConfigurations;
    @SerializedName("cashier_url")
    private String cashierUrl;
    @SerializedName("neo_configurations")
    private HashMap<String, Object> neoConfigurations;
    @SerializedName("nest_configurations")
    private HashMap<String, Object> nestConfigurations;

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public HashMap<String, Object> getCacheConfigurations() {
        return cacheConfigurations;
    }

    public void setCacheConfigurations(HashMap<String, Object> cacheConfigurations) {
        this.cacheConfigurations = cacheConfigurations;
    }

    public String getCashierUrl() {
        return cashierUrl;
    }

    public void setCashierUrl(String cashierUrl) {
        this.cashierUrl = cashierUrl;
    }

    public HashMap<String, Object> getNeoConfigurations() {
        return neoConfigurations;
    }

    public void setNeoConfigurations(HashMap<String, Object> neoConfigurations) {
        this.neoConfigurations = neoConfigurations;
    }

    public HashMap<String, Object> getNestConfigurations() {
        return nestConfigurations;
    }

    public void setNestConfigurations(HashMap<String, Object> nestConfigurations) {
        this.nestConfigurations = nestConfigurations;
    }
}
