package com.meituan.android.cashier.bean;

import android.net.Uri;
import android.support.annotation.Keep;
import android.text.TextUtils;

import com.meituan.android.cashier.common.CashierArrangeManager;
import com.meituan.android.cashier.common.CashierRouterInfoParse;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.common.CashierTypeConstant;
import com.meituan.android.cashier.common.PreDisplayInfoParser;
import com.meituan.android.cashier.newrouter.predisplay.data.PreDisplayInfo;
import com.meituan.android.paybase.utils.CatchException;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.JsonString;
import com.meituan.android.paybase.utils.MTPaySuppressFBWarnings;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 不需要加 JsonBean 注解
@Keep
public class CashierParams implements Serializable {
    private static final long serialVersionUID = 8376376276414697199L;
    private transient Uri uri;
    private String uriString; // 序列化使用
    // 业务方传入的 cashierType
    private String businessInputCashierType;
    private String tradeNo;
    private String cif;
    private String payToken;
    private String extraData;
    private String extraStatics;
    private String callbackUrl;
    private String merchantNo;
    private String lastResumedFeature;
    private PreDisplayInfo preDisplayInfo;
    private CashierRouterInfo cashierRouterInfo;
    // 降级信息：可能是 1.极速支付、美团支付前置失败的错误信息。jsonString 格式  2.降级到 i 版收银台时的 url
    private String mDowngradeInfo;
    @MTPaySuppressFBWarnings("SE_BAD_FIELD")
    private transient CashierRouterInfoParse mCashierRouterInfoParse;
    private String mProductType;
    private String mWebCashierUrl; // i 版收银台跳转链接
    private String uniqueId;
    private boolean useNewRouter;
    private boolean requestRulesEngine;
    private boolean requestPreDisplay;
    private transient String startTime;

    public void setProductType(String productType) {
        this.mProductType = productType;
    }

    public String getProductType() {
        return mProductType;
    }

    public Uri getUri() {
        if (uri != null) {
            return uri;
        }
        if (uriString != null) {
            uri = Uri.parse(uriString);
        }
        return uri;
    }

    public void setUri(Uri uri) {
        this.uri = uri;
        this.uriString = uri != null ? uri.toString() : null;
    }

    public String getBusinessInputCashierType() {
        return businessInputCashierType;
    }

    public void setBusinessInputCashierType(String businessInputCashierType) {
        this.businessInputCashierType = businessInputCashierType;
    }

    public String getCif() {
        return cif;
    }

    public void setCif(String cif) {
        this.cif = cif;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getPayToken() {
        return payToken;
    }

    public void setPayToken(String payToken) {
        this.payToken = payToken;
    }

    public String getExtraData() {
        return extraData;
    }

    public void setExtraData(String extraData) {
        this.extraData = extraData;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public String getExtraStatics() {
        return extraStatics;
    }

    public void setExtraStatics(String extraStatics) {
        this.extraStatics = extraStatics;
    }

    public PreDisplayInfo getPreDisplayInfo() {
        return preDisplayInfo;
    }

    public void setPreDisplayInfo(PreDisplayInfo preDisplayInfo) {
        this.preDisplayInfo = preDisplayInfo;
    }

    public CashierRouterInfo getCashierRouterInfo() {
        return cashierRouterInfo;
    }

    public void setCashierRouterInfo(CashierRouterInfo cashierRouterInfo) {
        this.cashierRouterInfo = cashierRouterInfo;
    }

    public String getDowngradeInfo() {
        return mDowngradeInfo;
    }

    public void setDowngradeInfo(String mDowngradeInfo) {
        this.mDowngradeInfo = mDowngradeInfo;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getWebCashierUrl() {
        return mWebCashierUrl;
    }

    public void setWebCashierUrl(String webCashierUrl) {
        this.mWebCashierUrl = webCashierUrl;
    }

    public String getLastResumedFeature() {
        return lastResumedFeature;
    }

    public void setLastResumedFeature(String lastResumedFeature) {
        this.lastResumedFeature = lastResumedFeature;
    }

    public String getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }

    /**
     * 获取编排配置，
     * 这里获取的是微服务下发的配置，如果获取完整的配置，
     * 可以使用 getFinalCashierArrange
     * @return
     */
    public Map<String, List<CashierScopeBean>> getPreDispatcherArrange() {
        if (mCashierRouterInfoParse == null) {
            mCashierRouterInfoParse = new CashierRouterInfoParse(cashierRouterInfo);
        } else {
            mCashierRouterInfoParse.setCashierRouterInfo(cashierRouterInfo);
        }
        return mCashierRouterInfoParse.getArrange();
    }

    /**
     * 获取 predispatcher 接口下发的 merchantNo，
     * 当业务方没有传递 merchantNo 时，使用此 merchantNo 代替
     * @return
     */
    public String getPreDispatcherMerchantNo() {
        if (mCashierRouterInfoParse == null) {
            mCashierRouterInfoParse = new CashierRouterInfoParse(cashierRouterInfo);
        } else {
            mCashierRouterInfoParse.setCashierRouterInfo(cashierRouterInfo);
        }
        return mCashierRouterInfoParse.getMerchantNo();
    }

    /**
     * 获取编排配置，包括 predispatcher 接口下发的编排配置、客户端从 horn 拉取的编排配置、预置的编排配置
     * @return
     */
    public Map<String, List<CashierScopeBean>> getFinalCashierArrange() {
        return CashierArrangeManager.getCashierArrangeMap(this);
    }

    /**
     * 各个收银台使用此方法获取编排配置
     * @param cashierType 定义在 CashierTypeConstant
     * @return
     */
    public CashierScopeBean getCashierScope(@CashierTypeConstant.CashierType String cashierType, String uniqueId) {
        List<CashierScopeBean> cashierScopeBeans = CashierArrangeManager.getCashierScopeBean(this, mProductType);
        if (CollectionUtils.isEmpty(cashierScopeBeans)) {
            CashierStaticsUtils.logCustom("cashier_route_getCashierScope_is_empty", null, null, uniqueId);
            return CashierScopeBean.getDefault(cashierType);
        }
        for (CashierScopeBean cashierScopeBean : cashierScopeBeans) {
            if (TextUtils.equals(cashierScopeBean.getDestCashier(), cashierType)) {
                return cashierScopeBean;
            }
        }
        CashierStaticsUtils.logCustom("cashier_route_getCashierScope_is_empty", null, null, uniqueId);
        return CashierScopeBean.getDefault(cashierType);
    }

    /**
     * 这里获取的是微服务下发的配置
     * @param cashierType 定义看 CashierTypeConstant
     * @return
     */
    public String getPreDispatcherCashierConfig(String cashierType) {
        if (cashierRouterInfo != null) {
            if (mCashierRouterInfoParse == null) {
                mCashierRouterInfoParse = new CashierRouterInfoParse(cashierRouterInfo);
            } else {
                mCashierRouterInfoParse.setCashierRouterInfo(cashierRouterInfo);
            }
            return mCashierRouterInfoParse.getCashierConfig(cashierType);
        } else {
            return preDisplayInfoParser().getCashierConfig(cashierType);
        }
    }

    public HashMap<String, String> getExtendTransmissionParams() {
        HashMap<String, String> hashMap = new HashMap<>();
        if (!TextUtils.isEmpty(getCif()) && !TextUtils.equals("null", getCif().toLowerCase())) {
            hashMap.put("cif", getCif());
        }
        return hashMap;
    }

    public String getProductTypeFromCif() {
        return CatchException.run(!TextUtils.isEmpty(cif), () -> JsonString.parser(cif).get("ct"), "")
                .catchForReport("CashierParams_getProductTypeFromCif").value();
    }

    public String getGuidePlanInfos() {
        return JsonString.parser(extraData).get("guide_plan_infos");
    }

    public String getAppId() {
        return JsonString.parser(extraData).get("app_id");
    }

    public static class Builder {
        private final CashierParams mCashierParams;

        public Builder() {
            mCashierParams = new CashierParams();
        }

        public Builder uri(Uri uri) {
            mCashierParams.setUri(uri);
            return this;
        }

        public Builder businessInputCashierType(String businessInputCashierType) {
            mCashierParams.setBusinessInputCashierType(businessInputCashierType);
            return this;
        }

        public Builder tradeNo(String tradeNo) {
            mCashierParams.setTradeNo(tradeNo);
            return this;
        }

        public Builder cif(String cif) {
            mCashierParams.setCif(cif);
            return this;
        }

        public Builder payToken(String payToken) {
            mCashierParams.setPayToken(payToken);
            return this;
        }

        public Builder extraData(String extraData) {
            mCashierParams.setExtraData(extraData);
            return this;
        }

        public Builder extraStatics(String extraStatics) {
            mCashierParams.setExtraStatics(extraStatics);
            return this;
        }

        public Builder callbackUrl(String callbackUrl) {
            mCashierParams.setCallbackUrl(callbackUrl);
            return this;
        }

        public Builder merchantNo(String merchantNo) {
            mCashierParams.setMerchantNo(merchantNo);
            return this;
        }

        public Builder lastResumedFeature(String lastResumedFeature) {
            mCashierParams.setLastResumedFeature(lastResumedFeature);
            return this;
        }

        public Builder uniqueId(String uniqueId) {
            mCashierParams.setUniqueId(uniqueId);
            return this;
        }

        public Builder cashierRouterInfo(CashierRouterInfo cashierRouterInfo) {
            mCashierParams.setCashierRouterInfo(cashierRouterInfo);
            return this;
        }

        public CashierParams build() {
            return mCashierParams;
        }
    }

    public static boolean checkValid(CashierParams cashierParams) {
        return cashierParams != null
                && cashierParams.getUri() != null;
    }

    public PreDisplayInfoParser preDisplayInfoParser() {
        return new PreDisplayInfoParser(preDisplayInfo);
    }

    public boolean isUseNewRouter() {
        return useNewRouter;
    }

    public void setUseNewRouter(boolean useNewRouter) {
        this.useNewRouter = useNewRouter;
    }

    public boolean isRequestRulesEngine() {
        return requestRulesEngine;
    }

    public void setRequestRulesEngine(boolean requestRulesEngine) {
        this.requestRulesEngine = requestRulesEngine;
    }

    public boolean isRequestPreDisplay() {
        return requestPreDisplay;
    }

    public void setRequestPreDisplay(boolean requestPreDisplay) {
        this.requestPreDisplay = requestPreDisplay;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }
}
