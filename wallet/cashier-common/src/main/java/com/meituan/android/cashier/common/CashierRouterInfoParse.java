package com.meituan.android.cashier.common;

import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.meituan.android.cashier.bean.CashierRouterInfo;
import com.meituan.android.cashier.bean.CashierScopeBean;
import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.paybase.utils.LoganUtils;

import org.json.JSONObject;

import java.util.List;
import java.util.Map;

public class CashierRouterInfoParse {
    private CashierRouterInfo mCashierRouterInfo;
    private Map<String, List<CashierScopeBean>> mCashierArrangeMap;

    protected CashierRouterInfoParse() {
    }

    public CashierRouterInfoParse(CashierRouterInfo cashierRouterInfo) {
        this.mCashierRouterInfo = cashierRouterInfo;
    }

    public void setCashierRouterInfo(CashierRouterInfo cashierRouterInfo) {
        this.mCashierRouterInfo = cashierRouterInfo;
    }

    public Map<String, List<CashierScopeBean>> getArrange() {
        if (mCashierArrangeMap != null) {
            return mCashierArrangeMap;
        }
        try {
            Object o = opt("arrange");
            if (o == null) {
                return null;
            }
            mCashierArrangeMap = GsonProvider.getInstance().fromJson(o.toString(),
                    new TypeToken<Map<String, List<CashierScopeBean>>>(){}.getType());
            return mCashierArrangeMap;
        } catch (Exception e) {
            LoganUtils.logError("CashierRouterInfoParse_getArrange", e.getMessage());
        }
        return null;
    }

    public String getMerchantNo() {
        Object merchantNo = opt("merchantNo");
        if (merchantNo != null) {
            return merchantNo.toString();
        }
        return "";
    }

    public String getCashierConfig(String cashierType) {
        Object o = opt(cashierType);
        if (o != null) {
            return o.toString();
        }
        return null;
    }

    private Object opt(String key) {
        if (mCashierRouterInfo == null) {
            return null;
        }
        String clientRouterInfo = mCashierRouterInfo.getClientRouterInfo();
        if (TextUtils.isEmpty(clientRouterInfo)) {
            return null;
        }
        try {
            JSONObject jsonObject = new JSONObject(clientRouterInfo);
            return jsonObject.opt(key);
        } catch (Exception e) {
            LoganUtils.logError("CashierRouterInfoParse_opt", e.getMessage());
        }
        return null;
    }
}
