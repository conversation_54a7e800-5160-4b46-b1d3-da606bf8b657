package com.meituan.android.cashier.common;

import static com.meituan.android.cashier.activity.MTCashierActivity.KEY_INSTALLED_APPS;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;

import com.meituan.android.cipstorage.CIPStorageCenter;
import com.meituan.android.paybase.asynctask.ConcurrentTask;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.MD5Utils;
import com.meituan.android.paybase.utils.SdkDataStorageUtils;
import com.meituan.android.paymentchannel.utils.PaymentInstallInfoUtils;
import com.sankuai.meituan.library.IAppDisplayTypeProvider;
import com.sankuai.meituan.serviceloader.ServiceLoader;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

/**
 * created by xingweike
 * 2019/5/7
 */
public final class CashierUtil {
    // 长辈版开关 -- 打开状态
    public static final String ELDER_SWITCH_OPENED = "1";

    private CashierUtil() {
    }

    // 金额显示两位小数
    public static String formatFloat(float f) {
        BigDecimal bg = new BigDecimal(f).setScale(2, BigDecimal.ROUND_HALF_UP);
        float num = bg.floatValue();
        return String.format("%.2f", num);
    }

    // 生成唯一标识
    public static String getUniqueId() {
        try {
            String md5 = MD5Utils.getStringMD5(UUID.randomUUID().toString());
            return md5.substring(md5.length() / 2);
        } catch (Exception e) {
            LoganUtils.logError("CashierUtil_getUniqueId", e.getMessage());
            return "";
        }
    }

    /**
     * 长辈版开关是否打开
     *
     * @return
     */
    public static boolean isElderCashier() {
        return TextUtils.equals(ELDER_SWITCH_OPENED, getOrientedElderlyType());
    }

    public static String getOrientedElderlyType() {
        int value = 0;
        List<IAppDisplayTypeProvider> providers = ServiceLoader.load(IAppDisplayTypeProvider.class, "app_display_type_provider");
        if (!CollectionUtils.isEmpty(providers) && providers.size() > 0) {
            IAppDisplayTypeProvider provider = providers.get(0);
            if (provider != null) {
                value = provider.getAppDisplayType();
            }
        }
        return String.valueOf(value);
    }

    /**
     * 是否是美团支付组件
     *
     * @param uri
     * @return
     */
    public static boolean isMeituanPayConponent(Uri uri) {
        final String MEITUANPAY_COMPONENT_HOST = "conchpay";
        final String MEITUANPAY_COMPONENT_PATH = "/launch";
        if (uri == null) {
            return false;
        }
        return TextUtils.equals(uri.getPath(), MEITUANPAY_COMPONENT_PATH)
                && TextUtils.equals(uri.getHost(), MEITUANPAY_COMPONENT_HOST);
    }


    /**
     * 获取当前系统安装微信、支付宝、云闪付和数币 APP 的情况
     *
     * @param context
     * @return
     */
    public static int getInstalledApps(Context context) {
        final int dValue = -1;
        final CIPStorageCenter[] dataStorage = {SdkDataStorageUtils.getDataStorageCenter(context)};
        final int installedApps = dataStorage[0].getInteger(KEY_INSTALLED_APPS, dValue);
        if (installedApps != dValue) {
            return installedApps;
        } else {
            final int installedAppsNew = PaymentInstallInfoUtils.getInstalledApps(context);
            // 子线程重新获取系统参数并覆盖原参数
            new ConcurrentTask<String, Integer, Integer>() {
                @Override
                protected Integer doInBackground(String... params) {
                    try {
                        if (dataStorage[0] == null) {
                            dataStorage[0] = SdkDataStorageUtils.getDataStorageCenter(context);
                        }
                        if (dataStorage[0] != null) {
                            dataStorage[0].setInteger(KEY_INSTALLED_APPS, installedAppsNew);
                        }

                    } catch (Exception e) {
                        LoganUtils.logError("CashierUtil_getInstalledApps", e.getMessage());
                    }
                    return installedAppsNew;
                }
            }.exe();
            return installedAppsNew;
        }
    }
}
