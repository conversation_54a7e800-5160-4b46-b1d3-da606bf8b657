package com.meituan.android.cashier.common;

import android.os.Bundle;
import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.meituan.android.cashier.bean.CashierScopeBean;
import com.meituan.android.neohybrid.util.SavedStateUtils;
import com.meituan.android.paybase.utils.CollectionUtils;

import java.util.List;

/**
 * 用于记录当前决策的产品类型、收银台类型、收银台加载位置
 * 当进行收银台加载时，按顺序从 mCashierScopeBeans 中选取
 */
class CashierLoaderRecorder {
    private static final String KEY_INDEX = "CashierLoaderRecorder_index";
    private static final String KEY_SCOPE_BEANS = "CashierLoaderRecorder_scope_beans";
    private static final String KEY_PRODUCT_TYPE = "CashierLoaderRecorder_product_type";
    private int mIndex;
    private List<CashierScopeBean> mCashierScopeBeans;
    private @ProductTypeConstant.ProductType String mProductType; // 产品决策的产品类型

    public String getProductType() {
        return mProductType;
    }

    public List<CashierScopeBean> getCashierScopeBeans() {
        return mCashierScopeBeans;
    }

    /**
     * 获取余下的收银台类型
     * 从 index 的下一个开始计算
     * @return
     */
    public String[] getRemainCashierTypes() {
        if (CollectionUtils.isEmpty(mCashierScopeBeans) || mCashierScopeBeans.size() <= mIndex) {
            return null;
        }
        String[] cashierTypes = new String[mCashierScopeBeans.size() - 1 - mIndex];
        for (int i = mIndex + 1; i < mCashierScopeBeans.size(); i++) {
            cashierTypes[i - mIndex - 1] = mCashierScopeBeans.get(i).getDestCashier();
        }
        return cashierTypes;
    }

    public void store(@ProductTypeConstant.ProductType String productType, List<CashierScopeBean> cashierScopeBeans, String cashierType) {
        this.mProductType = productType;
        this.mCashierScopeBeans = cashierScopeBeans;
        for (int i = 0; i < cashierScopeBeans.size(); i++) {
            if (TextUtils.equals(cashierScopeBeans.get(i).getDestCashier(), cashierType)) {
                this.mIndex = i;
                break;
            }
        }
    }

    public void onSaveInstanceState(Bundle savedInstanceState) {
        savedInstanceState.putInt(KEY_INDEX, mIndex);
        savedInstanceState.putString(KEY_PRODUCT_TYPE, mProductType);
        SavedStateUtils.saveToBundle(savedInstanceState, KEY_SCOPE_BEANS, mCashierScopeBeans);
    }

    public void onRestoreInstanceState(Bundle outState) {
        mIndex = outState.getInt(KEY_INDEX);
        mProductType = outState.getString(KEY_PRODUCT_TYPE);
        mCashierScopeBeans = SavedStateUtils.restoreFromBundle(outState, KEY_SCOPE_BEANS,
                new TypeToken<List<CashierScopeBean>>(){}.getType());
    }
}
