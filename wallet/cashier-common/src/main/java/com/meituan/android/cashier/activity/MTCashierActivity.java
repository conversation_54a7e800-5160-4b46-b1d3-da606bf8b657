package com.meituan.android.cashier.activity;

import static com.meituan.android.cashier.common.CashierUtil.getOrientedElderlyType;
import static com.meituan.android.paybase.payrouter.constants.RouterConstants.ROUTER_TYPE_CASHIER;
import static com.meituan.android.paybase.payrouter.constants.RouterConstants.ROUTER_TYPE_RESULT_PAGE;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.support.v4.content.Loader;
import android.support.v4.content.LocalBroadcastManager;
import android.support.v4.view.ViewCompat;
import android.support.v7.app.ActionBar;
import android.support.v7.widget.Toolbar;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.dianping.codelog.NovaCodeLog;
import com.dianping.titans.js.JsHandlerFactory;
import com.meituan.android.cashier.bean.CashierParams;
import com.meituan.android.cashier.common.BusinessParamsFilter;
import com.meituan.android.cashier.common.CashierCatConstants;
import com.meituan.android.cashier.common.CashierConstants;
import com.meituan.android.cashier.common.CashierDowngradeListener;
import com.meituan.android.cashier.common.CashierListener;
import com.meituan.android.cashier.common.CashierRouter;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.common.CashierTypeConstant;
import com.meituan.android.cashier.common.CashierUtil;
import com.meituan.android.cashier.common.ICashier;
import com.meituan.android.cashier.common.ICashierAdapter;
import com.meituan.android.cashier.common.ProductTypeConstant;
import com.meituan.android.cashier.common.R;
import com.meituan.android.cashier.launcher.CashierResult;
import com.meituan.android.cashier.newrouter.CashierActivityHandler;
import com.meituan.android.cashier.newrouter.CommonCashierRouterAdapter;
import com.meituan.android.cashier.newrouter.config.CashierRouterConfig;
import com.meituan.android.cashier.newrouter.remake.CashierRouterConstants;
import com.meituan.android.cashier.process.CashierProcessController;
import com.meituan.android.cashier.util.UniqueIdManager;
import com.meituan.android.common.locate.LocationLoaderFactory;
import com.meituan.android.common.locate.MasterLocator;
import com.meituan.android.common.locate.MasterLocatorFactoryImpl;
import com.meituan.android.common.locate.MtLocation;
import com.meituan.android.neohybrid.neo.report.MapBuilder;
import com.meituan.android.neohybrid.neo.tunnel.TunnelParamManager;
import com.meituan.android.pay.base.compat.BundleCompat;
import com.meituan.android.pay.base.compat.OSCompat;
import com.meituan.android.pay.base.config.ConfigCenter;
import com.meituan.android.pay.base.utils.observable.ObservableProvider;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.common.analyse.cat.CatConstants;
import com.meituan.android.paybase.common.analyse.cat.CatUtils;
import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.downgrading.DowngradingService;
import com.meituan.android.paybase.downgrading.PayHornConfigBean;
import com.meituan.android.paybase.downgrading.PayHornConfigService;
import com.meituan.android.paybase.moduleinterface.payment.PrePayerExecute;
import com.meituan.android.paybase.moduleinterface.picasso.coupondialog.CouponDialogConfirmBtnCallback;
import com.meituan.android.paybase.net.PayBaseCallFactory;
import com.meituan.android.paybase.password.verifypassword.OnPasswordInsertListener;
import com.meituan.android.paybase.password.verifypassword.PasswordExceptionHandler;
import com.meituan.android.paybase.payrouter.callback.RouterCallback;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.retrofit.PayException;
import com.meituan.android.paybase.utils.AppUtils;
import com.meituan.android.paybase.utils.CashierRepeatDownGradeSwitchManager;
import com.meituan.android.paybase.utils.CashierScreenSnapShotUtil;
import com.meituan.android.paybase.utils.CashierSessionIdUtil;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.IntentLargeObjectTransaction;
import com.meituan.android.paybase.utils.LocalBroadCastUtil;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.MTPayNeedToPersist;
import com.meituan.android.paybase.utils.MTPaySuppressFBWarnings;
import com.meituan.android.paybase.utils.ReportParamsManager;
import com.meituan.android.paybase.utils.StatisticsUtils;
import com.meituan.android.paybase.utils.UriUtils;
import com.meituan.android.paycommon.lib.coupon.model.Promotion;
import com.meituan.android.paycommon.lib.hybrid.HybridCashierHooker;
import com.meituan.android.paycommon.lib.router.PayRouterActivity;
import com.meituan.android.paycommon.lib.utils.ExceptionUtils;
import com.meituan.android.paycommon.lib.utils.HalfPageMarketingUtils;
import com.meituan.android.paycommon.lib.utils.OuterBusinessParamUtils;
import com.meituan.android.paycommon.lib.webview.specialcontainer.paymentdialog.PaymentDialogFragment;
import com.meituan.android.payrouter.remake.result.RouterResult;
import com.meituan.android.payrouter.remake.router.manager.RouterManager;
import com.meituan.android.payrouter.router.RouterManagerOld;
import com.meituan.android.payrouter.router.RouterRequestData;
import com.meituan.android.privacy.locate.MtLocationLoaderWrapper;
import com.meituan.android.singleton.RetrofitCallFactorySingleton;
import com.sankuai.meituan.retrofit2.raw.RawCall;

import org.json.JSONObject;

import java.io.Serializable;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import rx.Subscription;

/**
 * 美团支付收银台
 */
public class MTCashierActivity extends PayRouterActivity implements
        IRequestCallback, CashierDowngradeListener, CashierListener, CouponDialogConfirmBtnCallback,
        OnPasswordInsertListener, PrePayerExecute,
        OuterBusinessParamUtils.OuterBusinessParamInterface, CashierActivityHandler {
    public static final String STATUS_SUCCESS = "success";
    public static final String STATUS_FAIL = "fail";
    public static final String STATUS_CANCEL = "cancel";

    public static final int STATUS_FINISH = 1;
    public static final int STATUS_THIRD_PARTY_FAIL = 2;
    public static final String PARAM_EXTRA_DATA = "extra_data";
    public static final String KEY_PAY_EXTRA_DATA = "pay_extra_data";
    public static final String KEY_PAY_RESULT_EXTRA = "payResultExtra";
    public static final String KEY_PAY_RESULT_VALUE = "value";
    public static final String KEY_PAY_CALLBACK_ACTION = "action";
    public static final String KEY_ERROR_CODE = "errorCode";
    public static final String KEY_ERROR_MSG = "errorMsg";
    public static final String RESULT = "result";
    public static final String UNKNOWN = "unknown";

    public static final String VALUE_NULL = "null";

    public static final String VALUE_UNDEFINED = "undefined";
    public static final String ERROR_CODE_ILLEGAL_PAY_TOKEN = "1120019";
    public static final String ERROR_CODE_ILLEGAL_TRADE_NUMBER = "1120020";
    public static final String ERROR_CODE_ILLEGAL_OPEN_ONECLICKPAY = "1120022";
    public static final String ERROR_CODE_ILLEGAL_SERIALCODE = "1120023";
    public static final String ERROR_CODE_ILLEGAL_EXTRA_DATA = "1120024";
    public static final String ERROR_CODE_DEFAULT = "9999999";

    public static final String ERROR_MSG_ILLEGAL_PAY_TOKEN = "pay_token is illegal";
    public static final String ERROR_MSG_ILLEGAL_TRADE_NUMBER = "trade_number is illegal";
    public static final String ERROR_MSG_ILLEGAL_OPEN_ONECLICKPAY = "open_oneclickpay is illegal";
    public static final String ERROR_MSG_ILLEGAL_SERIALCODE = "serialCode is illegal";
    public static final String ERROR_MSG_ILLEGAL_EXTRA_DATA = "extra_data is illegal";
    public static final String ERROR_MSG_DEFAULT = "unknown error";
    public static final String ERROR_MSG_UNKNOWN_DISPATCHER_EXCEPTION = "unknown exception in the /cashier/dispatcher";
    public static final String ERROR_MSG_UNKNOWN_ONECLICKPAY_EXCEPTION = "unknown exception in the oneclickpay";
    public static final String TECH_TAG = "MTCashierActivity"; //打点相关的页面标示（是吗？表示怀疑）
    public static final String KEY_IS_ROOT = "is_root";
    public static final String KEY_INSTALLED_APPS = "installed_apps";
    public static final String TAG_CONTENT_FRAGMENT = "content";
    private static final String PARAM_CASHIER_TYPE = "param_cashier_type";

    private static final int CODE_PAY_SUCCESS = 1110000;
    private static final int CODE_PAY_CANCEL = 1110001;
    private static final int CODE_PAY_FAIL = 1110002;

    /**
     * 类似于空判断可以使用
     */
    private static final int ERROR_CODE_ICASHIER_IS_NULL = 1140001;
    private static final String KEY_OPERATION_ID = "operation_id";
    private static final String ENTRY_TIME_KEY = "pay_entry_time_key";
    private static final String LAST_RESUMED_PAGE_KEY = "last_resumed_page_key";
    @MTPaySuppressFBWarnings("MS_CANNOT_BE_FINAL")
    // 统计收银台的重入次数
    public static int cashierRepeatCount = 0;
    private final UniqueIdManager mUniqueIdManager = new UniqueIdManager();
    private final Handler mHandler = new Handler();
    private final List<Subscription> subscriptions = new ArrayList<>();
    private String callbackUrl;
    private String extraData;
    private String extraStatics;
    /**
     * 聚合收银台类型,极速支付：oneclickpay 普通收银台：traditionpay
     */
    private String businessInputCashierType;
    private long startTime; //打点统计接口访问时间的参数
    private Promotion promotion;
    private boolean isCouponOutOfDate = false; //directpay针对微信／支付宝下发的优惠券是否过期。默认不过期
    private TextView mActionbarTitleView;
    private CashierRouter mCashierRouter;
    private String mCurrentCashierType;
    private String tradeNo;
    private String payToken;
    private String mMerchantNo;
    private String mCif;
    private ICashier mCurrentCashier;
    @MTPayNeedToPersist
    private String resultStatus = STATUS_CANCEL;
    private CloseDialogReceiver mCloseDialogReceiver;
    @MTPayNeedToPersist
    private String mPayResultExtra;
    @MTPayNeedToPersist
    private String mLastResumedFeature;
    @MTPayNeedToPersist
    private long mEnterTime;// 统计用户在收银台停留的时长
    @MTPayNeedToPersist
    private long mSpecialCashierEnterTime;// 统计用户在具体某个收银台停留的时长。比如 Hybrid 标准收银台停留的时长
    // 是不是从不保留活动中恢复
    private boolean isRestore;
    @MTPayNeedToPersist
    private boolean mPayFinished;
    @MTPayNeedToPersist
    private String halfPageMarketingBackgroundColor = "";
    private CashierScreenSnapShotUtil.OnBitmapReadyListener mOnBitmapReadyListener;
    private Bitmap mSnapShotBitmap;
    private String uniqueId;
    private String cashierUniqueId;
    @MTPayNeedToPersist
    private boolean routerResultSwitch = false;
    private boolean getRouterResultPageResult; //是否路由返回了结果

    @MTPayNeedToPersist
    private String activityOperationId;

    //新路由数据
    @MTPayNeedToPersist
    private boolean useNewRouter;
    @MTPayNeedToPersist
    private boolean requestRulesEngine;
    @MTPayNeedToPersist
    private boolean requestPreDisplay;

    @MTPayNeedToPersist
    private String cashierRouterTrace;

//    private CashierRouterAdapter mNewCashierRouterAdapter;
    private Uri uri;

    // 错误信息
    @MTPayNeedToPersist
    private String errorMessage;

    // 错误信息
    @MTPayNeedToPersist
    private String errorCode;

    /**
     * 如果 callbackUrl 不为空，则跳转到指定的页面
     * <p>
     * 否则回到‘下订单’的页面，由接入方处理 支付成功 的事件
     *
     * @param status
     */
    public void handlePayResultAndFinish(int status) {
        this.mPayFinished = true;
        if (!TextUtils.isEmpty(callbackUrl)) {
            UriUtils.open(this, callbackUrl, false);
        }
        Intent result = new Intent(CashierConstants.KEY_CASHIER_CALLBACK_RESULT + tradeNo);
        result.putExtra(RESULT, status);
        result.putExtra(KEY_PAY_RESULT_VALUE, resultStatus);
        PayHornConfigBean payHornConfigBean = PayHornConfigService.get().getPayCashierHornConfigBean();
        boolean isAndroidOptimizeExperience = payHornConfigBean != null && payHornConfigBean.isAndroidOptimizeExperience();
        if (!TextUtils.isEmpty(mPayResultExtra)) {
            result.putExtra(isAndroidOptimizeExperience ? KEY_PAY_RESULT_EXTRA : KEY_PAY_EXTRA_DATA, mPayResultExtra);
        }
        if (TextUtils.equals(resultStatus, STATUS_FAIL)) {
            result.putExtra(KEY_ERROR_CODE, getErrorCodeOrDefault());
            result.putExtra(KEY_ERROR_MSG, getErrorMessageOrDefault());
        }
        setResult(Activity.RESULT_OK, result);
        finish();
    }

    @Override
    protected void customTheme() {
        if (this instanceof MTCashierWrapperActivity) {
            setTheme(R.style.cashier_wrapper_theme);
        } else {
            super.customTheme();
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        isRestore = savedInstanceState != null;
        if (!isRestore) {
            activityOperationId = AppUtils.generateOperationId();
            CashierSessionIdUtil.registerCashierSessionId(activityOperationId, "");

            setUniqueId(CashierUtil.getUniqueId());
            uniqueId = getUniqueId();
        } else {
            setUniqueId(uniqueId);
            CashierRepeatDownGradeSwitchManager.onRestoreInstanceState(savedInstanceState);

        }
        // MTCashierWrapperActivity的作用：不论外部是用哪种启动模式（singleTask/standard/singleTop/singleInstance），都按standard模式启动，这样允许收银台在支付成功页再次打开一个新的收银台
        if (this instanceof MTCashierWrapperActivity) {
            super.onCreate(savedInstanceState);
            setCustomActionBar();
            getSupportActionBar().hide();
            //去掉基类background干扰
            getWindow().setBackgroundDrawableResource(R.color.cashiercommon__transparent);
            Intent intent = new Intent(this, MTCashierActivity.class);
            intent.setData(getIntent().getData());
            mEnterTime = System.currentTimeMillis();
            intent.putExtra(ENTRY_TIME_KEY, mEnterTime);
            intent.putExtra(LAST_RESUMED_PAGE_KEY, HybridCashierHooker.getLastResumedFeature());
            intent.addFlags(Intent.FLAG_ACTIVITY_FORWARD_RESULT);
            startActivity(intent);
            mHandler.postDelayed(() -> finish(), 500);
            return;
        }
        // 进入任一收银台之前触发单次定位，更新定位缓存，保证支付风控
        MasterLocator masterLocator = new MasterLocatorFactoryImpl().createMasterLocator(this, getLocationRawCallFactory(this), "BKZCHMBBHANGSU8GLUKHBB56CCFF78U");
        if (masterLocator != null) {
            MtLocationLoaderWrapper mtLocationLoaderWrapper = MtLocationLoaderWrapper.with(this, "jf-4b58aa4469ef6adb", masterLocator);
            if (mtLocationLoaderWrapper != null) {
                Loader<MtLocation> mtLocationLoader = mtLocationLoaderWrapper.createMtLocationLoader(this, LocationLoaderFactory.LoadStrategy.normal);
                if (mtLocationLoader != null) {
                    mtLocationLoader.startLoading();
                }
            }
        }
        cashierRepeatCount++;
        ReportParamsManager.updateLXBasicParameters(KEY_OPERATION_ID, activityOperationId);
        super.onCreate(savedInstanceState);

        mUniqueIdManager.registerActivityLifecycleCallbacks(getUniqueId());
        HashMap<String, Object> logMap = new HashMap();
        logMap.put("recordStep", "start");
        LoganUtils.log("CASHIER_TTI_RECORD", logMap);
        if (savedInstanceState == null) {
            mEnterTime = getIntent().getLongExtra(ENTRY_TIME_KEY, System.currentTimeMillis());
        }

        setCustomActionBar();
        getSupportActionBar().hide();
        //去掉基类background干扰
        getWindow().setBackgroundDrawableResource(R.color.cashiercommon__transparent);
        setContentView(R.layout.cashiercommon__layout_content);
        uri = getIntent().getData();
        if (uri != null) {
            // 长辈版标识传入参数隧道，供hybrid收银台和支付后弹窗场景使用，app_前缀的参数需要放在全局参数传入参数隧道
            TunnelParamManager.getInstance().putParam("app_display_type", getOrientedElderlyType());
            tradeNo = uri.getQueryParameter(CashierConstants.QUERY_PARAM_TRADE_NUMBER);
            cashierUniqueId = uri.getQueryParameter("uniqueId");
            HalfPageMarketingUtils.setTradeNo(tradeNo);
            AnalyseUtils.setCashierTradeNo(tradeNo);
            removeStaticsParams();
            initStaticsParams(tradeNo, getUniqueId()); // 注册时间前移
            boolean urlChanged = false;
            mCif = uri.getQueryParameter(CashierConstants.QUERY_PARAM_CIF);
            String replacedCif = BusinessParamsFilter.filterCif(mCif);
            if (!TextUtils.equals(replacedCif, mCif)) {
                mCif = replacedCif;
                uri = BusinessParamsFilter.replaceUriParameter(uri, CashierConstants.QUERY_PARAM_CIF, mCif);
                urlChanged = true;
            }
            payToken = uri.getQueryParameter(CashierConstants.QUERY_PARAM_PAY_TOKEN);
            callbackUrl = uri.getQueryParameter(CashierConstants.QUERY_PARAM_CALLBACK_URL);
            extraData = uri.getQueryParameter(CashierConstants.QUERY_PARAM_EXTRA_DATA);
            String replacedExtraData = BusinessParamsFilter.filterExtraData(extraData, getUniqueId());
            if (!TextUtils.equals(replacedExtraData, extraData)) {
                extraData = replacedExtraData;
                uri = BusinessParamsFilter.replaceUriParameter(uri, CashierConstants.QUERY_PARAM_EXTRA_DATA, extraData);
                urlChanged = true;
            }
            extraStatics = uri.getQueryParameter(CashierConstants.QUERY_PARAM_EXTRA_STATICS);
            String replacedExtraStatics = BusinessParamsFilter.filterExtraStatics(extraStatics, getUniqueId());
            if (!TextUtils.equals(replacedExtraStatics, extraStatics)) {
                extraStatics = replacedExtraStatics;
                uri = BusinessParamsFilter.replaceUriParameter(uri, CashierConstants.QUERY_PARAM_EXTRA_STATICS, extraStatics);
                urlChanged = true;
            }
            if (urlChanged) {// 替换 url 还是要谨慎点，存在变化再替换
                String type = getIntent().getType();
                getIntent().setDataAndType(uri, type); // 替换 uri、type
                HashMap<String, Object> hashMap = new HashMap<>();
                hashMap.put("uri", uri.toString());
                LoganUtils.log("收银台路由参数校验-替换url后上报", hashMap);
            }
            if (CashierUtil.isMeituanPayConponent(uri)) {
                // 美团支付组件支付
                businessInputCashierType = ProductTypeConstant.CASHIERTYPE_MT_COMPONENT_CASHIER;
            } else {
                businessInputCashierType = uri.getQueryParameter(CashierConstants.ARG_CASHIER_TYPE);
            }
            mMerchantNo = uri.getQueryParameter(CashierConstants.ARG_MERCHANT_NO);
        }

        mCashierRouter = new CashierRouter();
        // 需要在 onCreate 中调用此方法
        mLastResumedFeature = getLastResumedFeature();
        CashierRouterConfig routerConfig = ConfigCenter.get(CashierRouterConfig.class);

        // Android 10 + 重建场景进行fix，其他场景(1.非Android10，2.Android10非重建)保持原样
        if (OSCompat.isAndroid10() && savedInstanceState != null) {
            BundleCompat.fixBundleClassLoader(this, savedInstanceState);
        } else {
            useNewRouter = routerConfig.useNewRouter();
            requestRulesEngine = routerConfig.requestRulesEngine();
            requestPreDisplay = routerConfig.requestPreDisplay();
        }
        if (useNewRouter) {
            routerManager = RouterManager.manager(this);
            routerManager.onCreate(savedInstanceState);
            ObservableProvider.bind(this);
        }
        if (savedInstanceState == null) {
            if (uri == null) { // 不应该为空
                CashierStaticsUtils.logCustom("paybiz_business_params_url_is_null", null, null, getUniqueId());
                finish();
                return;
            }
            startTime = System.currentTimeMillis();
            CatUtils.logRate(CashierCatConstants.ACTION_ENTRY_CASHIER, CatConstants.CODE_DEFAULT_OK);
            //新老路由切换开关

            CashierParams cashierParams = getCashierParams();
            cashierParams.setStartTime(String.valueOf(System.currentTimeMillis()));
            if (useNewRouter) {
                openWithNewRouter(cashierParams, false);
            } else {
                mCashierRouter.init(this, cashierParams, getUniqueId());
                mCashierRouter.loadCashier(this::onCashierReady);
            }

            // 美团支付组件不进行此项检查
            if (!CashierUtil.isMeituanPayConponent(uri)) {
                if (!paramsCheck(false)) {
                    return;
                }
            }
        } else {
            if (mPayFinished) {
                if (!CashierRepeatDownGradeSwitchManager.downGrade()) {
                    Subscription subscription = CashierScreenSnapShotUtil.getCashierSnapShotDrawable(this, tradeNo, drawable -> {
                        if (drawable != null) {
                            ViewCompat.setBackground(getWindow().getDecorView(), drawable);
                        } else {
                            CashierStaticsUtils.logCustom("paybiz_cashier_snapshot_restore_empty", null, null, getUniqueId());
                            CashierStaticsUtils.reportSystemCheck("b_pay_5l3pq2aw_sc", new AnalyseUtils.MapBuilder().add("scene", "MTCashierActivity_restore_snapshot").build(), getUniqueId());
                        }
                    });
                    if (subscription != null) {
                        subscriptions.add(subscription);
                    }
                }
            } else {
                if (useNewRouter) {
                    openWithNewRouter(getCashierParams(), true);
                } else {
                    restoreCurrentCashier(savedInstanceState, uri);
                }

            }
        }
        findViewById(R.id.content).setOnClickListener(v -> {
            if (shouldFinishWhenGotRiskError(MTCashierActivity.this)) {
                CashierStaticsUtils.reportSystemCheck("b_pay_click_translucent_to_cancel_sc",
                        MapBuilder.builder().add("activity", "MTCashierActivity").build("lastResumedFeature", getLastResumedFeature()),
                        getUniqueId());
                cancelPayment();
            }
        });
    }

    private void openWithNewRouter(CashierParams cashierParams, boolean restore) {
        if (cashierParams != null) {
            cashierParams.setUseNewRouter(useNewRouter);
            cashierParams.setRequestRulesEngine(requestRulesEngine);
            cashierParams.setRequestPreDisplay(requestPreDisplay);
        }
        cashierRouterTrace = RouterManager.launcher(this)
                .type(CashierRouterConstants.ROUTER_TYPE_CASHIER)
                .addData("cashierParams", cashierParams)
                .addData("cashierUniqueId", getUniqueId())
                .callback(this::handleCashierRouterResult)
                .trace(cashierRouterTrace)
                .launch(restore);
        updateMerchantNo();
        registerCashierSnapShotReceiver();
        CashierProcessController.bind(this, () -> cashierParams);
    }

    /**
     * 获取定位用的网络库，先使用平台提供的 shark通道 网络库，如果为空，则使用支付自定义的网络库
     *
     * @return 网络库
     */
    private RawCall.Factory getLocationRawCallFactory(Context context) {
        RawCall.Factory factory = RetrofitCallFactorySingleton.getInstance(RetrofitCallFactorySingleton.DEFAULTNV);
        return factory != null ? factory : PayBaseCallFactory.createMtNvCallFactory(context);
    }

    public CashierParams getCashierParams() {
        return new CashierParams.Builder()
                .uri(uri)
                .businessInputCashierType(businessInputCashierType)
                .tradeNo(tradeNo)
                .cif(mCif)
                .payToken(payToken)
                .extraData(extraData)
                .extraStatics(extraStatics)
                .callbackUrl(callbackUrl)
                .merchantNo(mMerchantNo)
                .lastResumedFeature(mLastResumedFeature)
                .uniqueId(uniqueId)
                .build();
    }

    private void restoreCurrentCashier(Bundle savedInstanceState, Uri uri) {
        registerCashierSnapShotReceiver();
        CashierParams cashierParams = getCashierParams();
        cashierParams.setUri(uri);
        mCashierRouter.init(this, cashierParams, getUniqueId());
        mCashierRouter.onRestoreInstanceState(savedInstanceState);
        mCurrentCashierType = savedInstanceState.getString(PARAM_CASHIER_TYPE);
        mCurrentCashier = mCashierRouter.loadCashierByCashierType("", mCurrentCashierType);
        if (mCurrentCashier != null) {
            if (mCurrentCashier instanceof ICashierAdapter) {
                ((ICashierAdapter) mCurrentCashier).setUniqueId(getUniqueId());
            }
            mCurrentCashier.onRestoreInstanceState(savedInstanceState);
        } else {
            crashReport("onCreate_savedInstanceState_not_null", "None");
            mCurrentCashier = mCashierRouter.loadStandardCashier();
            if (mCurrentCashier != null) {
                if (mCurrentCashier instanceof ICashierAdapter) {
                    ((ICashierAdapter) mCurrentCashier).setUniqueId(getUniqueId());
                }
                mCurrentCashier.onRestoreInstanceState(savedInstanceState);
            } else {
                crashReport("onCreate_savedInstanceState_not_null_cashier_is_null", "None");
            }
        }
    }

    public void setHalfPageMarketingBackgroundColor(String halfPageMarketingBackgroundColor) {
        this.halfPageMarketingBackgroundColor = halfPageMarketingBackgroundColor;
    }

    private void onCashierReady(ICashier iCashier, Map<String, Object> staticsInfo) {
        MTCashierActivity.this.mCurrentCashier = iCashier;
        updateMerchantNo();
        if (iCashier != null) {
            Map<String, Object> params = new HashMap<>();
            params.put(CashierConstants.LAST_RESUMED_FEATURE, mLastResumedFeature);
            if (!CollectionUtils.isEmpty(staticsInfo)) {
                Object flowSource = staticsInfo.get("flow_source");
                if (flowSource instanceof String) {
                    params.put("flow_source", flowSource);
                }
            }
            params.put("from_cashier", "empty");
            params.put("from_product_type", "empty");
            params.put("uniqueId", getUniqueId());
            if (!isRestore) {
                params.put("cashier_router_start_time", mEnterTime);
            }
            mSpecialCashierEnterTime = System.currentTimeMillis();
            registerCashierSnapShotReceiver();
            iCashier.start(null, params);
        } else {
            crashReport("onCreate_savedInstanceState_null", "None");
        }
    }

    /**
     * 检测业务方传递的 pay_token、tradeNo 是否合法
     * 如果参数不合法，该方法会退出收银台。调用者需禁止继续执行
     *
     * @param abandon 是否由方法内部处理参数不合法情况，finish当前页面
     * @return true 表示合法，false 表示不合法
     */
    public boolean paramsCheck(boolean abandon) {
        if (!paramCheck(abandon, tradeNo, ERROR_CODE_ILLEGAL_TRADE_NUMBER, ERROR_MSG_ILLEGAL_TRADE_NUMBER)) {
            return false;
        }

        return paramCheck(abandon, payToken, ERROR_CODE_ILLEGAL_PAY_TOKEN, ERROR_MSG_ILLEGAL_PAY_TOKEN);
    }

    private boolean paramCheck(boolean abandon, String param, String errorCode, String errorMsg) {
        if (TextUtils.isEmpty(param) || VALUE_NULL.equalsIgnoreCase(param) || VALUE_UNDEFINED.equalsIgnoreCase(param)) {
            if (abandon) {
                Uri uri = getIntent().getData();
                analyseUriParamError(uri, errorMsg);
                setErrorCodeAndErrorMessage(errorCode, errorMsg);
                onCashierPayFail(getString(R.string.cashiercommon__empty_param) + "(" + errorCode + ")", STATUS_FINISH);
            }
            return false;
        }
        return true;
    }

    /**
     * 异常情况上报，1～2个版本之后删除
     */
    public void crashReport(String scene, String currentCashierType) {
        try {
            String uri = null;
            if (getIntent() != null && getIntent().getData() != null) {
                uri = getIntent().getData().toString();
            }
            HashMap<String, Object> map = new HashMap<>();
            map.put("currentCashierType", "currentCashierType:" + currentCashierType);
            map.put("scene", scene);
            map.put("pay_token", "payToken:" + payToken);
            map.put("trade_number", "tradeNumber:" + tradeNo);
            map.put("uri", "uri:" + uri);
            CashierStaticsUtils.techMis("b_pay_aqzrolky_sc", map, getUniqueId());
        } catch (Exception e) {
            LoganUtils.logError("MTCashierActivity_crashReport", e.getMessage());
        }
    }

    // 该方法初始化收银台埋点的通用参数，退出收银台的时候会进行清空
    private void initStaticsParams(String tradeNo, String uniqueId) {
        HashMap<String, Object> commonParams = new HashMap<>();
        commonParams.put("tradeNo", tradeNo);
        commonParams.put("trade_no", tradeNo);
        // 单次收银台唯一ID （类似web中的session功能）
        commonParams.put("unique_id", uniqueId);
        commonParams.put("merchant_no", this.mMerchantNo);
        commonParams.put("cashier_repeat_count", cashierRepeatCount);
        // 记录调起收银台的页面
        commonParams.put("last_resumed_page", mLastResumedFeature);
        commonParams.put("use_new_cashier_callback", !CashierRepeatDownGradeSwitchManager.downGrade());
        CashierStaticsUtils.registerCommonBusinessParams(commonParams, getUniqueId());
        //StatisticsUtils里新增通用参数，用StatisticsUtils自己的businessKey
        StatisticsUtils.registerParametersWithBusinessKey(StatisticsUtils.getBusinessKey(), commonParams);
    }

    // 如果业务方没传 merchantNo，会使用 predispatcher 接口下发的 merchantNo 代替，需要更新埋点才能有效
    private void updateMerchantNo() {
        Uri uri = getIntent().getData();
        if (uri != null) {
            mMerchantNo = uri.getQueryParameter(CashierConstants.ARG_MERCHANT_NO);
            initStaticsParams(tradeNo, getUniqueId());
        }
    }

    private void removeStaticsParams() {
        CashierStaticsUtils.unRegisterCommonBusinessParams(getUniqueId());
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (ExceptionUtils.needFinish(intent)) {
            cancelPayment();
        } else {
            HashMap<String, Object> map = new HashMap<>();
            String uri = "";
            if (intent.getData() != null) {
                uri = intent.getData().toString();
            }
            map.put("uri", uri);
            CashierStaticsUtils.techMis("b_pay_au6ez764_sc", map, getUniqueId());
            if (TextUtils.equals("a", DowngradingService.get().getStrategy("cashier_reentener"))) {
                handlePayResultAndFinish(STATUS_FINISH);
                startActivity(intent);
            }
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        CashierRepeatDownGradeSwitchManager.onSaveInstanceState(outState);
        if (mCurrentCashier != null) {
            mCurrentCashierType = mCurrentCashier.getCashierType();
            mCurrentCashier.onSaveInstanceState(outState);
            outState.putString(PARAM_CASHIER_TYPE, mCurrentCashierType);
            if (mCashierRouter != null) {
                mCashierRouter.onSaveInstanceState(outState);
            }
        } else {
            crashReport("onSaveInstanceState_else", "None");
        }
    }

    @Override
    protected void onDestroy() {
        if (this instanceof MTCashierWrapperActivity) {
            super.onDestroy();
            mHandler.removeCallbacksAndMessages(null);
            return;
        }
        ObservableProvider.unObserve(this);
        AnalyseUtils.setCashierTradeNo(null);
        hideProgress();
        cleanProgressCount();
        if (mCloseDialogReceiver != null) {
            LocalBroadcastManager.getInstance(this).unregisterReceiver(mCloseDialogReceiver);
        }
        if (mCashierRouter != null) {
            mCashierRouter.onDestroy();
        }
        if (mCurrentCashier != null) {
            mCurrentCashier.onDestroy(mPayFinished);
        }
        super.onDestroy();
        // 修复支付结果回调时机不准确问题
        if (isFinishing()) {
            PayHornConfigBean payHornConfigBean = PayHornConfigService.get().getPayCashierHornConfigBean();
            boolean isAndroidOptimizeExperience = payHornConfigBean != null && payHornConfigBean.isAndroidOptimizeExperience();
            publishPayStatusToH5(isAndroidOptimizeExperience);
            publishPayStatusToNative(isAndroidOptimizeExperience);
            reportPayResult(isAndroidOptimizeExperience);
            publishCashierResult();
            CashierSessionIdUtil.unRegisterCashierSessionId(activityOperationId);
        } else {
            // 记录Activity仍是活跃状态，却被onDestroy时的订单信息。此时不再给业务方回调
            CashierStaticsUtils.reportSystemCheck("b_pay_bgki2c19_sc",
                    new AnalyseUtils.MapBuilder().add(KEY_PAY_CALLBACK_ACTION, CashierConstants.KEY_CASHIER_CALLBACK_RESULT + tradeNo)
                            .add(KEY_PAY_RESULT_VALUE, resultStatus)
                            .add(KEY_PAY_RESULT_EXTRA, mPayResultExtra)
                            .build(), getUniqueId());
        }
        mUniqueIdManager.unRegisterActivityLifecycleCallbacks();
        cashierRepeatCount--;
        if (isFinishing()) {
            CashierScreenSnapShotUtil.deleteCashierPic(this, tradeNo);
        }
        if (!CollectionUtils.isEmpty(subscriptions)) {
            for (Subscription subscription : subscriptions) {
                if (subscription != null && !subscription.isUnsubscribed()) {
                    subscription.unsubscribe();
                }
            }
        }
        unRegisterCashierSnapShotReceiver();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        ICashier iCashier = mCurrentCashier;
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == HalfPageMarketingUtils.OPEN_MARKETING_HALFPAGE_DIALOG_REQUEST_CODE) {
            if (routerResultSwitch && getRouterResultPageResult) {
                return;
            } else {
                handleMarketingHalfPageDialog(resultCode, data);
            }
        } else {
            if (iCashier != null) {
                iCashier.onActivityResult(requestCode, resultCode, data);
            }
        }
    }

    private void handleMarketingHalfPageDialog(int resultCode, Intent data) {
        HalfPageMarketingUtils.handleActivityResult(resultCode, data);
        onClickCouponDialogConfirm();
    }

    /**
     * 类似于引用计数，解决同时发起多个网络请求的问题
     *
     * @param cancelable
     * @param type
     * @param tip
     */
    public void showMTProgressWrapper(boolean cancelable, ProcessType type, String tip) {
        progressCount++;
        showMTProgress(cancelable, type, tip);
    }

    public void hideMTProgressWrapper() {
        if (--progressCount <= 0) {
            super.hideProgress();
        }
    }

    /**
     * 取消支付
     */
    public void onPayCancel() {
        // 删除旧的支付取消埋点，仅在给业务方回调时上报b_pay_n91dky23_sc和paybiz_pay_cashier埋点
        ToastUtils.showSnackToast(this, R.string.cashiercommon__pay_cancel);
        isCouponOutOfDate = false; //支付取消之后，变量恢复初值。
    }

    /**
     * 支付失败,
     * 该方法设置成public美团小程序需要使用该方法。
     *
     * @param failMsg
     */
    public void onPayFail(String failMsg) {
        onPayFail(failMsg, STATUS_THIRD_PARTY_FAIL);
    }

    public void onPayFail(String failMsg, int status) {
        // 删除旧的支付失败埋点，仅在给业务方回调时上报b_pay_n91dky23_sc和paybiz_pay_cashier埋点
        //TODO failMsg会不会不可理解
        if (!TextUtils.isEmpty(failMsg)) {
            ToastUtils.showSnackToast(MTCashierActivity.this, failMsg);
        }
        handlePayResultAndFinish(status);
        isCouponOutOfDate = false; //支付失败之后，变量恢复初值。
    }

    /**
     * 支付成功
     */
    public void onPaySuccess() {
        // 删除旧的支付成功埋点，仅在给业务方回调时上报b_pay_n91dky23_sc和paybiz_pay_cashier埋点
        if (promotion != null && promotion.getDynamicLayout() != null && isCouponOutOfDate) {
            AnalyseUtils.techMis("b_pay_hkk0y7f2_mv", null);
        }
        if (promotion != null && promotion.getDynamicLayout() != null && !isCouponOutOfDate) { //微信／支付宝支付后优惠弹窗，优先级高于微信免密引导
            LoganUtils.log("展示营销弹窗");
            if (HalfPageMarketingUtils.showHalfPageDialog(promotion)) {
                routerResultSwitch = PayHornConfigService.get().getPayCashierHornConfigBean().isRouterResultSwitch();
                if (routerResultSwitch) {
                    Map<String, Serializable> data = new HashMap<>();
                    data.put("promotion", promotion);
                    data.put("transId", "");
                    data.put("backgroundcolor", halfPageMarketingBackgroundColor);
                    data.put("mTradeNo", tradeNo);
                    RouterRequestData routerRequestData = new RouterRequestData();
                    routerRequestData.setBusinessData(data);
                    RouterManagerOld.open(ROUTER_TYPE_RESULT_PAGE, routerRequestData, this, null);
                } else {
                    HalfPageMarketingUtils.open(this, promotion, null, halfPageMarketingBackgroundColor, HalfPageMarketingUtils.OPEN_MARKETING_HALFPAGE_DIALOG_REQUEST_CODE);
                }
            } else {
                PaymentDialogFragment.showHybridDialog(this, promotion.getDynamicLayout(),
                        tradeNo, null, promotion.getHybridUrl(), promotion.getHybridLoadingTime(), this, R.id.content_dialog);
            }
        } else {
            handlePayResultAndFinish(STATUS_FINISH);
        }
        isCouponOutOfDate = false; //支付成功之后，变量恢复初值。
    }

    //-----------------------------------------------------------------------新路由回调-----------------------------------------------------------------------
    /*
        路由失败结果处理
     */
    public void onRouterError(String routerType, int code, String message) {
        if (TextUtils.equals(ROUTER_TYPE_RESULT_PAGE, routerType)) {
            getRouterResultPageResult = true;
            handleMarketingHalfPageDialog(RESULT_CANCELED, new Intent());
        } else if (TextUtils.equals(ROUTER_TYPE_CASHIER, routerType)) {
            onCashierPayFail("业务异常，请重试", STATUS_FINISH);
        }
    }

    /**
     * 路由成功结果处理
     *
     * @param result
     */
    public void onRouterComplete(RouterCallback.Result result) {
        if (result == null) {
            return;
        }
        if (TextUtils.equals(result.getRouterType(), ROUTER_TYPE_RESULT_PAGE)) {
            getRouterResultPageResult = true;
            Intent intent = result.getIntent();
            int code = result.getCode();
            handleMarketingHalfPageDialog(code, intent);
        } else if (TextUtils.equals(result.getRouterType(), ROUTER_TYPE_CASHIER)) {
            switch (result.getCode()) {
                case CommonCashierRouterAdapter.CODE_SUCCESS:
                    Promotion promotion = (Promotion) result.getResultData().get("promotion");
                    onCashierPaySuccess(promotion);
                    break;
                case CommonCashierRouterAdapter.CODE_FAIL:
                    String message = (String) result.getResultData().get("message");
                    onCashierPayFail(message, STATUS_FINISH);
                    break;
                case CommonCashierRouterAdapter.CODE_CANCEL:
                    onCashierCancel();
                    break;
                default:
                    break;
            }
        }
    }

    private void handleCashierRouterResult(RouterResult result) {
        if (result == null) {
            onCashierCancel(); // 如果没有支付结果，以支付取消进行处理
            return;
        }
        if (result.isSuccess()) { // 业务成功
            Promotion promotion = (Promotion) result.getData().getSerializableExtra("promotion");
            onCashierPaySuccess(promotion);
        } else if (result.isFail()) {
            onCashierPayFail(result.getMessage());
        } else if (result.isCancel()) {
            onCashierCancel();
        } else {
            // TODO 路由错误，支付取消，埋点
            onCashierCancel();
        }
    }

    //-----------------------------------------------------------------------新路由回调-----------------------------------------------------------------------

    public Promotion getPromotion() {
        return promotion;
    }

    public void setPromotion(Promotion promotion) {
        this.promotion = promotion;
    }

    public void setCouponOutOfDate(boolean isCouponOutOfDate) {
        this.isCouponOutOfDate = isCouponOutOfDate;
    }

    @Override
    public void onResume() {
        if (this instanceof MTCashierWrapperActivity) {
            super.onResume();
            return;
        }
        HashMap<String, Object> logMap = new HashMap();
        logMap.put("recordStep", getClass().getName() + " onResume");
        LoganUtils.log("CASHIER_TTI_RECORD", logMap);
        super.onResume();
    }

    @Override
    protected void onStart() {
        if (this instanceof MTCashierWrapperActivity) {
            super.onStart();
            return;
        }
        HashMap<String, Object> logMap = new HashMap();
        logMap.put("recordStep", getClass().getName() + " onStart");
        LoganUtils.log("CASHIER_TTI_RECORD", logMap);
        super.onStart();
        if (mCurrentCashier != null) {
            mCurrentCashier.onStart();
        }
    }


    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (mCurrentCashier != null) {
            mCurrentCashier.onWindowFocusChanged(hasFocus);
        }
    }

    @Override
    public final void onBackPressed() {
        AnalyseUtils.techMis("b_54855hko", null);
        if (mCurrentCashier != null) {
            if (!mCurrentCashier.onBackPressed()) {
                safeOnBackPressed();
            }
        } else {
            safeOnBackPressed();
        }
    }

    public void resetActionBar() {
        setActionBarTitle(R.string.cashiercommon__payinfo_title);
    }

    public void safeOnBackPressed() {
        try {
            super.onBackPressed();
        } catch (Exception e) {
            LoganUtils.logError("MTCashierActivity_safeOnBackPressed", e.getMessage());
            finish();
        }
    }

    public void cancelPayment() {
        setResult(Activity.RESULT_CANCELED);
        finish();
    }


    @Override
    public void onRequestSucc(int tag, Object obj) {
        if (mCurrentCashier != null) {
            mCurrentCashier.onRequestSucc(tag, obj);
        } else {
            mCashierRouter.onRequestSucc(tag, obj);
        }
    }

    @Override
    public void onRequestException(int i, Exception e) {
        if (mCurrentCashier != null) {
            if (CashierTypeConstant.CASHIERTYPE_MT_HYBRID_HALFPAGE_CASHIER.equals(mCurrentCashier.getCashierType())) {
                // 仅独立收银台消费progressCount
                progressCount--;
            }
            mCurrentCashier.onRequestException(i, e);
        } else {
            mCashierRouter.onRequestException(i, e);
        }
        if (e instanceof PayException && ((PayException) e).getCode() == 117003) {
            resultStatus = STATUS_SUCCESS;
        }
    }

    @Override
    public void onRequestFinal(int i) {
        if (!isHybridStandardCashierLoadingMerge()) {
            hideMTProgressWrapper();
        }
        if (mCurrentCashier != null) {
            mCurrentCashier.onRequestFinal(i);
        } else {
            mCashierRouter.onRequestFinal(i);
        }
    }

    @Override
    public void onRequestStart(int i) {
        if (mCurrentCashier != null) {
            ProcessType processType = mCurrentCashier.getRequestProgressType(i);
            if (processType != null) {
                showMTProgressWrapper(true, processType, null);
            }
            if (CashierTypeConstant.CASHIERTYPE_MT_HYBRID_HALFPAGE_CASHIER.equals(mCurrentCashier.getCashierType())) {
                progressCount++;
                registerCloseDialogReceiver();
            }
        } else {
            ProcessType processType = mCashierRouter.getRequestProgressType(i);
            if (processType != null) {
                PayHornConfigBean payHornConfigBean = PayHornConfigService.get().getPayCashierHornConfigBean();
                if (payHornConfigBean != null) {
                    showMTProgressWrapper(payHornConfigBean.isAndroidRouterLoadingBackEnabled(), processType, null);
                } else {
                    showMTProgressWrapper(true, processType, null);
                }
            }
        }
    }


    @Override
    public boolean isTransparent() {
        return true;
    }

    private boolean isHybridStandardCashierLoadingMerge() {
        return mCurrentCashier != null && CashierTypeConstant.CASHIERTYPE_HYBRID_STANDARD_CASHIER.equals(mCurrentCashier.getCashierType());
    }

    /**
     * 支付成功以后优惠信息弹窗dismiss之后的附加逻辑处理
     */
    @Override
    public void onClickCouponDialogConfirm() {
        handlePayResultAndFinish(STATUS_FINISH);
    }

    /**
     * 检查uri传递过来的参数异常后埋点与cat统计，该方法内部包含SLA失败的埋点，调用该方法时需引起重视
     *
     * @param uri
     * @param errorMsg
     */
    public void analyseUriParamError(Uri uri, String errorMsg) {
        CashierStaticsUtils.reportSystemCheck("b_VHR5n", new AnalyseUtils.InstantReportBuilder().addTradeNo().add("message", errorMsg).build(), getUniqueId());
        NovaCodeLog.e(this.getClass(), "cashier_empty_params", errorMsg);
        AnalyseUtils.techMis("b_pay_skhqxqct_mv", new AnalyseUtils.MapBuilder()
                .add("uri:", uri != null ? uri.toString() : "").add("message", errorMsg).build());
    }

    // 订制actionBar标题居中,
    @SuppressLint("InflateParams")
    private void setCustomActionBar() {
        ActionBar.LayoutParams layoutParams = new ActionBar.LayoutParams(ActionBar.LayoutParams.MATCH_PARENT,
                ActionBar.LayoutParams.WRAP_CONTENT, Gravity.CENTER);
        View actionBarView = LayoutInflater.from(this).inflate(R.layout.cashiercommon__custom_actionbar, null);
        mActionbarTitleView = actionBarView.findViewById(R.id.cashier_actionbar_title);
        ImageView actionBarBack = actionBarView.findViewById(R.id.cashier_action_back);
        ActionBar actionBar = getSupportActionBar();
        try {
            actionBar.setCustomView(actionBarView, layoutParams);
            actionBar.setDisplayOptions(ActionBar.DISPLAY_SHOW_CUSTOM);
            actionBarBack.setOnClickListener(view -> onBackPressed());
            setActionBarTitle(R.string.cashiercommon__payinfo_title);
            // 订制actionBar view后两边会有16dp留白所以使用该方法移除掉留白
            if (actionBarView.getParent() instanceof Toolbar) {
                Toolbar parent = (Toolbar) actionBarView.getParent();
                parent.setContentInsetsAbsolute(0, 0);
            }
        } catch (Exception e) {
            LoganUtils.logError("MTCashierActivity_setCustomActionBar", e.getMessage());
        }
    }

    // 设置导航栏和状态栏的背景颜色
    public void setActionBarAndStatusBarBackground() {
        setStatusBarColor(getResources().getColor(R.color.cashiercommon__bg_gray));
        getSupportActionBar().setBackgroundDrawable(getResources().getDrawable(R.color.cashiercommon__bg_gray));
        // 去除导航栏的底部阴影
        getSupportActionBar().setElevation(0);
    }

    // 设置状态栏背景色
    private void setStatusBarColor(int color) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                Window window = getWindow();
                window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
                window.setStatusBarColor(color);
                // 设置状态栏文字颜色为深色, 该方法最低支持到M, 设置状态栏颜色最低支持LOLLIPOP
                getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            } else {
                // doNothing 其它版本使用默认的状态栏颜色
            }
        } catch (Exception e) {
            LoganUtils.logError("MTCashierActivity_setStatusBarColor", e.getMessage());
        }
    }

    public void setActionBarTitle(int resId) {
        mActionbarTitleView.setText(resId);
    }

    public void setActionBarTitle(String actionBarTitle) {
        mActionbarTitleView.setText(actionBarTitle);
    }

    public void setActionBarTitleStyle(float size) {
        mActionbarTitleView.setTextSize(size);
    }

    @Override
    public void onCashierPaySuccess(Promotion promotion) {
        this.mPayFinished = true;
        if (promotion != null) {
            this.promotion = promotion;
        }
        if (!CashierRepeatDownGradeSwitchManager.downGrade()) {
            if (mCurrentCashier != null) {
                mCurrentCashier.onDestroy(true);
                mCurrentCashier = null;
            }
        }
        unRegisterCashierSnapShotReceiver();
        if (mSnapShotBitmap != null && !mSnapShotBitmap.isRecycled() && !CashierRepeatDownGradeSwitchManager.downGrade()) {
            Subscription subscription = CashierScreenSnapShotUtil.saveToDisk(this, mSnapShotBitmap, tradeNo);
            if (subscription != null) {
                subscriptions.add(subscription);
            }
            ViewCompat.setBackground(getWindow().getDecorView(), new BitmapDrawable(mSnapShotBitmap));
        }
        resultStatus = STATUS_SUCCESS;
        onPaySuccess();
        IntentLargeObjectTransaction.clear();
    }


    private void unRegisterCashierSnapShotReceiver() {
        if (mOnBitmapReadyListener != null) {
            CashierScreenSnapShotUtil.unRegisterBitmapReadyListener(mOnBitmapReadyListener);
        }
    }

    private void registerCashierSnapShotReceiver() {
        if (mOnBitmapReadyListener == null) {
            mOnBitmapReadyListener = new MyOnBitmapReadyListener(new WeakReference<>(this));
        }
        CashierScreenSnapShotUtil.unRegisterBitmapReadyListener(mOnBitmapReadyListener);
        CashierScreenSnapShotUtil.registerBitmapReadyListener(mOnBitmapReadyListener);
    }

    @Override
    public void onCashierPayFail(String message) {
        this.mPayFinished = true;
        resultStatus = STATUS_FAIL;
        onPayFail(message);
        IntentLargeObjectTransaction.clear();
    }

    public void onCashierPayFail(String message, int status) {
        this.mPayFinished = true;
        resultStatus = STATUS_FAIL;
        onPayFail(message, status);
        IntentLargeObjectTransaction.clear();
    }

    @Override
    public void onCashierCancel() {
        this.mPayFinished = true;
        resultStatus = STATUS_CANCEL;
        cancelPayment();

        IntentLargeObjectTransaction.clear();
    }

    private void requestPreDispatcher() {
        if (mCashierRouter == null) {
            // TODO 灵犀、Cat 埋点
            return;
        }
        mCashierRouter.requestPreDispatcher(MTCashierActivity.this::onCashierReady);
    }

    @Override
    public void onCashierTechDowngrade(@CashierTypeConstant.CashierType String sourceCashierType, @CashierTypeConstant.CashierType String destCashierType, String info) {
        if (mCurrentCashier != null) {
            mCurrentCashier.onDestroy(true);
            // 必须要置空，否则下面请求 predispatcher 接口时，cashierRouter 收不到回调
            mCurrentCashier = null;
        }
        // 下面两行代码的顺序不能颠倒
        String currentProductType = mCashierRouter.getCurrentProductType();
        mCurrentCashier = mCashierRouter.onCashierTechDowngrade(sourceCashierType, destCashierType, info);
        if (mCurrentCashier != null) {
            Map<String, Object> params = new HashMap<>();
            params.put(CashierConstants.LAST_RESUMED_FEATURE, mLastResumedFeature);
            params.put("flow_source", "tech_degrade");
            if (!TextUtils.isEmpty(sourceCashierType)) {
                params.put("from_cashier", sourceCashierType);
            }
            if (!TextUtils.isEmpty(currentProductType)) {
                params.put("from_product_type", currentProductType);
            }
            params.put("uniqueId", getUniqueId());
            if (!isRestore) {
                params.put("cashier_router_start_time", mEnterTime);
            }
            mSpecialCashierEnterTime = System.currentTimeMillis();
            registerCashierSnapShotReceiver();
            mCurrentCashier.start(null, params);
        }
    }

    @Override
    public void onCashierBusinessDowngrade(@CashierTypeConstant.CashierType String sourceCashierType,
                                           @ProductTypeConstant.ProductType String destProductType, String info) {
        if (mCurrentCashier != null) {
            mCurrentCashier.onDestroy(true);
            // 必须要置空，否则下面请求 predispatcher 接口时，cashierRouter 收不到回调
            mCurrentCashier = null;
        }
        // 请求 predispatcher 接口
        if (TextUtils.equals(destProductType, ProductTypeConstant.OTHER)) {
            requestPreDispatcher();
            return;
        }
        // 下面两行代码的顺序不能颠倒
        String currentProductType = mCashierRouter.getCurrentProductType();
        mCurrentCashier = mCashierRouter.onCashierBusinessDowngrade(sourceCashierType, destProductType, info);
        if (mCurrentCashier != null) {
            Map<String, Object> params = new HashMap<>();
            params.put(CashierConstants.LAST_RESUMED_FEATURE, mLastResumedFeature);
            params.put("flow_source", "business_degrade");
            if (!TextUtils.isEmpty(sourceCashierType)) {
                params.put("from_cashier", sourceCashierType);
            }
            if (!TextUtils.isEmpty(currentProductType)) {
                params.put("from_product_type", currentProductType);
            }
            params.put("uniqueId", getUniqueId());
            if (!isRestore) {
                params.put("cashier_router_start_time", mEnterTime);
            }
            mSpecialCashierEnterTime = System.currentTimeMillis();
            registerCashierSnapShotReceiver();
            mCurrentCashier.start(null, params);
        }
    }

    public ICashier getCurrentCashier() {
        return mCurrentCashier;
    }

    // todo @dudongxu 看一下
    @Override
    public void onPasswordInsert(String s, PasswordExceptionHandler passwordExceptionHandler) {
        if (mCurrentCashier instanceof OnPasswordInsertListener) {
            ((OnPasswordInsertListener) mCurrentCashier).onPasswordInsert(s, passwordExceptionHandler);
        }
    }

    @Override
    public void preJumpIntoThirdPaySDK() {
        if (useNewRouter) {
            RouterManager.helper(cashierRouterTrace).observeOn(PrePayerExecute.class).dispatch(false).preJumpIntoThirdPaySDK();
        }
        if (mCurrentCashier instanceof PrePayerExecute) {
            ((PrePayerExecute) mCurrentCashier).preJumpIntoThirdPaySDK();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            AnalyseUtils.techMis("b_pay_mbv58hmk_mc", null);
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
     * 注册关闭dialog的广播
     */
    public void registerCloseDialogReceiver() {
        mCloseDialogReceiver = new CloseDialogReceiver();
        // 注册关闭唤起独立收银台时的loading的广播，与PayActivity中发送广播的action保持一致
        LocalBroadCastUtil.registerBroadCast(this, "com.meituan.android.pay.activity.MTProcessDialog.close.action", mCloseDialogReceiver);
    }

    /**
     * 通过 KNB 回调 H5 页面
     * https://km.sankuai.com/page/429450438
     * https://km.sankuai.com/page/433460516
     * https://km.sankuai.com/page/28302746
     *
     */
    private void publishPayStatusToH5(boolean isAndroidOptimizeExperience) {
        try {
            if (TextUtils.isEmpty(tradeNo)) {
                CatUtils.logError("MTCashierActivity", "publishPayStatusToH5 tradeNo is null");
                return;
            }
            JSONObject result = new JSONObject();
            result.put(KEY_PAY_CALLBACK_ACTION, CashierConstants.KEY_CASHIER_CALLBACK_RESULT + tradeNo);
            result.put(KEY_PAY_RESULT_VALUE, resultStatus);
            if (!TextUtils.isEmpty(mPayResultExtra)) {
                result.put(isAndroidOptimizeExperience ? KEY_PAY_RESULT_EXTRA : KEY_PAY_EXTRA_DATA, mPayResultExtra);
            }
            if (TextUtils.equals(resultStatus, STATUS_FAIL)) {
                result.put(KEY_ERROR_CODE, getErrorCodeOrDefault());
                result.put(KEY_ERROR_MSG, getErrorMessageOrDefault());
            }
            JsHandlerFactory.publish(result);
        } catch (Exception e) {
            CatUtils.logError("MTCashierActivity", "publishPayStatusToH5");
        }
    }

    private void publishPayStatusToNative(boolean isAndroidOptimizeExperience) {
        try {
            if (TextUtils.isEmpty(tradeNo)) {
                CatUtils.logError("MTCashierActivity", "publishPayStatusToNative tradeNo is null");
                return;
            }
            Intent result = new Intent(isAndroidOptimizeExperience ? CashierConstants.KEY_CASHIER_CALLBACK_RESULT + tradeNo : CashierConstants.KEY_CASHIER_CALLBACK_RESULT_NATIVE + tradeNo);
            result.putExtra(KEY_PAY_RESULT_VALUE, resultStatus);
            if (!TextUtils.isEmpty(mPayResultExtra)) {
                result.putExtra(isAndroidOptimizeExperience ? KEY_PAY_RESULT_EXTRA : KEY_PAY_EXTRA_DATA, mPayResultExtra);
            }
            if (TextUtils.equals(resultStatus, STATUS_FAIL)) {
                result.putExtra(KEY_ERROR_CODE, getErrorCodeOrDefault());
                result.putExtra(KEY_ERROR_MSG, getErrorMessageOrDefault());
            }
            LocalBroadcastManager.getInstance(this).sendBroadcast(result);
        } catch (Exception e) {
            CatUtils.logError("MTCashierActivity", "publishPayStatusToNative");
        }
    }

    /**
     * 上报支付回调
     */
    private void reportPayResult(boolean isAndroidOptimizeExperience) {
        HashMap<String, Object> result = new HashMap<>();
        result.put(KEY_PAY_CALLBACK_ACTION, CashierConstants.KEY_CASHIER_CALLBACK_RESULT + tradeNo);
        result.put(KEY_PAY_RESULT_VALUE, resultStatus);
        if (!TextUtils.isEmpty(mPayResultExtra)) {
            result.put(isAndroidOptimizeExperience ? KEY_PAY_RESULT_EXTRA : KEY_PAY_EXTRA_DATA, mPayResultExtra);
        }
        int code = CODE_PAY_CANCEL;
        if (TextUtils.equals(resultStatus, STATUS_FAIL)) {
            code = CODE_PAY_FAIL;
            result.put(KEY_ERROR_CODE, getErrorCodeOrDefault());
            result.put(KEY_ERROR_MSG, getErrorMessageOrDefault());
        } else if (TextUtils.equals(resultStatus, STATUS_SUCCESS)) {
            code = CODE_PAY_SUCCESS;
        }
        CashierStaticsUtils.reportSystemCheck("b_pay_n91dky23_sc", result, getUniqueId());
        CatUtils.logRate(CashierCatConstants.ACTION_PAY_CASHIER, code);
    }

    private String getErrorCodeOrDefault() {
        return TextUtils.isEmpty(errorCode) ? ERROR_CODE_DEFAULT : errorCode;
    }

    private String getErrorMessageOrDefault() {
        return TextUtils.isEmpty(errorMessage) ? ERROR_MSG_DEFAULT : errorMessage;
    }

    private void publishCashierResult() {
        CashierResult.newResult(resultStatus)
                .setTradeNo(tradeNo)
                .setUniqueId(cashierUniqueId).build().publish();
    }

    public void setResultStatus(String resultStatus) {
        this.resultStatus = resultStatus;
    }

    @Override
    public String getExtraData() {
        return extraData;
    }

    @Override
    public String getExtraStatics() {
        return extraStatics;
    }

    @Override
    public String getTradeNo() {
        return tradeNo;
    }

    @Override
    public String getMerchantNo() {
        return mMerchantNo;
    }

    public void setPayResultExtra(String payResultExtra) {
        this.mPayResultExtra = payResultExtra;
    }

    /**
     * Activity onCreate的时候，将lastResumedFeature存储下来，后续都使用这个参数
     *
     * @return LastResumedFeature
     */
    public String getLastResumedFeature() {
        if (TextUtils.isEmpty(mLastResumedFeature)) {
            mLastResumedFeature = getIntent().getStringExtra(LAST_RESUMED_PAGE_KEY);
            if (TextUtils.isEmpty(mLastResumedFeature)) {
                mLastResumedFeature = HybridCashierHooker.getLastResumedFeature();
            }
            if (TextUtils.isEmpty(mLastResumedFeature)) {
                return UNKNOWN;
            }
            return mLastResumedFeature;
        }
        return mLastResumedFeature;
    }

    @Override
    public HashMap<String, String> getExtendTransmissionParams() {
        HashMap<String, String> extendTransmissionParams = new HashMap<>();
        if (!TextUtils.isEmpty(mCif) && !TextUtils.equals(VALUE_NULL, mCif.toLowerCase())) {
            extendTransmissionParams.put("cif", mCif);
        }
        return extendTransmissionParams;
    }

    @Override
    public String getActivityTag() {
        // PayRequestUtils 中有此判断
        return "MTCashierActivity";
    }

    public void setErrorCodeAndErrorMessage(String errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    @Override
    public void setActivityForCashier() {
        setActionBarAndStatusBarBackground();
    }

    private static class MyOnBitmapReadyListener implements CashierScreenSnapShotUtil.OnBitmapReadyListener {
        private final WeakReference<MTCashierActivity> weakReference;

        public MyOnBitmapReadyListener(WeakReference<MTCashierActivity> weakReference) {
            this.weakReference = weakReference;
        }

        @Override
        public void onBitmapReady(Bitmap bitmap) {
            MTCashierActivity cashierActivity = weakReference.get();
            if (cashierActivity == null || cashierActivity.isFinishing()) {
                return;
            }
            try {
                View decorView = cashierActivity.getWindow().getDecorView();
                cashierActivity.mSnapShotBitmap = bitmap;
                ViewCompat.setBackground(decorView, new BitmapDrawable(bitmap));
            } catch (Exception exception) {
                CashierStaticsUtils.logCustom("paybiz_cashier_snapshot_error", null, null, cashierActivity.getUniqueId());
                LoganUtils.logError("MTCashierActivity_MyOnBitmapReadyListener_onBitmapReady", exception.getMessage());
            }
        }
    }

    class CloseDialogReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            hideProgress();
            cleanProgressCount();
        }
    }
}
