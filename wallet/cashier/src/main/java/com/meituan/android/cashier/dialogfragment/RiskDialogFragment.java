package com.meituan.android.cashier.dialogfragment;

import static com.meituan.android.cashier.retrofit.CashierReqTagConstant.REQ_TAG_PAY_ORDER;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.dianping.titans.utils.Constants;
import com.meituan.android.cashier.R;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.fragment.MTCashierRevisionFragment;
import com.meituan.android.cashier.model.bean.PayResult;
import com.meituan.android.cashier.model.bean.PopUp;
import com.meituan.android.cashier.retrofit.CashierRequestUtils;
import com.meituan.android.paybase.common.fragment.MTPayBaseDialogFragment;
import com.meituan.android.paybase.dialog.BaseDialog;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.StatisticsUtils;
import com.meituan.android.paybase.utils.SystemInfoUtils;
import com.meituan.android.paybase.utils.UriUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;

/**
 * 风控弹窗对应的Fragment
 */
public class RiskDialogFragment extends MTPayBaseDialogFragment implements IRequestCallback {
    // 常量
    private static final String TYPE_A = "riskHint"; // 风险提示弹窗（A类）
    private static final String TYPE_B = "riskVerify"; // 风险验证弹窗（B类）
    private static final String SUB_TYPE_A = "origin"; // 风险提示弹窗（A类子类型）
    private static final String SUB_TYPE_B_VERIFY_CENTER = "verifyCenter"; // 风险验证-人脸验证弹窗（B类子类型）
    private static final String SUB_TYPE_B_QUESTION = "question"; // 风险验证-问题验证弹窗（B类子类型）
    private static final String VERIFY_TYPE_A = "0"; // 验证方式 - 风险提示弹窗（A类）
    private static final String VERIFY_B_VERIFY_CENTER = "1"; // 验证方式 - 风险验证-人脸验证弹窗（B类）
    private static final String VERIFY_B_QUESTION = "2"; // 验证方式 - 风险验证-问题验证弹窗（B类）
    private static final String VERIFY_RESULT_SUCCESS = "1"; // 验证结果-验证成功
    private static final String VERIFY_RESULT_FAIL = "2"; // 验证结果-验证失败
    private static final int REQUEST_CODE_VERIFY_CENTER = 66; // 人脸验证请求码
    private static final int REQUEST_CODE_QUESTION = 88; // 问题验证请求码
    private static final int RESULT_CODE_VERIFY_SUCCESS = 10; // 人脸验证或者问题验证通过

    // bundle交互数据
    private static final String PARAM_PAY_RESULT = "pay_result_bean";

    private PayResult mPayResult;
    private PopUp mPopup;
    private String mPayType;

    private IRequestCallback mRequestCallback;
    private MTCashierRevisionFragment parentFragment;

    // 风险弹窗埋点的业务参数的key
    private static final String KEY_PAY_ORDER_ID = "pay_order_id"; // 和后端下发的orderId一致
    private static final String KEY_POP_SCENE = "pop_scene"; // 拼接后端下发的type_subtype
    private static final String KEY_BUTTON_NAME = "button_name"; // 和后端下发的按钮名称一致，只上报【继续支付】和【申请解除限制】按钮

    // 灵犀埋点上报参数
    public HashMap<String, Object> technologyParameters;

    // Raptor埋点上报参数
    public HashMap<String, Object> customTags;

    public static RiskDialogFragment newInstance(PayResult payResult) {
        RiskDialogFragment fragment = new RiskDialogFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(PARAM_PAY_RESULT, payResult);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        if (parentFragment != null) {
            mRequestCallback = (IRequestCallback) parentFragment;
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
    }

    @Override
    protected String getTAG() {
        return null;
    }

    @Override
    protected BaseDialog createDialog(Bundle savedInstanceState) {
        setCancelable(false);
        return new RiskDialog(getContext(), mPopup, mPayType);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            Bundle bundle = getArguments();
            mPayResult = (PayResult) bundle.getSerializable(PARAM_PAY_RESULT);
            if (mPayResult != null) {
                mPopup = mPayResult.getPopUp();
                mPayType = mPayResult.getPayType();
            }
        }
        parentFragment = (MTCashierRevisionFragment) getParentFragment();
        addBusinessParameters();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        dismiss();
        if (requestCode == REQUEST_CODE_VERIFY_CENTER) {
            if (resultCode == RESULT_CODE_VERIFY_SUCCESS && data != null) {
                String result = data.getStringExtra(Constants.SET_RESULT_KEY);
                try {
                    JSONObject json = new JSONObject(result);
                    String verifyToken = json.getString("payToken");
                    if (TextUtils.isEmpty(verifyToken)) {
                        verifyFail(mPayType, mPopup.getOrderId(), VERIFY_B_VERIFY_CENTER);
                    } else {
                        verifySuccess(mPayType, mPopup.getOrderId(), VERIFY_B_VERIFY_CENTER, verifyToken);
                    }
                } catch (JSONException e) {
                    verifyFail(mPayType, mPopup.getOrderId(), VERIFY_B_VERIFY_CENTER);
                    LoganUtils.logError("RiskDialogFragment_onActivityResult", e.getMessage());
                }
            } else {
                verifyFail(mPayType, mPopup.getOrderId(), VERIFY_B_VERIFY_CENTER);
            }
        } else if (requestCode == REQUEST_CODE_QUESTION) {
            if (resultCode == RESULT_CODE_VERIFY_SUCCESS) {
                verifySuccess(mPayType, mPopup.getOrderId(), VERIFY_B_QUESTION, null);
            } else {
                verifyFail(mPayType, mPopup.getOrderId(), VERIFY_B_QUESTION);
            }
        } else {
            verifyFail(mPayType, mPopup.getOrderId(), VERIFY_B_QUESTION);
        }
    }

    private void startDirectPay() {
        if (parentFragment != null) {
            HashMap<String, String> map = CashierRequestUtils
                    .getDirectPayMap(parentFragment.genRiskVerifyParams(), SystemInfoUtils.getIMSI(getActivity()));
            parentFragment.startDirectPay(map);
        }
    }

    private void openUrl(int requestCode) {
        reportRiskVerifyStart();
        UriUtils.openForResult(this, mPopup.getUrl(), requestCode);
    }

    public void setVerifyFail(String verifyPayType, String verifyPayOrderId, String verifyType) {
        parentFragment.setRiskVerifyParams(verifyPayType, verifyPayOrderId, verifyType, VERIFY_RESULT_FAIL, null);
    }

    public void setVerifySuccess(String verifyPayType, String verifyPayOrderId, String verifyType, String verifyToken) {
        parentFragment.setRiskVerifyParams(verifyPayType, verifyPayOrderId, verifyType, VERIFY_RESULT_SUCCESS, verifyToken);
    }

    public void verifyFail(String verifyPayType, String verifyPayOrderId, String verifyType){
        setVerifyFail(verifyPayType, verifyPayOrderId, verifyType);
        reportRiskVerifyFail();
    }

    public void verifySuccess(String verifyPayType, String verifyPayOrderId, String verifyType, String verifyToken) {
        setVerifySuccess(verifyPayType, verifyPayOrderId, verifyType, verifyToken);
        reportRiskVerifySuccess();
        startDirectPay();
    }

    /**
     * 添加埋点上报必传参数
     */
    public void addBusinessParameters() {
        technologyParameters = CashierStaticsUtils.getTechnologyParameters();
        customTags = new HashMap<>();
        if (!TextUtils.isEmpty(mPopup.getOrderId())) {
            technologyParameters.put(KEY_PAY_ORDER_ID, mPopup.getOrderId());
        }
        if (!TextUtils.isEmpty(getPopScene())) {
            technologyParameters.put(KEY_POP_SCENE, getPopScene());
            customTags.put(KEY_POP_SCENE, getPopScene());
        }
    }

    /**
     * 获取风险弹窗类型
     * @return 风险弹窗类型
     */
    public String getPopScene() {
        if (TextUtils.isEmpty(mPopup.getType()) || TextUtils.isEmpty(mPopup.getSubtype())) {
            return "";
        }
        return mPopup.getType() + "_" + mPopup.getSubtype();
    }

    /**
     * 上报风险验证弹窗开始埋点，仅Raptor埋点
     */
    private void reportRiskVerifyStart() {
        CashierStaticsUtils.logCustom("thirdpay_riskverify_start", customTags, null, getUniqueId());
    }

    /**
     * 上报风险验证弹窗验证成功埋点，包括灵犀和Raptor埋点
     */
    private void reportRiskVerifySuccess() {
        CashierStaticsUtils.reportSystemCheck("b_pay_risk_check_success_sc", technologyParameters, getUniqueId());
        CashierStaticsUtils.logCustom("thirdpay_riskverify_success", customTags, null, getUniqueId());
    }

    /**
     * 上报风险验证弹窗验证失败埋点，仅Raptor埋点
     */
    private void reportRiskVerifyFail() {
        CashierStaticsUtils.logCustom("thirdpay_riskverify_fail", customTags, null, getUniqueId());
    }

    @Override
    public void onRequestSucc(int tag, Object obj) {
        if (tag == REQ_TAG_PAY_ORDER && mRequestCallback != null) {
            mRequestCallback.onRequestSucc(tag, obj);
        }
    }

    @Override
    public void onRequestException(int tag, Exception e) {
        if (tag == REQ_TAG_PAY_ORDER && mRequestCallback != null) {
            mRequestCallback.onRequestException(tag, e);
        }
    }

    @Override
    public void onRequestFinal(int tag) {
        if (tag == REQ_TAG_PAY_ORDER && mRequestCallback != null) {
            mRequestCallback.onRequestFinal(tag);
        }
    }

    @Override
    public void onRequestStart(int tag) {
        if (tag == REQ_TAG_PAY_ORDER && mRequestCallback != null) {
            mRequestCallback.onRequestStart(tag);
        }
    }

    /**
     * 风控弹窗Dialog
     */
    public class RiskDialog extends BaseDialog {

        private PopUp mPopup;
        private String mPayType;

        public RiskDialog(Context context, PopUp popUp, String payType) {
            super(context, R.style.mpay__transparent_dialog);
            this.mPopup = popUp;
            this.mPayType = payType;
            intiView();
        }

        private void intiView() {
            reportRiskDialogShowed();
            setContentView(R.layout.cashier__risk_dialog);
            TextView title = findViewById(R.id.risk_dialog_title);
            title.setText(mPopup.getTitle());
            TextView body = findViewById(R.id.risk_dialog_body);
            body.setText(mPopup.getBody());
            if (TextUtils.equals(mPopup.getType(), TYPE_A) && TextUtils.equals(mPopup.getSubtype(), SUB_TYPE_A)) {
                // 风险提示弹窗（A类）的左按钮是继续支付，右按钮是取消支付
                TextView leftButton = findViewById(R.id.risk_dialog_left_button);
                leftButton.setText(mPopup.getConfirmButton());
                TextView rightButton = findViewById(R.id.risk_dialog_right_button);
                rightButton.setText(mPopup.getCancelButton());
                // 风险提示弹窗（A类）点击继续支付后，弹窗直接消失，并重新请求directpay接口
                leftButton.setOnClickListener(v -> {
                    reportRiskDialogClickConfirmButton();
                    setVerifySuccess(mPayType, mPopup.getOrderId(), VERIFY_TYPE_A, null);
                    dismiss();
                    startDirectPay();
                });
                // 风险提示弹窗（A类）点击取消支付后，弹窗直接消失，返回收银台首页
                rightButton.setOnClickListener(v -> {
                    setVerifyFail(mPayType, mPopup.getOrderId(), VERIFY_TYPE_A);
                    dismiss();
                });
            } else if (TextUtils.equals(mPopup.getType(), TYPE_B)) {
                // 风险验证弹窗（B类）的左按钮是取消支付，右按钮是申请解除限制
                TextView leftButton = findViewById(R.id.risk_dialog_left_button);
                leftButton.setText(mPopup.getCancelButton());
                TextView rightButton = findViewById(R.id.risk_dialog_right_button);
                rightButton.setText(mPopup.getConfirmButton());
                // 风险验证弹窗（B类）点击取消支付后，弹窗直接消失，返回收银台首页
                leftButton.setOnClickListener(v -> {
                    if (TextUtils.equals(mPopup.getSubtype(), SUB_TYPE_B_VERIFY_CENTER)) {
                        setVerifyFail(mPayType, mPopup.getOrderId(), VERIFY_B_VERIFY_CENTER);
                    } else if (TextUtils.equals(mPopup.getSubtype(), SUB_TYPE_B_QUESTION)) {
                        setVerifyFail(mPayType, mPopup.getOrderId(), VERIFY_B_QUESTION);
                    }
                    dismiss();
                });
                // 风险验证弹窗（B类）点击申请解除限制后，跳转到对应页面，此时弹窗仅隐藏
                rightButton.setOnClickListener(v -> {
                    reportRiskDialogClickConfirmButton();
                    if (TextUtils.equals(mPopup.getSubtype(), SUB_TYPE_B_VERIFY_CENTER)) {
                        openUrl(REQUEST_CODE_VERIFY_CENTER);
                    } else if (TextUtils.equals(mPopup.getSubtype(), SUB_TYPE_B_QUESTION)) {
                        openUrl(REQUEST_CODE_QUESTION);
                    }
                    hideDialogInternal();
                });
            }
        }

        /**
         * 隐藏弹窗，参考跳转月付半页的隐藏弹窗逻辑
         */
        private void hideDialogInternal() {
            findViewById(R.id.risk_dialog_content).setVisibility(View.GONE);
            if (getWindow() != null) {
                getWindow().setDimAmount(0f);
            }
            new Handler().post(() -> getDialogFragment().hideDialog());
        }

        /**
         * 上报风险弹窗曝光的MV埋点
         */
        private void reportRiskDialogShowed() {
            CashierStaticsUtils.logModelEvent("c_PJmoK", "b_pay_htzirx3b_mv", "收银台首页-风险弹窗",
                    technologyParameters, StatisticsUtils.EventType.VIEW, getUniqueId());
        }

        /**
         * 上报风险弹窗点击继续支付或申请解除限制按钮的MC埋点
         */
        private void reportRiskDialogClickConfirmButton() {
            technologyParameters.put(KEY_BUTTON_NAME, mPopup.getConfirmButton());
            CashierStaticsUtils.logModelEvent("c_PJmoK", "b_pay_htzirx3b_mc", "收银台首页-风险弹窗点击继续支付或申请解除限制按钮",
                    technologyParameters, StatisticsUtils.EventType.CLICK, getUniqueId());
        }
    }
}
