package com.meituan.android.cashier.exception;

import com.meituan.android.cashier.NativeStandardCashierAdapter;
import com.meituan.android.cashier.R;
import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.common.ICashier;
import com.meituan.android.cashier.model.PayErrorCode;
import com.meituan.android.paybase.common.dialog.PayDialog;
import com.meituan.android.paybase.retrofit.PayException;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paycommon.lib.exception.BaseExceptionHandler;
import com.meituan.android.paycommon.lib.utils.ExceptionUtils;

public abstract class GoHelloPayAndDirectPayExceptionHandler extends BaseExceptionHandler {

    private static final String TECH_TAG = MTCashierActivity.TECH_TAG;
    private static final int STATUS_FINISH = MTCashierActivity.STATUS_FINISH;
    private MTCashierActivity mtCashierActivity;

    protected GoHelloPayAndDirectPayExceptionHandler(MTCashierActivity activity) {
        super(activity);
        mtCashierActivity = activity;
    }

    /**
     * 做为埋点使用
     */
    protected final String getPayType() {
        ICashier iCashier = mtCashierActivity.getCurrentCashier();
        if (iCashier instanceof NativeStandardCashierAdapter) {
            return ((NativeStandardCashierAdapter) iCashier).getPayType();
        }
        return "";
    }

    @Override
    public void handleException(Exception exception) {
        techBeforeHandleException(exception);
        if (exception instanceof PayException) {
            dealPayException((PayException) exception);
        } else {
            super.handleException(exception);
        }
    }

    protected abstract void techBeforeHandleException(Exception exception);

    @Override
    protected void handleNoPayException(Exception exception) {
        showToast(R.string.cashier__error_msg_pay_later);
        LoganUtils.logError("GoHelloPayAndDirectPayExceptionHandler_handleNoPayException", exception.getMessage());
    }

    private void dealPayException(PayException exception) {
        int errorCode = exception.getCode();
        String errorMsg = exception.getMessage();
        LoganUtils.logError("GoHelloPayAndDirectPayExceptionHandler_dealPayException", errorMsg + errorCode);
        switch (errorCode) {
            case PayErrorCode.NEED_VERIFY_SMS_CODE: //进入短信验证页面
                showToast(R.string.cashier__error_msg_pay_later);
                break;
            case PayErrorCode.ALREADY_PAYED:
                new PayDialog.Builder(mtCashierActivity)
                        .msg(errorMsg)
                        .subMsg(exception.getErrorCodeStr())
                        .rightBtn(PayDialog.DEFAULT_BUTTON_TEXT, (dialog) -> mtCashierActivity.handlePayResultAndFinish(STATUS_FINISH))
                        .build().show();
                break;
            default:
                super.handleException(exception);
                break;
        }
    }

    @Override
    protected void handleLevel2(PayException payException) {
        ExceptionUtils.alertAndFinish(activity, payException.getMessage(), payException.getErrorCodeStr(), MTCashierActivity.class);
    }
}
