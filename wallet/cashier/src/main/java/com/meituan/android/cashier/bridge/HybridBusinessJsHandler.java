package com.meituan.android.cashier.bridge;

import com.meituan.android.neohybrid.base.jshandler.NeoWrapperJsHandler;
import com.meituan.android.neohybrid.neo.report.NeoReport;

import java.util.HashMap;
import java.util.Map;

/**
 * author:  kkli
 * date:    2020-11-08
 * description:
 */
public abstract class HybridBusinessJsHandler extends NeoWrapperJsHandler {

    // 埋点
    protected void logSC(String bid, Map<String, Object> lab) {
        NeoReport.logSCEvent(getNeoCompat(), bid, lab);
    }

    protected void logMV(String bid, String cid, Map<String, Object> lab) {
        NeoReport.logMVEvent(getNeoCompat(), bid, cid, null, lab);
    }

    protected void logMC(String bid, String cid, Map<String, Object> lab) {
        NeoReport.logMCEvent(getNeoCompat(), bid, cid, null, lab);
    }

    protected void logCat(String command, Map<String, Object> lab) {
        if (lab == null) {
            lab = new HashMap<>();
            NeoReport.logCustom(getNeoCompat(), command, lab);
        }
    }

    public static void logSC(HybridBusinessJsHandler jsHandler, String bid, Map<String, Object> lab) {
        if (jsHandler == null) {
            return;
        }
        jsHandler.logSC(bid, lab);
    }

    public static void logMV(HybridBusinessJsHandler jsHandler, String bid, String cid, Map<String, Object> lab) {
        if (jsHandler == null) {
            return;
        }
        jsHandler.logMV(bid, cid, lab);
    }

    public static void logMC(HybridBusinessJsHandler jsHandler, String bid, String cid, Map<String, Object> lab) {
        if (jsHandler == null) {
            return;
        }
        jsHandler.logMC(bid, cid, lab);
    }

    public static void logCat(HybridBusinessJsHandler jsHandler, String command, Map<String, Object> lab) {
        if (jsHandler == null) {
            return;
        }
        jsHandler.logCat(command, lab);
    }
}
