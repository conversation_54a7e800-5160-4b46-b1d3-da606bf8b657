package com.meituan.android.cashier.bridge.icashier;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.support.v7.app.AppCompatActivity;

import com.meituan.android.cashier.R;
import com.meituan.android.cashier.common.HybridReportConstants;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.model.params.PayParams;
import com.meituan.android.cashier.utils.NativeStandardCashierPayProcessStatistic;
import com.meituan.android.neohybrid.neo.report.MapBuilder;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.pay.common.payment.bean.Payment;
import com.meituan.android.pay.common.payment.data.IBankcardData;
import com.meituan.android.pay.common.payment.data.IPaymentData;
import com.meituan.android.pay.common.selectdialog.IPaymentListPage;
import com.meituan.android.pay.common.selectdialog.bean.WalletPaymentListPage;
import com.meituan.android.pay.common.selectdialog.utils.SelectBankAnalyseUtils;
import com.meituan.android.pay.common.selectdialog.view.SelectBankDialog;
import com.meituan.android.pay.common.selectdialog.view.SelectBankDialogFragment;
import com.meituan.android.pay.desk.pack.WalletPayManager;
import com.meituan.android.pay.desk.payment.bean.standarddesk.WalletPayment;
import com.meituan.android.pay.utils.CreditOpenUtils;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.config.PayBaseConfig;
import com.meituan.android.paybase.utils.CashAmountArithUtils;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paycommon.lib.fragment.HalfPageFragment;

import java.util.HashMap;
import java.util.Map;

import static com.meituan.android.cashier.base.utils.CashierAnalyseUtils.getCreditPayStatus;
import static com.meituan.android.cashier.bridge.HybridBusinessJsHandler.logMC;
import static com.meituan.android.cashier.bridge.icashier.ICashierJSHandler.jsCallbackError;
import static com.meituan.android.cashier.bridge.icashier.ICashierJSHandler.logSC;
import static com.meituan.android.pay.common.payment.utils.PaymentListUtils.getCombinePayment;
import static com.meituan.android.pay.common.payment.utils.PaymentListUtils.isPaymentAbnormal;
import static com.meituan.android.pay.desk.payment.discount.DiscountCashierUtils.getCashierDiscount;

/**
 * author:  kkli
 * date:    2019-10-15
 * description:
 */
public class ICashierSelectHandler implements SelectBankDialog.SelectedBankOnCancelListener {
    private ICashierJSHandler jsHandler;
    private MTPayment checkedMTPayment;
    private IBankcardData mSelectedPayment;
    private WalletPayment mWalletPayment;
    private String mCreditPayOpenUrl;
    private int mMoneyChanged = 0;
    public static final int HYBRID_CREDIT_OPEN_SELECTDIALOG = 4;    //hybrid标准收银台切卡弹窗

    private ICashierSelectHandler(ICashierJSHandler jsHandler) {
        this.jsHandler = jsHandler;
    }

    @Override
    public void onSelected(IBankcardData selectedPayment) {
        if (selectedPayment == null || isPaymentAbnormal(selectedPayment)) {
            jsCallbackError(jsHandler, ICashierJSHandler.JS_CALLBACK_ILLEGAL_STATE,
                    MapBuilder.builder("step", "onSelected").build("reason", "selectedPayment illegal"));
            return;
        }
        // 点击确认支付灵犀埋点
        logMC(jsHandler, HybridReportConstants.HYBRID_CASHIER_PAY_CLICK, HybridReportConstants.CID_HYBRID_CASHIER_COMMON,
                MapBuilder.builder("from_select_bankcard", 1).
                        add("pay_type", selectedPayment.getPayType()).build());
        logSC(jsHandler, HybridReportConstants.MTPAY_BRIDGE_CUT_POP_WINDOW_SELECT_PAY_TYPE, new AnalyseUtils.MapBuilder()
                .add("pay_type", selectedPayment.getPayType())
                .add("status", selectedPayment.getStatus()).build());

        MTPayment mtPayment;
        mMoneyChanged = ICashierPayParams.PARAM_MONEY_CHANGED_NOT;
        mSelectedPayment = selectedPayment;
        if (selectedPayment instanceof Payment) {
            mtPayment = checkedMTPayment;
        } else if (selectedPayment instanceof MTPayment) {
            mtPayment = (MTPayment) selectedPayment;
            float mtPaymentDiscount = getCashierDiscount(mWalletPayment, mtPayment).floatValue();
            float checkedMTPaymentDiscount = getCashierDiscount(mWalletPayment, checkedMTPayment).floatValue();
            if (Math.abs(CashAmountArithUtils.subtract(mtPaymentDiscount, checkedMTPaymentDiscount).floatValue()) > 0.0001) {
                mMoneyChanged = ICashierPayParams.PARAM_MONEY_CHANGED;
            }
        } else {
            jsCallbackError(jsHandler, ICashierJSHandler.JS_CALLBACK_ILLEGAL_STATE,
                    MapBuilder.builder("step", "onSelected").build("reason", "selectedPayment type error"));
            return;
        }
        if (jsHandler != null) {
            Activity currentActivity = null;
            if (jsHandler.jsHost() != null && jsHandler.jsHost().getActivity() != null) {
                currentActivity = jsHandler.jsHost().getActivity();
            }
            WalletPayManager.getInstance().setOpenSource(currentActivity, CreditOpenUtils.STANDARD_SELECTDIALOG_SUFFIX);
            logOnHomePay(selectedPayment, CreditOpenUtils.STANDARD_SELECTDIALOG_SUFFIX);
            if (CreditOpenUtils.isNeedEnterNewOpenCredit(mtPayment) && currentActivity != null) {
                //若需要进入新的月付开通流程
                mCreditPayOpenUrl = CreditOpenUtils.getCreditOpenFinalURL(currentActivity, mtPayment.getCreditPayOpenInfo().getUrl(), CreditOpenUtils.STANDARD_SELECTDIALOG_SUFFIX, "");
                HalfPageFragment.HalfPageFragmentConfig halfPageFragmentConfig =
                        new HalfPageFragment.HalfPageFragmentConfig("credit_half_page", mCreditPayOpenUrl, mtPayment.getCreditPayOpenInfo().getData(), HYBRID_CREDIT_OPEN_SELECTDIALOG);
                halfPageFragmentConfig.setTunnelExtraData(HalfPageFragment.getTunnelExtraData(jsHandler));
                HalfPageFragment.openHalfPage(currentActivity, halfPageFragmentConfig);
                CashierStaticsUtils.reportSystemCheck("b_pay_credit_open_leave_cashier_sc",
                        new AnalyseUtils.MapBuilder().add("url", mCreditPayOpenUrl).add("scene", 3).build(), getUniqueId());

                return;
            }
            readyForGoHelloPay();
        }
    }

    private String getUniqueId() {
        if (jsHandler == null) {
            return "";
        }
        return jsHandler.getUniqueId();
    }

    private void logOnHomePay(IBankcardData checkedPaymentData, String openSource) {
        String payType = "-999";
        if (checkedPaymentData != null) {
            payType = checkedPaymentData.getPayType();
        }
        Context context = PayBaseConfig.getProvider().getApplicationContext();
        HashMap<String, Object> labels = new AnalyseUtils.MapBuilder().add("nb_version", PayBaseConfig.getProvider().getPayVersion()).
                add("pay_type", payType).
                add("tradeNo", jsHandler.getTradeNo()).
                add("merchant_no", jsHandler.getMerchantNo()).
                add("open_source", openSource).
                add("sub_type", "0").
                build();
        putCreditPayInfo(labels, checkedPaymentData);
        CashierStaticsUtils.reportModelEventWithClickEvent("c_PJmoK", "b_xgald577", context.getString(R.string.cashier__mge_act_click_pay),
                labels, getUniqueId());
    }

    /**
     * @param properties
     * @param creditPayment
     */
    private void putCreditPayInfo(Map<String, Object> properties, IBankcardData creditPayment) {
        // credit_style：0（老流程）/1（新流程）/ -999（不存在月付支付方式）
        // mtcreditpay_status：0（未开通）/ 1（已开通）/ -999（不存在月付支付方式）
        if (creditPayment instanceof MTPayment && CreditOpenUtils.isCreditPay((MTPayment) creditPayment)) {
            properties.put("credit_style", CreditOpenUtils.isHasNewOpenCreditInfo((MTPayment) creditPayment) ? "1" : "0");
            properties.put("mtcreditpay_status", CreditOpenUtils.isCreditOpen((MTPayment)creditPayment) ? "1" : "0");
        } else {
            properties.put("credit_style", "-999");
            properties.put("mtcreditpay_status", "-999");
        }
    }


    public void readyForGoHelloPay() {
        MTPayment mtPayment = mSelectedPayment instanceof MTPayment ? (MTPayment) mSelectedPayment : checkedMTPayment;
        IBankcardData payment = mSelectedPayment instanceof Payment ? mSelectedPayment : null;
        PayParams payParams = ICashierPayParams.genPayParams(jsHandler.getActivity(), mWalletPayment, mtPayment, payment,
                ICashierPayParams.ExtParams.init(jsHandler.getTradeNo(), jsHandler.getPayToken(),
                        ICashierPayParams.CASHIER_TYPE_WALLET, mMoneyChanged,
                        ICashierPayParams.PARAM_FROM_SELECT_BANKCARD));
        // 切卡弹窗 & 切换支付方式弹窗场景添加 guide_plan_infos相关参数
        ICashierPayParams.appendGuidePlanInfos(payParams, jsHandler.getExtraData());
        ICashierPayerHandler.startGoHelloPay(jsHandler, payParams);
    }

    @Override
    public void onClose() {
        logSC(jsHandler, HybridReportConstants.MTPAY_BRIDGE_CUT_POP_WINDOW_CANCELLATION, null);
        ICashierJSHandler.jsCallbackNotPaySucc(jsHandler, ICashierJSHandler.VALUE_STATUS_SELECT_BANK_CLOSE, null);
    }

    public static void startSelectPayment(WalletPayment walletPayment, ICashierJSHandler jsHandler, WalletPaymentListPage listPage, int index) {
        if (jsHandler == null || listPage == null) {
            jsCallbackError(jsHandler, ICashierJSHandler.JS_CALLBACK_PARAMS_CHECK_ERROR,
                    MapBuilder.builder("step", "startSelectPayment").build("reason", "listPage is null"));
            return;
        }
        ICashierSelectHandler selectHandler = new ICashierSelectHandler(jsHandler);
        jsHandler.setSelectHandler(selectHandler);
        selectHandler.showSelectListPageFragment(walletPayment, null, listPage, index, SelectBankDialogFragment.SELECTED_DIALOG_NO_TAIL_MODE);
    }

    public static void startSelectBank(WalletPayment walletPayment, ICashierJSHandler jsHandler, MTPayment checkedMTPayment, int index) {
        if (jsHandler == null || checkedMTPayment == null || checkedMTPayment.getMtPaymentListPage() == null) {
            jsCallbackError(jsHandler, ICashierJSHandler.JS_CALLBACK_PARAMS_CHECK_ERROR,
                    MapBuilder.builder("step", "startSelectBank").build("reason", "checkedMTPayment or listPage is null"));
            return;
        }
        ICashierSelectHandler selectHandler = new ICashierSelectHandler(jsHandler);
        jsHandler.setSelectHandler(selectHandler);
        selectHandler.showSelectListPageFragment(walletPayment, checkedMTPayment, checkedMTPayment.getMtPaymentListPage(), index, SelectBankDialogFragment.SELECTED_DIALOG_TAIL_MODE);
    }

    private void showSelectListPageFragment(WalletPayment walletPayment, MTPayment checkedMTPayment, IPaymentListPage listPage, int selectedIndex, int mode) {
        if (listPage == null || selectedIndex < 0) {
            jsCallbackError(jsHandler, ICashierJSHandler.JS_CALLBACK_PARAMS_CHECK_ERROR,
                    MapBuilder.builder("step", "showSelectListPageFragment").build("reason", "listPage is null or " +
                            "selectedIndex < 0"));
            return;
        }
        if (CollectionUtils.isEmpty(listPage.getMtPaymentList()) || selectedIndex >= listPage.getMtPaymentList().size()) {
            jsCallbackError(jsHandler, ICashierJSHandler.JS_CALLBACK_PARAMS_CHECK_ERROR,
                    MapBuilder.builder("step", "showSelectListPageFragment").build("reason", "MtPaymentList is empty " +
                            "or selectedIndex is error"));
            return;
        }
        this.mWalletPayment = walletPayment;
        this.checkedMTPayment = checkedMTPayment;
        SelectBankDialogFragment selectBankDialogFragment = new SelectBankDialogFragment();
        Bundle args = new Bundle();
        args.putSerializable(SelectBankDialogFragment.BANKLIST_PAGE, listPage);
        args.putSerializable(SelectBankDialogFragment.SELECTED_PAYMENT_INDEX, selectedIndex);
        args.putInt(SelectBankDialogFragment.SELECTED_DIALOG_MODE, mode);
        args.putSerializable(SelectBankDialogFragment.TITLE_TYPE, SelectBankDialog.TitleType.CLOSE);
        selectBankDialogFragment.setArguments(args);
        selectBankDialogFragment.setOnCancelListener(this);
        SelectBankAnalyseUtils.setSelectBankScene("zhifu_page");
        if (jsHandler != null && jsHandler.jsHost() != null) {
            Activity currentActivity = jsHandler.jsHost().getActivity();
            if (currentActivity instanceof AppCompatActivity) {
                selectBankDialogFragment.show(((AppCompatActivity) currentActivity).getSupportFragmentManager());
                return;
            }
        }
        jsCallbackError(jsHandler, ICashierJSHandler.JS_CALLBACK_ILLEGAL_STATE,
                MapBuilder.builder("step", "showSelectListPageFragment").build("reason", "jsHost or activity is null"));
    }

    public String getCreditOpenUrl() {
        return mCreditPayOpenUrl != null ? mCreditPayOpenUrl : "";
    }
}
