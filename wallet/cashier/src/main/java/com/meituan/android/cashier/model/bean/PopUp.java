package com.meituan.android.cashier.model.bean;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;

/**
 *  后端下发的风控弹窗"popup"的数据结构
 */
@JsonBean
public class PopUp implements Serializable {

    private static final long serialVersionUID = 5353125441992301819L;

    // 弹窗类型，"riskHint"代表风险提示弹窗（A类），"riskVerify"代表风险验证弹窗（B类）
    private String type;

    // 弹窗子类型，"origin"代表风险提示弹窗（A类），"verifyCenter"代表风险验证-人脸验证弹窗（B类），"question"代表风险验证-问题验证弹窗（B类）
    private String subtype;

    // 弹窗标题
    private String title;

    // 弹窗正文
    private String body;

    // 继续支付流程的按钮的文本
    private String confirmButton;

    // 取消支付流程的按钮的文本
    private String cancelButton;

    // 聚合支付单
    private String orderId;

    // 验证页面对应的URL，仅风险验证弹窗（B类）拥有
    private String url;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSubtype() {
        return subtype;
    }

    public void setSubtype(String subtype) {
        this.subtype = subtype;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getConfirmButton() {
        return confirmButton;
    }

    public void setConfirmButton(String confirmButton) {
        this.confirmButton = confirmButton;
    }

    public String getCancelButton() {
        return cancelButton;
    }

    public void setCancelButton(String cancelButton) {
        this.cancelButton = cancelButton;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
