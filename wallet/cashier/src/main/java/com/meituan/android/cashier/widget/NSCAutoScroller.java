package com.meituan.android.cashier.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.view.View;
import android.view.animation.LinearInterpolator;

import com.meituan.android.cashier.utils.ViewLayoutCalculation;
import com.meituan.android.paybase.utils.AppUtils;

/**
 * <AUTHOR>
 */
public class NSCAutoScroller {
    // 需要被滑动的view的上层scrollview
    private final NSCScrollView scrollView;
    // 需要被滑动的view
    private final View scrolledForBetterLocationView;
    // 符合预期时Location位置
    private final float suitableRate;
    // 不符合预期时，需要滑动到的Location指定位置
    private final float targetRate;
    // 进行scroll时的监听
    private final Runnable scrollEndListener;
    // 不进行scroll时的监听
    private final Runnable noScrolledListener;

    private NSCAutoScroller(Builder builder) {
        this.scrollView = builder.scrollView;
        this.scrolledForBetterLocationView = builder.scrolledForBetterLocationView;
        this.suitableRate = builder.suitableRate;
        this.targetRate = builder.targetRate;
        this.scrollEndListener = builder.scrollEndListener;
        this.noScrolledListener = builder.noScrolledListener;
        check();
    }

    private void check() {
        if (AppUtils.isDebug() && !ViewUtils.isChild(scrollView, scrolledForBetterLocationView)) {
            throw new IllegalArgumentException("scrolledView is <" + scrolledForBetterLocationView + "> , not scrollView's <" + scrollView + "> child");
        }
    }

    private int getScrollOffset() {
        if (scrollView == null || scrolledForBetterLocationView == null) {
            return -1;
        }
        // 计算scrollview坐标
        ViewLayoutCalculation scrollViewAttr = ViewLayoutCalculation.location(scrollView);
        // 计算被选中view的坐标
        ViewLayoutCalculation scrolledForBetterLocationViewAttr = ViewLayoutCalculation.location(scrolledForBetterLocationView);
        // 计算合适区域的定位
        int suitableLocation = (int) (scrollViewAttr.top + scrollViewAttr.height * suitableRate);
        // 计算目标区域的定位（当不再合适区域时，需要移动到目标区域）
        int targetLocation = (int) (scrollViewAttr.top + scrollViewAttr.height * targetRate);
        // 当目标view已经在合适的位置区域时，不需要再进行滑动
        if (scrolledForBetterLocationViewAttr.top <= suitableLocation) {
            return -1;
        }
        // 计算滑动距离
        int offset = scrolledForBetterLocationViewAttr.top - targetLocation;
        // 判断是否超过最大可滑动距离，如果超过则只滑动最大距离
        return offset > 0 ? (Math.min(offset, getMaxScrollOffset())) : -1;
    }

    private int getMaxScrollOffset() {
        if (scrollView == null || scrollView.getChildCount() != 1) {
            return -1;
        }
        int maxScrollOffset = scrollView.getChildAt(0).getHeight() - scrollView.getHeight();
        return maxScrollOffset <= 0 ? -1 : maxScrollOffset;
    }

    private void scroll(int offset) {
        if (offset <= 0 && noScrolledListener != null) {
            noScrolledListener.run();
            return;
        }
        // 耗时 = 移动距离（像素） / 速度（像素/ms）
        int duration = offset / 3;
        ValueAnimator valueAnimator = ValueAnimator.ofInt(scrollView.getScrollY(), offset + scrollView.getScrollY());
        valueAnimator.setInterpolator(new LinearInterpolator());
        valueAnimator.addUpdateListener(animation -> {
            if (!scrollView.isScrollable()) {
                valueAnimator.cancel();
                return;
            }
            int animatedValue = (int) animation.getAnimatedValue();
            scrollView.scrollTo(scrollView.getScrollX(), animatedValue);
            scrollView.invalidate();
        });
        valueAnimator.setDuration(duration);
        valueAnimator.start();
        if (scrollEndListener != null) {
            valueAnimator.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    super.onAnimationEnd(animation);
                    scrollEndListener.run();
                }
            });
        }
    }

    private void execute() {
        scroll(getScrollOffset());
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private NSCScrollView scrollView;
        private View scrolledForBetterLocationView;
        private float suitableRate;
        private float targetRate;
        private Runnable scrollEndListener;
        private Runnable noScrolledListener;

        public Builder setScrollView(NSCScrollView scrollView) {
            this.scrollView = scrollView;
            return this;
        }

        public Builder setScrolledView(View scrolledForBetterLocationView) {
            this.scrolledForBetterLocationView = scrolledForBetterLocationView;
            return this;
        }

        public Builder suitableRate(float suitableRate) {
            this.suitableRate = suitableRate;
            return this;
        }

        public Builder targetRate(float targetRate) {
            this.targetRate = targetRate;
            return this;
        }

        public Builder setScrollEndListener(Runnable scrollEndListener) {
            this.scrollEndListener = scrollEndListener;
            return this;
        }

        public Builder setNoScrolledListener(Runnable noScrolledListener) {
            this.noScrolledListener = noScrolledListener;
            return this;
        }

        public void execute() {
            new NSCAutoScroller(this).execute();
        }
    }

}
