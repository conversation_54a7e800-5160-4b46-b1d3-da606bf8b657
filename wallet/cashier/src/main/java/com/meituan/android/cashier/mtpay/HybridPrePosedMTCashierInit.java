package com.meituan.android.cashier.mtpay;

import android.content.Context;
import android.support.annotation.NonNull;

import com.meituan.android.cashier.common.HybridPrePosedMTCashierHornService;
import com.meituan.android.paycommon.lib.IInitSDK;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

@ServiceLoaderInterface(key = "hybridPrePosedMTCashier", interfaceClass = IInitSDK.class)
public class HybridPrePosedMTCashierInit implements IInitSDK {
    @Override
    public void onInit(@NonNull Context context) {
        HybridPrePosedMTCashierHornService.getInstance().init();
    }
}
