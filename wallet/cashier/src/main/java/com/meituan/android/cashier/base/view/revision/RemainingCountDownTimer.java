package com.meituan.android.cashier.base.view.revision;

import android.os.CountDownTimer;

import java.lang.ref.WeakReference;

/**
 * author: luo jing
 * date: 2018/8/28 13:29
 * description: cashier timer control
 */
public class RemainingCountDownTimer<T extends ITimerView> extends CountDownTimer {
    private WeakReference<T> weakReference;
    private ITimerFinishCallback callback;

    public RemainingCountDownTimer(T t, long millisInFuture, long countDownInterval,
                                   ITimerFinishCallback callback) {
        super(millisInFuture, countDownInterval);
        this.weakReference = new WeakReference<>(t);
        this.callback = callback;
    }

    @Override
    public void onTick(long millisUntilFinished) {
        T t = weakReference.get();
        if (t != null) {
            t.onTimerTick(millisUntilFinished);
        }
    }

    @Override
    public void onFinish() {
        T t = weakReference.get();
        if (t != null) {
            t.onTimerFinish();
        }
        if (callback != null) {
            callback.onFinishDeal();
        }
    }

    public interface ITimerFinishCallback {
        void onFinishDeal();
    }
}
