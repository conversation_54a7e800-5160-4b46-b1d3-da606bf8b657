package com.meituan.android.cashier.bridge;

import static com.meituan.android.paybase.constants.ThirdPayConstants.ALI_PAY_RESULT_CANCEL;
import static com.meituan.android.paybase.constants.ThirdPayConstants.ALI_PAY_RESULT_SUCC;
import static com.meituan.android.paybase.constants.ThirdPayConstants.CODE_EMPTY_PAYTYPE;
import static com.meituan.android.paybase.constants.ThirdPayConstants.CODE_EMPTY_URL;
import static com.meituan.android.paybase.constants.ThirdPayConstants.CODE_PARAMS_EXCEPTION;
import static com.meituan.android.paybase.constants.ThirdPayConstants.CODE_UNKNOWN_ERROR;
import static com.meituan.android.paybase.constants.ThirdPayConstants.MSG_EMPTY_PAYTYPE;
import static com.meituan.android.paybase.constants.ThirdPayConstants.MSG_EMPTY_URL;
import static com.meituan.android.paybase.constants.ThirdPayConstants.MSG_PARAMS_EXCEPTION;
import static com.meituan.android.paybase.constants.ThirdPayConstants.MSG_UNKNOWN_ERROR;
import static com.meituan.android.paybase.constants.ThirdPayConstants.TOAST_MSG_PAY_FAILED;
import static com.meituan.android.paybase.constants.ThirdPayConstants.alipayErrorMsgMap;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;

import com.dianping.titans.js.BridgeManager;
import com.google.gson.JsonObject;
import com.meituan.android.cashier.common.CashierConstants;
import com.meituan.android.pay.common.payment.data.PayType;
import com.meituan.android.paybase.common.analyse.cat.CatConstants;
import com.meituan.android.paybase.common.analyse.cat.CatUtils;
import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.moduleinterface.FinanceJsHandler;
import com.meituan.android.paybase.moduleinterface.payment.PayActionListener;
import com.meituan.android.paybase.moduleinterface.payment.PayFailInfo;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.webview.WebViewActivity;
import com.meituan.android.paymentchannel.PaymentChannelCatConstants;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;

@ServiceLoaderInterface(key = "pay.callThirdPay", interfaceClass = FinanceJsHandler.class)
public class CallThirdPayJsHandler extends HybridBusinessJsHandler implements FinanceJsHandler {

    private static final String NAME = "pay.callThirdPay";

    // todo 最终要挪到 JsHandlerRequestCode
    private static final int REQUEST_CODE_CALL_PAY_MIDDLE = 407;
    private static final int REQUEST_CODE_WEBVIEW = 408;
    private static final int CAT_PARAMS_ERROR = 9001;

    private static final int PAY_STATUS_DEFAULT_VALUE = -2;
    private static final String ARG_URL = "url";
    private static final String ARG_PAY_TYPE = "payType";
    private static final String ARG_TRADE_NO = "tradeNo";
    private static final String ARG_EXTRA_DATA = "extraData";

    // cat上报
    private static final String PAYBIZ_CALL_THIRD_PAY_BY_H5 = "paybiz_call_third_pay_by_h5";

    private String payType;
    private String url;
    private String tradeNo;
    private JSONObject extraData;

    @Override
    public void exec() {
        super.exec();
        Activity activity = jsHost().getActivity();
        if (activity == null) {
            callJsHandlerError(CODE_PARAMS_EXCEPTION, MSG_PARAMS_EXCEPTION);
            return;
        }
        if (jsBean() != null && jsBean().argsJson != null &&
                jsHost() != null) {
            url = jsBean().argsJson.optString(ARG_URL);
            payType = jsBean().argsJson.optString(ARG_PAY_TYPE);
            tradeNo = jsBean().argsJson.optString(ARG_TRADE_NO);

            if (TextUtils.isEmpty(url)) {
                ToastUtils.showSnackToast(activity, TOAST_MSG_PAY_FAILED, true);
                callJsHandlerError(CODE_EMPTY_URL, MSG_EMPTY_URL);
            } else if (TextUtils.isEmpty(payType)) {
                ToastUtils.showSnackToast(activity, TOAST_MSG_PAY_FAILED, true);
                callJsHandlerError(CODE_EMPTY_PAYTYPE, MSG_EMPTY_PAYTYPE);
            } else {
                if (TextUtils.equals(PayType.WEIXIN_JS_PAY, payType)) {
                    try {
                        extraData = jsBean().argsJson.getJSONObject(ARG_EXTRA_DATA);
                    } catch (Exception e) {
                        callJsHandlerError(CODE_PARAMS_EXCEPTION, MSG_PARAMS_EXCEPTION);
                        return;
                    }
                    if (extraData == null) {
                        callJsHandlerError(CODE_PARAMS_EXCEPTION, MSG_PARAMS_EXCEPTION);
                        return;
                    }
                }
                loadAndOverrideUrl(payType, url, activity, extraData);
            }
        } else {
            ToastUtils.showSnackToast(activity, TOAST_MSG_PAY_FAILED, true);
            callJsHandlerError(CODE_PARAMS_EXCEPTION, MSG_PARAMS_EXCEPTION);
        }

        LoganUtils.log("i版_桥已经被调用", getReportParams());
    }

    @Override
    public int jsHandlerType() {
        return BridgeManager.FOR_COMMON;
    }

    @Override
    public String getSignature() {
        return "l6cwSLzfayFjkRiYTtLkl8GA8KvGqatHi5sj5Kk1LM0HoviE4Nfl184GyAsJCwSf7akO3U+XEBu7UU/xBzsSeA==";
    }

    @Override
    public Class<?> getHandlerClass() {
        return getClass();
    }

    @Override
    public String getMethodName() {
        return NAME;
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (jsHost() != null) {
            ThirdPayerMediator.removePayActionListener(jsHost().getActivity());
        }
    }

    // 对url进行分发
    private void loadAndOverrideUrl(String payType, String url, Activity activity, JSONObject extraData) {
        // 由于支付宝wap的处理比较特殊,所以这个就单领出来处理
        if (TextUtils.equals(PayType.ALIPAY_WAP, payType)) {
            // 开启一个新的webview来加载调起支付宝支付的url
            WebViewActivity.openWithLinkedUrlForResultCashier(activity, url, REQUEST_CODE_WEBVIEW);
        } else {
            ThirdPayerMediator.startPay(activity, payType, url, tradeNo, new PayActionListener() {
                @Override
                public void onPayPreExecute(String s) {
                    // do nothing
                }

                @Override
                public void onGotPayResult(String payType, int payResult, PayFailInfo payFailInfo) {
                    String errorCodeString = CODE_UNKNOWN_ERROR;
                    String msg = MSG_UNKNOWN_ERROR;
                    if (payResult == PayActionListener.FAIL && payFailInfo != null) {
                        errorCodeString = payFailInfo.getErrorCodeStringOrDefault();
                        msg = payFailInfo.getMsgOrDefault();
                    }
                    callbackThirdPayResult(payResult, errorCodeString, msg);
                    // 置空,防止内存泄露
                    ThirdPayerMediator.removePayActionListener(activity);
                }
            }, extraData);
        }
    }

    private void jsCallbackPayCancel() {
        jsCallbackPayError("", CODE_CANCEL);
    }

    @Override
    public void jsCallbackPayError() {
        jsCallbackPayError("", CODE_ERROR);
    }

    @Override
    public void jsCallbackPayError(String errMsg) {
        jsCallbackPayError(errMsg, CODE_ERROR);
    }

    @Override
    public void jsCallbackPayError(String errMsg, int errorCode) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("status", "fail");
            jsonObject.put("errorCode", errorCode);
            jsonObject.put("errMsg", errMsg);
            jsonObject.put(ARG_PAY_TYPE, TextUtils.isEmpty(payType) ? "empty" : payType);
        } catch (JSONException e) {
            LoganUtils.logError("CallThirdPayJsHandler_jsCallbackPayError", e.getMessage());
        }
        jsCallback(jsonObject);
    }

    private void callJsHandlerError(String errorCodeString, String msg) {
        JsonObject errMsgObj = new JsonObject();
        errMsgObj.addProperty("errorCodeString", errorCodeString);
        errMsgObj.addProperty("msg", msg);
        jsCallbackPayError(errMsgObj.toString());
        if (TextUtils.equals(PayType.ALIPAY_WAP, payType)) {
            CatUtils.logRate(PaymentChannelCatConstants.ACTION_CALL_ALIPAY_BY_H5,
                    CatConstants.CODE_DEFAULT_ERROR);
            LoganUtils.log("i版_调用支付桥异常", getReportParams());
        } else {
            CatUtils.logRate(PAYBIZ_CALL_THIRD_PAY_BY_H5,
                    CAT_PARAMS_ERROR);
            LoganUtils.log("第三方支付桥_桥参数校验", getReportParams());
        }
    }

    private HashMap<String, Object> getReportParams() {
        HashMap<String, Object> logMap = new HashMap<>();
        logMap.put(ARG_URL, url);
        logMap.put(ARG_PAY_TYPE, payType);
        logMap.put(CashierConstants.PAY_TRADE_NO, tradeNo);
        return logMap;
    }

    /**
     * 给H5端传递支付宝支付结果
     */
    private void callbackAlipayResult(String result) {
        HashMap<String, Object> reportParams = getReportParams();
        reportParams.put("response", result);
        try {
            JSONObject resultJson = new JSONObject(result);
            String resultCode = resultJson.optString("resultCode");
            if (TextUtils.equals(ALI_PAY_RESULT_SUCC, resultCode)) {
                jsCallback();
                CatUtils.logRate(PaymentChannelCatConstants.ACTION_PAY_ALIPAY_BY_H5,
                        CatConstants.CODE_DEFAULT_OK);
                LoganUtils.log("i版_支付宝支付成功", reportParams);
            } else if (TextUtils.equals(ALI_PAY_RESULT_CANCEL, resultCode)) {
                jsCallbackPayCancel();
                CatUtils.logRate(PaymentChannelCatConstants.ACTION_PAY_ALIPAY_BY_H5,
                        CatConstants.CODE_DEFAULT_CANCEL);
                LoganUtils.log("i版_支付宝支付取消", reportParams);
            } else {
                callJsHandlerError(resultCode, alipayErrorMsgMap.getOrDefault(resultCode, MSG_UNKNOWN_ERROR));

                int code = CatConstants.CODE_DEFAULT_ERROR;
                try {
                    code = Integer.parseInt(resultCode);
                } catch (Exception e) {
                    LoganUtils.logError("CallThirdPayJsHandler_callBackAlipayResult1", e.getMessage());
                }
                CatUtils.logRate(PaymentChannelCatConstants.ACTION_PAY_ALIPAY_BY_H5, code);
                LoganUtils.log("i版_支付宝支付失败", reportParams);
            }
        } catch (JSONException e) {
            jsCallbackPayError(result);
            LoganUtils.logError("CallThirdPayJsHandler_callBackAlipayResult2", e.getMessage());
            CatUtils.logRate(PaymentChannelCatConstants.ACTION_PAY_ALIPAY_BY_H5, CatConstants.CODE_DEFAULT_ERROR);
        }
    }

    /**
     * 将三种返回结果回传给前端
     */
    private void callbackThirdPayResult(int status, String errorCodeString, String msg) {
        HashMap<String, Object> reportParams = getReportParams();
        reportParams.put("errorCodeString", errorCodeString);
        reportParams.put("msg", msg);
        switch (status) {
            case PayActionListener.FAIL:
                callJsHandlerError(errorCodeString, msg);
                CatUtils.logRate(PAYBIZ_CALL_THIRD_PAY_BY_H5,
                        CatConstants.CODE_DEFAULT_ERROR);
                LoganUtils.log("第三方支付桥_调用桥失败", reportParams);
                break;
            case PayActionListener.CANCEL:
                jsCallbackPayCancel();
                CatUtils.logRate(PAYBIZ_CALL_THIRD_PAY_BY_H5,
                        CatConstants.CODE_DEFAULT_CANCEL);
                LoganUtils.log("第三方支付-取消支付", reportParams);
                break;
            case PayActionListener.SUCCESS:
                jsCallback();
                CatUtils.logRate(PAYBIZ_CALL_THIRD_PAY_BY_H5,
                        CatConstants.CODE_DEFAULT_OK);
                LoganUtils.log("第三方支付桥_调用桥成功", reportParams);
                break;
            default:
                callJsHandlerError(errorCodeString, msg);
                CatUtils.logRate(PAYBIZ_CALL_THIRD_PAY_BY_H5,
                        CatConstants.CODE_DEFAULT_ERROR);
                break;
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        // 判断是否可以处理onActivityResult
        if (!ThirdPayerMediator.shouldHandleActivityResult(payType)) {
            return;
        }
        Activity activity = null;
        if (jsHost() != null) {
            activity = jsHost().getActivity();
        }
        if (requestCode == REQUEST_CODE_WEBVIEW && resultCode == Activity.RESULT_OK && data != null) {
            String result = data.getStringExtra("result");
            if (TextUtils.isEmpty(result)) {
                jsCallbackPayError();
                // 获取支付宝的支付结果异常
                LoganUtils.log("i版_支付宝回调结果为空");
                CatUtils.logRate(PaymentChannelCatConstants.ACTION_PAY_ALIPAY_BY_H5,
                        CatConstants.CODE_DEFAULT_ERROR);
            } else {
                // 处理支付宝的支付结果逻辑
                callbackAlipayResult(result);
            }
        } else if (ThirdPayerMediator.handleActivityResult(activity, requestCode, resultCode, data)) {
            // 上报
            LoganUtils.log("第三方支付桥处理activityresult成功", getReportParams());
        } else {
            // do nothing
        }
    }
}
