package com.meituan.android.cashier.business;

import android.support.v4.app.FragmentActivity;
import android.text.TextUtils;

import com.meituan.android.cashier.fragment.MTCashierRevisionFragment;
import com.meituan.android.cashier.model.bean.Cashier;
import com.meituan.android.cashier.model.bean.CashierPayment;
import com.meituan.android.cashier.model.bean.CashierPopWindowBean;
import com.meituan.android.cashier.model.bean.OrderResult;
import com.meituan.android.cashier.newrouter.CashierBusinessHandler;
import com.meituan.android.cashier.newrouter.NewCashierParams;
import com.meituan.android.cashier.retrofit.CashierReqTagConstant;
import com.meituan.android.cashier.retrofit.CashierRequestService;
import com.meituan.android.paybase.dialog.ProgressController;
import com.meituan.android.paybase.dialog.ProgressType;
import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.moduleinterface.payment.PayActionListener;
import com.meituan.android.paybase.moduleinterface.payment.PayFailInfo;
import com.meituan.android.paybase.moduleinterface.payment.PrePayerExecute;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.utils.ActivityStatusChecker;
import com.meituan.android.paybase.utils.CashierScreenSnapShotUtil;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.JsonString;
import com.meituan.android.paycommon.lib.retrofit.PayRetrofit;
import com.meituan.android.paymentchannel.PayerMediator;
import com.meituan.android.paymentchannel.PayersID;
import com.meituan.android.payrouter.remake.router.manager.RouterManager;

import java.util.List;

public class ThirdPayHandler extends CashierBusinessHandler implements IRequestCallback, PayActionListener {
    private static final int REQ_TAG_THIRD_PAY_FAIL_QUERY = CashierReqTagConstant.REQ_TAG_THIRD_PAY_FAIL_QUERY;
    private ThirdPayResultHandler resultHandler;
    private CashierPopWindowBean cashierPopWindowBean;
    private Cashier cashierBean;

    private String failMessage;
    private boolean hasShowThirdInterrupt;
    private boolean isInThirdPay;

    public ThirdPayHandler(FragmentActivity activity, NewCashierParams cashierParams, ThirdPayResultHandler resultHandler) {
        super(activity, cashierParams);
        this.resultHandler = resultHandler;
        RouterManager.helper(cashierParams.getCashierRouterTrace())
                .observeOn(PrePayerExecute.class)
                .subscribeUniquely(() -> isInThirdPay = true);
    }

    public void setCashierPopWindowBean(CashierPopWindowBean cashierPopWindowBean, Cashier cashierBean) {
        this.cashierPopWindowBean = cashierPopWindowBean;
        this.cashierBean = cashierBean;
    }

    public void startThirdPay(String payType, String url) {
        FragmentActivity activity = getActivity();
        if (ActivityStatusChecker.isValid(activity)) {
            PayerMediator.getInstance().startPay(activity, payType, url, getCashierParams().getTradeNo(), this);
        }
    }

    /**
     * 是否处理三方支付结果
     */
    public boolean handleThirdPayResult(String payType, int payResultCode, PayFailInfo payFailInfo) {
        isInThirdPay = false;
        if (TextUtils.isEmpty(payType)) {
            return false;
        }
        if (payResultCode == PayActionListener.SUCCESS) {
            if (TextUtils.equals(payType, PayersID.ID_ALIPAYHK_APP)) {
                // AlipayHK 结果不感知状态，需要 queryOrder 查询支付状态
                queryOrder("AlipayHK 支付");
            } else {
                CashierScreenSnapShotUtil.captureSnapShot(getActivity(), success -> {
                    getResultHandler().onThirdPaySuccess();
                });
            }
        } else if (payResultCode == PayActionListener.CANCEL) {
            if (TextUtils.equals(payType, PayersID.ID_ALIPAY_MINI)
                    || TextUtils.equals(payType, PayersID.ID_UNION_FLASH_PAY)
                    || TextUtils.equals(payType, PayersID.ID_UPSEPAY)
                    || TextUtils.equals(payType, PayersID.ID_ALIPAYWAP)
                    || TextUtils.equals(payType, PayersID.ID_WEIXINPAY)
                    || TextUtils.equals(payType, PayersID.ID_UPPAY)
                    || TextUtils.equals(payType, PayersID.ID_ALIPAYHK_APP)) {
                // 目前云闪付和安卓pay的回调共用ID_UPPAY方式，所以不能下线ID_UPPAY判断
                queryOrder("三方支付失败");
            } else {
                getResultHandler().onThirdPayNeedCancel();
            }
        } else if (payResultCode == PayActionListener.FAIL) {
            failMessage = payFailInfo.getMsg();
            if (TextUtils.equals(payType, PayersID.ID_ALIPAY_MINI)) {
                queryOrder("支付宝支付失败");
            } else if (TextUtils.equals(payType, PayersID.ID_WEIXINPAY)) {
                queryOrder("微信支付失败");
            } else if (TextUtils.equals(payType, PayersID.ID_UNION_FLASH_PAY)
                    || TextUtils.equals(payType, PayersID.ID_UPSEPAY)
                    || TextUtils.equals(payType, PayersID.ID_ALIPAYWAP)
                    || TextUtils.equals(payType, PayersID.ID_UPPAY)
                    || TextUtils.equals(payType, PayersID.ID_ALIPAYHK_APP)) {
                // 目前云闪付和安卓pay的回调共用ID_UPPAY方式，所以不能下线ID_UPPAY判断
                queryOrder("三方支付失败");
            } else {
                getResultHandler().onThirdPayFail(failMessage);
            }
        }
        return true;
    }

    /**
     * 是否处理三方中断
     * 如果处理，则直接展示弹窗
     * 判断条件：
     * 前端防打扰：一次支付周期，仅弹一次
     * 含有三方中断弹窗数据： pop_window中pop_scene =interruptPay
     * 选中的支付方式（getPayType）支付类型支持三方中断弹窗（support_interrupt =true)
     */
    private void handleThirdPayInterruptDialog() {
        FragmentActivity activity = getActivity();
        ThirdPayResultHandler resultHandler = getResultHandler();
        if (hasShowThirdInterrupt
                || !ActivityStatusChecker.isValid(activity)
                || resultHandler == null
                || cashierPopWindowBean == null
                || !TextUtils.equals("interruptPay", cashierPopWindowBean.getPopScene())
                || cashierBean == null
                || CollectionUtils.isEmpty(cashierBean.getPaymentDataList())) {
            return;
        }
        String currentPayType = resultHandler.currentPayType();
        if (TextUtils.isEmpty(currentPayType)) {
            return;
        }
        List<CashierPayment> paymentDataList = cashierBean.getPaymentDataList();
        for (CashierPayment cashierPayment : paymentDataList) {
            if (cashierPayment != null
                    && TextUtils.equals(cashierPayment.getPayType(), currentPayType)
                    && cashierPayment.isSupportInterrupt()
                    && cashierPopWindowBean.getPopDetailInfo() != null
                    && MTCashierRevisionFragment.isBindCardPayGuideInfoComplete(cashierPopWindowBean, CashierPopWindowBean.INTERRUPT_PAY_SCENE)) {
                hasShowThirdInterrupt = resultHandler.onInterruptDialog();
            }
        }
    }

    /**
     * 行为：查询订单信息
     *
     * @param queryOrderScene 查询订单场景
     */
    public void queryOrder(String queryOrderScene) {
        PayRetrofit.getInstance().create(CashierRequestService.class, this, REQ_TAG_THIRD_PAY_FAIL_QUERY)
                .queryOrder(getCashierParams().getTradeNo(),
                        getCashierParams().getPayToken(),
                        "1", // isauto
                        getCashierParams().getExtraData(),
                        JsonString.builder().add("outer_business_statics", getCashierParams().getExtraStatics()).build(),
                        getCashierParams().getExtendTransmissionParams());
    }

    public void queryOrderFromResume() {
        if (isInThirdPay) {
            isInThirdPay = false;
            queryOrder("第三方支付结果");
        }
    }

    @Override
    public void onRequestSucc(int tag, Object obj) {
        if (obj == null || !ActivityStatusChecker.isValid(getActivity())) {
            return;
        }
        OrderResult result = (OrderResult) obj;
        if (result.isResult()) {
            CashierScreenSnapShotUtil.captureSnapShot(getActivity(), success -> {
                getResultHandler().onThirdPaySuccess();
            });
        } else {
            if (TextUtils.isEmpty(failMessage)) {
                ToastUtils.showSnackToast(getActivity(), failMessage, true);
            }
            handleThirdPayInterruptDialog();
        }
    }

    @Override
    public void onRequestException(int tag, Exception e) {
    }

    @Override
    public void onRequestFinal(int tag) {
        ProgressController.of(getActivity()).hide();
    }

    @Override
    public void onRequestStart(int tag) {
        ProgressController.of(getActivity()).setProgressType(ProgressType.CASHIER).show();
    }

    @Override
    public void onPayPreExecute(String payType) {

    }

    @Override
    public void onGotPayResult(String payType, int payResult, PayFailInfo payFailInfo) {
        handleThirdPayResult(payType, payResult, payFailInfo);
    }

    private ThirdPayResultHandler getResultHandler() {
        return resultHandler;
    }
}
