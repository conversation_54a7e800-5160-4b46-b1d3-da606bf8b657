package com.meituan.android.cashier.mtpay;

import static com.meituan.android.paybase.moduleinterface.picasso.coupondialog.CouponDialogConstants.REQ_RESULT_PAGE;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;

import com.meituan.android.cashier.bean.CashierParams;
import com.meituan.android.cashier.common.CashierUtil;
import com.meituan.android.cashier.newrouter.remake.CashierRouterAdapter;
import com.meituan.android.cashier.newrouter.remake.CashierRouterConstants;
import com.meituan.android.cashier.newrouter.remake.CashierRouterHelper;
import com.meituan.android.paybase.moduleinterface.payment.PayActionListener;
import com.meituan.android.paybase.moduleinterface.payment.PayFailInfo;
import com.meituan.android.paybase.utils.CatchException;
import com.meituan.android.paybase.utils.JsonString;
import com.meituan.android.paybase.utils.MTPayBaseClass;
import com.meituan.android.paybase.utils.UriUtils;
import com.meituan.android.paymentchannel.PayerMediator;
import com.meituan.android.paymentchannel.PayersID;
import com.meituan.android.payrouter.remake.router.adapter.AbstractRouterAdapter;
import com.meituan.android.payrouter.remake.router.data.CheckResult;
import com.meituan.android.payrouter.remake.router.data.InvokeInfo;
import com.meituan.android.payrouter.remake.router.manager.RouterManager;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * 美团支付组件，跑腿充值业务使用
 */
@ServiceLoaderInterface(key = CashierRouterConstants.ADAPTER_TYPE_MT_PAY_COMPONENT, interfaceClass = AbstractRouterAdapter.class)
@MTPayBaseClass
public class MTPayComponentRouterAdapter extends CashierRouterAdapter implements PayActionListener {
    private CashierParams cashierParams;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.cashierParams = cashierParams();
    }

    @Override
    public CheckResult check() {
        if (!CashierParams.checkValid(cashierParams)) {
            return CheckResult.fail("001", "cashierParams invalid");
        }
        if (CashierUtil.isMeituanPayConponent(cashierParams.getUri())) { // 判断uri是否是美团支付组件
            return CheckResult.success();
        }
        return CheckResult.fail("002", "uri is not MTPayComponent");
    }

    @Override
    public void invoke(InvokeInfo info) {
        super.invoke(info);
        RouterManager.notifier(adapterContext().trace()).notifyLoadSuccess("MTPayComponent load success");
        Uri.Builder uriBuilder = cashierParams.getUri().buildUpon();
        Uri uri = uriBuilder.scheme("meituanpayment").authority("meituanpay").path("launch").build();
        PayerMediator.getInstance().startPay(getActivity(), PayersID.ID_MEITUANPAY_COMPONENT, uri.toString(), "", this);
    }

    @Override
    public boolean onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == REQ_RESULT_PAGE) { // 直接退出
            CashierRouterHelper.from(this).success().finish();
        } else {
            PayerMediator.getInstance().consumeActivityResult(getActivity(), requestCode, resultCode, data);
        }
        return true;
    }

    @Override
    public void onPayPreExecute(String payType) {
    }

    @Override
    public void onGotPayResult(String payType, int payResult, PayFailInfo payFailInfo) {
        Activity activity = getActivity();
        if (activity == null || !TextUtils.equals(payType, PayersID.ID_MEITUANPAY)) {
            return;
        }
        if (payResult == PayActionListener.SUCCESS) {
            if (payFailInfo == null) {
                CashierRouterHelper.from(this).success().finish();
            } else {
                String extra = payFailInfo.getExtra();
                if (TextUtils.isEmpty(extra)) {
                    CashierRouterHelper.from(this).success().finish();
                    return;
                }
                CatchException.run(() -> {
                    // pay_result_url 和 PayActivity.KEY_PAY_PROMOTION 保持一致
                    UriUtils.openForResult(getActivity(),
                            JsonString.parser(extra).get("pay_result_url"),
                            REQ_RESULT_PAGE);
                }).catchForReport("MTPayComponentRouterAdapter_onGotPayResult");
            }
        } else if (payResult == PayActionListener.CANCEL) {
            CashierRouterHelper.from(this).cancel().finish();
        } else {
            CashierRouterHelper.from(this).fail().message("").finish();
        }
    }


}
