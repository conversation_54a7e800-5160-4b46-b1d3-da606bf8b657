package com.meituan.android.cashier.dialogfragment;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;

import com.meituan.android.cashier.dialog.CommonGuideDialog;
import com.meituan.android.cashier.dialog.OnClickCommonDialogGuideButtonListener;
import com.meituan.android.cashier.model.bean.CashierPopWindowBean;
import com.meituan.android.cashier.model.bean.PopDetailInfo;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.pay.common.payment.data.PayStatus;
import com.meituan.android.paybase.common.fragment.MTPayBaseDialogFragment;
import com.meituan.android.paybase.dialog.BaseDialog;

/**
 * 引导弹窗5期-通用样式弹窗技术方案文档：https://km.sankuai.com/page/1206475250
 * <AUTHOR>
 */

public class CommonGuideFragment extends MTPayBaseDialogFragment {
    public static final String TAG = "CommonGuideFragment";
    private static final String  Common_POP_WINDOW_BEAN = "common_pop_window_bean";
    private CommonGuideDialog commonGuideDialog;
    private OnClickCommonDialogGuideButtonListener mOnClickCommonDialogGuideButtonListener;
    private CashierPopWindowBean mCashierPopWindowBean;

    public CommonGuideFragment() {

    }

    public static CommonGuideFragment newInstance(CashierPopWindowBean cashierPopWindowBean) {
        CommonGuideFragment fragment = new CommonGuideFragment();
        Bundle bundle = new Bundle();
        if (cashierPopWindowBean != null) {
            bundle.putSerializable(Common_POP_WINDOW_BEAN, cashierPopWindowBean);
        }
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected String getTAG() {
        return TAG;
    }

    @Override
    protected BaseDialog createDialog(Bundle savedInstanceState) {
        setCancelable(false);
        createDialog();
        return commonGuideDialog;
    }

    private void createDialog() {
        if (commonGuideDialog == null) {
            commonGuideDialog = new CommonGuideDialog(getContext(), mCashierPopWindowBean, mOnClickCommonDialogGuideButtonListener);
        }
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        if (getParentFragment() instanceof OnClickCommonDialogGuideButtonListener) {
            mOnClickCommonDialogGuideButtonListener = (OnClickCommonDialogGuideButtonListener) getParentFragment();
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            Bundle bundle = getArguments();
            mCashierPopWindowBean = (CashierPopWindowBean) bundle.getSerializable(Common_POP_WINDOW_BEAN);
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        mOnClickCommonDialogGuideButtonListener = null;
    }

    public static boolean allowShowCommonDialog(PopDetailInfo mGuideInfo) {
        if (mGuideInfo == null) {
            return false;
        }
        return isStatusAllowed(mGuideInfo) && !TextUtils.isEmpty(mGuideInfo.getMarketingMainTitle());
    }

    private static boolean isStatusAllowed(PopDetailInfo popDetailInfo) {
        MTPayment mtPayment = popDetailInfo.getGuidePayTypeInfo();
        if (mtPayment != null) {
            return mtPayment.getStatus() == PayStatus.NORMAL || mtPayment.getStatus() == PayStatus.ACTIVE;
        }
        return false;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        createDialog();
        commonGuideDialog.onActivityResult(requestCode, resultCode, data);
    }
}