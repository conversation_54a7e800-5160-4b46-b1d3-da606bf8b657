package com.meituan.android.cashier.dialog;

import com.meituan.android.cashier.model.bean.CashierPopWindowBean;
import com.meituan.android.pay.common.payment.bean.MTPayment;

/**
 * 通用弹窗按钮点击监听器，引导弹窗5期技术方案文档：https://km.sankuai.com/page/1206475250
 * <AUTHOR>
 */
public interface OnClickCommonDialogGuideButtonListener {
    void onClickCommonDialogGuideButton(MTPayment guidePayTypeInfo, CashierPopWindowBean cashierPopWindowBean);
}
