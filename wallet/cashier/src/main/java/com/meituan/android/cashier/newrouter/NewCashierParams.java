package com.meituan.android.cashier.newrouter;

import android.support.annotation.Keep;
import android.text.TextUtils;

import com.meituan.android.cashier.bean.CashierParams;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.utils.JsonString;
import com.meituan.android.paybase.utils.MapUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 收银台参数管理类,重新包装，解除和路由参数的耦合，专注管理业务参数
 */
@Keep
public class NewCashierParams implements Serializable {
    private String productType;
    private String tradeNo;
    private String payToken;
    private String merchantNo;
    private String extraData;
    private String extraStatics;
    private String callbackUrl;
    private String cif;
    private String downgradeInfo;
    // i版收银台使用
    private String webCashierUrl;
    // 标准收银台使用
    private String appId;
    private String guidePlanInfos;
    // 请求参数使用
    private String extDimStat;
    // 内部技术参数
    private String cashierUniqueId;
    private String cashierRouterTrace;

    private int installedApps;

    public NewCashierParams(CashierParams cashierParams) {
        if (!CashierParams.checkValid(cashierParams)) {
            return;
        }
        this.productType = cashierParams.getProductType();
        this.tradeNo = cashierParams.getTradeNo();
        this.payToken = cashierParams.getPayToken();
        this.merchantNo = cashierParams.getMerchantNo();
        this.extraData = cashierParams.getExtraData();
        this.extraStatics = cashierParams.getExtraStatics();
        this.callbackUrl = cashierParams.getCallbackUrl();
        this.cif = cashierParams.getCif();
        this.downgradeInfo = cashierParams.getDowngradeInfo();
        JsonString parser = JsonString.parser(extraData);
        this.appId = parser.get("app_id");
        this.guidePlanInfos = parser.get("guide_plan_infos");
        this.extDimStat = JsonString.builder().add("outer_business_statics", extraStatics).build();
        this.cashierUniqueId = cashierParams.getUniqueId();
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getPayToken() {
        return payToken;
    }

    public void setPayToken(String payToken) {
        this.payToken = payToken;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getExtraData() {
        return extraData;
    }

    public void setExtraData(String extraData) {
        this.extraData = extraData;
    }

    public String getExtraStatics() {
        return extraStatics;
    }

    public void setExtraStatics(String extraStatics) {
        this.extraStatics = extraStatics;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public String getCif() {
        return cif;
    }

    public void setCif(String cif) {
        this.cif = cif;
    }

    public String getDowngradeInfo() {
        return downgradeInfo;
    }

    public void setDowngradeInfo(String downgradeInfo) {
        this.downgradeInfo = downgradeInfo;
    }

    public String getWebCashierUrl() {
        return webCashierUrl;
    }

    public void setWebCashierUrl(String webCashierUrl) {
        this.webCashierUrl = webCashierUrl;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getGuidePlanInfos() {
        return guidePlanInfos;
    }

    public void setGuidePlanInfos(String guidePlanInfos) {
        this.guidePlanInfos = guidePlanInfos;
    }

    public String getExtDimStat() {
        return extDimStat;
    }

    public void setExtDimStat(String extDimStat) {
        this.extDimStat = extDimStat;
    }

    public String getCashierUniqueId() {
        return cashierUniqueId;
    }

    public void setCashierUniqueId(String cashierUniqueId) {
        this.cashierUniqueId = cashierUniqueId;
    }

    public HashMap<String, String> getExtendTransmissionParams() {
        return MapUtils.builder("cif", getCif()).buildAndCheck();
    }

    public String getCashierRouterTrace() {
        return cashierRouterTrace;
    }

    public void setCashierRouterTrace(String cashierRouterTrace) {
        this.cashierRouterTrace = cashierRouterTrace;
    }

    public int getInstalledApps() {
        return installedApps;
    }

    public void setInstalledApps(int installedApps) {
        this.installedApps = installedApps;
    }
}
