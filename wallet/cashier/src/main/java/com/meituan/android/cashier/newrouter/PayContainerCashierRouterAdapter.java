package com.meituan.android.cashier.newrouter;

import android.content.Intent;
import android.support.v4.app.Fragment;
import android.support.v4.app.FragmentActivity;
import android.text.TextUtils;

import com.google.gson.JsonObject;
import com.meituan.android.cashier.R;
import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.bean.CashierParams;
import com.meituan.android.cashier.data.params.PayParamsProvider;
import com.meituan.android.cashier.hybridwrapper.result.PayResultHelper;
import com.meituan.android.cashier.newrouter.predisplay.data.CashierInfo;
import com.meituan.android.cashier.newrouter.predisplay.data.PreDisplayInfo;
import com.meituan.android.cashier.newrouter.remake.CashierRouterAdapter;
import com.meituan.android.cashier.newrouter.remake.CashierRouterConstants;
import com.meituan.android.cashier.newrouter.remake.CashierRouterHelper;
import com.meituan.android.pay.base.fragment.FragmentUtils;
import com.meituan.android.pay.base.utils.exception.Getter;
import com.meituan.android.pay.base.utils.function.MapBuilder;
import com.meituan.android.pay.base.utils.log.PayLogger;
import com.meituan.android.pay.base.utils.observable.ObservableProvider;
import com.meituan.android.pay.base.utils.serialize.GsonUtils;
import com.meituan.android.pay.base.utils.serialize.JsonFinder;
import com.meituan.android.pay.common.component.container.PayContainerAPI;
import com.meituan.android.pay.common.component.container.PayContainerConst;
import com.meituan.android.pay.common.component.container.PayContainerRecceNeoFragment;
import com.meituan.android.pay.common.component.container.data.PayContainerData;
import com.meituan.android.pay.common.component.container.service.DomainService;
import com.meituan.android.pay.common.recce.RecceUtils;
import com.meituan.android.payrouter.remake.router.adapter.AbstractRouterAdapter;
import com.meituan.android.payrouter.remake.router.data.CheckResult;
import com.meituan.android.payrouter.remake.router.data.InvokeInfo;
import com.meituan.android.payrouter.remake.router.manager.RouterManager;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import java.util.Map;

/**
 * <AUTHOR>
 * pay_container_hybrid_adapter
 */
@ServiceLoaderInterface(key = CashierRouterConstants.ADAPTER_TYPE_PAY_CONTAINER_CASHIER, interfaceClass = AbstractRouterAdapter.class)
public class PayContainerCashierRouterAdapter extends CashierRouterAdapter {
    private static final String TAG_CONTENT_FRAGMENT = "content";
    private static final int FRAGMENT_ID = R.id.content;

    private PayContainerData payContainerData;

    @Override
    public CheckResult check() {
        if (!CashierParams.checkValid(cashierParams())) {
            return CheckResult.fail("001", "cashierParams check failed");
        }
        String cashierType = cashierParams().getProductType();
        JsonObject cashierConfig = cashierParams().preDisplayInfoParser().getCashierJsonConfig(cashierType);

        String nbBundleVersion = JsonFinder.with(cashierConfig).object("nb_bundle_version").getAsString();
        JsonFinder serverConfig = JsonFinder.with(cashierConfig).object("server_config");
        String nbFe = serverConfig.object("nb_fe").getAsString();
        String nbContainer = serverConfig.object("nb_container").getAsString();
        JsonObject clientConfig = JsonFinder.with(cashierConfig).object("client_config").getAsObject();

        if (TextUtils.isEmpty(nbBundleVersion)) {
            return CheckResult.fail("002", "nbBundleVersion is Empty");
        }
        if (clientConfig == null) {
            return CheckResult.fail("002", "config is Empty");
        }

        PayParamsProvider paramsProvider = PayParamsProvider.builder()
                .cashier(cashierParams())
                .report(cashierParams().getStartTime())
                .tech().verify().build();

        JsonObject businessParams = Getter.get(() ->
                GsonUtils.toJsonObject(paramsProvider.toClientParams()));
        JsonObject businessResponse = Getter.get(() ->
                cashierParams().preDisplayInfoParser().getNSFInfo());

        PayContainerAPI payContainerAPI = PayContainerAPI.from(cashierType, clientConfig)
                .setContainerType(PayContainerConst.CONTAINER_TYPE_RECCE_NEO);

        payContainerData = payContainerAPI.buildData();
        payContainerData.setBundleVersion(nbBundleVersion);
        payContainerData.setBundleName(RecceUtils.getBundleName(cashierType));
        payContainerData.setBusinessParams(businessParams);
        Map<String, Object> cashierProductEnvInfo = MapBuilder.object()
                .add(cashierType, MapBuilder.object()
                        .add("nb_container", nbContainer)
                        .add("nb_bundle_version", nbBundleVersion)
                        .add("nb_fe", nbFe).build())
                .build();
        String cashierProductEnvInfoString = GsonUtils.toString(cashierProductEnvInfo);
        payContainerData.nsfOperator(false)
                .setParam("nsf_response", businessResponse)
                .setNSFParams(MapBuilder.builder(paramsProvider.toServerParams())
                        .add("dispatcher_scene", "preCashierScene")
                        .add("cashier_product_env_info", cashierProductEnvInfoString)
                        .add("nb_fe", nbFe)
                        .add("from_nsf", "1")
                        .build());

        payContainerData.loadingOperator(false)
                .setParam("loading_visible", false)
                .setParam("loading_timeout_action", "");

        if (!payContainerAPI.check()) {
            return CheckResult.fail("003", "check not success");
        } else {
            return CheckResult.success();
        }
    }

    @Override
    public void invoke(InvokeInfo info) {
        super.invoke(info);
        PayContainerRecceNeoFragment fragment = PayContainerRecceNeoFragment
                .newInstance(getActivity(), payContainerData);
        onBindNeoV2Fragment(fragment);

        getActivity().getSupportFragmentManager()
                .beginTransaction()
                .replace(FRAGMENT_ID, fragment, TAG_CONTENT_FRAGMENT)
                .commitAllowingStateLoss();
    }

    @Override
    public void restore() {
        super.restore();
        PayContainerRecceNeoFragment fragment = FragmentUtils.findFragment(getActivity(),
                PayContainerRecceNeoFragment.class);
        onBindNeoV2Fragment(fragment);
    }

    private void onBindNeoV2Fragment(PayContainerRecceNeoFragment fragment) {
        if (fragment != null) {
            PreDisplayInfo preDisplayInfo = cashierParams().getPreDisplayInfo();
            if (preDisplayInfo != null) {
                CashierInfo cashierInfo = preDisplayInfo.getCashierInfo();
                if (cashierInfo != null) {
                    cashierInfo.setDisplayData(null);
                }
            }
            ObservableProvider.bind(getActivity())
                    .dispatch(DomainService.class)
                    .subscribe(fragment, new DomainService() {
                        @Override
                        public void downgrade(Intent intent) {
                            PayLogger.debug("PayContainerCashierRouterAdapter|downgrade", "");
                            onContainerDowngrade(fragment, null, null);
                        }

                        @Override
                        public void onLoadResult(Intent intent) {
                            RouterManager.notifier(trace()).notifyLoadSuccess("load success");
                        }

                        @Override
                        public void onResult(Intent intent) {
                            onContainerResult(fragment, intent);
                        }
                    });
        }
    }

    private void onContainerResult(Fragment fragment, Intent intent) {
        PayResultHelper helper = PayResultHelper.from(intent);
        if (PayResultHelper.PAY_RESULT_ACTION_DOWNGRADE.equals(helper.action())) {
            onContainerDowngrade(fragment, helper.getDowngradeType(), helper.downgradeInfo());
        } else if (PayResultHelper.PAY_RESULT_STATUS_SUCCESS.equals(helper.status())) {
            CashierRouterHelper.from(this).success().promotion(helper.promotion()).finish();
        } else if (PayResultHelper.PAY_RESULT_STATUS_FAIL.equals(helper.status())) {
            ((MTCashierActivity) getActivity()).setErrorCodeAndErrorMessage(helper.getErrorCode(),
                    helper.getErrorMsg());
            CashierRouterHelper.from(this).fail().message("").finish();
        } else {
            CashierRouterHelper.from(this).cancel().finish();
        }
    }

    private void onContainerDowngrade(Fragment fragment, String downgradeType, String downgradeInfo) {
        getActivity().getSupportFragmentManager()
                .beginTransaction()
                .remove(fragment)
                .commitAllowingStateLoss();
        CashierRouterHelper.from(this)
                .destProductType(downgradeType)
                .info(downgradeInfo)
                .downgrade();
    }

    @Override
    public FragmentActivity getActivity() {
        return (FragmentActivity) super.getActivity();
    }
}
