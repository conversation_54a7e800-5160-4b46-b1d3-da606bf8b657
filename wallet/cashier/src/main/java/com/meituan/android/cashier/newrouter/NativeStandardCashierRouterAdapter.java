package com.meituan.android.cashier.newrouter;

import android.os.Bundle;

import com.meituan.android.cashier.bean.CashierParams;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.newrouter.remake.CashierRouterAdapter;
import com.meituan.android.cashier.newrouter.remake.CashierRouterConstants;
import com.meituan.android.paybase.downgrading.DowngradingService;
import com.meituan.android.paybase.payrouter.constants.RouterAdapterConstants;
import com.meituan.android.paybase.utils.MTPayBaseClass;
import com.meituan.android.paycommon.lib.config.MTPayConfig;
import com.meituan.android.payrouter.adapter.PayRouterAdapterInterface;
import com.meituan.android.payrouter.remake.router.adapter.AbstractRouterAdapter;
import com.meituan.android.payrouter.remake.router.data.CheckResult;
import com.meituan.android.payrouter.remake.router.data.InvokeInfo;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

@ServiceLoaderInterface(key = CashierRouterConstants.ADAPTER_TYPE_NATIVE_STANDARD_CASHIER, interfaceClass = AbstractRouterAdapter.class)
@MTPayBaseClass
public class NativeStandardCashierRouterAdapter extends CashierRouterAdapter {
    private NativeStandardCashierHandler cashierHandler;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        DowngradingService.get().load(MTPayConfig.getProvider().getApplicationContext());
    }

    @Override
    public CheckResult check() {
        if (!CashierParams.checkValid(cashierParams())) {
            return CheckResult.fail("001", "cashierParams invalid");
        }
        return CheckResult.success();
    }

    @Override
    public void invoke(InvokeInfo info) {
        super.invoke(info);
        CashierStaticsUtils.reportModelEventWithViewEvent("c_PJmoK", "b_pay_p3cw2gqv_mv", "", null, cashierUniqueId());
        startNativeStandardCashier();
    }

    @Override
    public void restore() {
        super.restore();
        startNativeStandardCashier();
    }

    @Override
    public void onDestroy() {
        if (cashierHandler != null) {
            cashierHandler.onDestroy();
            cashierHandler = null;
        }
    }

    private void startNativeStandardCashier() {
        NewCashierParams cashierParams = new NewCashierParams(cashierParams());
        cashierParams.setCashierRouterTrace(adapterContext().trace());
        cashierHandler = new NativeStandardCashierHandler(adapterContext(), cashierParams);
        cashierHandler.onCreate(null);
    }
}
