package com.meituan.android.cashier.utils;

import android.text.TextUtils;

import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.model.bean.MTPaymentURL;
import com.meituan.android.pay.common.payment.data.PayType;
import com.meituan.android.pay.common.payment.data.WalletPayParams;
import com.meituan.android.pay.common.payment.utils.PayTypeUtils;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.utils.Base64;
import com.meituan.android.paybase.utils.LoganUtils;

import java.io.IOException;
import java.util.HashMap;

import static com.meituan.android.pay.common.payment.data.PayConstants.TRANS_ID;
import static com.meituan.android.pay.common.payment.data.WalletPayParams.KEY_LAUNCH_URL;
import static com.meituan.android.pay.desk.payment.verify.VerifyType.NO_PASSWORD_CASHIER;
import static com.meituan.android.pay.process.web.WebProcess.KEY_REAL_NAME_AUTH_URL;
import static com.meituan.android.pay.utils.HelpFunctionUtils.getOrderInfoMap;

/**
 * author: luo jing
 * date: 2022/3/31 20:26
 * description: gohellopay 埋点场景区分
 */

public final class GoHelloPaySceneUtils {
    private GoHelloPaySceneUtils() {

    }

    /**
     * gohellopay 流量分布埋点 1，去cashdesk；2，免密支付；3，月付实名/国际卡支付；4，独立收银台；5，签约支付
     */
    private static HashMap<String, Object> getGoHelloPayScene(MTPaymentURL mtPaymentURL) {
        String url = mtPaymentURL.getUrl();
        String orderInfo = null;
        String scene = "0";
        String launchUrl = "";
        String payType = "";
        String qdbNo = "";
        try {
            orderInfo = new String(Base64.decode(url));
            HashMap<String, String> orderInfoMap = getOrderInfoMap(orderInfo);
            payType = orderInfoMap.get(PayType.KEY);
            launchUrl = orderInfoMap.get(KEY_LAUNCH_URL);
            String verifyType = orderInfoMap.get(WalletPayParams.KEY_VERIFY_TYPE);
            String realNameUrl = orderInfoMap.get(KEY_REAL_NAME_AUTH_URL);
            if (TextUtils.equals("/qdbsign/sign", launchUrl)) {
                scene = "5";
            } else if (TextUtils.equals("/qdbdisplay/cashdesk", launchUrl)) {
                scene = "1";
            } else if (PayTypeUtils.isForeignCardPay(payType) || (PayTypeUtils.isCreditPay(payType)
                    && !TextUtils.isEmpty(realNameUrl))) {
                scene = "3";
            } else if (TextUtils.equals(String.valueOf(NO_PASSWORD_CASHIER), verifyType)) {
                scene = "2";
            } else if (TextUtils.equals("/qdbdisplay/mtpaycashier", launchUrl)) {
                scene = "4";
            }
            qdbNo = orderInfoMap.get(TRANS_ID);
        } catch (IOException e) {
            AnalyseUtils.techMis("b_pay_5ijm6qk8_mv", new AnalyseUtils.MapBuilder()
                    .add("message", "直连url调起异常,base64解析错误")
                    .add("exception", "base64_" + url + "_exception_" + e.toString())
                    .build());
        }
        // 四个元素，一个场景，一个请求的path，一个 qdb_no，一个 pay_type全是埋点需要值。
        return new AnalyseUtils.MapBuilder().add("dispatch_scene", scene)
                .add("launch_url", launchUrl)
                .add("pay_type", payType)
                .add("qdb_no", qdbNo).build();
    }

    /**
     * 美团支付SLA起点
     */
    public static void reportMtPaySLAStart(MTPaymentURL mtPaymentURL, String nbContainer, String uniqueId) {
        if (mtPaymentURL == null || TextUtils.isEmpty(mtPaymentURL.getUrl())) {
            return;
        }
        HashMap<String, Object> map = GoHelloPaySceneUtils.getGoHelloPayScene(mtPaymentURL);
        map.put("nb_container", nbContainer);
        CashierStaticsUtils.logCustomRequestSuccess("mt_pay_dispatch", map, uniqueId);
        LoganUtils.log("b_pay_mt_pay_dispatch", map);
        //签约支付，独立收银台不计算sla起点
        String path = String.valueOf(map.get("launch_url"));
        if (TextUtils.equals("/qdbsign/sign", path)
                || TextUtils.equals("/qdbdisplay/mtpaycashier", path)) {

        } else {//美团支付sla起点-内部起点
            CashierStaticsUtils.logCustomRequestStart("standard_cashier_mt_pay_start", uniqueId);
            map.put("cashier_type", "wallet");
            LoganUtils.log("美团支付SLA起点", map);
        }
    }
}
