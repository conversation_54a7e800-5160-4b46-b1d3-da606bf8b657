package com.meituan.android.cashier.widget;

import android.view.View;
import android.view.ViewGroup;

/**
 * <AUTHOR>
 */
public class ViewUtils {
    // 判断是否是子view
    public static boolean isChild(ViewGroup father, View child) {
        if (father == null || child == null) {
            return false;
        }
        if (father == child) {
            return true;
        }
        for (int i = 0; i < father.getChildCount(); i++) {
            View next = father.getChildAt(i);
            if (next instanceof ViewGroup && isChild((ViewGroup) next, child)) {
                return true;
            }
        }
        return false;
    }
}
