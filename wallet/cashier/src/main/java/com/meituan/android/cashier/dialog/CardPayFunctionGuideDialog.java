package com.meituan.android.cashier.dialog;

import android.content.Context;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.view.Window;
import android.widget.TextView;

import com.meituan.android.cashier.R;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.model.bean.CashierPopWindowBean;
import com.meituan.android.cashier.model.bean.PopDetailInfo;
import com.meituan.android.cashier.utils.NativeStandardCashierPayProcessStatistic;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.pay.desk.pack.WalletPayManager;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.dialog.BaseDialog;
import com.meituan.android.paybase.utils.FontUtils;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.StatisticsUtils;
import com.meituan.android.paybase.utils.Strings;

import java.util.HashMap;
import java.util.Map;

import static com.meituan.android.cashier.base.utils.CashierAnalyseUtils.isPromotionScene;

public class CardPayFunctionGuideDialog extends BaseDialog {

    private HashMap<String, Object> technologyParameters;
    private OnClickGuidePayTypeListener mOnClickGuidePayTypeListener;
    private PopDetailInfo mGuideInfo;
    private CashierPopWindowBean mCashierPopWindowBean;

    public CardPayFunctionGuideDialog(Context context) {
        super(context);
        initView();
    }

    public CardPayFunctionGuideDialog(Context context, CashierPopWindowBean cashierPopWindowBean,
                                      OnClickGuidePayTypeListener onClickGuidePayTypeListener) {
        super(context, R.style.cashier__card_pay_guide_transparent_dialog);
        if (cashierPopWindowBean != null) {
            mCashierPopWindowBean = cashierPopWindowBean;
            mGuideInfo = cashierPopWindowBean.getPopDetailInfo();
        }
        mOnClickGuidePayTypeListener = onClickGuidePayTypeListener;
        initView();
    }

    private void initView() {
        addBusinessParameters();
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setCanceledOnTouchOutside(false);
        setContentView(R.layout.cashier__card_pay_function_guide_dialog);
        findViewById(R.id.card_pay_guide_dialog_close).setOnClickListener(v -> {
            reportOnClick("cancel");
            dismiss();
            CashierStaticsUtils.logModelEvent("c_PJmoK",
                    isPromotionScene(mCashierPopWindowBean) ? "b_pay_nvb88kbl_mc" : "b_pay_sod9pe8x_mc",
                    isPromotionScene(mCashierPopWindowBean) ? "引导绑多卡弹窗-关闭按钮" : "收银台首页-拉新优惠弹窗-关闭",
                    technologyParameters, StatisticsUtils.EventType.CLICK, getUniqueId());
        });
        if (mGuideInfo != null) {
            TextView title = findViewById(R.id.card_pay_guide_dialog_title);
            title.setText(mGuideInfo.getTitle());
            TextView money = findViewById(R.id.card_pay_guide_dialog_money);
            Typeface typeFace = FontUtils.getKeyboardFontType(getContext());
            if (typeFace != null) {
                ((TextView) findViewById(R.id.card_pay_guide_dialog_money_symbol)).setTypeface(typeFace);
                money.setTypeface(typeFace);
            }
            money.setText(Strings.getFormattedDoubleValueWithZero(mGuideInfo.getPromotionMoney()));
            TextView subtitle = findViewById(R.id.card_pay_guide_dialog_subtitle);
            subtitle.setText(mGuideInfo.getSubtitle());
            TextView button = findViewById(R.id.card_pay_guide_dialog_button);
            button.setText(mGuideInfo.getGuideButton());
            findViewById(R.id.card_pay_guide_dialog_button).setOnClickListener(v -> {
                reportOnClick("ensure");
                dismiss();
                WalletPayManager.getInstance().setOpenSource(getOwnerActivity(), "Beforepay_popwindow");
                if (mOnClickGuidePayTypeListener != null) {
                    mOnClickGuidePayTypeListener.onClickGuidePayType(mGuideInfo.getGuidePayTypeInfo());
                }
                technologyParameters.put("open_source", "Beforepay_popwindow");
                if (mGuideInfo != null) {
                    NativeStandardCashierPayProcessStatistic.putCreditPayInfo(technologyParameters, mGuideInfo.getGuidePayTypeInfo());
                }
                CashierStaticsUtils.logModelEvent("c_PJmoK",
                        isPromotionScene(mCashierPopWindowBean) ? "b_pay_pupgzmqd_mc" : "b_pay_inig81vs_mc",
                        isPromotionScene(mCashierPopWindowBean) ? "引导绑多卡弹窗-主按钮" : "收银台首页-拉新优惠弹窗-绑卡",
                        technologyParameters, StatisticsUtils.EventType.CLICK, getUniqueId());
                analyseClickConfirmButton(mGuideInfo.getGuidePayTypeInfo());
            });
        }
        technologyParameters.put("open_source", "Beforepay_popwindow");
        if (mGuideInfo != null) {
            NativeStandardCashierPayProcessStatistic.putCreditPayInfo(technologyParameters, mGuideInfo.getGuidePayTypeInfo());
        }
        CashierStaticsUtils.logModelEvent("c_PJmoK",
                isPromotionScene(mCashierPopWindowBean) ? "b_pay_emgbc2xg_mv" : "b_pay_fabizu1a_mv",
                isPromotionScene(mCashierPopWindowBean) ? "引导绑多卡弹窗" : "收银台首页-拉新优惠弹窗",
                technologyParameters, StatisticsUtils.EventType.VIEW, getUniqueId());

    }

    /**
     * 上报弹窗的业务参数
     * "pop_scene"：支付前场景或者三方中断场景
     * "style_type"：为0时，代表老样式
     * "ad_id"：广告id，旧弹窗中为兜底值"-999"
     */
    private void addBusinessParameters(){
        technologyParameters = CashierStaticsUtils.getTechnologyParameters();
        if (mGuideInfo != null && !TextUtils.isEmpty(mGuideInfo.getPopScene())) {
            technologyParameters.put("pop_scene", mGuideInfo.getPopScene());
        }
        technologyParameters.put("style_type", "0");
        technologyParameters.put("ad_id", "-999");
        technologyParameters.put("pay_type", getPayType());
    }

    private String getPayType() {
        if (mCashierPopWindowBean != null && mCashierPopWindowBean.getPopDetailInfo() != null && mCashierPopWindowBean.getPopDetailInfo().getGuidePayTypeInfo() != null) {
            return mCashierPopWindowBean.getPopDetailInfo().getGuidePayTypeInfo().getPayType();
        }
        return "";
    }

    private void reportOnClick(String button) {
        Map<String, Object> customTags = new HashMap<>();
        customTags.put("style", "function_style");
        if (mCashierPopWindowBean != null && mCashierPopWindowBean.getPopDetailInfo() != null && mCashierPopWindowBean.getPopDetailInfo().getGuidePayTypeInfo() != null) {
            customTags.put("pay_type", mCashierPopWindowBean.getPopDetailInfo().getGuidePayTypeInfo().getPayType());
        }
        customTags.put("click", button);
        CashierStaticsUtils.logCustom("paybiz_bind_card_guide_dialog_click", customTags, null, getUniqueId());
    }

    private void analyseClickConfirmButton(MTPayment guidePayTypeInfo) {
        if (guidePayTypeInfo == null) {
            return;
        }
        if (!TextUtils.isEmpty(guidePayTypeInfo.getPayType())) {
            HashMap<String, Object> map = new AnalyseUtils.MapBuilder().add("pay_type", guidePayTypeInfo.getPayType()).build();
            LoganUtils.log("standard_cashier_mt_pay_confirm", map);
            CashierStaticsUtils.logCustom("standard_cashier_mt_pay_confirm", map, null, getUniqueId());
        }
    }
}
