package com.meituan.android.cashier.bridge.icashier;

import static com.meituan.android.cashier.bridge.icashier.ICashierJSHandler.VALUE_CODE_COMMON_BACK_CANCEL;
import static com.meituan.android.cashier.bridge.icashier.ICashierJSHandler.jsCallbackError;
import static com.meituan.android.cashier.bridge.icashier.ICashierJSHandler.jsCallbackPaySucc;
import static com.meituan.android.cashier.bridge.icashier.ICashierJSHandler.logCat;
import static com.meituan.android.cashier.bridge.icashier.ICashierJSHandler.logMV;
import static com.meituan.android.cashier.bridge.icashier.ICashierJSHandler.logSC;
import static com.meituan.android.cashier.utils.GoHelloPaySceneUtils.reportMtPaySLAStart;
import static com.meituan.android.pay.desk.component.analyse.DeskAnalyseUtils.analyseComponentDispatch;
import static com.meituan.android.pay.desk.component.analyse.DeskCatConstants.ACTION_COMPONENT_START;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.meituan.android.cashier.common.CashierCatConstants;
import com.meituan.android.cashier.common.HybridReportConstants;
import com.meituan.android.cashier.model.bean.CashierPopWindowBean;
import com.meituan.android.cashier.model.bean.MTPaymentURL;
import com.meituan.android.cashier.model.bean.PayLaterSubmitBean;
import com.meituan.android.cashier.model.params.PayParams;
import com.meituan.android.cashier.retrofit.CashierRequestService;
import com.meituan.android.cashier.retrofit.CashierRequestUtils;
import com.meituan.android.neohybrid.neo.report.MapBuilder;
import com.meituan.android.pay.common.analyse.MtPaySceneUtils;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.pay.desk.pack.WalletPayManager;
import com.meituan.android.pay.desk.payment.bean.standarddesk.WalletPayment;
import com.meituan.android.pay.utils.MeituanPayAPI;
import com.meituan.android.paybase.downgrading.PayHornConfigBean;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.retrofit.PayException;
import com.meituan.android.paybase.utils.Base64;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.MTPaySuppressFBWarnings;
import com.meituan.android.paycommon.lib.retrofit.PayRetrofit;
import com.meituan.android.paycommon.lib.utils.OuterBusinessParamUtils;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * author:  kkli
 * date:    2019-10-14
 * description: 必须被持有，否则容易被回收
 */
public class ICashierPayerHandler implements IRequestCallback {
    private static final int REQ_TAG_GO_HELLO_PAY = 0x233;
    private static final int REQ_TAG_PAY_ACTIVITY = 1314;

    private static final String KEY_STATUS = "status";
    private static final String KEY_DATA = "data";
    public static final String KEY_DATA_CODE = "code";
    public static final String KEY_DATA_MESSAGE = "message";
    private static final String KEY_DATA_LEVEL = "level";
    private static final String KEY_DATA_TYPE = "type";
    public static final String KEY_DATA_EXTRA = "extra";
    public static final String KEY_CREDIT_OPEN = "openCreditPayStatus";
    static final String KEY_GUIDE_PLAN_INFO = "guide_plan_infos";
    static final String VALUE_STATUS_PAY_SUCCESS = "pay_success";
    public static final String VALUE_STATUS_PAY_CANCEL = "pay_cancel";
    private static final String VALUE_STATUS_PAY_FAIL = "pay_fail";
    private static final String VALUE_STATUS_PAY_OVERTIME = "pay_overtime";
    private static final String VALUE_STATUS_PAY_FATAL_ERROR = "pay_fatal_error";
    private static final String VALUE_STATUS_NATIVE_EXCEPTION = "native_exception";
    private static final String VALUE_STATUS_PAY_EXCEPTION = "pay_exception";
    private static final String VALUE_STATUS_GO_HELLO_PAY_OVERLOAD = "go_hello_pay_overload";
    private static final String KEY_PAY_RESULT = "pay_result";
    private static final String KEY_PAY_MESSAGE = "pay_msg";
    private static final String KEY_PAY_RESULT_CANCEL = "pay_result_cancel";
    private static final String KEY_PAY_ERROR_CODE = "pay_error_code";
    private static final String KEY_PAY_FAILED_EXTRA = "pay_failed_extra"; // 美团支付失败场景下的额外信息，判断是否需要刷新收银台

    private static final int KEY_MT_PAY_SUCCESS = 1;
    private static final int KEY_MT_PAY_CANCEL = 2;
    private static final int KEY_MT_PAY_FAIL = 3;
    private static final int KEY_MT_PAY_OVERTIME = 4;
    private static final int KEY_MT_PAY_FATAL_ERROR = 5;

    private static final String KEY_NB_CONTAINER = "nb_container";
    private static final String VALUE_NB_CONTAINER = "hybrid";

    private ICashierJSHandler jsHandler;

    private ICashierPayerHandler(ICashierJSHandler jsHandler) {
        this.jsHandler = jsHandler;
    }

    @Override
    public void onRequestSucc(int tag, Object obj) {
        if (tag == REQ_TAG_GO_HELLO_PAY) {
            // 埋点：{goHellopay 请求业务成功}
            logCat(jsHandler, CashierCatConstants.COMMAND_REQUEST_GOHELLOPAY_SUCCESS, null);
            logMV(jsHandler, HybridReportConstants.HYBRID_CASHIER_GOHELLOPAY_REQUEST_SUCC, HybridReportConstants.CID_HYBRID_CASHIER_COMMON, null);
            MTPaymentURL mtPaymentURL = (MTPaymentURL) obj;
            if(mtPaymentURL !=null ){
                PayHornConfigBean.setGmDegradeFlag(mtPaymentURL.getUrl());
            }
            if (mtPaymentURL == null) {
                jsCallbackError(jsHandler, ICashierJSHandler.JS_CALLBACK_BACKGROUND_ERROR,
                        MapBuilder.builder("step", "onRequestSucc").build("reason", "mtPaymentURL is null"));
                return;
            }
            if (mtPaymentURL.getOverLoadInfo() != null && mtPaymentURL.getOverLoadInfo().isStatus()) {
                logCat(jsHandler, VALUE_STATUS_GO_HELLO_PAY_OVERLOAD, null);
                logSC(jsHandler, HybridReportConstants.MTPAY_BRIDGE_RETURN_FREQUENCY_CONTROLLER, null);
                ICashierJSHandler.jsCallbackNotPaySucc(jsHandler, VALUE_STATUS_GO_HELLO_PAY_OVERLOAD, mtPaymentURL.getOverLoadInfo());
            } else {
                String mixedUrl = addParamsToBase64Url(mtPaymentURL.getUrl());
                startMeituanPay(mixedUrl, jsHandler != null ? jsHandler.getTradeNo() : "");
                reportHybridSLAStart(mtPaymentURL);
            }
        }
    }

    @Override
    public void onRequestException(int tag, Exception e) {
        if (tag == REQ_TAG_GO_HELLO_PAY) {
            logSC(jsHandler, HybridReportConstants.MTPAY_BRIDGE_REQUEST_FAIL, null);
            handleException(e);
        }
    }

    @Override
    public void onRequestStart(int tag) {
    }

    @Override
    public void onRequestFinal(int tag) {
    }

    private void handleException(Exception exception) {
        if (exception instanceof PayException) {
            PayException payException = (PayException) exception;
            //  埋点：{gohellopay 请求业务失败}
            logCat(jsHandler, CashierCatConstants.COMMAND_REQUEST_GOHELLOPAY_FAIL, null);
            logMV(jsHandler, HybridReportConstants.HYBRID_CASHIER_GOHELLOPAY_REQUEST_FAIL, HybridReportConstants.CID_HYBRID_CASHIER_COMMON, MapBuilder.builder("errorCode", payException.getCode()).build());
            try {
                JSONObject eJson = new JSONObject();
                eJson.put(KEY_DATA_CODE, payException.getCode());
                eJson.put(KEY_DATA_MESSAGE, payException.getMessage());
                eJson.put(KEY_DATA_LEVEL, payException.getLevel());
                eJson.put(KEY_DATA_TYPE, payException.getType());
                eJson.put(KEY_DATA_EXTRA, payException.getExtra());
                ICashierJSHandler.jsCallbackNotPaySucc(jsHandler, VALUE_STATUS_PAY_EXCEPTION, eJson);
            } catch (Exception e) {
                jsCallbackError(jsHandler, ICashierJSHandler.JS_CALLBACK_PARAMS_PARSER_ERROR,
                        MapBuilder.builder("step", "handleException").build("reason", "catch exception: " + e.getMessage()));
            }
        } else {
            // 埋点：{gohellopay 请求业务失败}
            logCat(jsHandler, CashierCatConstants.COMMAND_REQUEST_GOHELLOPAY_FAIL, null);
            logMV(jsHandler, HybridReportConstants.HYBRID_CASHIER_GOHELLOPAY_REQUEST_FAIL, HybridReportConstants.CID_HYBRID_CASHIER_COMMON, MapBuilder.builder("errorCode", 0).build());
            ICashierJSHandler.jsCallbackNotPaySucc(jsHandler, VALUE_STATUS_NATIVE_EXCEPTION, null);
        }
    }

    @MTPaySuppressFBWarnings("RCN_REDUNDANT_NULLCHECK_OF_NONNULL_VALUE")
    private void startMeituanPay(String url, String tradeNo) {
        if (jsHandler == null || TextUtils.isEmpty(url)) {
            jsCallbackError(jsHandler, ICashierJSHandler.JS_CALLBACK_ILLEGAL_STATE,
                    MapBuilder.builder("step", "startMeituanPay").build("reason", "url is null"));
            return;
        }
        if (jsHandler != null && jsHandler.jsHost() != null) {
            Activity activity = jsHandler.jsHost().getActivity();
            if (activity != null) {
                logSC(jsHandler, HybridReportConstants.MTPAY_BRIDGE_RETURN_AND_PAY,
                        MapBuilder.builder("trade_no", tradeNo).build());
                MeituanPayAPI.openMeituanPayWithUrl(activity, url, tradeNo, REQ_TAG_PAY_ACTIVITY);
                activity.overridePendingTransition(0, 0);
                return;
            }
        }
        jsCallbackError(this.jsHandler, ICashierJSHandler.JS_CALLBACK_ILLEGAL_STATE,
                MapBuilder.builder("step", "startMeituanPay").build("reason", "activity is null"));
    }

    // 把nb_container参数添加到base64编码的url中
    // 先把base64解码为json，然后添加nb_container参数，然后再进行base64编码
    private String addParamsToBase64Url(String url) {
        if (TextUtils.isEmpty(url)) {
            return url;
        }
        String mixedUrl = url;
        try {
            String decodeUrl = new String(Base64.decode(url));
            JsonObject urlJson = new JsonParser().parse(decodeUrl).getAsJsonObject();
            if (urlJson != null) {
                urlJson.addProperty(KEY_NB_CONTAINER, VALUE_NB_CONTAINER);
                mixedUrl = Base64.encodeBytes(urlJson.toString().getBytes());
            }
        } catch (Exception e) {
            LoganUtils.logError("ICashierPayerHandler_addParamsInBase64Url", e.getMessage());
        }
        return mixedUrl;
    }

    static void appendOpenSource(Activity activity, HashMap<String, String> params) {
        WalletPayManager.appendOpenSource(params, WalletPayManager.getInstance().getOpenSource(activity));
    }

    // tools
    static void startGoHelloPay(ICashierJSHandler jsHandler, PayParams payParams) {
        if (jsHandler == null || payParams == null) {
            jsCallbackError(jsHandler, ICashierJSHandler.JS_CALLBACK_PARAMS_CHECK_ERROR,
                    MapBuilder.builder("step", "startGoHelloPay").build("reason", "payParams is null"));
            return;
        }
        // hybrid调用goHelloPay需要设置nb_container参数
        HashMap<String, String> params = CashierRequestUtils.getHelloPayMap(payParams);
        if (params == null) {
            params = new HashMap<>();
        }
        params.put(KEY_NB_CONTAINER, VALUE_NB_CONTAINER);
        ICashierPayerHandler payerHandler = new ICashierPayerHandler(jsHandler);
        jsHandler.setPayerHandler(payerHandler);
        logSC(jsHandler, HybridReportConstants.MTPAY_BRIDGE_REQUEST, null);
        appendOpenSource(jsHandler.getActivity(), params);
        // gohellopay 请求开始埋点
        logCat(jsHandler, CashierCatConstants.COMMAND_REQUEST_GOHELLOPAY_START, null);
        logMV(jsHandler, HybridReportConstants.HYBRID_CASHIER_GOHELLOPAY_REQUEST_START, HybridReportConstants.CID_HYBRID_CASHIER_COMMON, null);
        OuterBusinessParamUtils.appendExtraParamsTogoHelloPay(jsHandler, params);
        PayRetrofit.getInstance().create(CashierRequestService.class, payerHandler, REQ_TAG_GO_HELLO_PAY).
                goHelloPay(params);
        analyseComponentDispatch(ACTION_COMPONENT_START, MtPaySceneUtils.CASHIER_PAY);
    }

    static void startGoHelloPay(WalletPayment walletPayment, ICashierJSHandler jsHandler,
                                MTPayment mtPayment, String extraData, CashierPopWindowBean popWindowBean,
                                JSONObject transmissionParam, String cashierType) {
        if (jsHandler == null || mtPayment == null) {
            jsCallbackError(jsHandler, ICashierJSHandler.JS_CALLBACK_PARAMS_CHECK_ERROR,
                    MapBuilder.builder("step", "startGoHelloPay").build("reason", "mtPayment is null"));
            return;
        }
        PayParams payParams = genPayParams(walletPayment, jsHandler, mtPayment, extraData, popWindowBean, cashierType);
        // 新拉新收银台探测二期：需要额外添加transmission参数
        try {
            if (transmissionParam != null && payParams != null) {
                Map<String, String> walletPayParams = payParams.walletPayParams;
                if (!CollectionUtils.isEmpty(walletPayParams)) {
                    String payExtendParamsString = walletPayParams.get("payExtendParams");
                    JSONObject payExtendParams = TextUtils.isEmpty(payExtendParamsString) ? new JSONObject() :
                            new JSONObject(payExtendParamsString);
                    String oldTransmissionParamString = payExtendParams.optString("transmission_param");
                    JSONObject oldTransmissionParam = TextUtils.isEmpty(oldTransmissionParamString) ? new JSONObject() :
                            new JSONObject(oldTransmissionParamString);
                    for (Iterator<String> itr = transmissionParam.keys(); itr.hasNext(); ) {
                        String key = itr.next();
                        if (!TextUtils.isEmpty(key)) {
                            oldTransmissionParam.put(key, transmissionParam.opt(key));
                        }
                    }
                    payExtendParams.put("transmission_param", oldTransmissionParam.toString());
                    walletPayParams.put("payExtendParams", payExtendParams.toString());
                }
            }
        } catch (Exception ignored) {
        }

        appendNewCreditPayOpenResultParams(payParams, jsHandler);

        startGoHelloPay(jsHandler, payParams);
    }

    /**
     * 美团支付的参数 payExtendParams 中增加 verify_scene。月付开通新流程（半页样式）需要再开通成功之后，给服务端传递
     * 该参数，服务端据此可决策出支付时是否免验（月付开通已经验证过一次），避免月付开通和支付重复验证。
     */
    private static void appendNewCreditPayOpenResultParams(PayParams payParams, ICashierJSHandler jsHandler) {
        try {
            if (payParams != null && jsHandler != null && !TextUtils.isEmpty(jsHandler.getVerifyScene())) {
                Map<String, String> walletPayParams = payParams.walletPayParams;
                if (!CollectionUtils.isEmpty(walletPayParams)) {
                    String payExtendParamsString = walletPayParams.get("payExtendParams");
                    JSONObject payExtendParams = TextUtils.isEmpty(payExtendParamsString) ? new JSONObject() :
                            new JSONObject(payExtendParamsString);
                    payExtendParams.put(ICashierJSHandler.KEY_NEW_CREDITPAY_OPEN_VERIFY_SCENE, jsHandler.getVerifyScene());
                    walletPayParams.put("payExtendParams", payExtendParams.toString());
                }
            }
        } catch (Exception e) {
            LoganUtils.logError("ICashierPayerHandler_appendNewCreditPayOpenResultParams", e.getMessage());
        }
    }

    private static PayParams genPayParams(WalletPayment walletPayment, ICashierJSHandler jsHandler,
                                          MTPayment mtPayment, String extraData,
                                          CashierPopWindowBean popWindowBean, String cashierType) {
        PayParams payParams = null;
        // 当为先享后付弹窗场景时
        if (popWindowBean != null && popWindowBean.getType() == CashierPopWindowBean.POPWINDOW_PAYLATER_GUIDE
                && popWindowBean.getPayLaterPopDetailInfoBean() != null) {
            PayLaterSubmitBean submitBean = popWindowBean.getPayLaterPopDetailInfoBean().getPayLaterSubmitBean();
            // 如果是月付引导 或 新卡支付引导（不需要上传pay_type），则用单独的生成方法
            if (submitBean != null && (submitBean.openCreditPay() || submitBean.bindNewCard())) {
                payParams = ICashierPayParams.genPayParamsForOpenCreditCard(jsHandler.getTradeNo(),
                        jsHandler.getPayToken());
            }
        }
        if (payParams == null) {
            payParams = ICashierPayParams.genPayParams(jsHandler.getActivity(), walletPayment, mtPayment, null,
                    ICashierPayParams.ExtParams.init(jsHandler.getTradeNo(), jsHandler.getPayToken(),
                            TextUtils.isEmpty(cashierType) ? ICashierPayParams.CASHIER_TYPE_WALLET : cashierType,
                            ICashierPayParams.PARAM_MONEY_CHANGED_NOT,
                            ICashierPayParams.PARAM_FROM_SELECT_BANKCARD_NOT));
        }
        // 追加guidePlanInfos字段
        ICashierPayParams.appendGuidePlanInfos(payParams, extraData);
        // 当是先享后付场景时追加guideRequestNo字段
        if (popWindowBean != null && popWindowBean.getPayLaterPopDetailInfoBean() != null) {
            PayLaterSubmitBean submitBean = popWindowBean.getPayLaterPopDetailInfoBean().getPayLaterSubmitBean();
            String guidRequestNo = popWindowBean.getPayLaterPopDetailInfoBean().getGuideRequestNo();
            if (guidRequestNo == null) {
                guidRequestNo = "";
            }
            String utmSource = popWindowBean.getPayLaterPopDetailInfoBean().getUtmSource();
            if (!TextUtils.isEmpty(utmSource)) {
                submitBean.setPromotionInfo("{\"utmSource\":\"" + utmSource + "\"}");
            }
            String ext = popWindowBean.getPayLaterPopDetailInfoBean().getExt();
            if (!TextUtils.isEmpty(ext)) {
                submitBean.setExt(ext);
            }
            payParams.openWithholdInfoBefore = GsonProvider.getInstance().toJson(submitBean);
            if (payParams.walletPayParams == null) {
                payParams.walletPayParams = new HashMap<>();
            }
            CashierRequestUtils.setGoHelloPayExtParamToParams(payParams.walletPayParams, guidRequestNo, jsHandler.getDowngradeErrorInfo());
        }
        return payParams;
    }

    static void handlePayResultCode(ICashierJSHandler jsHandler, int requestCode, int resultCode, Intent data, boolean isCreditOpen) {
        if (jsHandler == null) {
            return;
        }
        if (requestCode == REQ_TAG_PAY_ACTIVITY) {
            logSC(jsHandler, HybridReportConstants.MTPAY_BRIDGE_RETURN_RESULT, null);
            if (resultCode == Activity.RESULT_OK && data != null) {
                isCreditOpen = isCreditOpen || data.getBooleanExtra(KEY_CREDIT_OPEN, false);
                int payResultCode = data.getIntExtra(KEY_PAY_RESULT, -1);
                String payMessage = data.getStringExtra(KEY_PAY_MESSAGE);
                int payErrorCode = data.getIntExtra(KEY_PAY_ERROR_CODE, 0);
                boolean payResultCancel = data.getBooleanExtra(KEY_PAY_RESULT_CANCEL, false);
                String payFailedExtra = data.getStringExtra(KEY_PAY_FAILED_EXTRA);
                if (payResultCode == KEY_MT_PAY_SUCCESS) {
                    // 获取参数
                    JSONObject promotion = null;
                    try {
                        JSONObject extraJson = new JSONObject(payFailedExtra);
                        String promotionString = extraJson.optString("pay_promotion");
                        if (!TextUtils.isEmpty(promotionString)) {
                            promotion = new JSONObject(promotionString);
                        }
                    } catch (Exception ignored) {
                    }
                    jsCallbackPaySucc(jsHandler, VALUE_STATUS_PAY_SUCCESS, isCreditOpen, promotion);
                    // 美团支付成功
                    logCat(jsHandler, CashierCatConstants.COMMAND_REQUEST_MTPAY_PAY_SUCCESS, null);
                    logMV(jsHandler, HybridReportConstants.HYBRID_CASHIER_MAPAY_SUCC, HybridReportConstants.CID_HYBRID_CASHIER_COMMON, null);
                } else if (payResultCode == KEY_MT_PAY_FAIL) {
                    jsCallbackPaySucc(jsHandler, payResultCancel ? VALUE_STATUS_PAY_CANCEL : VALUE_STATUS_PAY_FAIL, payErrorCode, payMessage, payFailedExtra, isCreditOpen);
                } else if (payResultCode == KEY_MT_PAY_OVERTIME) {
                    jsCallbackPaySucc(jsHandler, VALUE_STATUS_PAY_OVERTIME, isCreditOpen);
                } else if (payResultCode == KEY_MT_PAY_FATAL_ERROR) {
                    jsCallbackPaySucc(jsHandler, VALUE_STATUS_PAY_FATAL_ERROR, isCreditOpen);
                } else {
                    jsCallbackError(jsHandler, ICashierJSHandler.JS_CALLBACK_ILLEGAL_STATE,
                            MapBuilder.builder("step", "handlePayResultCode").add("payResultCode", payResultCode)
                                    .build("reason", "payResultCode error"));
                }
            } else if (resultCode == Activity.RESULT_CANCELED) {
                jsCallbackPaySucc(jsHandler, VALUE_STATUS_PAY_CANCEL, VALUE_CODE_COMMON_BACK_CANCEL, "点击back键退出", data == null ? isCreditOpen : isCreditOpen || data.getBooleanExtra(KEY_CREDIT_OPEN, false));
            } else {
                jsCallbackError(jsHandler, ICashierJSHandler.JS_CALLBACK_ILLEGAL_STATE,
                        MapBuilder.builder("step", "handlePayResultCode").add("resultCode", resultCode)
                                .build("reason", "resultCode error"));
            }
        }
    }

    private void reportHybridSLAStart(MTPaymentURL mtPaymentURL) {
        String uniqueId = "";
        if (jsHandler != null) {
            uniqueId = jsHandler.getUniqueId();
        }
        reportMtPaySLAStart(mtPaymentURL, "hybrid", uniqueId);
    }
}
