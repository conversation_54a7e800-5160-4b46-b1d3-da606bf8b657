package com.meituan.android.cashier.business;

import android.app.Activity;
import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.android.cashier.R;
import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.model.bean.MTPaymentURL;
import com.meituan.android.cashier.newrouter.NativeStandardCashierHandler;
import com.meituan.android.paybase.common.dialog.PayDialog;
import com.meituan.android.paybase.downgrading.PayHornConfigBean;
import com.meituan.android.paybase.retrofit.PayException;
import com.meituan.android.paybase.utils.ActivityStatusChecker;
import com.meituan.android.paycommon.lib.utils.ExceptionUtils;
import com.meituan.android.payrouter.utils.ProxyUtils;

public class GoHelloPayHandler extends PayExceptionHandler {
    private final OverLoadHandler overLoadHandler;
    private final GoHelloPayResultHandler resultHandler;

    public GoHelloPayHandler(Activity activity, OverLoadHandler overLoadHandler, GoHelloPayResultHandler resultHandler) {
        super(activity);
        this.overLoadHandler = overLoadHandler;
        this.resultHandler = resultHandler;
    }

    public void onResponse(MTPaymentURL goHelloPayResponse) {
        if (goHelloPayResponse == null) {
            return;
        }
        PayHornConfigBean.setGmDegradeFlag(goHelloPayResponse.getUrl()); // 处理国密降级开关
        // 如果没有处理后端负载过重逻辑，则继续处理支付相关逻辑
        if (!overLoadHandler.handleOverLoad(goHelloPayResponse.getOverLoadInfo())) {
            getResultHandler().onGoHelloPaySuccess(goHelloPayResponse.getPayType(), goHelloPayResponse.getUrl());
        }
    }

    public void onRequestException(int tag, Exception exception) {
        Activity activity = getActivity();
        if (!ActivityStatusChecker.isValid(activity)) {
            return;
        }
        if (tag == NativeStandardCashierHandler.REQ_TAG_GO_HELLO_PAY_AUTOMATIC_PAY_GUIDE) {
            String message = exception instanceof PayException && !TextUtils.isEmpty(exception.getMessage()) ?
                    exception.getMessage() : activity.getString(R.string.cashier__pay_error_msg_try_later);
            ExceptionUtils.alertAndFinish(activity, message, null, MTCashierActivity.class);
        } else {
            handleException(exception);
        }
    }

    @Override
    protected void handlePayException(@NonNull PayException payException) {
        int errorCode = payException.getCode();
        String errorMessage = payException.getMessage();
        if (errorCode == CashierErrorConstants.NEED_VERIFY_SMS_CODE) {
            showToast(getString(com.meituan.android.cashier.common.R.string.cashier_common__error_msg_pay_later));
        } else if (errorCode == CashierErrorConstants.ALREADY_PAYED) {
            Activity activity = getActivity();
            if (!ActivityStatusChecker.isValid(activity)) {
                return;
            }
            new PayDialog.Builder(activity)
                    .msg(errorMessage)
                    .subMsg(payException.getErrorCodeStr())
                    .rightBtn(PayDialog.DEFAULT_BUTTON_TEXT, (dialog) -> getResultHandler().onAlreadyPayed())
                    .build().show();
        } else {
            super.handlePayException(payException);
        }
    }

    private GoHelloPayResultHandler getResultHandler() {
        return ProxyUtils.nonNullObject(GoHelloPayResultHandler.class, resultHandler);
    }
}
