package com.meituan.android.cashier.model.bean;


import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.common.analyse.cat.CatUtils;
import com.meituan.android.paybase.utils.LoganUtils;

import java.lang.reflect.Type;

public class CashierPopWindowBeanDeserializer implements JsonDeserializer<CashierPopWindowBean>, JsonSerializer<CashierPopWindowBean> {
    private static final String KEY_POP_DETAIL_INFO = "pop_detail_info";
    private static final String KEY_TYPE = "type";
    private static final String KEY_SCENE = "pop_scene";

    @Override
    public CashierPopWindowBean deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        try {
            if (json != null) {
                CashierPopWindowBean cashierPopWindowBean = new CashierPopWindowBean();
                JsonObject jsonObject = json.getAsJsonObject();
                int type = jsonObject.get(KEY_TYPE).getAsInt();
                if (type == CashierPopWindowBean.POPWINDOW_PAYLATER_GUIDE) {
                    cashierPopWindowBean.setPayLaterPopDetailInfoBean(context.deserialize(jsonObject.get(KEY_POP_DETAIL_INFO), PayLaterPopDetailInfoBean.class));
                } else if (type == CashierPopWindowBean.STOP_PAYMENT_GUIDE
                        || type == CashierPopWindowBean.BIND_CARD_PAY_GUIDE
                        || type == CashierPopWindowBean.CREDIT_PAY_GUIDE
                        || type == CashierPopWindowBean.PROMOTION_SIGNED_PAY_GUIDE
                        || type == CashierPopWindowBean.PROMOTION_BINDED_CARD_PAY_GUIDE) {
                    cashierPopWindowBean.setPopDetailInfo(context.deserialize(jsonObject.get(KEY_POP_DETAIL_INFO), PopDetailInfo.class));
                }
                JsonElement jsonElement = jsonObject.get(KEY_SCENE);
                if (jsonElement != null) {
                    String popScene = jsonElement.getAsString();
                    cashierPopWindowBean.setPopScene(popScene);
                }
                cashierPopWindowBean.setType(type);
                return cashierPopWindowBean;
            }
        } catch (Exception e) {
            CatUtils.logError(getSubTag("deserialize"), "deserialize error");
            LoganUtils.logError("CashierPopWindowBeanDeserializer_deserialize", e.getMessage());
        }
        return null;
    }

    private String getSubTag(String tag) {
        return "CashierPopWindowBeanDeserializer_" + tag;
    }

    @Override
    public JsonElement serialize(CashierPopWindowBean cashierPopWindowBean, Type typeOfSrc, JsonSerializationContext context) {
        try {
            if (cashierPopWindowBean != null) {
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty(KEY_TYPE, cashierPopWindowBean.getType());
                if (cashierPopWindowBean.getType() == CashierPopWindowBean.POPWINDOW_PAYLATER_GUIDE) {
                    jsonObject.add(KEY_POP_DETAIL_INFO, context.serialize(cashierPopWindowBean.getPayLaterPopDetailInfoBean()));
                } else if (cashierPopWindowBean.getType() == CashierPopWindowBean.STOP_PAYMENT_GUIDE
                        || cashierPopWindowBean.getType() == CashierPopWindowBean.BIND_CARD_PAY_GUIDE
                        || cashierPopWindowBean.getType() == CashierPopWindowBean.CREDIT_PAY_GUIDE
                        || cashierPopWindowBean.getType() == CashierPopWindowBean.PROMOTION_SIGNED_PAY_GUIDE
                        || cashierPopWindowBean.getType() == CashierPopWindowBean.PROMOTION_BINDED_CARD_PAY_GUIDE) {
                    jsonObject.add(KEY_POP_DETAIL_INFO, context.serialize(cashierPopWindowBean.getPopDetailInfo()));
                }
                return jsonObject;
            }
        } catch (Exception e) {
            CatUtils.logError(getSubTag("serialize"), "serialize error");
            LoganUtils.logError("CashierPopWindowBeanDeserializer_serialize", e.getMessage());
        }
        return null;
    }
}
