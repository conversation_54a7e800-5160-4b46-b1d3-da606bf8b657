package com.meituan.android.cashier.preguide;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.support.annotation.CallSuper;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.app.FragmentActivity;
import android.support.v4.content.LocalBroadcastManager;
import android.support.v4.view.ViewCompat;
import android.text.TextUtils;
import android.view.View;

import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.bean.CashierParams;
import com.meituan.android.cashier.bean.CashierRouterPreGuideHornConfig;
import com.meituan.android.cashier.common.CashierConstants;
import com.meituan.android.cashier.common.CashierListener;
import com.meituan.android.cashier.common.CashierRouterHornService;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.common.CashierTypeConstant;
import com.meituan.android.cashier.common.ICashier;
import com.meituan.android.cashier.common.ICashierAdapter;
import com.meituan.android.cashier.common.ProductTypeConstant;
import com.meituan.android.cashier.utils.DelayPayUtils;
import com.meituan.android.neohybrid.init.HybridSDK;
import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.utils.CashierRepeatDownGradeSwitchManager;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.MTPayBaseClass;
import com.meituan.android.paybase.utils.MTPayNeedToPersist;
import com.meituan.android.paycommon.lib.coupon.model.Promotion;
import com.meituan.android.paycommon.lib.fragment.HalfPageFragment;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用的前置引导类功能
 */
@ServiceLoaderInterface(key = CashierTypeConstant.CASHIERTYPE_HYBRID_PRE_GUIDE, interfaceClass = ICashier.class)
@MTPayBaseClass
public class PreGuideCashier extends ICashierAdapter {
    private static final String ACTION_DOWNGRADE = "downgrade";
    private static final String ACTION_FINISH = "finish";
    private static final String STATUS_SUCCESS = "success";
    private static final String STATUS_FAIL = "fail";
    private static final String STATUS_CANCEL = "cancel";
    @MTPayNeedToPersist
    private CashierRouterPreGuideHornConfig mCashierRouterPreGuideHornConfig;
    private MTCashierActivity mtCashierActivity;
    private static final int REQUEST_CODE = 101;
    private CashierParams mCashierParams;
    private final Handler mHandler = new Handler();
    private Drawable mDecorViewBackground;
    private BroadcastReceiver mSLABroadcastReceiver;

    @Override
    @CallSuper
    public <T extends FragmentActivity & CashierListener & IRequestCallback> ConsumeResult consume(T t, CashierParams cashierParams) {
        this.mCashierRouterPreGuideHornConfig = null;
        this.mtCashierActivity = (MTCashierActivity) t;
        List<CashierRouterPreGuideHornConfig> cashierRouterPreGuideHornConfigs = CashierRouterHornService.getInstance().getCashierRouterPreGuideHornConfigList();
        this.mCashierParams = cashierParams;
        Uri uri = cashierParams.getUri();
        if (uri == null) {
            return new ConsumeResult(false, "pay_defer_sign_001", "uri is null");
        }
        String merchantNo = uri.getQueryParameter(CashierConstants.ARG_MERCHANT_NO);
        String cashierType = cashierParams.getProductType();
        boolean hornExist = existHornConfig(cashierRouterPreGuideHornConfigs, cashierType, merchantNo);
        return hornExist ? new ConsumeResult(true) : new ConsumeResult(false, "pay_defer_sign_002", "horn not exist");
    }


    /**
     * 1.判断 cashier_type 是否匹配
     * 2.判断 merchantNo 是否匹配，如果 CashierRouterPreGuideHornConfig 没有 supportedMerchantNos，则不进行此项条件的匹配
     *
     * @param cashierRouterPreGuideHornConfigs
     * @param cashierType
     * @param merchantNo
     * @return
     */
    private boolean existHornConfig(List<CashierRouterPreGuideHornConfig> cashierRouterPreGuideHornConfigs, String cashierType, String merchantNo) {
        if (CollectionUtils.isEmpty(cashierRouterPreGuideHornConfigs)) {
            return false;
        }
        if (TextUtils.isEmpty(cashierType)) {
            return false;
        }
        for (CashierRouterPreGuideHornConfig cashierRouterPreGuideHornConfig : cashierRouterPreGuideHornConfigs) {
            if (cashierRouterPreGuideHornConfig == null) {
                continue;
            }
            if (TextUtils.equals(cashierRouterPreGuideHornConfig.getCashierType(), cashierType)) {
                this.mCashierRouterPreGuideHornConfig = cashierRouterPreGuideHornConfig;
                break;
            }
        }
        return this.mCashierRouterPreGuideHornConfig != null && !TextUtils.isEmpty(mCashierRouterPreGuideHornConfig.getUrl());
    }

    @Override
    public void invoke(String cashierFrom, Map<String, Object> cashierParams) {
        CashierRouterPreGuideHornConfig cashierRouterPreGuideHornConfig = this.mCashierRouterPreGuideHornConfig;
        String url = cashierRouterPreGuideHornConfig.getUrl().trim();
        if (!url.startsWith("https://") && !url.startsWith("http://")) {
            url = HybridSDK.getHost() + cashierRouterPreGuideHornConfig.getUrl();
        }
        HalfPageFragment.HalfPageFragmentConfig halfPageFragmentConfig = new HalfPageFragment.HalfPageFragmentConfig(
                cashierRouterPreGuideHornConfig.getCashierType(), url, "", REQUEST_CODE);
        halfPageFragmentConfig.setTunnelExtraData(getTunnelData());
        halfPageFragmentConfig.setLoadingTimeOut(String.valueOf(cashierRouterPreGuideHornConfig.getLoadingTimeOut()));
        if (CashierRepeatDownGradeSwitchManager.downGrade()) {
            halfPageFragmentConfig.setBackgroundColor(cashierRouterPreGuideHornConfig.getBackgroundColor());
        } else {
            halfPageFragmentConfig.setBackgroundColor("#00000000");
        }
        prefetch(cashierRouterPreGuideHornConfig, halfPageFragmentConfig);
        DelayPayUtils.prefetch(cashierRouterPreGuideHornConfig, halfPageFragmentConfig, mCashierParams);
        registerSLABroadCastReceiver(cashierRouterPreGuideHornConfig.getCashierType());
        HalfPageFragment.openHalfPage(mtCashierActivity, halfPageFragmentConfig);
    }

    private void registerSLABroadCastReceiver(String targetScene) {
        if (mSLABroadcastReceiver == null) {
            mSLABroadcastReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    openStatus(true, null);
                    unRegisterSLABroadCastReceiver();
                }
            };
        }
        IntentFilter intentFilter = new IntentFilter("com.meituan.android.paycommon.lib.fragment.HalfPageFragment_" + targetScene);
        LocalBroadcastManager.getInstance(mtCashierActivity).registerReceiver(mSLABroadcastReceiver, intentFilter);
    }

    private void unRegisterSLABroadCastReceiver() {
        if (mSLABroadcastReceiver != null) {
            LocalBroadcastManager.getInstance(mtCashierActivity).unregisterReceiver(mSLABroadcastReceiver);
        }
    }

    private String getTunnelData() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("tradeno", mCashierParams.getTradeNo());
            jsonObject.put("extra_statics", mCashierParams.getExtraStatics());
            jsonObject.put("extra_data", mCashierParams.getExtraData());
            jsonObject.put("merchant_no", mtCashierActivity.getMerchantNo());
            jsonObject.put("pay_token", mCashierParams.getPayToken());
            HashMap<String, String> hashMap = mCashierParams.getExtendTransmissionParams();
            if (!CollectionUtils.isEmpty(hashMap)) {
                for (Map.Entry<String, String> entry : hashMap.entrySet()) {
                    jsonObject.put(entry.getKey(), entry.getValue());
                }
            }
        } catch (Exception e) {
            LoganUtils.logError("PreGuideCashier_getTunnelData", e.getMessage());
        }
        appendTunnelDate(jsonObject);
        return jsonObject.toString();
    }

    protected void appendTunnelDate(JSONObject jsonObject) {
    }

    protected void prefetch(@NonNull CashierRouterPreGuideHornConfig cashierRouterPreGuideHornConfig, @NonNull HalfPageFragment.HalfPageFragmentConfig halfPageFragmentConfig) {
    }

    protected String getExtDimStat() {
        if (TextUtils.isEmpty(this.mCashierParams.getExtraStatics())) {
            return "";
        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("outer_business_statics", this.mCashierParams.getExtraStatics());
        } catch (Exception e) {
            LoganUtils.logError("PreGuideCashier_getExtDimStat", e.getMessage());
        }
        return jsonObject.toString();
    }

    @Override
    public String getCashierType() {
        return CashierTypeConstant.CASHIERTYPE_HYBRID_PRE_GUIDE;
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {

    }

    @Override
    public void onRestoreInstanceState(Bundle savedInstanceState) {

    }

    private void onCashierPaySuccess(Promotion promotion) {
        if (!CashierRepeatDownGradeSwitchManager.downGrade()) {
            mtCashierActivity.setHalfPageMarketingBackgroundColor("#********");
        }
        mtCashierActivity.onCashierPaySuccess(promotion);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == REQUEST_CODE) {
            HalfPageFragment.onActivityResult(resultCode, data, new HalfPageFragment.HalfPageListener() {
                @Override
                public void onLoadFail(int errorCode, String errorMessage) {
                    // 弹窗加载超时
                    onLoadTimeOut();
                }

                @Override
                public void onSuccess(@Nullable String result) {
                    if (TextUtils.isEmpty(result)) {
                        // 按照支付成功处理
                        onCashierPaySuccess(null);
                        CashierStaticsUtils.logCustom("paybiz_pay_later_result_is_illegal", null, null, getUniqueId());
                        return;
                    }
                    try {
                        JSONObject jsonObject = new JSONObject(result);
                        String action = jsonObject.optString("action");
                        if (TextUtils.equals(ACTION_DOWNGRADE, action)) {
                            handleDowngrade(jsonObject);
                        } else if (TextUtils.equals(ACTION_FINISH, action)) {
                            handlePayFinish(jsonObject);
                        } else {
                            HashMap<String, Object> hashMap = new HashMap<>();
                            hashMap.put("action", action);
                            CashierStaticsUtils.logCustom("paybiz_pay_later_result_action_is_not_defined", hashMap, null, getUniqueId());
                        }
                    } catch (Exception e) {
                        // 按照支付成功处理
                        onCashierPaySuccess(null);
                        CashierStaticsUtils.logCustom("paybiz_pay_later_result_is_illegal", null, null, getUniqueId());
                    }
                }
            });
        }
    }

    private void handleDowngrade(JSONObject jsonObject) {
        if (jsonObject == null) {
            return;
        }
        String destCashierType = jsonObject.optString("dest_cashier_type");
        String sourceCashierType = jsonObject.optString("source_cashier_type");
        String downgradeInfo = jsonObject.optString("downgrade_info");
        String payResultExtra = jsonObject.optString("pay_result_extra");
        mtCashierActivity.setPayResultExtra(payResultExtra);
        if (TextUtils.isEmpty(destCashierType)) {
            final String bid = "b_pay_5l3pq2aw_sc";
            HashMap<String, Object> valLabs = new HashMap<>();
            valLabs.put("scene", "PreGuideCashier_handleDowngrade");
            CashierStaticsUtils.reportSystemCheck(bid, valLabs, getUniqueId());
            CashierStaticsUtils.logCustom("paybiz_pay_later_result_dest_cashier_empty", null, null, getUniqueId());
        }
        if (TextUtils.equals(destCashierType, CashierTypeConstant.CASHIERTYPE_MT_HYBRID_HALFPAGE_CASHIER)) {
            mtCashierActivity.onCashierBusinessDowngrade(sourceCashierType, ProductTypeConstant.PREPOSED_MTCASHIER, downgradeInfo);
        } else if (TextUtils.equals(destCashierType, CashierTypeConstant.CASHIERTYPE_REQUEST_PREDISPATCHER)) {
            mtCashierActivity.onCashierBusinessDowngrade(sourceCashierType, ProductTypeConstant.OTHER, downgradeInfo);
        } else {
            mtCashierActivity.onCashierBusinessDowngrade(sourceCashierType, ProductTypeConstant.STANDARD_CASHIER, downgradeInfo);
        }
    }

    private void handlePayFinish(JSONObject jsonObject) {
        if (jsonObject == null) {
            return;
        }
        String status = jsonObject.optString("status");
        String payResultExtra = jsonObject.optString("pay_result_extra");
        Promotion promotion = null;
        try {
            JSONObject promotionJSONObject = jsonObject.optJSONObject("promotion");
            if (promotionJSONObject != null) {
                promotion = GsonProvider.getInstance().fromJson(promotionJSONObject.toString(), Promotion.class);
            }
        } catch (Exception e) {
            LoganUtils.logError("PreGuideCashier_handlePayFinish", e.getMessage());
        }
        mtCashierActivity.setPayResultExtra(payResultExtra);
        if (TextUtils.equals(STATUS_SUCCESS, status)) {
            onCashierPaySuccess(promotion);
        } else if (TextUtils.equals(STATUS_FAIL, status)) {
            mtCashierActivity.onCashierPayFail("");
        } else if (TextUtils.equals(STATUS_CANCEL, status)) {
            mtCashierActivity.onCashierCancel();
        } else {
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("status", status);
            CashierStaticsUtils.logCustom("paybiz_pay_later_result_status_is_not_defined", hashMap, null, getUniqueId());
        }
    }

    private void onLoadingTimeOutDegrade(String destCashierType) {
        if (TextUtils.equals(destCashierType, CashierTypeConstant.CASHIERTYPE_MT_HYBRID_HALFPAGE_CASHIER)) {
            mtCashierActivity.onCashierBusinessDowngrade(getCashierType(), ProductTypeConstant.PREPOSED_MTCASHIER, "");
        } else if (TextUtils.equals(destCashierType, CashierTypeConstant.CASHIERTYPE_REQUEST_PREDISPATCHER)) {
            mtCashierActivity.onCashierBusinessDowngrade(getCashierType(), ProductTypeConstant.OTHER, "");
        } else {
            mtCashierActivity.onCashierBusinessDowngrade(getCashierType(), ProductTypeConstant.STANDARD_CASHIER, "");
        }
    }

    // 弹窗加载超时，根据 horn 配置，决定请求 predispatcher 或者降级到标准收银台、或者返回业务方
    private void onLoadTimeOut() {
        CashierRouterPreGuideHornConfig cashierRouterPreGuideHornConfig = this.mCashierRouterPreGuideHornConfig;
        if (cashierRouterPreGuideHornConfig == null) {
            LoganUtils.logError("PreGuideCashier_onLoadTimeOut", "cashierRouterPreGuideHornConfig == null");
            mtCashierActivity.onCashierPayFail("");
            return;
        }
        String renderErrorAction = cashierRouterPreGuideHornConfig.getRenderErrorAction();
        String renderErrorToast = cashierRouterPreGuideHornConfig.getRenderErrorToast();
        if (TextUtils.equals(renderErrorAction, "pay_finish")) {
            mtCashierActivity.onCashierPayFail(renderErrorToast);
        } else {
            if (TextUtils.isEmpty(renderErrorToast)) {
                onLoadingTimeOutDegrade(renderErrorAction);
            } else {
                ToastUtils.showSnackToast(mtCashierActivity, renderErrorToast, false);
                // 等 toast 消失后再进入收银台。否则网络请求的 loading 动画会覆盖 toast 信息
                // 1500ms 是参考的 SnackbarManager.SHORT_DURATION_MS，虽然是硬编码，但是这个时间不会变动
                mHandler.postDelayed(() -> onLoadingTimeOutDegrade(renderErrorAction), 1500);
            }
        }
    }

    @Override
    public void onDestroy(boolean release) {
        super.onDestroy(release);
        mHandler.removeCallbacksAndMessages(null);
        if (mDecorViewBackground != null && !mtCashierActivity.isFinishing()) {
            View decorView = mtCashierActivity.getWindow().getDecorView();
            ViewCompat.setBackground(decorView, mDecorViewBackground);
        }
        unRegisterSLABroadCastReceiver();
    }

    @Override
    protected void onSLASuccess() {
        if (CashierRepeatDownGradeSwitchManager.downGrade()) {
            return;
        }
        try {
            View decorView = mtCashierActivity.getWindow().getDecorView();
            mDecorViewBackground = decorView.getBackground();
            decorView.setBackgroundColor(Color.parseColor(TextUtils.isEmpty(mCashierRouterPreGuideHornConfig.getBackgroundColor()) ? "#********" : mCashierRouterPreGuideHornConfig.getBackgroundColor()));
        } catch (Exception e) {
            LoganUtils.logError("PreGuideCashier_onSLASuccess", e.getMessage());
        }
    }
}