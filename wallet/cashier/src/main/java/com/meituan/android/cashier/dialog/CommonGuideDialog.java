package com.meituan.android.cashier.dialog;

import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.support.annotation.Nullable;
import android.support.v4.app.Fragment;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.Window;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.meituan.android.cashier.R;
import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.fragment.MTCashierRevisionFragment;
import com.meituan.android.cashier.model.bean.CashierPopWindowBean;
import com.meituan.android.cashier.model.bean.PopDetailInfo;
import com.meituan.android.cashier.utils.NativeStandardCashierPayProcessStatistic;
import com.meituan.android.pay.common.payment.bean.CreditPayOpenInfoBean;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.pay.common.payment.utils.PayTypeUtils;
import com.meituan.android.pay.common.promotion.bean.Icon;
import com.meituan.android.pay.desk.pack.WalletPayManager;
import com.meituan.android.pay.utils.CreditOpenUtils;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.dialog.BaseDialog;
import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.MTPaySuppressFBWarnings;
import com.meituan.android.paybase.utils.StatisticsUtils;
import com.meituan.android.paybase.utils.TransferUtils;
import com.meituan.android.paybase.widgets.bankcard.RoundImageView;
import com.meituan.android.paycommon.lib.fragment.HalfPageFragment;
import com.meituan.android.paycommon.lib.utils.WebpImageLoader;
import com.meituan.android.paycommon.lib.widgets.NoDuplicateClickListener;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;

import static com.meituan.android.cashier.base.utils.CashierAnalyseUtils.isPromotionScene;

/**
 * 引导弹窗5期-通用样式弹窗技术方案文档：https://km.sankuai.com/page/**********
 *
 * <AUTHOR>
 */

public class CommonGuideDialog extends BaseDialog {

    private HashMap<String, Object> technologyParameters;
    private PopDetailInfo mGuideInfo;
    private OnClickCommonDialogGuideButtonListener mOnClickCommonDialogGuideButtonListener;
    private CashierPopWindowBean mCashierPopWindowBean;
    private static final int REQUEST_CODE = 400;
    private static final int MARKETING_BACKGROUND_IMAGE_CORNER_RADIUS_VALUE = 8;
    private static final int GUIDE_BUTTON_BACKGROUND_IMAGE_CORNER_RADIUS_VALUE = 9;

    public CommonGuideDialog(Context context, CashierPopWindowBean cashierPopWindowBean,
                             OnClickCommonDialogGuideButtonListener onClickCommonDialogGuideButtonListener) {
        super(context, R.style.cashier__card_pay_guide_transparent_dialog);
        if (cashierPopWindowBean == null) {
            return;
        }
        mCashierPopWindowBean = cashierPopWindowBean;
        if (cashierPopWindowBean.getPopDetailInfo() == null) {
            return;
        }
        mGuideInfo = cashierPopWindowBean.getPopDetailInfo();
        if (mGuideInfo.getGuidePayTypeInfo() == null
                || TextUtils.isEmpty(mGuideInfo.getTitle())
                || TextUtils.isEmpty(mGuideInfo.getMarketingMainTitle())
                || TextUtils.isEmpty(mGuideInfo.getGuideButton())
                || TextUtils.isEmpty(mGuideInfo.getStyle())) {
            return;
        }
        mOnClickCommonDialogGuideButtonListener = onClickCommonDialogGuideButtonListener;
        initView();
    }

    private void initView() {
        addBusinessParameters();
        int type = mCashierPopWindowBean.getType();
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setCanceledOnTouchOutside(false);
        setContentView(R.layout.cashier__common_guide_dialog);
        findViewById(R.id.common_dialog_close).setOnClickListener(v -> {
            dismiss();
            reportCommonDialogClosed(type);
        });
        TextView title = findViewById(R.id.common_dialog_title);
        title.setText(mGuideInfo.getTitle());
        TextView marketingMainTitle = findViewById(R.id.common_dialog_marketing_main_title);
        marketingMainTitle.setText(mGuideInfo.getMarketingMainTitle());
        TextView marketingSubTitle = findViewById(R.id.common_dialog_marketing_sub_title);
        marketingSubTitle.setText(mGuideInfo.getMarketingSubTitle());
        if (TextUtils.isEmpty(mGuideInfo.getMarketingSubTitle())) {
            marketingSubTitle.setVisibility(View.GONE);
        }
        ImageView payIcon = findViewById(R.id.common_dialog_guide_pay_type_pay_icon);
        TextView paymentName = findViewById(R.id.common_dialog_guide_pay_type_payment_name);
        if (type == CashierPopWindowBean.PROMOTION_SIGNED_PAY_GUIDE) {
            MTPayment mtPayment = mGuideInfo.getGuidePayTypeInfo();
            Icon icon;
            if (mtPayment != null && (icon = mtPayment.getIcon()) != null && !TextUtils.isEmpty(icon.getEnable())) {
                WebpImageLoader.load(icon.getEnable(),
                        payIcon,
                        R.drawable.mpay__payment_default_pic,
                        R.drawable.mpay__payment_default_pic
                );
            }
            if (mGuideInfo.getPaymentSuffix() != null) {
                paymentName.setText(getNameText(mtPayment) + mGuideInfo.getPaymentSuffix());
            } else {
                paymentName.setText(getNameText(mtPayment));
            }
        } else {
            payIcon.setVisibility(View.GONE);
            paymentName.setVisibility(View.GONE);
        }
        if (!TextUtils.isEmpty(mGuideInfo.getMarketingBackgroundImage())) {
            RoundImageView marketingBackgroundImage = findViewById(R.id.marketing_background_image);
            FrameLayout marketingBackgroundTextFrameLayout = findViewById(R.id.common_dialog_marketing_area);
            transferURLIntoRoundImageView(MARKETING_BACKGROUND_IMAGE_CORNER_RADIUS_VALUE, marketingBackgroundImage,
                    marketingBackgroundTextFrameLayout, mGuideInfo.getMarketingBackgroundImage());
        }
        initButton(type);
        reportCommonDialogShowed(type);
    }

    /**
     * 上报通用弹窗的业务参数
     * "pop_scene"：支付前场景或者三方中断场景
     * "style_type"：为1时，代表新的通用样式
     * "ad_id"：广告id，鲸湾提供的新埋点
     */
    private void addBusinessParameters() {
        technologyParameters = CashierStaticsUtils.getTechnologyParameters();
        if (!TextUtils.isEmpty(mGuideInfo.getPopScene())) {
            technologyParameters.put("pop_scene", mGuideInfo.getPopScene());
        }
        technologyParameters.put("style_type", "1");
        if (!TextUtils.isEmpty(mGuideInfo.getAdId())) {
            technologyParameters.put("ad_id", mGuideInfo.getAdId());
        } else {
            technologyParameters.put("ad_id", "-999");
        }
        if (mGuideInfo.getGuidePayTypeInfo() != null) {
            technologyParameters.put("pay_type", mGuideInfo.getGuidePayTypeInfo().getPayType());
        }
    }

    private void reportCommonDialogShowed(int type) {
        if (type == CashierPopWindowBean.BIND_CARD_PAY_GUIDE || type == CashierPopWindowBean.PROMOTION_BINDED_CARD_PAY_GUIDE) {
            technologyParameters.put("open_source", "Beforepay_popwindow");
            if (mGuideInfo != null) {
                NativeStandardCashierPayProcessStatistic.putCreditPayInfo(technologyParameters, mGuideInfo.getGuidePayTypeInfo());
            }
            CashierStaticsUtils.logModelEvent("c_PJmoK",
                    isPromotionScene(mCashierPopWindowBean) ? "b_pay_emgbc2xg_mv" : "b_pay_fabizu1a_mv",
                    isPromotionScene(mCashierPopWindowBean) ? "引导绑多卡弹窗" : "收银台首页-拉新优惠弹窗",
                    technologyParameters, StatisticsUtils.EventType.VIEW, getUniqueId());
        } else if (type == CashierPopWindowBean.CREDIT_PAY_GUIDE) {
            technologyParameters.put("open_source", CreditOpenUtils.STANDARD_PAYBEFOREDIALOG_SUFFIX);
            if (mGuideInfo != null) {
                NativeStandardCashierPayProcessStatistic.putCreditPayInfo(technologyParameters, mGuideInfo.getGuidePayTypeInfo());
            }
            CashierStaticsUtils.logModelEvent("c_PJmoK", "b_pay_rmbynygf_mv", "收银台首页-月付优惠弹窗",
                    technologyParameters, StatisticsUtils.EventType.VIEW, getUniqueId());
        } else if (type == CashierPopWindowBean.PROMOTION_SIGNED_PAY_GUIDE) {
            technologyParameters.put("open_source", "promotion_signed_guide_popwindow");
            if (mGuideInfo != null) {
                NativeStandardCashierPayProcessStatistic.putCreditPayInfo(technologyParameters, mGuideInfo.getGuidePayTypeInfo());
            }
            CashierStaticsUtils.logModelEvent("c_PJmoK", "b_pay_ue0rpr3c_mv", "引导使用已有支付方式弹窗",
                    technologyParameters, StatisticsUtils.EventType.VIEW, getUniqueId());
        }
    }

    private void reportCommonDialogClosed(int type) {
        if (type == CashierPopWindowBean.BIND_CARD_PAY_GUIDE || type == CashierPopWindowBean.PROMOTION_BINDED_CARD_PAY_GUIDE) {
            CashierStaticsUtils.logModelEvent("c_PJmoK",
                    isPromotionScene(mCashierPopWindowBean) ? "b_pay_nvb88kbl_mc" : "b_pay_sod9pe8x_mc",
                    isPromotionScene(mCashierPopWindowBean) ? "引导绑多卡弹窗-关闭按钮" : "收银台首页-拉新优惠弹窗-关闭",
                    technologyParameters, StatisticsUtils.EventType.CLICK, getUniqueId());
        } else if (type == CashierPopWindowBean.CREDIT_PAY_GUIDE) {
            CashierStaticsUtils.logModelEvent("c_PJmoK", "b_pay_kgukyblu_mc", "收银台首页-月付优惠弹窗-关闭",
                    technologyParameters, StatisticsUtils.EventType.CLICK, getUniqueId());
        } else if (type == CashierPopWindowBean.PROMOTION_SIGNED_PAY_GUIDE) {
            CashierStaticsUtils.logModelEvent("c_PJmoK", "b_pay_ofkpwvjx_mc", "引导使用已有支付方式弹窗-主按钮-关闭按钮",
                    technologyParameters, StatisticsUtils.EventType.CLICK, getUniqueId());
        }
    }

    private void initButton(int type) {
        Button guideButton = findViewById(R.id.common_dialog_guide_button_text);
        guideButton.setText(mGuideInfo.getGuideButton());
        if (!TextUtils.isEmpty(mGuideInfo.getGuideButtonBackgroundImage())) {
            RoundImageView guideButtonBackgroundImage = findViewById(R.id.common_dialog_guide_button_image);
            FrameLayout guideButtonBackgroundImageFrameLayout = findViewById(R.id.common_dialog_guide_button);
            transferURLIntoRoundImageView(GUIDE_BUTTON_BACKGROUND_IMAGE_CORNER_RADIUS_VALUE, guideButtonBackgroundImage,
                    guideButtonBackgroundImageFrameLayout, mGuideInfo.getGuideButtonBackgroundImage());
        }
        guideButton.setOnClickListener(new NoDuplicateClickListener() {
            //设置防重复点击
            @Override
            public void onSingleClick(View v) {
                if (type == CashierPopWindowBean.BIND_CARD_PAY_GUIDE || type == CashierPopWindowBean.PROMOTION_BINDED_CARD_PAY_GUIDE) {
                    dismiss();
                    WalletPayManager.getInstance().setOpenSource(getOwnerActivity(), "Beforepay_popwindow");
                    if (mOnClickCommonDialogGuideButtonListener != null) {
                        mOnClickCommonDialogGuideButtonListener.onClickCommonDialogGuideButton(mGuideInfo.getGuidePayTypeInfo(), mCashierPopWindowBean);
                    }
                    technologyParameters.put("open_source", "Beforepay_popwindow");
                    if (mGuideInfo != null) {
                        NativeStandardCashierPayProcessStatistic.putCreditPayInfo(technologyParameters, mGuideInfo.getGuidePayTypeInfo());
                    }
                    CashierStaticsUtils.logModelEvent("c_PJmoK",
                            isPromotionScene(mCashierPopWindowBean) ? "b_pay_pupgzmqd_mc" : "b_pay_inig81vs_mc",
                            isPromotionScene(mCashierPopWindowBean) ? "引导绑多卡弹窗-主按钮" : "收银台首页-拉新优惠弹窗-绑卡",
                            technologyParameters, StatisticsUtils.EventType.CLICK, getUniqueId());
                    if (mGuideInfo != null) {
                        analyseClickConfirmButton(mGuideInfo.getGuidePayTypeInfo());
                    }
                } else if (type == CashierPopWindowBean.CREDIT_PAY_GUIDE) {
                    WalletPayManager.getInstance().setOpenSource(getOwnerActivity(),
                            CreditOpenUtils.STANDARD_PAYBEFOREDIALOG_SUFFIX);
                    if (canOpenCreditHalfPage()) {
                        hideDialogInternal();
                        CreditPayOpenInfoBean creditPayOpenInfoBean = mGuideInfo.getGuidePayTypeInfo().getCreditPayOpenInfo();
                        reportWhenLeaveCashier();
                        String url = CreditOpenUtils.getCreditOpenFinalURL(getOwnerActivity(), creditPayOpenInfoBean.getUrl(), CreditOpenUtils.STANDARD_PAYBEFOREDIALOG_SUFFIX, "");
                        HalfPageFragment.HalfPageFragmentConfig halfPageFragmentConfig =
                                new HalfPageFragment.HalfPageFragmentConfig("credit_half_page", url, creditPayOpenInfoBean.getData(), REQUEST_CODE);
                        halfPageFragmentConfig.setTunnelExtraData(HalfPageFragment.getTunnelExtraData((MTCashierActivity) getOwnerActivity()));
                        HalfPageFragment.openHalfPage(getDialogFragment(), halfPageFragmentConfig);
                    } else {
                        dismiss();
                        Fragment fragment = getDialogFragment().getParentFragment();
                        if (fragment instanceof MTCashierRevisionFragment) {
                            ((MTCashierRevisionFragment) fragment).onClickCommonDialogGuideButton(mGuideInfo.getGuidePayTypeInfo(), mCashierPopWindowBean);
                        }
                    }
                    technologyParameters.put("open_source", CreditOpenUtils.STANDARD_PAYBEFOREDIALOG_SUFFIX);
                    if (mGuideInfo != null) {
                        NativeStandardCashierPayProcessStatistic.putCreditPayInfo(technologyParameters, mGuideInfo.getGuidePayTypeInfo());
                    }
                    CashierStaticsUtils.logModelEvent("c_PJmoK", "b_pay_8kstrxvt_mc", "收银台首页-月付优惠弹窗-使用月付支付",
                            technologyParameters, StatisticsUtils.EventType.CLICK, getUniqueId());
                } else if (type == CashierPopWindowBean.PROMOTION_SIGNED_PAY_GUIDE) {
                    dismiss();
                    WalletPayManager.getInstance().setOpenSource(getOwnerActivity(),
                            "promotion_signed_guide_popwindow");
                    if (mOnClickCommonDialogGuideButtonListener != null) {
                        mOnClickCommonDialogGuideButtonListener.onClickCommonDialogGuideButton(mGuideInfo.getGuidePayTypeInfo(), mCashierPopWindowBean);
                    }
                    technologyParameters.put("open_source", "promotion_signed_guide_popwindow");
                    if (mGuideInfo != null) {
                        NativeStandardCashierPayProcessStatistic.putCreditPayInfo(technologyParameters, mGuideInfo.getGuidePayTypeInfo());
                    }
                    CashierStaticsUtils.logModelEvent("c_PJmoK", "b_pay_ma3yhfn3_mc", "引导使用已有支付方式弹窗-主按钮",
                            technologyParameters, StatisticsUtils.EventType.CLICK, getUniqueId());
                    if (mGuideInfo != null) {
                        analyseClickConfirmButton(mGuideInfo.getGuidePayTypeInfo());
                    }
                }

            }
        }.setClickInternal(1000));
    }

    @MTPaySuppressFBWarnings("SIC_INNER_SHOULD_BE_STATIC_ANON")
    private void transferURLIntoRoundImageView(int cornerRadiusValue, RoundImageView roundImageView,
                                               FrameLayout frameLayout, String url) {
        float cornerRadius = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, cornerRadiusValue,
                getContext().getResources().getDisplayMetrics());
        roundImageView.setCornerRadius(cornerRadius, cornerRadius, cornerRadius, cornerRadius);
        WebpImageLoader.load(url, roundImageView);
        ViewGroup.LayoutParams params = roundImageView.getLayoutParams();
        params.width = TransferUtils.dip2px(getContext(), 300 - 18 * 2);
        frameLayout.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                params.height = frameLayout.getHeight();
                roundImageView.setLayoutParams(params);
                roundImageView.requestLayout();
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.JELLY_BEAN) {
                    frameLayout.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                } else {
                    frameLayout.getViewTreeObserver().removeGlobalOnLayoutListener(this);
                }
            }
        });
    }

    // 支付名称拼接
    protected String getNameText(MTPayment mtPayment) {
        if (mtPayment == null) {
            return "";
        }
        String nameStr = "";
        String nameSuffix = "";
        if (!TextUtils.isEmpty(mtPayment.getName())) {
            nameStr = mtPayment.getName();
        }
        // 区分银行卡和非银行
        if (PayTypeUtils.isBankcardPay(mtPayment.getPayType())) {
            nameSuffix = getBankNameExtText(mtPayment);
        } else if (!TextUtils.isEmpty(mtPayment.getNameSuffix())) {
            nameSuffix = mtPayment.getNameSuffix();
        }
        return nameStr + nameSuffix;
    }

    private String getBankNameExtText(MTPayment mtPayment) {
        if (mtPayment != null && mtPayment.getCardInfo() != null) {
            return mtPayment.getCardInfo().getNameExt();
        }
        return "";
    }

    private void analyseClickConfirmButton(MTPayment guidePayTypeInfo) {
        if (guidePayTypeInfo == null) {
            return;
        }
        if (!TextUtils.isEmpty(guidePayTypeInfo.getPayType())) {
            HashMap<String, Object> map = new AnalyseUtils.MapBuilder().add("pay_type", guidePayTypeInfo.getPayType()).build();
            LoganUtils.log("standard_cashier_mt_pay_confirm", map);
            CashierStaticsUtils.logCustom("standard_cashier_mt_pay_confirm", map, null, getUniqueId());
        }
    }

    /**
     * 埋点使用
     *
     * @return 月付开通流程跳转链接
     */
    private String geJumpUrl() {
        if (mGuideInfo == null || mGuideInfo.getGuidePayTypeInfo() == null) {
            return "";
        }
        CreditPayOpenInfoBean creditPayOpenInfoBean = mGuideInfo.getGuidePayTypeInfo().getCreditPayOpenInfo();
        if (creditPayOpenInfoBean == null) {
            return "";
        }
        return creditPayOpenInfoBean.getUrl();
    }

    private void reportWhenLeaveCashier() {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("scene", "1");
        hashMap.put("url", geJumpUrl());
        CashierStaticsUtils.reportSystemCheck("b_pay_credit_open_leave_cashier_sc", hashMap, getUniqueId());
    }

    private void reportWhenBackToCashier() {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("scene", "1");
        hashMap.put("url", geJumpUrl());
        CashierStaticsUtils.reportSystemCheck("b_pay_credit_open_back_to_cashier_sc", hashMap, getUniqueId());
    }

    private void handleStatus(int status) {
        switch (status) {
            case 2: // 开通失败
                ToastUtils.showSnackToast(getOwnerActivity(), "月付开通失败，请更换其他支付方式");
                dismiss();
                break;
            case 3:  // 开通成功
                Fragment fragment = getDialogFragment().getParentFragment();
                if (fragment instanceof MTCashierRevisionFragment) {
                    // 发起支付网络请求之前，生成月付开通已成功的参数。请求时带上。
                    CreditOpenUtils.setCreditOpenVerifyScene();
                    ((MTCashierRevisionFragment) fragment).onCreditOpened(mGuideInfo.getGuidePayTypeInfo());
                }
                dismiss();
                break;
            default:
                showDialogInternal();
                break;
        }
    }

    // 展示弹窗
    private void showDialogInternal() {
        new Handler().post(() -> {
            getDialogFragment().show(null);
            findViewById(R.id.cashier_common_guide_dialog_content).setVisibility(View.VISIBLE);
            if (getWindow() != null) {
                getWindow().setDimAmount(0.7f);
            }
        });
    }

    // 隐藏弹窗
    private void hideDialogInternal() {
        findViewById(R.id.cashier_common_guide_dialog_content).setVisibility(View.GONE);
        if (getWindow() != null) {
            getWindow().setDimAmount(0f);
        }
        new Handler().post(() -> getDialogFragment().hideDialog());
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == REQUEST_CODE) {
            reportWhenBackToCashier();
            HalfPageFragment.onActivityResult(resultCode, data, new HalfPageFragment.HalfPageListener() {
                @Override
                public void onLoadFail(int errorCode, String errorMessage) {
                    ToastUtils.showSnackToast(getOwnerActivity(), "系统繁忙，请稍后重试");
                    dismiss();
                }

                @Override
                public void onSuccess(@Nullable String result) {
                    if (TextUtils.isEmpty(result)) {
                        showDialogInternal();
                    } else {
                        try {
                            JSONObject jsonObject = new JSONObject(result);
                            int status = jsonObject.optInt("fd_maidan_opened_status");
                            handleStatus(status);
                        } catch (JSONException e) {
                            showDialogInternal();
                        }
                    }
                }
            });
        }
    }

    private boolean canOpenCreditHalfPage() {
        MTPayment mtPayment = mGuideInfo.getGuidePayTypeInfo();
        if (mtPayment == null) {
            return false;
        }
        return mtPayment.getCreditPayOpenInfo() != null && !TextUtils.isEmpty(mtPayment.getCreditPayOpenInfo().getUrl());
    }

}
