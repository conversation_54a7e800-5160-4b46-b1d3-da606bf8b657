package com.meituan.android.cashier.newrouter;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.support.v4.app.Fragment;
import android.support.v4.app.FragmentActivity;
import android.text.TextUtils;

import com.meituan.android.cashier.R;
import com.meituan.android.cashier.business.DirectPayHandler;
import com.meituan.android.cashier.business.DirectPayResultHandler;
import com.meituan.android.cashier.business.DispatcherHandler;
import com.meituan.android.cashier.business.DispatcherResultHandler;
import com.meituan.android.cashier.business.GoHelloPayHandler;
import com.meituan.android.cashier.business.GoHelloPayResultHandler;
import com.meituan.android.cashier.business.MTPayHandler;
import com.meituan.android.cashier.business.MTPayResultHandler;
import com.meituan.android.cashier.business.OverLoadHandler;
import com.meituan.android.cashier.business.ThirdPayHandler;
import com.meituan.android.cashier.business.ThirdPayResultHandler;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.fragment.MTCashierRevisionFragment;
import com.meituan.android.cashier.model.bean.Cashier;
import com.meituan.android.cashier.model.bean.CashierPopWindowBean;
import com.meituan.android.cashier.model.bean.MTPaymentURL;
import com.meituan.android.cashier.model.bean.OrderResult;
import com.meituan.android.cashier.model.bean.PayResult;
import com.meituan.android.cashier.model.bean.RouteInfo;
import com.meituan.android.cashier.model.bean.SubmitData;
import com.meituan.android.cashier.newrouter.cashierdialog.CashierDialogHandler;
import com.meituan.android.cashier.newrouter.cashierdialog.CashierDialogResultHandler;
import com.meituan.android.cashier.newrouter.cashierdialog.CashierViewDialogHandler;
import com.meituan.android.cashier.newrouter.detainment.DetainmentDialogHandler;
import com.meituan.android.cashier.newrouter.detainment.DetainmentDialogResultHandler;
import com.meituan.android.cashier.newrouter.mtpaydialog.MTPayDialogHandler;
import com.meituan.android.cashier.newrouter.mtpaydialog.MTPayDialogResultHandler;
import com.meituan.android.cashier.newrouter.remake.CashierRouterConstants;
import com.meituan.android.cashier.newrouter.remake.CashierRouterHelper;
import com.meituan.android.cashier.retrofit.CashierReqTagConstant;
import com.meituan.android.cashier.retrofit.CashierRequestService;
import com.meituan.android.cashier.util.CashierSLAMonitor;
import com.meituan.android.cipstorage.CIPStorageCenter;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.pay.common.payment.data.IBankcardData;
import com.meituan.android.pay.common.payment.data.IPaymentData;
import com.meituan.android.pay.utils.DiscountMonitorHelper;
import com.meituan.android.paybase.asynctask.ConcurrentTask;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.common.dialog.PayDialog;
import com.meituan.android.paybase.config.PayBaseConfig;
import com.meituan.android.paybase.dialog.ProgressController;
import com.meituan.android.paybase.dialog.ProgressType;
import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.moduleinterface.payment.PayActionListener;
import com.meituan.android.paybase.moduleinterface.payment.PayFailInfo;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.utils.ActivityStatusChecker;
import com.meituan.android.paybase.utils.CatchException;
import com.meituan.android.paybase.utils.LocalBroadCastUtil;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.SdkDataStorageUtils;
import com.meituan.android.paybase.utils.SystemInfoUtils;
import com.meituan.android.paycommon.lib.abtest.CommonABTestManager;
import com.meituan.android.paycommon.lib.config.MTPayConfig;
import com.meituan.android.paycommon.lib.coupon.model.Promotion;
import com.meituan.android.paycommon.lib.retrofit.PayRetrofit;
import com.meituan.android.paymentchannel.PayerMediator;
import com.meituan.android.paymentchannel.utils.PaymentInstallInfoUtils;
import com.meituan.android.paymentchannel.utils.UPPayUtils;
import com.meituan.android.payrouter.remake.base.OnActivityResult;
import com.meituan.android.payrouter.remake.base.OnBackPressed;
import com.meituan.android.payrouter.remake.base.OnResume;
import com.meituan.android.payrouter.remake.base.OnWindowFocusChanged;
import com.meituan.android.payrouter.remake.router.context.AdapterContext;

import java.lang.ref.WeakReference;
import java.util.HashMap;

/**
 * 与路由逻辑解耦，专注处理Native收银台业务层逻辑。
 * 1. 生命周期回调
 * 2. 参数处理
 * 3. 网络请求
 * 4. 业务回调处理
 */
public class NativeStandardCashierHandler implements IRequestCallback {
    public static final String ACTION_REFRESH_CASHIER = "com.meituan.android.cashier.standardCashier.refresh";
    private static final String DEF_VALUE_IS_ROOT = "-1";
    public static final int REQ_TAG_GO_HELLO_PAY = CashierReqTagConstant.REQ_TAG_GO_HELLO_PAY;
    public static final int REQ_TAG_GO_HELLO_PAY_AUTOMATIC_PAY_GUIDE = CashierReqTagConstant.REQ_TAG_GUIDE_GO_HELLO_PAY;
    public static final int REQ_TAG_DIRECT_PAY = CashierReqTagConstant.REQ_TAG_PAY_ORDER;
    public static final int REQ_TAG_EXCEED_TIME_LIMIT = CashierReqTagConstant.REQ_TAG_EXCEED_TIME_LIMIT;
    private static final int REQ_TAG_ROUTE = CashierReqTagConstant.REQ_TAG_ROUTE;
    private static final int DEF_VALUE_INSTALLED_APPS = -1;
    private final int commonFragmentId = R.id.content;

    // 初始化参数 && 业务数据
    private final NewCashierParams cashierParams; // 业务方/preDispatcher 输入数据
    private RouteInfo mRouteInfo; // dispatcher 业务数据
    private Promotion promotion; // 支付结果页 业务数据
    private String finalFeeText; // 支付金额

    // 业务逻辑模块
    private StandardCashierBroadCastReceiver standardCashierBroadCastReceiver; // 收银台刷新相关逻辑
    private OverLoadHandler overLoadHandler; // 支付接口负载处理相关逻辑
    private DispatcherHandler dispatcherHandler; // dispatcher接口错误处理逻辑
    private GoHelloPayHandler goHelloPayHandler; // goHelloPay接口数据处理逻辑
    private DirectPayHandler directPayHandler; // directPay接口数据处理逻辑
    private MTPayHandler mtPayHandler; // 美团支付处理逻辑
    private ThirdPayHandler thirdPayHandler; // 三方支付处理逻辑

    // 弹窗逻辑模块：美团支付、收银台、标准收银台内弹窗、收银台挽留弹窗处理逻辑
    private MTPayDialogHandler mtPayDialogHandler;
    private CashierDialogHandler cashierDialogHandler;
    private CashierViewDialogHandler cashierViewDialogHandler;
    private DetainmentDialogHandler detainmentDialogHandler;

    // 其他模块
    private final AdapterContext adapterContext;
    private CIPStorageCenter cipStorageCenter;

    // 临时变量
    private boolean isRefresh;
    private boolean isRoot;
    private boolean isOrderTimeout;
    private boolean isOrderTimeoutHandled;

    private boolean fromAutomaticPayGuide;
    private boolean fromPayLaterGuide;
    private boolean fromMTPayAndRefresh;
    private String curPayType;
    private NSCHandlerProxy nscHandlerProxy;

    public NativeStandardCashierHandler(AdapterContext adapterContext, NewCashierParams cashierParams) {
        this.adapterContext = adapterContext;
        this.cashierParams = cashierParams;
    }

    public void onCreate(Bundle saveInstanceState) {
        CashierStaticsUtils.logCustom("native_standcashier_start", null, null, cashierParams.getCashierUniqueId());
        LoganUtils.log("业务方调起收银台");
        FragmentActivity activity = getActivity();
        standardCashierBroadCastReceiver = new StandardCashierBroadCastReceiver(this);
        // 过载
        overLoadHandler = new OverLoadHandler(activity, cashierParams);
        // 主接口逻辑：dispatcher、goHelloPay、directPay。由于请求相关的逻辑比较杂乱，暂时没有把请求相关的逻辑移入Handler
        dispatcherHandler = new DispatcherHandler(activity, cashierParams, new DispatcherResultHandler() {
            @Override
            public void onNeedRetryRequest() {
                AnalyseUtils.techMis("b_pay_w0yqzlx3_mv", null);
                requestDispatcherWithRefreshParams(null, false);
            }

            @Override
            public void onAlreadyPayed() {
                CashierRouterHelper.from(adapterContext).success().message("此订单已支付").finish();
            }
        });
        goHelloPayHandler = new GoHelloPayHandler(activity, overLoadHandler, new GoHelloPayResultHandler() {
            @Override
            public void onGoHelloPaySuccess(String payType, String url) {
                mtPayHandler.startMTPay(payType, url);
            }

            @Override
            public void onAlreadyPayed() {
                CashierRouterHelper.from(adapterContext).fail().message("此订单已支付").finish();
            }
        });
        directPayHandler = new DirectPayHandler(activity, overLoadHandler, new DirectPayResultHandler() {
            @Override
            public void onDirectPaySuccess(String payType, String url, Promotion promotion) {
                NativeStandardCashierHandler.this.promotion = promotion;
                thirdPayHandler.startThirdPay(payType, url);
            }

            @Override
            public void onAlreadyPayed() {
                CashierRouterHelper.from(adapterContext).fail().message("此订单已支付").finish();
            }
        });
        // 支付逻辑：美支、三方
        mtPayHandler = new MTPayHandler(activity, cashierParams, new MTPayResultHandler() {
            @Override
            public void onMTPaySuccess(Promotion promotion) {
                NativeStandardCashierHandler.this.promotion = promotion;
                CashierRouterHelper.from(adapterContext).success()
                        .promotion(Promotion.getExpectedPromotion(promotion, finalFeeText)).finish();
            }

            @Override
            public void onMTPayCancel() {
                onMTPayFail(); // 美团支付流程中，cancel和fail处理逻辑一致
            }

            @Override
            public void onMTPayFail() {
                if (fromAutomaticPayGuide) {
                    fromAutomaticPayGuide = false;
                    CashierRouterHelper.from(adapterContext).cancel().finish();
                } else if (fromPayLaterGuide) {
                    fromPayLaterGuide = false;
                    processCashier(null);
                    CashierSLAMonitor.reportStandardCashierRepeat(cashierParams.getCashierUniqueId());
                } else if (fromMTPayAndRefresh) {
                    fromMTPayAndRefresh = false;
                    requestDispatcherWithRefreshParams(null, true);
                }
            }

            @Override
            public void onMTPayOverTime() {
                CashierRouterHelper.from(adapterContext).fail().message("支付超时").finish();
            }

            @Override
            public void onMTPayFatalError(String scene) {
                requestDispatcherWithRefreshParams(scene, true);
            }
        });
        thirdPayHandler = new ThirdPayHandler(activity, cashierParams, new ThirdPayResultHandler() {
            @Override
            public void onThirdPaySuccess() {
                CashierRouterHelper.from(adapterContext).success()
                        .promotion(Promotion.getExpectedPromotion(promotion, finalFeeText))
                        .finish();
            }

            @Override
            public void onThirdPayNeedCancel() {
                CashierRouterHelper.from(adapterContext).cancel().finish();
            }

            @Override
            public void onThirdPayFail(String message) {
                CashierRouterHelper.from(adapterContext).fail().message(message).finish();
            }

            @Override
            public boolean onInterruptDialog() {
                return cashierViewDialogHandler.showCashierPopWindow(mRouteInfo.getCashierPopWindowBean(), getFragment());
            }

            @Override
            public String currentPayType() {
                return curPayType;
            }
        });
        // 弹窗：美支，收银台、收银台内弹窗
        mtPayDialogHandler = new MTPayDialogHandler(activity, cashierParams, new MTPayDialogResultHandler() {
            @Override
            public void onBlockWindowFinished() {
                CashierRouterHelper.from(adapterContext).fail().message("").finish();
            }

            @Override
            public void onMTPayRequestSuccess(MTPaymentURL goHelloPayResult) {
                NativeStandardCashierHandler.this.onRequestSucc(REQ_TAG_GO_HELLO_PAY, goHelloPayResult);
            }

            @Override
            public void onMTPaySelected(String payType) {
                NativeStandardCashierHandler.this.curPayType = payType;
            }

            @Override
            public void onMTPayCancel(String scene) {
                if ("payLaterGuideDialog".equals(scene)) {
                    fromPayLaterGuide = false;
                    processCashier(null);
                    CashierSLAMonitor.reportStandardCashierRepeat(cashierParams.getCashierUniqueId());
                }
            }
        });
        cashierDialogHandler = new CashierDialogHandler(activity, cashierParams, new CashierDialogResultHandler() {
            @Override
            public void onCashierDialogSuccess(CashierDialogHandler.Result result) {
                PayRetrofit.getInstance().create(CashierRequestService.class, NativeStandardCashierHandler.this, REQ_TAG_GO_HELLO_PAY_AUTOMATIC_PAY_GUIDE)
                        .goHelloPay(result.getUrl(), result.getRequestParams(), MTPayConfig.getProvider().getFingerprint());
            }

            @Override
            public void onCashierDialogCancel() { // 收银台弹窗场景下，不需要再processDispatcher，直接展示标准收银台
                Cashier cashier = mRouteInfo != null ? mRouteInfo.getCashier() : null;
                showNativeStandardCashierFragment(cashier, null);
            }
        });
        cashierViewDialogHandler = new CashierViewDialogHandler(activity, cashierParams);
        detainmentDialogHandler = new DetainmentDialogHandler(activity, cashierParams, new DetainmentDialogResultHandler() {
            @Override
            public void onDetainmentDialogCanceled() {
                CashierRouterHelper.from(adapterContext).cancel().finish();
            }

            @Override
            public void onSelectedThirdPay(SubmitData submitData) {
                Fragment fragment = getFragment();
                if (fragment instanceof MTCashierRevisionFragment) {
                    HashMap<String, String> param = new HashMap<>();
                    param.put("tradeno", cashierParams.getTradeNo());
                    param.put("pay_token", cashierParams.getPayToken());
                    param.put("pay_type", submitData.getPayType());
                    param.put("payScene", submitData.getPayScene());
                    param.put("paySceneParams", submitData.getPaySceneParams());
                    ((MTCashierRevisionFragment) fragment).startDirectPay(param);
                }
            }

            @Override
            public void onSelectedMTPay(SubmitData submitData) {
                IBankcardData mtPayment = detainmentDialogHandler.searchMTPayment(mRouteInfo.getCashier(), submitData);
                Fragment fragment = getFragment();
                if (fragment instanceof MTCashierRevisionFragment && mtPayment instanceof MTPayment) {
                    ((MTCashierRevisionFragment) fragment).payOrder((IPaymentData) mtPayment);
                }
            }
        });
        // 标准收银台页面适配逻辑, 后续需要下掉
        nscHandlerProxy = new NSCHandlerProxy() {
            @Override
            public Cashier getCashier() {
                return mRouteInfo != null ? mRouteInfo.getCashier() : null;
            }

            @Override
            public String getAppId() {
                return cashierParams.getAppId();
            }

            @Override
            public String getMchId() {
                return cashierParams.getMerchantNo();
            }

            @Override
            public String getGuidePlanInfos() {
                return cashierParams.getGuidePlanInfos();
            }

            @Override
            public String getExtraData() {
                return cashierParams.getExtraData();
            }

            @Override
            public String getExtraStatics() {
                return cashierParams.getExtraStatics();
            }

            @Override
            public String getGuideRequestNo() {
                return mRouteInfo != null ? mRouteInfo.getGuideRequestNo() : "";
            }

            @Override
            public String getDowngradeErrorInfo() {
                return cashierParams.getDowngradeInfo();
            }

            @Override
            public HashMap<String, String> getExtendTransmissionParams() {
                return cashierParams.getExtendTransmissionParams();
            }

            @Override
            public void queryThirdPayOrder() {
                // 查询支付结果的逻辑转移到thirdPayHandler中执行
            }

            @Override
            public void showPopWindow(CashierPopWindowBean cashierPopWindowBean, Fragment fragment) {
                cashierViewDialogHandler.showCashierPopWindow(cashierPopWindowBean, fragment);
            }

            @Override
            public void setPayType(String payType) {
                curPayType = payType;
            }

            @Override
            public void onOrderTimeout() {
                NativeStandardCashierHandler.this.onOrderTimeout();
            }

            @Override
            public boolean canSendRequest() {
                return !overLoadHandler.isOverLoading();
            }

            @Override
            public void processSuspendPaying() {
                overLoadHandler.onOverLoading();
            }

            @Override
            public void refreshWhenBackFromMTPay(boolean fromMTPayNeedRefresh) {
                NativeStandardCashierHandler.this.fromMTPayAndRefresh = fromMTPayNeedRefresh;
            }

            @Override
            public void setFinalFeeText(String finalFeeText) {
                NativeStandardCashierHandler.this.finalFeeText = finalFeeText;
            }

            @Override
            public void onRequestSucc(int tag, Object obj) {
                NativeStandardCashierHandler.this.onRequestSucc(tag, obj);
            }

            @Override
            public void onRequestException(int tag, Exception e) {
                NativeStandardCashierHandler.this.onRequestException(tag, e);
            }

            @Override
            public void onRequestFinal(int tag) {
            }

            @Override
            public void onRequestStart(int tag) {
            }

            @Override
            public AdapterContext adapterContext() {
                return adapterContext;
            }
        };

        // 通过路由注册通知事件
        adapterContext.observable(OnResume.class).subscribe(() -> thirdPayHandler.queryOrderFromResume());
        adapterContext.observable(OnWindowFocusChanged.class).subscribe(this::handleOrderTimeout);
        adapterContext.observable(OnBackPressed.class).subscribe(
                () -> getFragment() instanceof MTCashierRevisionFragment && detainmentDialogHandler.setRetainWindow(mRouteInfo.getRetainWindow()).show());
        adapterContext.observable(OnActivityResult.class).subscribe(
                (requestCode, resultCode, data) -> PayerMediator.getInstance().consumeActivityResult(getActivity(), requestCode, resultCode, data));
        // 广播能力：刷新收银台。TODO 后续改为其他形式
        LocalBroadCastUtil.registerBroadCast(activity, ACTION_REFRESH_CASHIER, standardCashierBroadCastReceiver);
        DiscountMonitorHelper.getInstance().registerProcess();
        // 兜底机制，重建或刷新场景重新接收外部返回的支付结果。
        PayerMediator.getInstance().setPayActionListener(activity, new PayActionListener() {
            @Override
            public void onPayPreExecute(String payType) {

            }

            @Override
            public void onGotPayResult(String payType, int payResult, PayFailInfo payFailInfo) {
                if (!mtPayHandler.handleMTPayResult(payType, payResult, payFailInfo)
                        || !thirdPayHandler.handleThirdPayResult(payType, payResult, payFailInfo)) {
                    CashierStaticsUtils.reportSystemCheck("", null, null); // 如果美团和三方都没有能够处理，上报错误
                }
            }
        });
        // 加载收银台
        openCommonCashier();
    }

    public void onDestroy() {
        detainmentDialogHandler.onDestroy();
        overLoadHandler.onDestroy();
        PayerMediator.getInstance().removePayActionListener(getActivity());
        LocalBroadCastUtil.unregisterReceiver(getActivity(), standardCashierBroadCastReceiver);
        DiscountMonitorHelper.getInstance().unRegisterProcess();
        FragmentActivity activity = getActivity();
        if (activity != null) {
            Fragment fragment = getActivity().getSupportFragmentManager().findFragmentById(commonFragmentId);
            if (fragment instanceof MTCashierRevisionFragment) {
                activity.getSupportFragmentManager().beginTransaction().remove(fragment).commitNowAllowingStateLoss();
            }
        }
    }

    private void openCommonCashier() {
        requestDispatcherWithRefreshParams(null, false);
        loadNativeStandardCashierFragment();
    }

    private void processCashier(CashierPopWindowBean cashierToShow) {
        Cashier cashier = mRouteInfo.getCashier();
        if (cashier == null) { // 如果数据不符合预期，则重新请求
            requestDispatcherWithRefreshParams(null, false);
            return;
        }
        if (!cashierDialogHandler.show()) { // 如果不展示收银台弹窗，则展示标准收银台
            showNativeStandardCashierFragment(cashier, cashierToShow);
        }
    }

    /**
     * 加载标准收银台Fragment，有预加载流程
     */
    private void loadNativeStandardCashierFragment() {
        FragmentActivity activity = getActivity();
        if (activity == null) {
            return;
        }
        CatchException.run(() -> {
            Fragment fragment = activity.getSupportFragmentManager().findFragmentById(commonFragmentId);
            if (fragment instanceof MTCashierRevisionFragment) {
                ((MTCashierRevisionFragment) fragment).setNSCHandlerProxy(nscHandlerProxy);
                ((MTCashierRevisionFragment) fragment).init(null, null, null, cashierParams.getMerchantNo(), null, null, isRefresh);
            } else {
                MTCashierRevisionFragment nscFragment = new MTCashierRevisionFragment();
                nscFragment.setNSCHandlerProxy(nscHandlerProxy);
                activity.getSupportFragmentManager().beginTransaction()
                        .replace(commonFragmentId, nscFragment)
                        .commitAllowingStateLoss();
            }
        }).catchForReport("NativeStandardCashierHandler_loadNativeStandardCashierFragment");
    }

    /**
     * 展示标准收银台Fragment，使用参数进行初始化，在不同的场景逻辑不通：
     * 1. 非刷新场景（首次），使用收银台参数Cashier以及弹窗参数CashierPopWindowBean进行初始化（会有收银台引导弹窗）
     * 2. 刷新场景，使用新的收银台Fragment替换旧的收银台Fragment，并不展示收银台前引导弹窗(拉新弹窗)
     *
     * @param cashier              收银台数据
     * @param cashierPopWindowBean 弹窗数据
     */
    private void showNativeStandardCashierFragment(Cashier cashier, CashierPopWindowBean cashierPopWindowBean) {
        FragmentActivity activity = getActivity();
        if (activity == null) {
            return;
        }
        Fragment fragment = activity.getSupportFragmentManager().findFragmentById(commonFragmentId);
        if (fragment instanceof MTCashierRevisionFragment) {
            if (isRefresh) { // refresh场景下新生成MTCashierRevisionFragment
                isRefresh = false;
                MTCashierRevisionFragment nscFragment = new MTCashierRevisionFragment();
                nscFragment.setNSCHandlerProxy(nscHandlerProxy);
                //刷新收银台不展示拉新弹窗
                nscFragment.init(cashierParams.getTradeNo(),
                        cashierParams.getPayToken(), cashier,
                        cashierParams.getMerchantNo(),
                        cashierParams.getAppId(), null, true);
                activity.getSupportFragmentManager().beginTransaction().replace(commonFragmentId, nscFragment).commitAllowingStateLoss();
            } else { // 非refresh场景下使用原来的MTCashierRevisionFragment
                ((MTCashierRevisionFragment) fragment).setNSCHandlerProxy(nscHandlerProxy);
                ((MTCashierRevisionFragment) fragment).init(cashierParams.getTradeNo(),
                        cashierParams.getPayToken(), cashier,
                        cashierParams.getMerchantNo(),
                        cashierParams.getAppId(), cashierPopWindowBean, false);
            }
        }
    }

    @Override
    public void onRequestSucc(int tag, Object obj) {
        switch (tag) {
            case REQ_TAG_ROUTE:
                handleDispatcher((RouteInfo) obj);
                break;

            case REQ_TAG_GO_HELLO_PAY_AUTOMATIC_PAY_GUIDE:
                fromAutomaticPayGuide = true;
            case REQ_TAG_GO_HELLO_PAY:
                goHelloPayHandler.onResponse((MTPaymentURL) obj);
                break;

            case REQ_TAG_DIRECT_PAY:
                directPayHandler.onResponse((PayResult) obj);
                break;

            case REQ_TAG_EXCEED_TIME_LIMIT:
                handleRequestOrderTimeout((OrderResult) obj);
                break;
        }
    }

    @Override
    public void onRequestException(int tag, Exception e) {
        switch (tag) {
            case REQ_TAG_ROUTE:
                dispatcherHandler.handleException(e);
                break;

            case REQ_TAG_GO_HELLO_PAY_AUTOMATIC_PAY_GUIDE:
            case REQ_TAG_GO_HELLO_PAY:
                goHelloPayHandler.onRequestException(tag, e);
                break;

            case REQ_TAG_DIRECT_PAY:
                directPayHandler.handleException(e);
                break;

            case REQ_TAG_EXCEED_TIME_LIMIT:
                handleRequestOrderTimeout(null);
                break;
        }
    }

    @Override
    public void onRequestFinal(int tag) {
        ProgressController.of(getActivity()).hide();
    }

    @Override
    public void onRequestStart(int tag) {
        ProgressController.of(getActivity()).setProgressType(ProgressType.CASHIER).show();
    }

    public void onNeedRefresh() {
        this.fromMTPayAndRefresh = true;
    }

    private FragmentActivity getActivity() {
        Activity activity = adapterContext.getActivity();
        return activity instanceof FragmentActivity ? (FragmentActivity) activity : null;
    }

    private Fragment getFragment() {
        FragmentActivity activity = getActivity();
        return activity != null ? activity.getSupportFragmentManager().findFragmentById(commonFragmentId) : null;
    }

    // ******************** 订单超时处理 ********************
    public void onOrderTimeout() {
        isOrderTimeout = true;
        FragmentActivity activity = getActivity();
        if (activity != null) {
            handleOrderTimeout(activity.hasWindowFocus());
        }
    }

    private void handleOrderTimeout(boolean hasWindowFocus) {
        if (isOrderTimeout && hasWindowFocus && !isOrderTimeoutHandled) {
            isOrderTimeoutHandled = true;
            PayRetrofit.getInstance().create(CashierRequestService.class, this, REQ_TAG_EXCEED_TIME_LIMIT).queryOrder(
                    cashierParams.getTradeNo(),
                    cashierParams.getPayToken(),
                    "1",// isauto
                    cashierParams.getExtraData(),
                    cashierParams.getExtDimStat(),
                    cashierParams.getExtendTransmissionParams());
        }
    }

    private void handleRequestOrderTimeout(OrderResult result) {
        FragmentActivity activity = getActivity();
        if (result != null && result.isResult()) {
            CashierRouterHelper.from(adapterContext).success().finish();
        } else if (ActivityStatusChecker.isValid(activity)) {
            new PayDialog.Builder(activity)
                    .msg(activity.getString(R.string.cashier__pay_timeout_content))
                    .leftBtn(activity.getString(R.string.cashier__pay_timeout_btn), (dialog) -> CashierRouterHelper.from(adapterContext).cancel().finish())
                    .build().show();
        }
    }

    // ********************* Dispatcher请求与处理 *********************
    public void requestDispatcherWithRefreshParams(final String dispatcherScene, boolean refresh) {
        isRefresh = refresh;
        cipStorageCenter = SdkDataStorageUtils.getDataStorageCenter();
        final int savedInstalledApps = cipStorageCenter.getInteger(StandardCashierConstants.KEY_INSTALLED_APPS, DEF_VALUE_INSTALLED_APPS);
        final String savedIsRoot = cipStorageCenter.getString(StandardCashierConstants.KEY_IS_ROOT, DEF_VALUE_IS_ROOT);
        final String savedUpSePayType = UPPayUtils.loadSepayType(MTPayConfig.getProvider().getApplicationContext());
        cashierParams.setInstalledApps(savedInstalledApps);
        // 如果获取过installedApps和isRoot，则可以直接进行请求，否则需要异步获取后再进行请求
        final boolean withoutRefreshParams = savedInstalledApps != DEF_VALUE_INSTALLED_APPS && !TextUtils.equals(savedIsRoot, DEF_VALUE_IS_ROOT);
        if (withoutRefreshParams) {
            requestDispatcher(dispatcherScene, String.valueOf(savedInstalledApps), savedIsRoot, savedUpSePayType);
            this.isRoot = TextUtils.equals("1", savedIsRoot);
        }
        new ConcurrentTask<String, Integer, StandardCashierRefreshParams>() {
            // 异步获取installedApps和isRoot每次必执行
            @Override
            protected StandardCashierRefreshParams doInBackground(String... params) {
                String root = SystemInfoUtils.isRoot() ? "1" : "0";
                int installedApps = PaymentInstallInfoUtils.getInstalledApps(MTPayConfig.getProvider().getApplicationContext());
                cipStorageCenter.setString(StandardCashierConstants.KEY_IS_ROOT, root);
                cipStorageCenter.setInteger(StandardCashierConstants.KEY_INSTALLED_APPS, installedApps);
                return new StandardCashierRefreshParams(root, installedApps);
            }

            @Override
            protected void onPostExecute(StandardCashierRefreshParams refreshParams) {
                if (!withoutRefreshParams) { // 如果需要refresh参数后再请求
                    int installedApps = refreshParams.getInstalledApps();
                    String isRoot = refreshParams.getIsRoot();
                    cashierParams.setInstalledApps(savedInstalledApps);
                    requestDispatcher(dispatcherScene, String.valueOf(installedApps), isRoot, savedUpSePayType);
                    NativeStandardCashierHandler.this.isRoot = TextUtils.equals("1", isRoot);
                }
            }
        }.exe();
        // 获取银联相关参数
        if (UPPayUtils.isCheckStatusEqualNull()) {
            UPPayUtils.startToGetSEPayInfo(PayBaseConfig.getProvider().getApplicationContext());
        }
    }

    private void requestDispatcher(String dispatcherScene, String installedApps, String isRoot, String upSePayType) {
        PayRetrofit.getInstance().create(CashierRequestService.class, this, REQ_TAG_ROUTE).startRouting(
                cashierParams.getTradeNo(),
                cashierParams.getPayToken(),
                isRoot,
                installedApps,
                cashierParams.getCallbackUrl(),
                dispatcherScene,
                MTPayConfig.getProvider().getFingerprint(),
                upSePayType,
                cashierParams.getDowngradeInfo(),
                cashierParams.getGuidePlanInfos(),
                cashierParams.getExtraData(),
                cashierParams.getExtDimStat(),
                cashierParams.getExtendTransmissionParams());
    }

    private void handleDispatcher(RouteInfo routeInfo) {
        FragmentActivity activity = getActivity();
        if (activity instanceof CashierActivityHandler) { // 设置ActionBar、StatusBar等
            ((CashierActivityHandler) activity).setActivityForCashier();
        }
        mRouteInfo = routeInfo;

        // 优先处理降级i版收银台
        String webCashierUrl = mRouteInfo.getUrl();
        if (!TextUtils.isEmpty(webCashierUrl)) {
            CashierSLAMonitor.notifyRouterLoadEnd(cashierParams.getCashierRouterTrace(), "downgrade web cashier");
            CashierRouterHelper.from(adapterContext.adapter())
                    .destProductType(CashierRouterConstants.PRODUCT_TYPE_WEB_CASHIER)
                    .destAdapterType(CashierRouterConstants.ADAPTER_TYPE_WEB_CASHIER)
                    .extra("webCashierUrl", webCashierUrl).downgrade();
            return;
        }

        // 展示手机被root时的toast
        String rootDescription = mRouteInfo.getRootDesc();
        if (isRoot && !TextUtils.isEmpty(rootDescription)) {
            ToastUtils.showSnackToast(activity, rootDescription, true);
        }

        // 设置abtest分组
        CommonABTestManager.setAbTestGroup(mRouteInfo.getAbTestGroup());

        // 处理美支弹窗展示逻辑
        Cashier cashierBean = mRouteInfo.getCashier();
        CashierPopWindowBean cashierPopWindowBean = mRouteInfo.getCashierPopWindowBean();
        cashierDialogHandler.setCashierResponse(cashierBean);
        mtPayDialogHandler.setCashierResponse(cashierPopWindowBean, cashierBean);
        thirdPayHandler.setCashierPopWindowBean(cashierPopWindowBean, cashierBean);

        // 返回美支弹窗数据处理结果
        MTPayDialogHandler.Result result = mtPayDialogHandler.show();
        if (!result.isSuccess()) { // 如果不展示美支弹窗，则开始处理收银台数据
            processCashier(result.getCashierToShow());
        } else if (result.getType() == MTPayDialogHandler.POP_WINDOW_PAY_LATER_GUIDE) {
            fromPayLaterGuide = true;
        }
    }

    // ********************* 每次进入都要刷新的参数 *********************
    private static class StandardCashierRefreshParams {
        private final String isRoot;
        private final int installedApps;

        public StandardCashierRefreshParams(String isRoot, int installedApps) {
            this.isRoot = isRoot;
            this.installedApps = installedApps;
        }

        public String getIsRoot() {
            return isRoot;
        }

        public int getInstalledApps() {
            return installedApps;
        }
    }

    private static class StandardCashierBroadCastReceiver extends BroadcastReceiver {
        private final WeakReference<NativeStandardCashierHandler> weakHandler;

        public StandardCashierBroadCastReceiver(NativeStandardCashierHandler handler) {
            this.weakHandler = new WeakReference<>(handler);
        }

        @Override
        public void onReceive(Context context, Intent intent) {
            NativeStandardCashierHandler handler = weakHandler.get();
            if (handler != null && intent != null && ACTION_REFRESH_CASHIER.equals(intent.getAction())) {
                handler.onNeedRefresh();
            }
        }
    }
}
