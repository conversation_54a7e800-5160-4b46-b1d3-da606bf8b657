package com.meituan.android.cashier.widget;

import android.app.Activity;
import android.content.Context;
import android.support.v7.widget.AppCompatCheckBox;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.meituan.android.cashier.R;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.pay.common.promotion.bean.Agreement;
import com.meituan.android.paybase.common.activity.PayBaseActivity;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paycommon.lib.utils.ViewUtils;
import com.meituan.android.paycommon.lib.webview.specialcontainer.dialogclose.WebViewDialogCloseActivity;

/**
 *  底部按钮上面的品宣view，展示买单协议
 */
public class CashierBrandView extends LinearLayout {

    // 品宣协议是否选中 - （目前是针对买单）
    private AppCompatCheckBox mAgreementCheckBox;
    // 品宣中的协议信息 - （目前是只有买单有协议）
    private Agreement mBrandAgreement;
    // 底部提示品宣文案
    private String mBrandText;
    // 协议名字
    private TextView mAgreementName;
    // 协议前缀
    private TextView mAgreementPrefix;
    // 品宣内容文案
    private TextView mBrandTextView;
    // checkbox的点击区域
    private LinearLayout mAgreementTapArea;
    private Activity activity;


    public CashierBrandView(Context context) {
        super(context);
        initView();
    }

    public void setActivity(Activity activity) {
        this.activity = activity;
    }

    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.cashier__creditpay_brand_view,
                this);
        mAgreementCheckBox = findViewById(R.id.cashier_agreement_check_box);
        mAgreementName = findViewById(R.id.cashier_agreement_name);
        mAgreementPrefix = findViewById(R.id.cashier_agreement_prefix);
        mBrandTextView = findViewById(R.id.cashier_brand_text);
        mAgreementTapArea = findViewById(R.id.cashier_agreement_tap_area);
    }

    // 获取买单协议数据
    public Agreement getBrandAgreement() {
        return this.mBrandAgreement;
    }

    // 是否展示底部品宣布局
    private boolean isShowBrandLayout() {
        return getBrandAgreement() != null || !TextUtils.isEmpty(mBrandText);
    }

    // 是否展示协议的checkbox
    public boolean isShowAgreementCheckBox() {
        if (getBrandAgreement() != null) {
            return getBrandAgreement().canCheck();
        } else {
            return false;
        }
    }

    // 协议的默认选中状态
    public boolean agreementDefaultSelected() {
        if (getBrandAgreement() != null) {
            return getBrandAgreement().isChecked();
        } else {
            return false;
        }
    }

    // 检查买单协议是否选中
    public boolean checkAgreementIsSelected() {
        return mAgreementCheckBox.isChecked();
    }

    // 设置品宣显示的数据内容
    public void setBrandData(Agreement agreement, String brandText) {
        this.mBrandAgreement = agreement;
        this.mBrandText = brandText;
    }

    // 设置品宣布局的内容刷新
    public void refresh() {
        if (isShowBrandLayout()) {
            if (isShowAgreementCheckBox()) {
                mAgreementCheckBox.setChecked(agreementDefaultSelected());
                mAgreementCheckBox.setVisibility(View.VISIBLE);
                mAgreementCheckBox.setOnCheckedChangeListener((buttonView, isChecked) -> {
                    if (getBrandAgreement() != null) {
                        getBrandAgreement().setIsChecked(isChecked);
                    } else {
                        setVisibility(GONE);
                        CashierStaticsUtils.techMis("b_pay_27j1p3ms_mc", new AnalyseUtils.MapBuilder().add("info", "getBrandAgreement()为空：" + isChecked).build(), getUniqueId());
                    }
                    // checkbox状态改变
                    CashierStaticsUtils.techMis("b_pay_27j1p3ms_mc", new AnalyseUtils.MapBuilder().add("info", "checkout状态修改为：" + isChecked).build(), getUniqueId());
                });
                // 点击区域包括协议前面的文字范围
                mAgreementTapArea.setEnabled(true);
                mAgreementTapArea.setOnClickListener(v -> {
                    mAgreementCheckBox.setChecked(!mAgreementCheckBox.isChecked());
                    // 点击月付协议前文字"我已阅读与同意"
                    CashierStaticsUtils.techMis("b_pay_27j1p3ms_mc", new AnalyseUtils.MapBuilder().add("info", "点击我已阅读与同意").build(), getUniqueId());
                    });
            } else {
                mAgreementTapArea.setEnabled(false);
                mAgreementCheckBox.setVisibility(View.GONE);
            }

            if (getBrandAgreement() != null) {
                if (TextUtils.isEmpty(getBrandAgreement().getName())) {
                    mAgreementName.setVisibility(View.GONE);
                } else {
                    mAgreementName.setVisibility(View.VISIBLE);
                    mAgreementName.setText(getBrandAgreement().getName());
                    mAgreementName.setOnClickListener(v -> {
                        if (getBrandAgreement() != null && !TextUtils.isEmpty(getBrandAgreement().getUrl())) {
                            WebViewDialogCloseActivity.open(getContext(), getBrandAgreement().getUrl());
                            CashierStaticsUtils.techMis("b_pay_26x8f4eq_mc", new AnalyseUtils.MapBuilder().add("info", "点击月付协议" + getBrandAgreement().getUrl()).build(), getUniqueId());
                        } else {
                            CashierStaticsUtils.techMis("b_pay_26x8f4eq_mc", new AnalyseUtils.MapBuilder().add("info", "url为空").build(), getUniqueId());
                        }
                    });
                }
                if (TextUtils.isEmpty(getBrandAgreement().getAgreementPrefix())) {
                    mAgreementPrefix.setVisibility(View.GONE);
                } else {
                    mAgreementPrefix.setVisibility(View.VISIBLE);
                    mAgreementPrefix.setText(getBrandAgreement().getAgreementPrefix());
                }
            } else {
                mAgreementName.setVisibility(View.GONE);
                mAgreementPrefix.setVisibility(View.GONE);
            }

            if (TextUtils.isEmpty(mBrandText)) {
                mBrandTextView.setVisibility(View.GONE);
            } else {
                mBrandTextView.setVisibility(View.VISIBLE);
                mBrandTextView.setText(mBrandText);
            }
            setVisibility(VISIBLE);
            CashierStaticsUtils.techMis("b_pay_tr4fl35l_mv", new AnalyseUtils.MapBuilder().add("info", "品宣view展示").build(), getUniqueId());
        } else {
            setVisibility(GONE);
        }
    }

    private String getUniqueId() {
        Activity activity = ViewUtils.getActivityFromView(this);
        if (activity == null) {
            activity = this.activity;
        }
        if (activity instanceof PayBaseActivity && !TextUtils.isEmpty(((PayBaseActivity) activity).getUniqueId())) {
            return ((PayBaseActivity) activity).getUniqueId();
        }
        return "";
    }
}
