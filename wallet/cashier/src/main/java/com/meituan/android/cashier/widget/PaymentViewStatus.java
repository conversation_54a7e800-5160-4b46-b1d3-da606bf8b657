package com.meituan.android.cashier.widget;

import android.support.annotation.Keep;
import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.pay.common.payment.bean.BasePayment;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.pay.desk.payment.view.BasePaymentView;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 埋点用
 */
@Keep
public class PaymentViewStatus implements Serializable {
    @SerializedName("pay_type")
    private String payType;
    @SerializedName("pay_mode")
    private String payMode;
    @SerializedName("bank_type_id")
    private String bankTypeId;
    @SerializedName("is_selected")
    private String isSelected;
    @SerializedName("is_folded")
    private String isFolded;
    @SerializedName("is_first_screen_exposed")
    private String isFirstScreenExposed;
    @SerializedName("item_index")
    private int itemIndex; // 兼容Hybrid上报，数组的顺序就是index的值

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getBankTypeId() {
        return bankTypeId;
    }

    public void setBankTypeId(String bankTypeId) {
        this.bankTypeId = bankTypeId;
    }

    public String getIsSelected() {
        return isSelected;
    }

    public void setIsSelected(boolean isSelected) {
        this.isSelected = isSelected ? "1" : "0";
    }

    public String getIsFolded() {
        return isFolded;
    }

    public void setIsFolded(boolean isFolded) {
        this.isFolded = isFolded ? "1" : "0";
    }

    public String getIsFirstScreenExposed() {
        return isFirstScreenExposed;
    }

    public void setIsFirstScreenExposed(boolean isFirstScreenExposed) {
        this.isFirstScreenExposed = isFirstScreenExposed ? "1" : "0";
    }

    public int getItemIndex() {
        return itemIndex;
    }

    public void setItemIndex(int itemIndex) {
        this.itemIndex = itemIndex;
    }

    public static PaymentViewStatus initFromPayment(BasePaymentView paymentView) {
        if (paymentView == null || !(paymentView.getPaymentData() instanceof BasePayment)) {
            return null;
        }
        BasePayment paymentData = (BasePayment) paymentView.getPaymentData();
        PaymentViewStatus status = new PaymentViewStatus();
        if (paymentData instanceof MTPayment) {
            status.setPayMode(((MTPayment) paymentData).getPayTypeUniqueKey());
            status.setBankTypeId(((MTPayment) paymentData).getBankTypeId());
        }
        if (TextUtils.isEmpty(status.getPayMode())) {
            status.setPayMode("-999");
        }
        if (TextUtils.isEmpty(status.getBankTypeId())) {
            status.setBankTypeId("-999");
        }
        status.setPayType(paymentData.getPayType());
        status.setIsSelected(paymentView.isChecked());
        status.setIsFolded(paymentData.isFolded());
        return status;
    }


}
