package com.meituan.android.cashier;

import static com.meituan.android.paybase.constants.ThirdPayConstants.MSG_PARAMS_EXCEPTION;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.support.v4.content.LocalBroadcastManager;
import android.text.TextUtils;

import com.meituan.android.cashier.common.CashierConstants;
import com.meituan.android.paybase.downgrading.PayHornConfigBean;
import com.meituan.android.paybase.downgrading.PayHornConfigService;
import com.meituan.android.paybase.moduleinterface.payment.PayActionListener;
import com.meituan.android.paybase.moduleinterface.payment.PayFailInfo;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paymentchannel.PayerMediator;
import com.meituan.android.paymentchannel.PayersID;
import com.tencent.mm.opensdk.constants.ConstantsAPI;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by liuqi on 15-7-6.
 */
public final class CashierAPI {

    private static final String CASHIER_LAUNCH_URL = "meituanpayment://cashier/launch";
    private static final String TRADE_NUMBER = "trade_number";
    private static final String PAY_TOKEN = "pay_token";
    private static final String EXTRA_DATA = "extra_data";
    private static final String EXTRA_STATICS = "extra_statics";

    private CashierAPI() {
    }

    public static void onDCEPPayResultGot(Uri uri) {
        PayerMediator.onGotDCEPResult(uri);
    }

    public static void onGotAlipayHkResult(Uri uri) {
        PayerMediator.onGotAlipayHkResult();
    }

    public static void handleAppPayResult(Activity activity) {
        PayerMediator.handleAppPayResult(activity);
    }

    public static void onWXPayResultGot(Context context, BaseResp resp) {
        if (resp.getType() == ConstantsAPI.COMMAND_LAUNCH_WX_MINIPROGRAM) {
            PayerMediator.onGotWechatJSPayResult(resp);
        } else {
            PayerMediator.onGotWechatResult(resp);
        }

    }

    public static void open(Activity activity, String tradeNo, String payToken, int requestCode) {
        open(activity, tradeNo, payToken, requestCode, "", "");
    }

    public static void open(Activity activity, String tradeNo, String payToken, int requestCode, String extraData) {
        open(activity, tradeNo, payToken, requestCode, extraData, "");
    }

    public static void open(Activity activity, String tradeNo, String payToken, int requestCode, String extraData, String extraStatics) {
        Uri.Builder builder = Uri.parse(CASHIER_LAUNCH_URL).buildUpon();
        builder.appendQueryParameter(TRADE_NUMBER, tradeNo);
        builder.appendQueryParameter(PAY_TOKEN, payToken);
        if (!TextUtils.isEmpty(extraData)) {
            builder.appendQueryParameter(EXTRA_DATA, extraData);
        }
        if (!TextUtils.isEmpty(extraStatics)) {
            builder.appendQueryParameter(EXTRA_STATICS, extraStatics);
        }
        Uri uri = builder.build();
        Intent intent = new Intent(Intent.ACTION_VIEW, uri);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
        intent.setPackage(activity.getPackageName());
        activity.startActivityForResult(intent, requestCode);
    }

    public static void open(Activity activity, String tradeNo, String payToken, String callbackUrl) {
        Uri.Builder builder = Uri.parse(CASHIER_LAUNCH_URL).buildUpon();
        builder.appendQueryParameter(TRADE_NUMBER, tradeNo);
        builder.appendQueryParameter(PAY_TOKEN, payToken);
        if (!TextUtils.isEmpty(callbackUrl)) {
            builder.appendQueryParameter("callback_url", callbackUrl);
        }
        Uri uri = builder.build();
        Intent intent = new Intent(Intent.ACTION_VIEW, uri);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
        intent.setPackage(activity.getPackageName());
        activity.startActivity(intent);
    }

    public static void subscribe(Activity activity, String tradeNo, BroadcastReceiver broadcastReceiver) {
        IntentFilter intentFilter = new IntentFilter();
        PayHornConfigBean payHornConfigBean = PayHornConfigService.get().getPayCashierHornConfigBean();
        if (payHornConfigBean != null && payHornConfigBean.isAndroidOptimizeExperience()) {
            intentFilter.addAction(CashierConstants.KEY_CASHIER_CALLBACK_RESULT + tradeNo);
        } else {
            intentFilter.addAction(CashierConstants.KEY_CASHIER_CALLBACK_RESULT_NATIVE + tradeNo);
        }
        LocalBroadcastManager.getInstance(activity).registerReceiver(broadcastReceiver, intentFilter);
    }

    public static void unsubscribe(Activity activity, BroadcastReceiver broadcastReceiver) {
        LocalBroadcastManager.getInstance(activity).unregisterReceiver(broadcastReceiver);
    }
}
