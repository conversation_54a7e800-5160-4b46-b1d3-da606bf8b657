package com.meituan.android.cashier.newrouter;

import android.support.v4.app.FragmentActivity;

import java.lang.ref.WeakReference;

public class CashierBusinessHandler {
    private final WeakReference<FragmentActivity> weakActivity;
    private final NewCashierParams cashierParams;

    public CashierBusinessHandler(FragmentActivity activity, NewCashierParams cashierParams) {
        this.weakActivity = new WeakReference<>(activity);
        this.cashierParams = cashierParams;
    }

    protected NewCashierParams getCashierParams() {
        return cashierParams;
    }

    protected FragmentActivity getActivity() {
        return weakActivity.get();
    }

}
