package com.meituan.android.cashier.widget;

import static com.meituan.android.cashier.retrofit.CashierRequestUtils.isMTPayOrCreditPay;

import android.app.Activity;
import android.content.Context;
import android.support.annotation.Nullable;
import android.text.Html;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.meituan.android.cashier.R;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.common.CashierUtil;
import com.meituan.android.cashier.model.bean.Cashier;
import com.meituan.android.cashier.retrofit.CashierRequestUtils;
import com.meituan.android.cashier.utils.NativeStandardCashierPayProcessStatistic;
import com.meituan.android.pay.common.payment.bean.FinanceServiceBean;
import com.meituan.android.pay.common.payment.bean.FloatingLayer;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.pay.common.payment.data.IPaymentData;
import com.meituan.android.pay.common.promotion.bean.LabelAbTest;
import com.meituan.android.pay.common.selectdialog.bean.WalletPaymentListPage;
import com.meituan.android.pay.desk.pack.WalletPayManager;
import com.meituan.android.pay.desk.payment.bean.standarddesk.WalletPayment;
import com.meituan.android.pay.desk.payment.discount.DiscountCashierUtils;
import com.meituan.android.pay.utils.CreditOpenUtils;
import com.meituan.android.paybase.common.activity.PayBaseActivity;
import com.meituan.android.paybase.utils.CashAmountArithUtils;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.StatisticsUtils;
import com.meituan.android.paycommon.lib.utils.ViewUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 营销引导技术方案设计 https://km.sankuai.com/page/367533746
 */
public class CashierMarketingGuideFloatView extends LinearLayout {

    private MTPayment marketingPayment;
    private FloatingLayer mFloatingLayer;
    private float marketingDiscount;
    private OnTapMarketingPaymentListener onTapMarketingPaymentListener;
    private Cashier cashier;
    private String mDiscountMsg;
    private IPaymentData mCheckedPayment;

    public CashierMarketingGuideFloatView(Context context) {
        super(context);
    }

    public CashierMarketingGuideFloatView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public CashierMarketingGuideFloatView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void init(Cashier cashier, OnTapMarketingPaymentListener onTapMarketingPaymentListener) {
        this.cashier = cashier;
        this.onTapMarketingPaymentListener = onTapMarketingPaymentListener;
    }

    public void refresh(IPaymentData checkedPaymentData) {
        this.mCheckedPayment = checkedPaymentData;
        refreshMarketingFloat(cashier, checkedPaymentData);
    }


    /**
     * 营销引导技术方案设计 https://km.sankuai.com/page/367533746
     *
     * @param checkedPaymentData
     * @return
     */
    private boolean isShowMarketingFloat(Cashier cashier, IPaymentData checkedPaymentData) {
        if (cashier == null || checkedPaymentData == null) {
            return false;
        }
        WalletPayment walletPayment = CashierRequestUtils.getWalletPayment(cashier);
        if (isMTPayOrCreditPay(walletPayment, checkedPaymentData)) { // 判断条件1，选中的需为第三方支付
            return false;
        }
        if (walletPayment == null) {
            return false;
        }
        WalletPaymentListPage walletPaymentListPage = walletPayment.getWalletPaymentListPage();
        if (walletPaymentListPage == null) {
            return false;
        }
        LabelAbTest labelAbTest = walletPaymentListPage.getLabelAbTest();
        if (labelAbTest == null) {
            return false;
        }
        if (!labelAbTest.isShowFloat()) { // 判断条件2，营销返回的 showFloat 字段为 true
            return false;
        }
        if (!CollectionUtils.isEmpty(checkedPaymentData.getBottomLabels())) { // 判断条件3，选中的支付方式没有营销数据
            return false;
        }
        // 判断条件4，营销返回浮层数据（或者美团支付有聚合营销标签）, 优惠金额需要大于0
        return hasMarketingPayment(cashier);
    }

    private void refreshMarketingFloat(Cashier cashier, IPaymentData checkedPaymentData) {
        if (isShowMarketingFloat(cashier, checkedPaymentData)) {
            MTPayment mtPayment = getMarketingPayment(cashier);
            if (mtPayment != null) {
                TextView discountMsg = findViewById(R.id.cashier_discount_text);
                if (discountMsg == null) {
                    setOnClickListener(view -> tapMarketingFloatButton(cashier));
                    if (mFloatingLayer != null) {
                        mDiscountMsg = mFloatingLayer.getFirstContent();
                        if (mFloatingLayer.getFloatTemplate() == FloatingLayer.FLOATING_LAYER_TEMPLATE_B) {
                            inflate(getContext(), R.layout.cashier__marketing_float_view_with_btn, this);
                            discountMsg = findViewById(R.id.cashier_discount_text);
                            if (!TextUtils.isEmpty(mFloatingLayer.getFirstContent())) {
                                discountMsg.setText(Html.fromHtml(mFloatingLayer.getFirstContent()));
                            }
                            TextView discountBtnMsg = findViewById(R.id.cashier_discount_text_btn);
                            discountBtnMsg.setText(mFloatingLayer.getSecondContent());
                            discountBtnMsg.setVisibility(VISIBLE);
                        } else {
                            inflate(getContext(), R.layout.cashier__marketing_float_view, this);
                            discountMsg = findViewById(R.id.cashier_discount_text);
                            if (!TextUtils.isEmpty(mFloatingLayer.getFirstContent())) {
                                discountMsg.setText(Html.fromHtml(mFloatingLayer.getFirstContent()));
                            }
                        }
                    } else if (marketingDiscount > 0) {
                        inflate(getContext(), R.layout.cashier__marketing_float_view, this);
                        discountMsg = findViewById(R.id.cashier_discount_text);
                        mDiscountMsg = String.format(getContext().getString(R.string.cashier__discount_button_text),
                                CashierUtil.formatFloat(marketingDiscount));
                        discountMsg.setText(mDiscountMsg);
                    }
                }
            }
            showMarketingFloat(cashier);
        } else {
            hideMarketingFloat();
        }
    }

    // 屏蔽优惠按钮
    private void hideMarketingFloat() {
        if (onTapMarketingPaymentListener != null) {
            onTapMarketingPaymentListener.onVisible(false);
        }
    }

    // 展示优惠按钮
    private void showMarketingFloat(Cashier cashier) {
        if (onTapMarketingPaymentListener != null) {
            onTapMarketingPaymentListener.onVisible(true);
        }
        CashierStaticsUtils.logModelEvent("c_PJmoK", "b_pay_r1j06raz_mv", "再减x元按钮曝光",
                getValLabMap(), StatisticsUtils.EventType.VIEW, getUniqueId());

        Map<String, Object> customTags = new HashMap<>();
        if (cashier != null) {
            MTPayment mtPayment = getMarketingPayment(cashier);
            if (mtPayment != null) {
                customTags.put("pay_type", mtPayment.getPayType());
            }
        }
        customTags.put("scene", "standard-cashier");
        CashierStaticsUtils.logCustom("paybiz_payment_marketing_float_show", customTags, null, getUniqueId());
    }

    public interface OnTapMarketingPaymentListener {
        void onTap(MTPayment marketingPayment);

        void onVisible(boolean visible);
    }

    // 点击权益按钮
    private void tapMarketingFloatButton(Cashier cashier) {
        MTPayment mtPayment = getMarketingPayment(cashier);
        if (mtPayment != null) {
            WalletPayManager.getInstance().setOpenSource(getContext(), CreditOpenUtils.STANDARD_MARKETBUTTON_SUFFIX);
            CashierStaticsUtils.logModelEvent("c_PJmoK", "b_pay_r1j06raz_mc", "点击再减x元按钮",
                    getValLabMap(), StatisticsUtils.EventType.CLICK, getUniqueId());
            if (onTapMarketingPaymentListener != null) {
                onTapMarketingPaymentListener.onTap(mtPayment);
            }
        }
    }

    private Map<String, Object> getValLabMap() {
        Map<String, Object> valLab = new HashMap<>();
        valLab.put("pay_type", marketingPayment != null ? marketingPayment.getPayType() : "");
        valLab.put("activity_id", mFloatingLayer != null ? mFloatingLayer.getFloatId() : "");
        valLab.put("title", mDiscountMsg != null ? mDiscountMsg : "");
        valLab.put("open_source", CreditOpenUtils.STANDARD_MARKETBUTTON_SUFFIX);
        if (mCheckedPayment != null) {
            valLab.put("currentSelected_pay_type", mCheckedPayment.getPayType());
        }
        NativeStandardCashierPayProcessStatistic.putCreditPayInfo(valLab, marketingPayment);
        return valLab;
    }

    // 得到点击按钮后选中的支付方式
    private MTPayment getMarketingPayment(Cashier cashier) {
        if (marketingPayment != null) {
            return marketingPayment;
        }
        WalletPayment mtPayment = CashierRequestUtils.getWalletPayment(cashier);
        if (cashier == null || mtPayment == null) {
            return null;
        }
        List<FinanceServiceBean> financeServiceBeanList = cashier.getFinanceDataList();
        Object[] objects = DiscountCashierUtils.getSuitablePaymentAndFloatingLayer(mtPayment, financeServiceBeanList);
        // 营销引导1.3逻辑
        if (objects[0] instanceof IPaymentData) {
            marketingPayment = (MTPayment) objects[0];
            mFloatingLayer = (FloatingLayer) objects[1];
        } else {
            // 营销引导1.3之前老逻辑
            objects = DiscountCashierUtils.getInitMaxDiscountPaymentData(mtPayment, financeServiceBeanList);
            if (objects[0] instanceof IPaymentData) {
                marketingPayment = (MTPayment) objects[0];
                marketingDiscount = (float) objects[1];
            }
        }
        return marketingPayment;
    }

    /**
     * 营销引导1.3，会返回引导FloatingLayer浮层，如果有返回该浮层则直接展示引导浮层
     * 营销引导1.2，不会返回FloatingLayer浮层，需要判断优惠金额是否大于0如果大于零则展示（如果只有返赠优惠金额可能为0）
     * 具体逻辑参考文档：https://km.sankuai.com/page/438152863
     *
     * @param cashier
     * @return
     */
    private boolean hasMarketingPayment(Cashier cashier) {
        MTPayment mtPayment = getMarketingPayment(cashier);
        if (mtPayment != null) {
            // 有引导浮层则进行展示
            if (mFloatingLayer != null) {
                return true;
            } else {
                // 未返回引导浮层，需要判断优惠金额是否大于0
                return CashAmountArithUtils.compare(marketingDiscount, 0) > 0;
            }
        } else {
            return false;
        }
    }

    private String getUniqueId() {
        Activity activity = ViewUtils.getActivityFromView(this);
        if (activity instanceof PayBaseActivity && !TextUtils.isEmpty(((PayBaseActivity) activity).getUniqueId())) {
            return ((PayBaseActivity) activity).getUniqueId();
        }
        return "";
    }
}
