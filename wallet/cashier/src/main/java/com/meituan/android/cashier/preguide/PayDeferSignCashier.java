package com.meituan.android.cashier.preguide;

import android.support.annotation.NonNull;
import android.support.v4.app.FragmentActivity;
import android.text.TextUtils;

import com.meituan.android.cashier.bean.CashierParams;
import com.meituan.android.cashier.bean.CashierRouterPreGuideHornConfig;
import com.meituan.android.cashier.common.CashierListener;
import com.meituan.android.cashier.common.CashierTypeConstant;
import com.meituan.android.cashier.common.ICashier;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.utils.CashierRepeatDownGradeSwitchManager;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paycommon.lib.fragment.HalfPageFragment;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * 先用后付
 */
@ServiceLoaderInterface(key = CashierTypeConstant.CASHIERTYPE_PAY_DEFER_SIGN, interfaceClass = ICashier.class)
public class PayDeferSignCashier extends PreGuideCashier {
    private final static String CASHIER_TYPE = CashierTypeConstant.CASHIERTYPE_PAY_DEFER_SIGN;
    private final static String PREFETCH_PATH = "/mtScorepay/payDefer/inPay/homePage";
    private CashierParams mCashierParams;

    @Override
    public <T extends FragmentActivity & CashierListener & IRequestCallback> ConsumeResult consume(T t, CashierParams cashierParams) {
        this.mCashierParams = cashierParams;
        if (TextUtils.equals(cashierParams.getProductType(), CASHIER_TYPE)) {
            return super.consume(t, cashierParams);
        }
        return new ConsumeResult(false, "pay_defer_sign_003", "product_type is " + cashierParams.getProductType());
    }

    @Override
    protected void prefetch(@NonNull CashierRouterPreGuideHornConfig cashierRouterPreGuideHornConfig, @NonNull HalfPageFragment.HalfPageFragmentConfig halfPageFragmentConfig) {
        super.prefetch(cashierRouterPreGuideHornConfig, halfPageFragmentConfig);
        // 预请求增加开关控制，预置 Horn 配置默认为关。
        // 如果后端上线后接口参数无变化，则打开预请求开关
        if (cashierRouterPreGuideHornConfig.isNsf()) {
            halfPageFragmentConfig.setRequestUrl(PREFETCH_PATH);
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("outer_business_data", this.mCashierParams.getExtraData());
                HashMap<String, String> extendTransmissionParams = this.mCashierParams.getExtendTransmissionParams();
                if (!CollectionUtils.isEmpty(extendTransmissionParams)) {
                    for (Map.Entry<String, String> entry : extendTransmissionParams.entrySet()) {
                        jsonObject.put(entry.getKey(), entry.getValue());
                    }
                }
                String extDimStat = getExtDimStat();
                if (!TextUtils.isEmpty(extDimStat)) {
                    jsonObject.put("ext_dim_stat", extDimStat);
                }
            } catch (Exception e) {
                LoganUtils.logError("PayDeferSignCashier_prefetch", e.getMessage());
            }
            halfPageFragmentConfig.setRequestData(jsonObject.toString());
        }
    }

    @Override
    protected void appendTunnelDate(JSONObject jsonObject) {
        super.appendTunnelDate(jsonObject);
        try {
            jsonObject.put("promotion_degrade_switch", CashierRepeatDownGradeSwitchManager.downGrade() ? "close" : "open");
        } catch (Exception e) {
            LoganUtils.logError("PayDeferSignCashier_appendTunnelDate", e.getMessage());
        }
    }

    @Override
    public String getCashierType() {
        return CashierTypeConstant.CASHIERTYPE_PAY_DEFER_SIGN;
    }
}