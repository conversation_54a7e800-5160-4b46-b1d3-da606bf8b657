package com.meituan.android.cashier.bridge.icashier;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.android.cashier.R;
import com.meituan.android.cashier.bridge.HybridBusinessJsHandler;
import com.meituan.android.cashier.common.CashierCatConstants;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.common.HybridReportConstants;
import com.meituan.android.cashier.model.bean.CashierPayment;
import com.meituan.android.cashier.model.bean.CashierPopWindowBean;
import com.meituan.android.neohybrid.neo.report.MapBuilder;
import com.meituan.android.neohybrid.neo.tunnel.TunnelParamManager;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.pay.desk.pack.WalletPayManager;
import com.meituan.android.pay.desk.payment.bean.standarddesk.WalletPayment;
import com.meituan.android.pay.utils.CreditOpenUtils;
import com.meituan.android.paybase.common.activity.PayBaseActivity;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.common.analyse.cat.CatUtils;
import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.moduleinterface.FinanceJsHandler;
import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.Strings;
import com.meituan.android.paycommon.lib.fragment.HalfPageFragment;
import com.meituan.android.paycommon.lib.utils.OuterBusinessParamUtils;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * author:  kkli
 * date:    2019-09-03
 * description: 不支持非v4.Activity的页面调起
 */
@ServiceLoaderInterface(key = "pay.selectBank", interfaceClass = FinanceJsHandler.class)
public class ICashierJSHandler extends HybridBusinessJsHandler implements FinanceJsHandler, OuterBusinessParamUtils.OuterBusinessParamInterface {

    private static final String NAME = "pay.selectBank";

    private static final String KEY_ACTION = "action";
    private static final String KEY_STATUS = "status";
    private static final String KEY_DATA = "data";
    public static final String KEY_PROMOTION = "promotion";
    private static final String KEY_PAY_RESULT = "pay_result";

    private static final String KEY_DATA_TRADE_NO = "tradeno";
    private static final String KEY_MERCHANT_NO = "merchant_no";
    private static final String KEY_DATA_PAY_TOKEN = "pay_token";
    private static final String KEY_DATA_OPEN_SOURCE = "open_source";

    private static final String KEY_ACTION_START_SELECT_PAYMENT = "start_select_payment";
    private static final String KEY_ACTION_START_SELECT_BANK = "start_select_bank";
    private static final String KEY_ACTION_START_GO_HELLO_PAY = "start_go_hello_pay";
    private static final String KEY_DATA_SELECT_PAYMENT_INDEX = "payment_index"; // int
    private static final String KEY_DATA_SELECT_MT_PAYMENT = "mt_payment"; // MTPayment
    private static final String KEY_DATA_SELECT_BANK_INDEX = "bank_index"; // int
    private static final String KEY_DATA_CASHIER_PAYMENT = "cashier_payment"; // CashierPayment
    private static final String KEY_DATA_EXTRA_DATA = "extra_data";
    private static final String KEY_DATA_CIF = "cif";
    public static final String KEY_NEW_CREDITPAY_OPEN_VERIFY_SCENE = "verify_scene";
    private static final String KEY_DATA_EXTRA_STATICS = "extra_statics";
    private static final String KEY_DATA_POP_WINDOW = "pop_window";
    private static final String KEY_DATA_ENTRY = "entry"; // String
    private static final String KEY_TRANSMISSION_PARAM = "transmission_param";
    private static final String KEY_CASHIER_TYPE = "cashier_type";

    static final String VALUE_STATUS_SELECT_BANK_CLOSE = "select_bank_close";
    static final int VALUE_CODE_COMMON_BACK_CANCEL = -11025;

    static final String JS_CALLBACK_PARAMS_CHECK_ERROR = "参数校验错误:";
    static final String JS_CALLBACK_ILLEGAL_STATE = "非法状态错误:";
    static final String JS_CALLBACK_PARAMS_PARSER_ERROR = "参数解析失败:";
    static final String JS_CALLBACK_BACKGROUND_ERROR = "后端返回参数错误:";

    private String tradeNo;
    private String payToken;
    private String merchantNo;
    private String openSource;
    private ICashierPayerHandler payerHandler;
    private ICashierSelectHandler selectHandler;
    private String extraData;
    private String cif;
    private boolean isCreditOpen = false;
    private String verifyScene;

    @Override
    public String getSignature() {
        return "Y5b9L7KCtVLGS2MoCmqcPtxHZUzhuyId363JHQD/IOaJ9fmE6/4Uoq0Qdo8xZEj2QXdiHjry+KHOEjeKG7WXRg==";
    }

    @Override
    public Class<?> getHandlerClass() {
        return getClass();
    }

    @Override
    public String getMethodName() {
        return NAME;
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public void exec() {
        super.exec();
        if (jsBean() != null && jsBean().argsJson != null
                && jsHost() != null && jsHost().getActivity() != null) {
            JSONObject json = jsBean().argsJson;
            String action = json.optString(KEY_ACTION);
            JSONObject data = json.optJSONObject(KEY_DATA);
            logSC(HybridReportConstants.MTPAY_BRIDGE_PARAMETER_VERIFICATION, null);
            handleActionAndData(action, data);
        } else {
            jsCallbackError(this, JS_CALLBACK_ILLEGAL_STATE, MapBuilder.builder("step", "exec").build("reason",
                    "jsHost is null"));
        }
    }

    public Activity getActivity() {
        return jsHost().getActivity();
    }

    /**
     * 美团支付切卡弹窗开通月付（半页新流程），结果处理
     */
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == ICashierSelectHandler.HYBRID_CREDIT_OPEN_SELECTDIALOG) {
            HalfPageFragment.onActivityResult(resultCode, data, new HalfPageFragment.HalfPageListener() {
                @Override
                public void onLoadFail(int errorCode, String errorMessage) {
                    if (jsHost() != null && jsHost().getActivity() != null) {
                        ToastUtils.showSnackToast(jsHost().getActivity(), jsHost().getActivity().getString(R.string.mpay__open_credit_pay_error));
                    }
                    CashierStaticsUtils.reportSystemCheck("b_pay_credit_open_back_to_cashier_sc",
                            new AnalyseUtils.MapBuilder().add("errorCode", errorCode)
                                    .add("errorMessage", errorMessage).add("scene", 3)
                                    .add("url", selectHandler != null ? selectHandler.getCreditOpenUrl() : "")
                                    .build(), getUniqueId());
                    jsCallbackPaySucc(ICashierJSHandler.this, ICashierPayerHandler.VALUE_STATUS_PAY_CANCEL, 0, "", "", isCreditOpen);
                }

                @Override
                public void onSuccess(@Nullable String result) {
                    dealCreditPayOpenResult(result);
                    CashierStaticsUtils.reportSystemCheck("b_pay_credit_open_back_to_cashier_sc",
                            new AnalyseUtils.MapBuilder().add("result", result).add("scene", 3)
                                    .add("url", selectHandler != null ? selectHandler.getCreditOpenUrl() : "")
                                    .build(), getUniqueId());
                }
            });
        } else {
            ICashierPayerHandler.handlePayResultCode(this, requestCode, resultCode, data, isCreditOpen);
        }

    }

    public String getUniqueId() {
        Activity activity = getActivity();
        if (activity instanceof PayBaseActivity && TextUtils.isEmpty(((PayBaseActivity) activity).getUniqueId())) {
            return ((PayBaseActivity) activity).getUniqueId();
        }
        return "";
    }

    private void dealCreditPayOpenResult(String result) {
        if (!TextUtils.isEmpty(result)) {
            try {
                JSONObject jsonObject = new JSONObject(result);
                int openResult = jsonObject.getInt("fd_maidan_opened_status");
                if (openResult == CreditOpenUtils.OPEN_SUCCESS) {
                    // 发起支付网络请求之前，生成月付开通已成功的参数。请求时带上。
                    CreditOpenUtils.setCreditOpenVerifyScene();
                    //直接发起支付
                    isCreditOpen = true;
                    if (selectHandler != null) {
                        selectHandler.readyForGoHelloPay();
                    }
                } else if (openResult == CreditOpenUtils.OPEN_REFUSE) {
                    //失败弹toast
                    ToastUtils.showSnackToast(jsHost().getActivity(), jsHost().getActivity().getString(R.string.mpay__open_credit_pay_fail));
                    jsCallbackPaySucc(ICashierJSHandler.this, ICashierPayerHandler.VALUE_STATUS_PAY_CANCEL, 0, "", "", isCreditOpen);
                } else {
                    //do nothing
                    jsCallbackPaySucc(ICashierJSHandler.this, ICashierPayerHandler.VALUE_STATUS_PAY_CANCEL, 0, "", "", isCreditOpen);
                }
            } catch (JSONException e) {
                LoganUtils.logError("ICashierJSHandler_dealCreditPayOpenResult", e.getMessage());
                jsCallbackPaySucc(ICashierJSHandler.this, ICashierPayerHandler.VALUE_STATUS_PAY_CANCEL, 0, "", "", isCreditOpen);
            }
        } else {
            jsCallbackPaySucc(ICashierJSHandler.this, ICashierPayerHandler.VALUE_STATUS_PAY_CANCEL, 0, "", "", isCreditOpen);
        }
    }

    @Override
    public String getExtraStatics() {
        try {
            if (jsBean() == null) {
                return "";
            }
            JSONObject json = jsBean().argsJson;
            if (json == null) {
                return "";
            }
            JSONObject data = json.optJSONObject(ICashierJSHandler.KEY_DATA);
            if (data == null) {
                return "";
            }
            return data.optString(ICashierJSHandler.KEY_DATA_EXTRA_STATICS);
        } catch (Exception e) {
            // TODO 埋点
            return "";
        }
    }

    private void handleActionAndData(String action, JSONObject data) {
        if (data == null || !Strings.isLegal(action,
                KEY_ACTION_START_SELECT_PAYMENT,
                KEY_ACTION_START_SELECT_BANK,
                KEY_ACTION_START_GO_HELLO_PAY)) {
            jsCallbackError(this, JS_CALLBACK_PARAMS_CHECK_ERROR + "action不符合要求",
                    MapBuilder.builder("step", "handleActionAndData").add("action", action)
                            .build("reason", "action isn't at scope"));
            return;
        }
        try {
            tradeNo = data.optString(KEY_DATA_TRADE_NO);
            payToken = data.optString(KEY_DATA_PAY_TOKEN);
            merchantNo = data.optString(KEY_MERCHANT_NO);
            openSource = data.optString(KEY_DATA_OPEN_SOURCE);
        } catch (Exception e) {
            jsCallbackError(this, JS_CALLBACK_PARAMS_PARSER_ERROR + "tradeNo或payToken异常",
                    MapBuilder.builder("step", "handleActionAndData")
                            .add("trade_no", tradeNo).add("pay_token", payToken).build("reason",
                            "catch exception" + e.getMessage()));
            return;
        }
        if (!Strings.isNonEmpty(tradeNo, payToken)) {
            jsCallbackError(this, JS_CALLBACK_PARAMS_CHECK_ERROR + "tradeNo或payToken为空",
                    MapBuilder.builder("step", "handleActionAndData")
                            .add("trade_no", tradeNo).add("pay_token", payToken).build("reason",
                            "tradeNo or payToken is empty"));
            return;
        }
        logSC(HybridReportConstants.MTPAY_BRIDGE_ACTION_DETERMINATION, new AnalyseUtils.MapBuilder().add("action", action).build());

        // https://ones.sankuai.com/ones/product/9177/workItem/requirement/detail/8513092
        // 美团支付桥Entry埋点
        String entry = "";
        if (data.has(KEY_DATA_ENTRY)) {
            String entryTemp = data.optString(KEY_DATA_ENTRY);
            if (!TextUtils.isEmpty(entryTemp)) {
                entry = entryTemp;
            }
        }

        // 通用字段：
        extraData = data.optString(KEY_DATA_EXTRA_DATA);
        cif = data.optString(KEY_DATA_CIF);

        // 月付开通新流程特殊参数。服务端可据此避免开通验证和支付验证的重复验证。
        verifyScene = data.optString(KEY_NEW_CREDITPAY_OPEN_VERIFY_SCENE);
        if (TextUtils.equals(KEY_ACTION_START_SELECT_PAYMENT, action)) {
            if (TextUtils.isEmpty(entry)) {
                entry = WalletPayManager.ENTRY_FROM_CASHIER_CHANGE_PAYTYPE;
            }
            WalletPayManager.getInstance().setEntry(getActivity(), entry);

            logSC(HybridReportConstants.MTPAY_BRIDGE_MORE_PAY_TYPE_POP_WINDOW, null);
            handleSelectPayment(data);
        } else if (TextUtils.equals(KEY_ACTION_START_SELECT_BANK, action)) {
            if (TextUtils.isEmpty(entry)) {
                entry = WalletPayManager.ENTRY_FROM_CASHIER_CHANGE_PAYTYPE;
            }
            WalletPayManager.getInstance().setEntry(getActivity(), entry);

            logSC(HybridReportConstants.MTPAY_BRIDGE_CUT_POP_WINDOW, null);
            handleSelectBank(data);
        } else if (TextUtils.equals(KEY_ACTION_START_GO_HELLO_PAY, action)) {
            if (TextUtils.isEmpty(entry)) {
                entry = WalletPayManager.ENTRY_FROM_CASHIER_BUTTON;
            }
            WalletPayManager.getInstance().setEntry(getActivity(), entry);
            WalletPayManager.getInstance().setOpenSource(getActivity(), openSource);

            logSC(HybridReportConstants.MTPAY_BRIDGE_GOHELLOPAY, null);
            handleGoHelloPay(data);
        } else {
            jsCallbackError(this, JS_CALLBACK_ILLEGAL_STATE + "action类型异常",
                    MapBuilder.builder("step", "handleActionAndData").build("action", action));
        }
    }

    private void handleSelectPayment(JSONObject data) {
        if (data == null) {
            jsCallbackError(this, JS_CALLBACK_PARAMS_CHECK_ERROR + "更多支付方式data为空",
                    MapBuilder.builder("step", "handleSelectPayment").build("reason", "data is null"));
            return;
        }
        WalletPayment walletPayment;
        int selectPaymentIndex;
        try {
            selectPaymentIndex = data.optInt(KEY_DATA_SELECT_PAYMENT_INDEX);
            walletPayment = getWalletPayment(data);
        } catch (Exception e) {
            jsCallbackError(this, JS_CALLBACK_PARAMS_PARSER_ERROR + "更多支付方式解析异常",
                    MapBuilder.builder("step", "handleSelectPayment").build("reason", "catch exception" + e.getMessage()));
            return;
        }
        if (walletPayment == null || walletPayment.getWalletPaymentListPage() == null) {
            jsCallbackError(this, JS_CALLBACK_PARAMS_CHECK_ERROR + "更多支付方式data内容异常",
                    MapBuilder.builder("step", "handleSelectPayment").build("reason", "walletPayment or ListPage is null"));
            return;
        }
        ICashierSelectHandler.startSelectPayment(walletPayment, this, walletPayment.getWalletPaymentListPage(), selectPaymentIndex);
    }

    private void handleSelectBank(JSONObject data) {
        if (data == null) {
            jsCallbackError(this, JS_CALLBACK_PARAMS_CHECK_ERROR + "更多银行卡data异常",
                    MapBuilder.builder("step", "handleSelectBank").build("reason", "data is null"));
            return;
        }
        int selectBankIndex;
        MTPayment checkedMTPayment;
        WalletPayment walletPayment;
        try {
            checkedMTPayment = GsonProvider.getInstance().fromJson(data.optJSONObject(KEY_DATA_SELECT_MT_PAYMENT).toString(), MTPayment.class);
            selectBankIndex = data.optInt(KEY_DATA_SELECT_BANK_INDEX);
            walletPayment = getWalletPayment(data);
        } catch (Exception e) {
            jsCallbackError(this, JS_CALLBACK_PARAMS_PARSER_ERROR + "更多银行卡解析异常",
                    MapBuilder.builder("step", "handleSelectBank").build("reason", "catch exception" + e.getMessage()));
            return;
        }
        ICashierSelectHandler.startSelectBank(walletPayment, this, checkedMTPayment, selectBankIndex);
    }

    private void handleGoHelloPay(JSONObject data) {
        if (data == null) {
            jsCallbackError(this, JS_CALLBACK_PARAMS_CHECK_ERROR + "请求GoHelloPay时data为空",
                    MapBuilder.builder("step", "handleGoHelloPay").build("reason", "data is null"));
            return;
        }
        WalletPayment walletPayment;
        MTPayment mtPayment;
        CashierPopWindowBean popWindow;
        JSONObject transmissionParam;
        String cashierType;
        try {
            mtPayment = GsonProvider.getInstance().fromJson(data.optJSONObject(KEY_DATA_SELECT_MT_PAYMENT).toString(), MTPayment.class);
            walletPayment = getWalletPayment(data);
            popWindow = getPopWindowBean(data);
            transmissionParam = data.optJSONObject(KEY_TRANSMISSION_PARAM);
            cashierType = data.optString(KEY_CASHIER_TYPE);
        } catch (Exception e) {
            jsCallbackError(this, JS_CALLBACK_PARAMS_PARSER_ERROR + "GoHelloPay解析异常",
                    MapBuilder.builder("step", "handleGoHelloPay").build("reason", "catch exception" + e.getMessage()));
            return;
        }
        ICashierPayerHandler.startGoHelloPay(walletPayment, this, mtPayment, extraData, popWindow, transmissionParam, cashierType);
    }

    private WalletPayment getWalletPayment(JSONObject data) {
        JSONObject cashierData;
        if ((cashierData = data.optJSONObject(KEY_DATA_CASHIER_PAYMENT)) != null) {
            // CashierPayment是WalletPayment的子类,收银台解析的时候以CashierPayment为主
            return GsonProvider.getInstance().fromJson(cashierData.toString(), CashierPayment.class);
        }
        return null;
    }

    private CashierPopWindowBean getPopWindowBean(JSONObject data) {
        if (data == null) {
            return null;
        }
        JSONObject popWindowJson = data.optJSONObject(KEY_DATA_POP_WINDOW);
        if (popWindowJson == null) {
            return null;
        }
        return GsonProvider.getInstance().fromJson(popWindowJson.toString(), CashierPopWindowBean.class);
    }

    @Override
    public String getTradeNo() {
        return tradeNo;
    }

    String getPayToken() {
        return payToken;
    }

    public String getVerifyScene() {
        return verifyScene;
    }

    public void setPayerHandler(ICashierPayerHandler payerHandler) {
        this.payerHandler = payerHandler;
    }

    public void setSelectHandler(ICashierSelectHandler selectHandler) {
        this.selectHandler = selectHandler;
    }

    ICashierPayerHandler getPayerHandler() {
        return payerHandler;
    }

    ICashierSelectHandler getSelectHandler() {
        return selectHandler;
    }

    @Override
    public String getExtraData() {
        return extraData;
    }

    @Override
    public String getMerchantNo() {
        return merchantNo;
    }

    @SuppressWarnings("LoopDetector")
    @SuppressLint("LoopDetector")
    static void jsCallbackError(ICashierJSHandler jsHandler, String msg, Map<String, Object> lab) {
        if (jsHandler == null) {
            return;
        }
        jsHandler.jsCallbackError(CODE_ERROR, msg);
        if (lab == null) {
            lab = new HashMap<>();
        }
        lab.put("error", msg);
        logSC(jsHandler, HybridReportConstants.MTPAY_BRIDGE_RETURN_RESULT_TO_ICASHIER, lab);
    }

    static void jsCallbackPaySucc(ICashierJSHandler jsHandler, String status, boolean isCreditOpen) {
        jsCallbackPaySucc(jsHandler, status, 0, null, null, isCreditOpen);
    }

    static void jsCallbackPaySucc(ICashierJSHandler jsHandler, String status, boolean isCreditOpen, JSONObject promotion) {
        jsCallbackPaySucc(jsHandler, status, 0, null, null, isCreditOpen, promotion);
    }

    static void jsCallbackPaySucc(ICashierJSHandler jsHandler, String status, int code, String msg, boolean isCreditOpen) {
        jsCallbackPaySucc(jsHandler, status, code, msg, null, isCreditOpen);
    }

    static void jsCallbackPaySucc(ICashierJSHandler jsHandler, String status, int code, String msg, String extra,
                                  boolean isCreditOpen, JSONObject promotion) {
        JSONObject payResultJson = new JSONObject();
        try {
            JSONObject dataJson = new JSONObject();
            if (code != 0) {
                dataJson.put(ICashierPayerHandler.KEY_DATA_CODE, code);
            }
            if (!TextUtils.isEmpty(msg)) {
                dataJson.put(ICashierPayerHandler.KEY_DATA_MESSAGE, msg);
            }
            if (!TextUtils.isEmpty(extra)) {
                dataJson.put(ICashierPayerHandler.KEY_DATA_EXTRA, extra);
            }
            if (isCreditOpen) {
                dataJson.put(ICashierPayerHandler.KEY_CREDIT_OPEN, "success");
            }
            if (dataJson.length() != 0) {
                payResultJson.put(KEY_DATA, dataJson);
            }
            payResultJson.put(KEY_STATUS, status);
            if (promotion != null) {
                payResultJson.put(KEY_PROMOTION, promotion);
            }
            if (TextUtils.equals(status, ICashierPayerHandler.VALUE_STATUS_PAY_SUCCESS)) {
                logSC(jsHandler, HybridReportConstants.MTPAY_BRIDGE_RETURN_RESULT_SUCC, new AnalyseUtils.MapBuilder().add("payResult", status).build());
                CatUtils.logRate(CashierCatConstants.BRIDGE_MT_PAY_CALLBACK, CashierCatConstants.BRIDGE_MT_PAY_CALLBACK_SUCC_CODE);
            } else {
                logSC(jsHandler, HybridReportConstants.MTPAY_BRIDGE_RETURN_RESULT_FAIL, new AnalyseUtils.MapBuilder().add("payResult", status).build());
                CatUtils.logRate(CashierCatConstants.BRIDGE_MT_PAY_CALLBACK, CashierCatConstants.BRIDGE_MT_PAY_CALLBACK_FAIL_CODE);
            }
            jsCallbackSucc(jsHandler, payResultJson);
        } catch (JSONException e) {
            jsCallbackError(jsHandler, JS_CALLBACK_PARAMS_PARSER_ERROR + " exception",
                    MapBuilder.builder("step", "jsCallbackPaySucc").build("reason", "catch exception" + e.getMessage()));
        }
    }

    static void jsCallbackPaySucc(ICashierJSHandler jsHandler, String status, int code, String msg, String extra, boolean isCreditOpen) {
        jsCallbackPaySucc(jsHandler, status, code, msg, extra, isCreditOpen, null);
    }

    static void jsCallbackNotPaySucc(ICashierJSHandler jsHandler, String status, Object data) {
        JSONObject payResultJson = new JSONObject();
        try {
            if (data instanceof JSONObject) {
                payResultJson.put(KEY_DATA, data);
            } else if (data != null) {
                payResultJson.put(KEY_DATA, new JSONObject(GsonProvider.getInstance().toJson(data)));
            }
            payResultJson.put(KEY_STATUS, status);
            jsCallbackSucc(jsHandler, payResultJson);
        } catch (JSONException e) {
            jsCallbackError(jsHandler, JS_CALLBACK_PARAMS_PARSER_ERROR + " exception",
                    MapBuilder.builder("step", "jsCallbackNotPaySucc").build("reason", "catch exception:" + e.getMessage()));
        }
    }

    private static void jsCallbackSucc(ICashierJSHandler jsHandler, JSONObject result) {
        if (jsHandler == null) {
            return;
        }
        try {
            JSONObject resultJson = new JSONObject().put(KEY_PAY_RESULT, result);
            logSC(jsHandler, HybridReportConstants.MTPAY_BRIDGE_RETURN_RESULT_TO_ICASHIER, new AnalyseUtils.MapBuilder().add("result", resultJson).build());
            jsHandler.jsCallback(resultJson);
        } catch (JSONException e) {
            jsCallbackError(jsHandler, JS_CALLBACK_PARAMS_PARSER_ERROR + " exception",
                    MapBuilder.builder("step", "jsCallbackSucc").build("reason", "catch exception: " + e.getMessage()));
        }
    }

    public String getDowngradeErrorInfo() {
        String downgradeErrorInfo = String.valueOf(TunnelParamManager.getInstance().getParam(getNeoCompat(),
                "ext_param"));
        return TextUtils.isEmpty(downgradeErrorInfo) ? "" : downgradeErrorInfo;
    }

    @Override
    public HashMap<String, String> getExtendTransmissionParams() {
        HashMap<String, String> extendTransmissionParams = new HashMap<>();
        if (!TextUtils.isEmpty(cif) && !TextUtils.equals("null", cif.toLowerCase())) {
            extendTransmissionParams.put("cif", cif);
        }
        return extendTransmissionParams;
    }
}
