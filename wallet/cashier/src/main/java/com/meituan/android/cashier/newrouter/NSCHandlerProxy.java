package com.meituan.android.cashier.newrouter;

import android.support.v4.app.Fragment;

import com.meituan.android.cashier.model.bean.Cashier;
import com.meituan.android.cashier.model.bean.CashierPopWindowBean;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.payrouter.remake.router.context.AdapterContext;

import java.util.HashMap;

public interface NSCHandlerProxy extends IRequestCallback {
    Cashier getCashier();

    String getAppId();

    String getMchId();

    String getGuidePlanInfos();

    String getExtraData();

    String getExtraStatics();

    String getGuideRequestNo();

    String getDowngradeErrorInfo();

    HashMap<String, String> getExtendTransmissionParams();

    void queryThirdPayOrder();

    void showPopWindow(CashierPopWindowBean cashierPopWindowBean, Fragment fragment);

    void setPayType(String payType);

    void onOrderTimeout();

    boolean canSendRequest();

    void processSuspendPaying();

    void refreshWhenBackFromMTPay(boolean fromMTPayNeedRefresh);

    void setFinalFeeText(String finalFeeText);

    AdapterContext adapterContext();
}
