package com.meituan.android.cashier.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.Display;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;

import com.meituan.android.cashier.R;
import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.common.CashierCatConstants;;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.model.bean.Cashier;
import com.meituan.android.cashier.model.bean.NoPswGuide;
import com.meituan.android.cashier.util.CashierSLAMonitor;
import com.meituan.android.paybase.common.activity.PayBaseActivity;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.common.analyse.cat.CatConstants;
import com.meituan.android.paybase.common.analyse.cat.CatUtils;
import com.meituan.android.paybase.dialog.BaseDialog;
import com.meituan.android.paybase.utils.UriUtils;

/**
 * Created by ljj
 * Date:17/3/15
 * Time:下午8:08
 */

public class AutomaticPayGuideDialog extends BaseDialog {
    private NoPswGuide noPswGuide;
    private TextView title;
    private TextView description;
    private TextView agreementTip;
    private TextView agreementName;
    private TextView cancel;
    private Button openBtn;
    private Cashier cashier;
    private AutomaticPayGuideDialog.OnClickGuideButtonListener onClickGuideButton;

    public AutomaticPayGuideDialog(Context context) {
        super(context, R.style.mpay__transparent_dialog);
    }

    public AutomaticPayGuideDialog(Context context, Cashier cashier, AutomaticPayGuideDialog.OnClickGuideButtonListener onclickGuideButton) {
        super(context, R.style.mpay__TransparentDialog);
        this.noPswGuide = cashier.getNoPswGuide();
        this.cashier = cashier;
        this.onClickGuideButton = onclickGuideButton;
        @SuppressLint("InflateParams")
        View view = LayoutInflater.from(getContext()).inflate(R.layout.cashier__no_psw_guide_dialog, null);
        setContentView(view, getViewLayoutParam());
        initView();
    }


    public void setOnClickGuideButton(OnClickGuideButtonListener onClickGuideButton) {
        this.onClickGuideButton = onClickGuideButton;
    }

    private ViewGroup.LayoutParams getViewLayoutParam() {
        WindowManager windowManager = getOwnerActivity().getWindowManager();
        Display display = windowManager.getDefaultDisplay();
        int width = (int) (display.getWidth() * 0.833);
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(width, ViewGroup.LayoutParams.WRAP_CONTENT);
        return params;
    }

    private void initView() {
        if (noPswGuide == null) {
            return;
        }
        title = (TextView) findViewById(R.id.guide_title);
        description = (TextView) findViewById(R.id.guide_description);
        agreementTip = (TextView) findViewById(R.id.guide_agreement_tip);
        agreementName = (TextView) findViewById(R.id.guide_agreement);
        cancel = (TextView) findViewById(R.id.guide_cancel);
        openBtn = (Button) findViewById(R.id.guide_open);
        if (!TextUtils.isEmpty(noPswGuide.getGuideTitle())) {
            title.setText(noPswGuide.getGuideTitle());
        }
        if (!TextUtils.isEmpty(noPswGuide.getDescription())) {
            description.setText(noPswGuide.getDescription());
        }
        if (!TextUtils.isEmpty(noPswGuide.getAgreeTip())) {
            agreementTip.setText(noPswGuide.getAgreeTip());
        }
        if (!TextUtils.isEmpty(noPswGuide.getAgreeName())) {
            agreementName.setText(noPswGuide.getAgreeName());
        }
        if (!TextUtils.isEmpty(noPswGuide.getAgreementUrl())) {
            agreementName.setOnClickListener(v -> UriUtils.open(getContext(), noPswGuide.getAgreementUrl()));
        } else {
            CatUtils.logError("urlIsNull", "打车代扣协议链接为空");
        }
        if (!TextUtils.isEmpty(noPswGuide.getOpenButton())) {
            openBtn.setText(noPswGuide.getOpenButton());
            openBtn.setOnClickListener(v -> {
                dismiss();
                if (onClickGuideButton != null) {
                    onClickGuideButton.onClickGuideOpen(noPswGuide.getSubmitUrl());
                    CatUtils.logRate(CashierCatConstants.ACTION_DISPATCHER_CASHIER,
                            CatConstants.CODE_DEFAULT_OK);
                }
                AnalyseUtils.techMis("b_oysht4uc", null);
            });
        }

        if (!TextUtils.isEmpty(noPswGuide.getCancleButton())) {
            cancel.setText(noPswGuide.getCancleButton());
            cancel.setOnClickListener(v -> {
                dismiss();
                if (onClickGuideButton != null) {
                    if (getOwnerActivity() instanceof MTCashierActivity) {
                        CashierStaticsUtils.logCustom("pop_cashier_cancel", null, null, getUniqueId());
                        CashierSLAMonitor.reportStandardCashierRepeat(getUniqueId());
                    }
                    onClickGuideButton.onClickGuideCancel(cashier);
                }
                AnalyseUtils.techMis("b_efw02ysi", null);
            });
        }
        setCanceledOnTouchOutside(false);
    }

    public interface OnClickGuideButtonListener {
        void onClickGuideOpen(String url);

        void onClickGuideCancel(Cashier cashier);
    }
}

