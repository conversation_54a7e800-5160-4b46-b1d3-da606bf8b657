package com.meituan.android.cashier.mtpay;

import android.text.TextUtils;

import com.meituan.android.cashier.bean.CashierParams;
import com.meituan.android.cashier.bean.ClientRouterInfoBean;
import com.meituan.android.cashier.common.CashierTypeConstant;
import com.meituan.android.cashier.common.HybridPrePosedMTCashierHornService;
import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.paybase.utils.LoganUtils;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

class HybridPrePosedMTCashierConfigManager {
    private static final String HYBRID_PRE_POSED_MTCASHIER = CashierTypeConstant.CASHIERTYPE_MT_HYBRID_HALFPAGE_CASHIER;
    private ClientRouterInfoBean mClientRouterInfoBean;

    public ClientRouterInfoBean getHybridPrePosedMTCashierConfig(CashierParams cashierParams) {
        if (mClientRouterInfoBean != null) {
            return mClientRouterInfoBean;
        }
        if (cashierParams != null) {
            String config = cashierParams.getPreDispatcherCashierConfig(HYBRID_PRE_POSED_MTCASHIER);
            mClientRouterInfoBean = getClientRouterInfoBean(config);
        }
        if (mClientRouterInfoBean == null) {
            String config = HybridPrePosedMTCashierHornService.getInstance().getHybridPrePosedMTCashierConfig();
            if (TextUtils.isEmpty(config)) {
                return null;
            }
            mClientRouterInfoBean = getClientRouterInfoBean(config);
        }
        return mClientRouterInfoBean;
    }

    // 缓存是否可用
    private boolean cacheAvailable(ClientRouterInfoBean clientRouterInfoBean) {
        if (clientRouterInfoBean == null) {
            return false;
        }
        HashMap<String, Object> hashMap = clientRouterInfoBean.getCacheConfigurations();
        return getOrDefault(hashMap, "enable_cache", false);
    }

    private <T> T getOrDefault(Map<String, ? super T> map, String key, T defaultValue) {
        if (map == null) {
            return defaultValue;
        }
        if (map.containsKey(key)) {
            return (T) map.get(key);
        }
        return defaultValue;
    }

    private ClientRouterInfoBean getClientRouterInfoBean(String clientRouterInfoStr) {
        if (TextUtils.isEmpty(clientRouterInfoStr)) {
            return null;
        }
        try {
            JSONObject jsonObject = new JSONObject(clientRouterInfoStr);
            if (jsonObject.length() == 0) {
                return null;
            }
            ClientRouterInfoBean clientRouterInfoBean;
            clientRouterInfoBean = GsonProvider.getInstance().fromJson(clientRouterInfoStr, ClientRouterInfoBean.class);
            return clientRouterInfoBean;
        } catch (Exception e) {
            LoganUtils.logError("HybridPrePosedMTCashierConfigManager_getClientRouterInfoBean", e.getMessage());
        }
        return null;
    }
}
