package com.meituan.android.cashier.model.bean;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;

@JsonBean
public class RetainWindow implements Serializable {
    private static final long serialVersionUID = -2853684455282222857L;

    private static final String RETAIN_TYPE_ALIPAY = "alipay";
    private static final String RETAIN_TYPE_BASIC = "basic";
    private static final String RETAIN_TYPE_CARDPAY = "cardpay";
    private static final String RETAIN_TYPE_BANKSELECTPAY = "bankselectpay";

    private String title;

    private String detail;

    @SerializedName("rbtn")
    private String rightButton;

    @SerializedName("lbtn")
    private String leftButton;

    private SubmitData submitData;

    private String retainType;

    private boolean isNewRetainWindow;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getRightButton() {
        return rightButton;
    }

    public void setRightButton(String rightButton) {
        this.rightButton = rightButton;
    }

    public String getLeftButton() {
        return leftButton;
    }

    public void setLeftButton(String leftButton) {
        this.leftButton = leftButton;
    }

    public SubmitData getSubmitData() {
        return submitData;
    }

    public void setSubmitData(SubmitData submitData) {
        this.submitData = submitData;
    }

    public String getRetainType() {
        return retainType;
    }

    public void setRetainType(String retainType) {
        this.retainType = retainType;
    }

    public boolean isNewRetainWindow() {
        return isNewRetainWindow;
    }

    public void setNewRetainWindow(boolean newRetainWindow) {
        isNewRetainWindow = newRetainWindow;
    }

    public boolean isAlipayRetainType() {
        return TextUtils.equals(getRetainType(), RETAIN_TYPE_ALIPAY);
    }

    public boolean isBankselectpayRetainType() {
        return TextUtils.equals(getRetainType(), RETAIN_TYPE_BANKSELECTPAY);
    }

    public boolean isCardpayRetainType() {
        return TextUtils.equals(getRetainType(), RETAIN_TYPE_CARDPAY);
    }

    public boolean isDefaultRetainType() {
        return TextUtils.equals(getRetainType(), RETAIN_TYPE_BASIC);
    }

    /**
     * 埋点使用的数据，如果为1则是支付宝挽留弹框
     *
     * @return
     */
    public String getStaticsRetainType() {
        if (isDefaultRetainType()) {
            return RETAIN_TYPE_BASIC;
        } else if (isAlipayRetainType()) {
            return RETAIN_TYPE_ALIPAY;
        } else if (isCardpayRetainType()) {
            return RETAIN_TYPE_CARDPAY;
        } else if (isBankselectpayRetainType()) {
            return RETAIN_TYPE_BANKSELECTPAY;
        }
        return "";
    }

    @Override
    public String toString() {
        return new Gson().toJson(this);
    }

    public static boolean isValid(RetainWindow retainWindow) {
        return retainWindow != null
                && !TextUtils.isEmpty(retainWindow.getTitle())
                && !TextUtils.isEmpty(retainWindow.getDetail())
                && !TextUtils.isEmpty(retainWindow.getLeftButton())
                && !TextUtils.isEmpty(retainWindow.getRightButton());
    }
}
