package com.meituan.android.cashier.bridge.icashier;

import android.app.Activity;
import android.text.TextUtils;

import com.meituan.android.cashier.model.params.PayParams;
import com.meituan.android.cashier.retrofit.CashierRequestUtils;
import com.meituan.android.neohybrid.neo.report.NeoAnalyseUtils;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.pay.common.payment.data.IBankcardData;
import com.meituan.android.pay.desk.pack.WalletPayManager;
import com.meituan.android.pay.desk.payment.bean.standarddesk.WalletPayment;

import org.json.JSONObject;

/**
 * author:  kkli
 * date:    2019-09-05
 * description:
 */
public class ICashierPayParams {
    static final String CASHIER_TYPE_WALLET = "wallet";

    static final int PARAM_MONEY_CHANGED_NOT = 0;
    static final int PARAM_MONEY_CHANGED = 1;
    static final int PARAM_FROM_SELECT_BANKCARD_NOT = 0;
    static final int PARAM_FROM_SELECT_BANKCARD = 1;
    private PayParams payParams;

    private MTPayment mtPayment;
    private IBankcardData payment;
    private ExtParams extParams;
    private WalletPayment walletPayment;
    private Activity activity;

    private ICashierPayParams(Activity activity, WalletPayment walletPayment, MTPayment mtPayment, IBankcardData payment, ExtParams extParams) {
        this.activity = activity;
        this.payParams = new PayParams();
        this.mtPayment = mtPayment;
        this.payment = payment;
        this.extParams = extParams;
        this.walletPayment = walletPayment;
    }

    public static PayParams genPayParams(Activity activity, WalletPayment walletPayment, MTPayment mtPayment, IBankcardData payment, ExtParams extParams) {
        return new ICashierPayParams(activity, walletPayment, mtPayment, payment, extParams).genPayParams();
    }

    /**
     * 先享后付开通月付引导专用
     */
    public static PayParams genPayParamsForOpenCreditCard(String tradeNo, String payToken) {
        PayParams payParams = new PayParams();
        payParams.tradeNo = tradeNo;
        payParams.payToken = payToken;
        payParams.cashierType = ICashierPayParams.CASHIER_TYPE_WALLET;
        return payParams;
    }

    public static void appendGuidePlanInfos(PayParams payParams, String extraData) {
        if (payParams == null || TextUtils.isEmpty(extraData)) {
            return;
        }
        String guidePlanInfoS = null;
        try {
            guidePlanInfoS = new JSONObject(extraData).optString(ICashierPayerHandler.KEY_GUIDE_PLAN_INFO);
        } catch (Exception e) {
            NeoAnalyseUtils.logException(e, "ICashierPayParams_appendGuidePlanInfos", null);
        }
        if (TextUtils.isEmpty(guidePlanInfoS)) {
            return;
        }
        CashierRequestUtils.appendGuidePlans(payParams, guidePlanInfoS);
    }

    private PayParams genPayParams() {
        if (mtPayment == null) {
            return null;
        }
        fillPayParamsExt(payParams);
        fillPayParamsIfFromSelect(payParams, extParams.fromSelectBankCard == PARAM_FROM_SELECT_BANKCARD);
        return payParams;
    }

    private void fillPayParamsExt(PayParams payParams) {
        payParams.tradeNo = extParams.getTradeNo();
        payParams.payToken = extParams.getPayToken();
        payParams.moneyChanged = extParams.getMoneyChanged();
        payParams.fromSelectBankCard = extParams.getFromSelectBankCard();
        payParams.cashierType = extParams.getCashierType();
    }

    private void fillPayParamsIfFromSelect(PayParams payParams, boolean fromSelect) {
        if (fromSelect) {
            if (mtPayment != null) {
                payParams.walletPayParams = WalletPayManager.getInstance().appendRequestParams(activity, walletPayment, mtPayment, WalletPayManager.CASHIER_SELECT_BANK_DIALOG_PARAMS);
            }
            if (payment != null) {
                WalletPayManager.getInstance().updateBankcardParams(activity, mtPayment, payment, payParams.walletPayParams);
            }
        } else {
            payParams.walletPayParams = WalletPayManager.getInstance().appendRequestParams(activity, walletPayment, mtPayment, WalletPayManager.CASHIER_PARAMS);
        }
    }

    public static class ExtParams {
        private String tradeNo;
        private String payToken;
        private String cashierType;
        private int moneyChanged;
        private int fromSelectBankCard;

        public ExtParams(String tradeNo, String payToken, String cashierType, int moneyChanged, int fromSelectBankCard) {
            this.tradeNo = tradeNo;
            this.payToken = payToken;
            this.cashierType = cashierType;
            this.moneyChanged = moneyChanged;
            this.fromSelectBankCard = fromSelectBankCard;
        }

        public static ExtParams init(String tradeNo, String payToken, String cashierType, int moneyChanged, int fromSelectBankCard) {
            return new ExtParams(tradeNo, payToken, cashierType, moneyChanged, fromSelectBankCard);
        }

        public String getTradeNo() {
            return tradeNo;
        }

        public void setTradeNo(String tradeNo) {
            this.tradeNo = tradeNo;
        }

        public String getPayToken() {
            return payToken;
        }

        public void setPayToken(String payToken) {
            this.payToken = payToken;
        }

        public String getCashierType() {
            return cashierType;
        }

        public void setCashierType(String cashierType) {
            this.cashierType = cashierType;
        }

        public int getMoneyChanged() {
            return moneyChanged;
        }

        public void setMoneyChanged(int moneyChanged) {
            this.moneyChanged = moneyChanged;
        }

        public int getFromSelectBankCard() {
            return fromSelectBankCard;
        }

        public void setFromSelectBankCard(int fromSelectBankCard) {
            this.fromSelectBankCard = fromSelectBankCard;
        }
    }

}
