package com.meituan.android.cashier.dialogfragment;

import static com.meituan.android.cashier.NativeStandardCashierAdapter.REQ_TAG_PAY_ORDER;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import com.dianping.titans.utils.Constants;
import com.meituan.android.cashier.R;
import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.base.view.revision.CashierOrderInfoView;
import com.meituan.android.cashier.common.CashierCatConstants;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.model.PayErrorCode;
import com.meituan.android.cashier.model.bean.BankListPage;
import com.meituan.android.cashier.model.bean.Cashier;
import com.meituan.android.cashier.model.bean.DCEPPayment;
import com.meituan.android.cashier.model.bean.PayResult;
import com.meituan.android.cashier.model.params.PayParams;
import com.meituan.android.cashier.retrofit.CashierRequestService;
import com.meituan.android.cashier.retrofit.CashierRequestUtils;
import com.meituan.android.pay.common.promotion.bean.PaymentReduce;
import com.meituan.android.pay.common.promotion.bean.ReduceInfo;
import com.meituan.android.pay.desk.payment.discount.DiscountCashierUtils;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.common.analyse.cat.CatConstants;
import com.meituan.android.paybase.common.analyse.cat.CatUtils;
import com.meituan.android.paybase.common.fragment.MTPayBaseDialogFragment;
import com.meituan.android.paybase.config.PayBaseConfig;
import com.meituan.android.paybase.dialog.BaseDialog;
import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.retrofit.PayException;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.MTPayNeedToPersist;
import com.meituan.android.paybase.utils.StatisticsUtils;
import com.meituan.android.paybase.utils.SystemInfoUtils;
import com.meituan.android.paybase.utils.TransferUtils;
import com.meituan.android.paybase.utils.UriUtils;
import com.meituan.android.paybase.widgets.ProgressButton;
import com.meituan.android.paycommon.lib.assist.PayBaseAdapter;
import com.meituan.android.paycommon.lib.config.MTPayConfig;
import com.meituan.android.paycommon.lib.retrofit.PayRetrofit;
import com.meituan.android.paycommon.lib.utils.WebpImageLoader;
import com.meituan.android.paycommon.lib.widgets.NoDuplicateClickListener;
import com.meituan.android.paycommon.lib.widgets.PayLabelContainer;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class DCEPDialogFragment extends MTPayBaseDialogFragment implements IRequestCallback {

    public static final String TAG = "DCEPDialogFragment";
    private static final int REQUEST_CODE_DENTIFY_CENTER = 66;
    private static final int RESULT_CODE_VERIFY_CENTER_SUCCESS = 10;
    private static final String PARAM_BANK_LIST_PAGE = "bank_list_page";
    private static final String PARAM_PAY_PARAMS = "pay_params";
    private static final String PARAM_PAY_EXTEND_TRANSMISSION_PARAMS = "extend_transmission_params";
    private static final String PARAM_APP_ID = "app_id";
    private static final String PARAM_EXT_PARAM = "ext_param";
    private static final String PARAM_EXT_DATA = "ext_data";
    private static final String PARAM_EXT_STATICS = "ext_statics";
    private static final String PARAM_TRADENO = "tradeNo";
    private static final String PARAM_CASHIER = "cashier";
    private static final String ACTION_VERIFY = "verify";
    private static final String ACTION_SUCCESS = "success";
    private static final String TIP_PAY_ERROR = "支付异常，请稍后再试";
    private static final String TIP_PAY_FAILED = "支付失败，请稍后重试";

    private BankListPage mBankListPage;
    private PayParams mPayParams;
    @MTPayNeedToPersist
    private PayParams mDCEPPayParams;
    private String mAppId;
    private String mExtParam;
    private String mTradeNo;
    private String mExtraData;
    private String mExtraStatics;
    private HashMap<String, String> mExtendTransmissionParams;
    private IRequestCallback mRequestCallback;

    public static DCEPDialogFragment newInstance(String tradeNo, BankListPage bankListPage,
                                                 PayParams payParams, String appId, String extParam,
                                                 String extraStatics, String extraData, HashMap<String, String> extendTransmissionParams) {
        DCEPDialogFragment fragment = new DCEPDialogFragment();
        Bundle args = new Bundle();
        args.putString(PARAM_TRADENO, tradeNo);
        args.putSerializable(PARAM_BANK_LIST_PAGE, bankListPage);
        args.putSerializable(PARAM_PAY_PARAMS, payParams);
        args.putString(PARAM_APP_ID, appId);
        args.putString(PARAM_EXT_PARAM, extParam);
        args.putString(PARAM_EXT_DATA, extraData);
        args.putString(PARAM_EXT_STATICS, extraStatics);
        args.putSerializable(PARAM_PAY_EXTEND_TRANSMISSION_PARAMS, extendTransmissionParams);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        if (getParentFragment() != null && getParentFragment() instanceof IRequestCallback) {
            mRequestCallback = (IRequestCallback) getParentFragment();
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            Bundle bundle = getArguments();
            mTradeNo = bundle.getString(PARAM_TRADENO);
            mBankListPage = (BankListPage) bundle.getSerializable(PARAM_BANK_LIST_PAGE);
            mPayParams = (PayParams) bundle.getSerializable(PARAM_PAY_PARAMS);
            mAppId = bundle.getString(PARAM_APP_ID);
            mExtParam = bundle.getString(PARAM_EXT_PARAM);
            mExtraData = bundle.getString(PARAM_EXT_DATA);
            mExtraStatics = bundle.getString(PARAM_EXT_STATICS);
            mExtendTransmissionParams = (HashMap<String, String>) bundle.getSerializable(PARAM_PAY_EXTEND_TRANSMISSION_PARAMS);
        }
    }

    @Override
    public void onResume() {
        super.onResume();

        CashierStaticsUtils.techMis("b_pay_0m5b4vo6_sc", null, getUniqueId());
    }

    @Override
    protected String getTAG() {
        return TAG;
    }

    @Override
    protected BaseDialog createDialog(Bundle savedInstanceState) {
        setCancelable(false);
        return new DCEPDialog(getContext(), mBankListPage);
    }

    @Override
    public void onStart() {
        super.onStart();
        Dialog dialog = getDialog();
        if (dialog != null && dialog.getWindow() != null && getActivity() != null) {
            DisplayMetrics dm = new DisplayMetrics();
            getActivity().getWindowManager().getDefaultDisplay().getMetrics(dm);
            WindowManager.LayoutParams attributes = dialog.getWindow().getAttributes();
            attributes.gravity = Gravity.BOTTOM;
            attributes.width = dm.widthPixels;
            attributes.height = (int) (dm.heightPixels * 0.7);
            attributes.windowAnimations = R.style.paycommon__window_bottom_popup;
            dialog.getWindow().setAttributes(attributes);
        }
    }

    private void startDirectPay(DCEPPayment selectedPayment, String verifyToken, HashMap<String, String> extendTransmissionParams) {
        // DCEP的会请求两次/cashier/directpay接口，第二次请求时候selectedPayment为空，需要带上第一次请求时生成的营销参数
        // selectedPayment == null时需要确保mDCEPPayParams ！= null，否则会有异常场景crash
        if (selectedPayment != null || mDCEPPayParams == null) {
            mDCEPPayParams = genDCEPPayParams(selectedPayment);
        }
        HashMap<String, String> map = CashierRequestUtils
                .getDirectPayMap(mDCEPPayParams, SystemInfoUtils.getIMSI(getActivity()));
        if (!TextUtils.isEmpty(verifyToken)) {
            map.put("verify_token", verifyToken);
        }
        PayRetrofit.getInstance().create(CashierRequestService.class, this, REQ_TAG_PAY_ORDER)
                .startDirectPay(map, MTPayConfig.getProvider().getFingerprint(), mAppId,
                        mExtParam, "", "",
                        mExtraData, getExtDimStat(), extendTransmissionParams);
    }

    private PayParams genDCEPPayParams(DCEPPayment selectedPayment) {
        // 每次请求DirectPay时都初始化payParams参数。由于传递参数都是基础数据类型，所以浅拷贝可用。
        PayParams payParams = mPayParams == null ? new PayParams() : mPayParams.clone();
        if (selectedPayment != null) {
            if (selectedPayment.getCardInfo() != null && !TextUtils.isEmpty(selectedPayment.getCardInfo().getTokenId())) {
                payParams.tokenId = selectedPayment.getCardInfo().getTokenId();
            }
            if (selectedPayment.getPaymentReduce() != null) {
                ReduceInfo reduceInfo = selectedPayment.getPaymentReduce().getNoBalanceReduceInfo();
                if (reduceInfo != null) {
                    payParams.campaignId = reduceInfo.getCampaignId();
                    payParams.couponCode = reduceInfo.getCashTicketId();
                }
            }
        }
        return payParams;
    }

    private String getExtDimStat() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("outer_business_statics", mExtraStatics);
        } catch (Exception e) {
            LoganUtils.logError("DCEPDialogFragment_getExtDimStat", e.getMessage());
        }
        return jsonObject.toString();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_DENTIFY_CENTER) {
            if (resultCode == RESULT_CODE_VERIFY_CENTER_SUCCESS && data != null) {
                String result = data.getStringExtra(Constants.SET_RESULT_KEY);
                try {
                    JSONObject json = new JSONObject(result);
                    String verifyToken = json.getString("payToken");
                    if (TextUtils.isEmpty(verifyToken)) {
                        ToastUtils.showSnackToast(getDialog(), TIP_PAY_ERROR, "",
                                ToastUtils.ToastType.TOAST_TYPE_COMMON, false);

                        CashierStaticsUtils.techMis("b_pay_9ovxih44_sc", null, getUniqueId());
                    } else {
                        startDirectPay(null, verifyToken, mExtendTransmissionParams);

                        CashierStaticsUtils.techMis("b_pay_bfjxm2bl_sc", null, getUniqueId());
                    }
                } catch (JSONException e) {
                    ToastUtils.showSnackToast(getDialog(), TIP_PAY_ERROR, "",
                            ToastUtils.ToastType.TOAST_TYPE_COMMON, false);
                    LoganUtils.logError("DCEPDialogFragment_onActivityResult", e.getMessage());
                }
            } else if (resultCode == Activity.RESULT_CANCELED) {
                CashierStaticsUtils.techMis("b_pay_zpvcbxrf_sc", null, getUniqueId());
            } else {
                ToastUtils.showSnackToast(getDialog(), TIP_PAY_ERROR, "",
                        ToastUtils.ToastType.TOAST_TYPE_COMMON, false);
            }
        }
    }

    @Override
    public void onRequestSucc(int tag, Object obj) {
        if (tag == REQ_TAG_PAY_ORDER) {
            PayResult payResult = (PayResult) obj;
            if (TextUtils.equals(ACTION_VERIFY, payResult.getAction())) {
                if (!TextUtils.isEmpty(payResult.getVerifyUrl())) {
                    UriUtils.openForResult(this, payResult.getVerifyUrl(), REQUEST_CODE_DENTIFY_CENTER);
                    CatUtils.logRate(CashierCatConstants.ACTION_RESPONSE_DIRECTPAY, CatConstants.CODE_DEFAULT_OK);
                } else {
                    CatUtils.logRate(CashierCatConstants.ACTION_RESPONSE_DIRECTPAY, CatConstants.CODE_DEFAULT_ERROR);
                    ToastUtils.showSnackToast(getDialog(), TIP_PAY_ERROR, "",
                            ToastUtils.ToastType.TOAST_TYPE_COMMON, false);
                }
            } else if (TextUtils.equals(ACTION_SUCCESS, payResult.getAction())) {
                if (mRequestCallback != null) {
                    // 支付成功后，关闭掉当前Dialog，因为支付后弹框会展示在该dialog的后面
                    if (getDialog() instanceof DCEPDialog) {
                        ((DCEPDialog) getDialog()).stopLoading();
                    }
                    dismissAllowingStateLoss();
                    mRequestCallback.onRequestSucc(tag, obj);
                }
            } else {
                CatUtils.logRate(CashierCatConstants.ACTION_RESPONSE_DIRECTPAY, CatConstants.CODE_DEFAULT_ERROR);
                ToastUtils.showSnackToast(getDialog(), TIP_PAY_ERROR, "",
                        ToastUtils.ToastType.TOAST_TYPE_COMMON, false);
            }
        }
    }

    @Override
    public void onRequestException(int tag, Exception e) {
        if (mRequestCallback == null) {
            return;
        }
        boolean hasResolved = false;
        if (tag == REQ_TAG_PAY_ORDER) {
            if (e instanceof PayException) {
                PayException payException = (PayException) e;
                int errorCode = payException.getCode();
                if (errorCode != PayErrorCode.NEED_VERIFY_SMS_CODE
                        && errorCode != PayErrorCode.ALREADY_PAYED) {
                    int level = payException.getLevel();
                    if (level != PayException.RESP_LEVEL_2 && level != PayException.RESP_LEVEL_3) {
                        ToastUtils.showSnackToast(getDialog(), payException.getMessage(), payException.getErrorCodeStr(),
                                ToastUtils.ToastType.TOAST_TYPE_COMMON, false);
                        hasResolved = true;
                        // 添加directpay接口请求失败的埋点，避免DCEP弹toast场景缺失埋点
                        CashierStaticsUtils.reportSystemCheck("b_21iwgx7m", new AnalyseUtils.MapBuilder()
                                .add("code", "" + errorCode).add("message", e.getMessage())
                                .add("level", "" + level).build(), getUniqueId());
                        CatUtils.logRate(CashierCatConstants.ACTION_RESPONSE_DIRECTPAY, errorCode);
                    }
                }
            } else {
                ToastUtils.showSnackToast(getDialog(), TIP_PAY_FAILED, "",
                        ToastUtils.ToastType.TOAST_TYPE_COMMON, false);
                hasResolved = true;
                // 添加directpay接口请求失败的埋点，避免DCEP弹toast场景缺失埋点
                CashierStaticsUtils.reportSystemCheck("b_21iwgx7m", new AnalyseUtils.MapBuilder()
                        .add("message", TIP_PAY_FAILED).build(), getUniqueId());
                CatUtils.logRate(CashierCatConstants.ACTION_RESPONSE_DIRECTPAY, 0);
            }
        }
        if (!hasResolved) {
            mRequestCallback.onRequestException(tag, e);
        }
    }

    @Override
    public void onRequestFinal(int tag) {
        if (tag == REQ_TAG_PAY_ORDER) {
            if (mRequestCallback != null) {
                mRequestCallback.onRequestFinal(tag);
            }
            if (getDialog() instanceof DCEPDialog) {
                ((DCEPDialog) getDialog()).stopLoading();
            }
        }
    }

    @Override
    public void onRequestStart(int tag) {
        if (tag == REQ_TAG_PAY_ORDER) {
            if (getActivity() != null) {
                ((MTCashierActivity) getActivity()).setPromotion(null);
            }
            if (getDialog() instanceof DCEPDialog) {
                ((DCEPDialog) getDialog()).startLoading();
            }
        }
    }

    @Override
    public String getPageName() {
        return "c_pay_h7g2fc35";
    }

    @Override
    public HashMap<String, Object> getPageProperties() {
        HashMap<String, Object> properties = new HashMap<>();
        properties.put("tradeNo", mTradeNo);
        properties.put("nb_version", PayBaseConfig.getProvider().getPayVersion());
        return properties;
    }

    private class DCEPDialog extends BaseDialog {

        private BankListPage mBankListPage;
        private ArrayList<DCEPPayment> mPaymentList;
        private DCEPPayment mSelectedPayment;

        private ProgressButton mConfirmButton;
        private CashierOrderInfoView mOrderInfoView;

        DCEPDialog(Context context, BankListPage bankListPage) {
            super(context, R.style.cashier__dcep_transparent_dialog);
            mBankListPage = bankListPage;
            if (mBankListPage != null && !CollectionUtils.isEmpty(mBankListPage.getPaymentList())) {
                mPaymentList = new ArrayList<>(mBankListPage.getPaymentList());
                mSelectedPayment = getSelectedPayment(mPaymentList);
            }
            init();
        }

        private DCEPPayment getSelectedPayment(List<DCEPPayment> paymentList) {
            DCEPPayment selectedPayment = null;
            for (DCEPPayment payment : paymentList) {
                if (payment.isSelected()) {
                    if (selectedPayment == null) {
                        selectedPayment = payment;
                    } else {
                        payment.setSelected(false);
                    }
                }
            }
            if (selectedPayment == null) {
                DCEPPayment payment = paymentList.get(0);
                selectedPayment = payment;
                payment.setSelected(true);
            }
            return selectedPayment;
        }

        private void init() {
            setCanceledOnTouchOutside(false);
            setContentView(R.layout.cashier__dialog_dcep_pay);
            if (mBankListPage == null) {
                return;
            }

            Cashier cashier = new Cashier();
            cashier.setTotalFee(mBankListPage.getTotalFee());
            mOrderInfoView = findViewById(R.id.dcep_money);
            mOrderInfoView.init(cashier);
            setPriceLayoutHeight(mOrderInfoView.getOrderPriceAndInfoLayout());
            refresh(DiscountCashierUtils.getDCEPPayMoney(mBankListPage.getTotalFee(), getNowSelectedPaymentReduce()).floatValue());

            ((TextView) findViewById(R.id.dcep_title)).setText(mBankListPage.getPageTitle());
            mConfirmButton = findViewById(R.id.dcep_confirm_btn);
            if (!TextUtils.isEmpty(mBankListPage.getPayButton())) {
                mConfirmButton.setText(mBankListPage.getPayButton());
            }
            mConfirmButton.setOnClickListener(new NoDuplicateClickListener() {
                @Override
                public void onSingleClick(View v) {
                    HashMap<String, Object> map = CashierStaticsUtils.getTechnologyParameters();
                    map.put("bank_name", mSelectedPayment != null ? mSelectedPayment.getName() : "");
                    CashierStaticsUtils.logModelEvent("c_pay_h7g2fc35", "b_pay_h4ezb2s6_mc", "DCEP选择半弹窗-立即付款",
                            map, StatisticsUtils.EventType.CLICK, getUniqueId());

                    if (mSelectedPayment != null && mSelectedPayment.getCardInfo() != null) {
                        startDirectPay(mSelectedPayment, null, mExtendTransmissionParams);
                    } else {
                        ToastUtils.showSnackToast(getDialog(), TIP_PAY_ERROR, "",
                                ToastUtils.ToastType.TOAST_TYPE_COMMON, false);
                    }
                }
            });
            findViewById(R.id.dcep_close).setOnClickListener(v -> {
                HashMap<String, Object> map = CashierStaticsUtils.getTechnologyParameters();
                CashierStaticsUtils.logModelEvent("c_pay_h7g2fc35", "b_pay_vkfjg7dc_mc", "DCEP选择半弹窗-关闭",
                        map, StatisticsUtils.EventType.CLICK, getUniqueId());

                cancel();
            });
            if (!CollectionUtils.isEmpty(mPaymentList)) {
                ListView listView = findViewById(R.id.dcep_bank_list);
                listView.setAdapter(new DCEPBankAdapter(getContext(), mPaymentList));
                listView.setOnItemClickListener((parent, view, position, id) -> {
                    if (mSelectedPayment != mPaymentList.get(position)) {
                        mSelectedPayment = mPaymentList.get(position);
                        for (int i = 0; i < mPaymentList.size(); i++) {
                            DCEPPayment payment = mPaymentList.get(i);
                            if (i == position) {
                                payment.setSelected(true);
                                refresh(DiscountCashierUtils.getDCEPPayMoney(mBankListPage.getTotalFee(), payment.getPaymentReduce()).floatValue());
                            } else {
                                payment.setSelected(false);
                            }
                        }
                        ((PayBaseAdapter) listView.getAdapter()).notifyDataSetChanged();

                        HashMap<String, Object> map = CashierStaticsUtils.getTechnologyParameters();
                        map.put("bank_name", mSelectedPayment != null ? mSelectedPayment.getName() : "");
                        CashierStaticsUtils.logModelEvent("c_pay_h7g2fc35", "b_pay_0twy1fj8_mc", "DCEP银行列表",
                                map, StatisticsUtils.EventType.CLICK, getUniqueId());
                    }
                });
            }
        }

        private void refresh(float money) {
            mOrderInfoView.refreshView(money);
        }

        private void setPriceLayoutHeight(FrameLayout frameLayout) {
            if (frameLayout != null) {
                LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) frameLayout.getLayoutParams();
                layoutParams.height = TransferUtils.dip2px(getContext(), 15);
                frameLayout.setLayoutParams(layoutParams);
            }
        }

        /**
         * 获取当前选中支付钱包的优惠对象
         * @return
         */
        private PaymentReduce getNowSelectedPaymentReduce() {
            if (!CollectionUtils.isEmpty(mPaymentList)) {
                for (DCEPPayment dcepPayment : mPaymentList) {
                    if (dcepPayment.isSelected()) {
                        return dcepPayment.getPaymentReduce();
                    }
                }
                // 如果后端没有返回默认选中的支付方式，则取出第一个方式的优惠。（列表默认选中也是这个逻辑）
                return mPaymentList.get(0).getPaymentReduce();
            }
            return null;
        }

        private void startLoading() {
            if (mConfirmButton != null) {
                mConfirmButton.start();
            }
        }

        private void stopLoading() {
            if (mConfirmButton != null && mConfirmButton.isLoading()) {
                mConfirmButton.stop();
            }
        }

        @Override
        public void onBackPressed() {
        }
    }

    private static class DCEPBankAdapter extends PayBaseAdapter<DCEPPayment> {

        DCEPBankAdapter(Context context, ArrayList<DCEPPayment> list) {
            super(context, list);
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            DCEPViewHolder viewHolder;
            if (convertView == null) {
                convertView = LayoutInflater.from(getContext())
                        .inflate(R.layout.cashier__dcep_bank_item, parent, false);
                viewHolder = new DCEPViewHolder();
                viewHolder.icon = convertView.findViewById(R.id.dcep_icon);
                viewHolder.name = convertView.findViewById(R.id.dcep_name);
                viewHolder.nameExt = convertView.findViewById(R.id.dcep_name_ext);
                viewHolder.isChosen = convertView.findViewById(R.id.dcep_selected);
                viewHolder.payLabelView = convertView.findViewById(R.id.dcep_label_layout);
                convertView.setTag(viewHolder);
            } else {
                viewHolder = (DCEPViewHolder) convertView.getTag();
            }
            DCEPPayment payment = getItem(position);
            viewHolder.name.setText(payment.getName());
            if (payment.getCardInfo() != null) {
                viewHolder.nameExt.setText(payment.getCardInfo().getNameExt());
            } else {
                viewHolder.nameExt.setText("");
            }
            if (payment.getIcon() != null) {
                WebpImageLoader.load(payment.getIcon().getEnable(),
                        viewHolder.icon,
                        R.drawable.mpay__payment_default_pic,
                        R.drawable.mpay__payment_default_pic
                );
            } else {
                viewHolder.icon.setImageResource(R.drawable.mpay__payment_default_pic);
            }
            if (payment.isSelected()) {
                viewHolder.isChosen.setImageResource(R.drawable.mtpaysdk__payment_checkbox_selected);
            } else {
                viewHolder.isChosen.setImageResource(R.drawable.mtpaysdk__payment_checkbox_unselected);
            }
            if (!CollectionUtils.isEmpty(payment.getBottomLabels())) {
                viewHolder.payLabelView.showCombineLabel(payment.getBottomLabels(), 3);
                viewHolder.payLabelView.setVisibility(View.VISIBLE);
            } else {
                viewHolder.payLabelView.setVisibility(View.GONE);
            }

            return convertView;
        }

        static class DCEPViewHolder {
            ImageView icon;
            TextView name;
            TextView nameExt;
            ImageView isChosen;
            PayLabelContainer payLabelView;
        }
    }
}
