package com.meituan.android.cashier.newrouter.mtpaydialog;

import static com.meituan.android.cashier.activity.MTCashierActivity.ERROR_CODE_DEFAULT;

import android.support.annotation.Keep;
import android.support.annotation.NonNull;
import android.support.v4.app.FragmentActivity;

import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.dialogfragment.PayLaterGuideDialogFragment;
import com.meituan.android.cashier.model.bean.Cashier;
import com.meituan.android.cashier.model.bean.CashierPopWindowBean;
import com.meituan.android.cashier.model.bean.PayLaterPopDetailInfoBean;
import com.meituan.android.cashier.model.bean.PopDetailInfo;
import com.meituan.android.cashier.newrouter.CashierBusinessHandler;
import com.meituan.android.cashier.newrouter.NewCashierParams;
import com.meituan.android.cashier.util.CashierSLAMonitor;
import com.meituan.android.paybase.common.dialog.PayDialog;
import com.meituan.android.paybase.utils.ActivityStatusChecker;
import com.meituan.android.paybase.utils.StatisticsUtils;
import com.meituan.android.paybase.utils.UriUtils;

import java.io.Serializable;

public class MTPayDialogHandler extends CashierBusinessHandler {
    public static final int NO_POP_WINDOW_AND_DIALOG = 0;
    public static final int STOP_PAYMENT_GUIDE = CashierPopWindowBean.STOP_PAYMENT_GUIDE;
    public static final int POP_WINDOW_PAY_LATER_GUIDE = CashierPopWindowBean.POPWINDOW_PAYLATER_GUIDE;
    public static final int BIND_CARD_PAY_GUIDE = CashierPopWindowBean.BIND_CARD_PAY_GUIDE;
    public static final int CREDIT_PAY_GUIDE = CashierPopWindowBean.CREDIT_PAY_GUIDE;
    public static final int PROMOTION_SIGNED_PAY_GUIDE = CashierPopWindowBean.PROMOTION_SIGNED_PAY_GUIDE;
    public static final int PROMOTION_BOUND_CARD_PAY_GUIDE = CashierPopWindowBean.PROMOTION_BINDED_CARD_PAY_GUIDE;

    private final MTPayDialogResultHandler resultHandler;
    private CashierPopWindowBean cashierPopWindowBean;
    private Cashier cashierBean;
    private boolean reportedSLA;

    public MTPayDialogHandler(FragmentActivity activity, NewCashierParams cashierParams, MTPayDialogResultHandler resultHandler) {
        super(activity, cashierParams);
        this.resultHandler = resultHandler;
    }

    /**
     * 设置下发的业务数据
     *
     * @param cashierBean 收银台相关数据
     */
    public void setCashierResponse(CashierPopWindowBean cashierPopWindowBean, Cashier cashierBean) {
        this.cashierPopWindowBean = cashierPopWindowBean;
        this.cashierBean = cashierBean;
    }

    public @NonNull Result show() {
        FragmentActivity activity = getActivity();
        if (!ActivityStatusChecker.isValid(activity) || cashierPopWindowBean == null) {
            return new Result(false, NO_POP_WINDOW_AND_DIALOG);
        }
        switch (cashierPopWindowBean.getType()) {
            case STOP_PAYMENT_GUIDE:
                boolean blockPayWindowShown = showBlockPayWindow(activity, cashierPopWindowBean.getPopDetailInfo());
                return new Result(blockPayWindowShown, CashierPopWindowBean.STOP_PAYMENT_GUIDE);
            case POP_WINDOW_PAY_LATER_GUIDE:
                boolean payLaterWindowShown = showPayLaterWindow(activity, cashierPopWindowBean.getPayLaterPopDetailInfoBean());
                return new Result(payLaterWindowShown, CashierPopWindowBean.POPWINDOW_PAYLATER_GUIDE);
            case BIND_CARD_PAY_GUIDE:
            case CREDIT_PAY_GUIDE:
            case PROMOTION_SIGNED_PAY_GUIDE:
            case PROMOTION_BOUND_CARD_PAY_GUIDE:
                return new Result(false, cashierPopWindowBean.getType()).setCashierPopWindowBean(cashierPopWindowBean);
        }
        return new Result(false, NO_POP_WINDOW_AND_DIALOG);
    }

    private boolean showBlockPayWindow(FragmentActivity activity, PopDetailInfo popDetailInfo) {
        if (!PopDetailInfo.isDialogValid(popDetailInfo)) {
            return false;
        }
        String errorMsg = popDetailInfo.getDetail();
        if (activity instanceof MTCashierActivity) {
            ((MTCashierActivity) activity).setErrorCodeAndErrorMessage(ERROR_CODE_DEFAULT, errorMsg);
        }
        new PayDialog.Builder(activity)
                .msg(errorMsg)
                .canceledOnTouchOutside(false)
                .cancelable(false)
                .leftBtn(popDetailInfo.getLeftBtn(), dialog -> {
                    CashierStaticsUtils.logModelEvent("c_pay_jjckzxmj", "b_pay_v5l55ue3_mc", "解止付申诉弹窗-终止支付", null,
                            StatisticsUtils.EventType.CLICK, getCashierParams().getCashierUniqueId());
                    dialog.cancel();
                    resultHandler.onBlockWindowFinished();
                })
                .rightBtn(popDetailInfo.getRightBtn(), dialog -> {
                    CashierStaticsUtils.logModelEvent("c_pay_jjckzxmj", "b_pay_2royud7a_mc", "解止付申诉弹窗-申请解除限制", null,
                            StatisticsUtils.EventType.CLICK, getCashierParams().getCashierUniqueId());
                    dialog.cancel();
                    resultHandler.onBlockWindowFinished();
                    UriUtils.open(getActivity(), popDetailInfo.getRedirectUrl(), false);
                })
                .build().show();
        if (!reportedSLA) {
            reportedSLA = true;
            CashierSLAMonitor.reportStandardCashierFinished(getCashierParams().getCashierUniqueId(),
                    CashierSLAMonitor.CASHIER_FINISHED_STATUS_SUCCESS,
                    CashierSLAMonitor.NATIVE_STANDARD_CASHIER_SUCCESS_SCENE_STOP_PAYMENT_GUIDE);
        }
        CashierSLAMonitor.notifyRouterLoadEnd(getCashierParams().getCashierRouterTrace(), "风控弹窗展示成功");
        return true;
    }

    private boolean showPayLaterWindow(FragmentActivity activity, PayLaterPopDetailInfoBean payLaterPopDetailInfoBean) {
        if (payLaterPopDetailInfoBean == null) {
            return false;
        }
        PayLaterGuideDialogFragment payLaterGuideDialogFragment = PayLaterGuideDialogFragment.newInstance(
                getCashierParams().getGuidePlanInfos(),
                getCashierParams().getTradeNo(),
                getCashierParams().getPayToken(), payLaterPopDetailInfoBean,
                cashierBean,
                getCashierParams().getDowngradeInfo());
        payLaterGuideDialogFragment.setResultHandler(resultHandler);
        payLaterGuideDialogFragment.show(activity.getSupportFragmentManager());
        CashierSLAMonitor.notifyRouterLoadEnd(getCashierParams().getCashierRouterTrace(), "支付签约引导展示成功");
        return true;
    }

    @Keep
    public static class Result implements Serializable {
        private final boolean success;
        private final int type;
        private CashierPopWindowBean cashierToShow;

        public Result(boolean success, int type) {
            this.success = success;
            this.type = type;
        }

        private Result setCashierPopWindowBean(CashierPopWindowBean cashierPopWindowBean) {
            cashierToShow = cashierPopWindowBean;
            return this;
        }

        public boolean isSuccess() {
            return success;
        }

        public int getType() {
            return type;
        }

        public CashierPopWindowBean getCashierToShow() {
            return cashierToShow;
        }
    }
}
