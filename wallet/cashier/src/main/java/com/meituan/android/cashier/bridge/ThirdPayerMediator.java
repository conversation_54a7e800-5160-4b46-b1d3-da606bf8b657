package com.meituan.android.cashier.bridge;

import static com.meituan.android.paybase.constants.ThirdPayConstants.CODE_EMPTY_PAYTYPE;
import static com.meituan.android.paybase.constants.ThirdPayConstants.CODE_PARAMS_EXCEPTION;
import static com.meituan.android.paybase.constants.ThirdPayConstants.CODE_UNEXPECTED_PAYTYPE;
import static com.meituan.android.paybase.constants.ThirdPayConstants.MSG_EMPTY_PAYTYPE;
import static com.meituan.android.paybase.constants.ThirdPayConstants.MSG_PARAMS_EXCEPTION;
import static com.meituan.android.paybase.constants.ThirdPayConstants.MSG_UNEXPECTED_PAYTYPE;
import static com.meituan.android.paybase.constants.ThirdPayConstants.TOAST_MSG_PAY_FAILED;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;

import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.moduleinterface.payment.PayActionListener;
import com.meituan.android.paybase.moduleinterface.payment.PayFailInfo;
import com.meituan.android.paybase.moduleinterface.payment.Payer;
import com.meituan.android.paybase.webview.WebViewActivity;
import com.meituan.android.paymentchannel.PayerFactory;
import com.meituan.android.paymentchannel.PayerMediator;
import com.meituan.android.paymentchannel.PayersID;
import com.meituan.android.paymentchannel.payers.DCEPPayer;
import com.meituan.android.paymentchannel.payers.WechatJsPayer;
import com.meituan.android.paymentchannel.utils.UPPayUtils;
import com.meituan.android.paymentchannel.webpay.MTCPayWebActivity;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * created by xingweike
 * 2019/9/28
 * 第三方支付路由,包装了PayMediator的功能
 */
public final class ThirdPayerMediator {

    private static final int REQUEST_UPPAY_RESULT = 10;
    // todo 最终要挪到 JsHandlerRequestCode
    private static final int REQUEST_CODE_CALL_SHANGQITONG = 407;

    /**
     * 不可以处理activity result的黑名单.
     * 由于调桥跳到第三方再回到支付的时候可能会导致requestCode冲突的可能(比如唤起了微信支付,进入后台并回到收银台，然后有第三方桥要跳到其他app并使用了requestcode 10、
     * 再回到支付SDK的时候就会调到我们的onActivityResult里,此时我们的银联就会去处理该事件).
     * 所以凡是会跳转到第三方app的支付方式,都需要加入到黑名单里.
     * 目前有微信和QQ.
     */
    private static final List<String> blackList = new ArrayList<>();

    static {
        blackList.add(PayersID.ID_WEIXINPAY);
        blackList.add(PayersID.ID_WX_JS_PAY);
    }

    private static final class Holder {
        private static final ThirdPayerMediator INSTANCE = new ThirdPayerMediator();
    }

    public static ThirdPayerMediator getInstance() {
        return Holder.INSTANCE;
    }

    private ThirdPayerMediator() {
    }

    /**
     * 该方法目前只提供给三方支付桥使用。针对 dcep 支付方式做了特殊处理，一旦有其他地方使用一定要注意！
     *
     * @param activity          上下文 必传
     * @param payType           支付方式 必传
     * @param url               directPay接口返回的url 必传
     * @param tradeNo           订单号,非必传
     * @param payActionListener 回调 必传
     */
    public static void startPay(Activity activity, String payType, String url, String tradeNo, PayActionListener payActionListener, JSONObject extraData) {
        if (payActionListener == null || activity == null) {
            return;
        }
        PayerMediator.getInstance().setPayActionListener(activity, payActionListener);
        PayerMediator.getInstance().onPrePay(activity, payType);
        if (!TextUtils.isEmpty(payType)) {
            Payer payer = PayerFactory.getPayer(payType);
            if (payer == null) {
                ToastUtils.showSnackToast(activity, TOAST_MSG_PAY_FAILED, true);
                PayFailInfo info = new PayFailInfo();
                info.setErrorCodeString(CODE_UNEXPECTED_PAYTYPE);
                info.setMsg(MSG_UNEXPECTED_PAYTYPE);
                payActionListener.onGotPayResult(payType, PayActionListener.FAIL, info);
                return;
            }

            if (TextUtils.equals(PayersID.ID_SHANGQITONGPAY, payType)) {
                // 跳转到商企通页面
                Intent payWebIntent = new Intent(activity, MTCPayWebActivity.class);
                payWebIntent.putExtra(WebViewActivity.KEY_URL, url);
                activity.startActivityForResult(payWebIntent, REQUEST_CODE_CALL_SHANGQITONG);
            } else if (payer instanceof DCEPPayer) {
                ((DCEPPayer) payer).execute(activity, url);
            } else if (payer instanceof WechatJsPayer) {
                if (extraData == null) {
                    PayFailInfo info = new PayFailInfo();
                    info.setErrorCodeString(CODE_PARAMS_EXCEPTION);
                    info.setMsg(MSG_PARAMS_EXCEPTION);
                    payActionListener.onGotPayResult(payType, PayActionListener.FAIL, info);
                } else {
                    ((WechatJsPayer) payer).execute(activity, url, tradeNo, extraData);
                }
            } else {
                payer.execute(activity, url, tradeNo);
            }
        } else {
            ToastUtils.showSnackToast(activity, TOAST_MSG_PAY_FAILED, true);
            PayFailInfo info = new PayFailInfo();
            info.setErrorCodeString(CODE_EMPTY_PAYTYPE);
            info.setMsg(MSG_EMPTY_PAYTYPE);
            payActionListener.onGotPayResult(payType, PayActionListener.FAIL, info);
        }
    }

    /**
     * @return 事件是否被处理
     */
    public static boolean handleActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
        boolean hasHandled = false;
        if (requestCode == REQUEST_UPPAY_RESULT) {
            // 处理银联的返回结果
            getInstance().onGotUPPAYResult(activity, resultCode, data);
            hasHandled = true;
        } else if (requestCode == REQUEST_CODE_CALL_SHANGQITONG) {
            // 处理商企通的返回结果
            getInstance().onGotShangQiTongPayResult(activity, resultCode);
            hasHandled = true;
        }

        return hasHandled;
    }

    private void onGotUPPAYResult(Activity activity, int resultCode, Intent data) {
        // 进行埋点
        UPPayUtils.dealUPPayResult(resultCode, data);
        PayActionListener payActionListener = PayerMediator.getInstance().getPayActionListener(activity);
        if (payActionListener == null) {
            return;
        }
        if (data == null) {
            payActionListener.onGotPayResult(PayersID.ID_UPPAY, PayActionListener.ENV_ERROR, null);
            return;
        }
        String payResult = data.getStringExtra("pay_result");
        if (resultCode == Activity.RESULT_OK) {
            if ("success".equalsIgnoreCase(payResult)) {
                payActionListener.onGotPayResult(PayersID.ID_UPPAY, PayActionListener.SUCCESS, null);
            } else if ("fail".equalsIgnoreCase(payResult)) {
                PayFailInfo info = new PayFailInfo();
                info.setMsg("支付错误");
                payActionListener.onGotPayResult(PayersID.ID_UPPAY, PayActionListener.FAIL, info);
            } else if ("cancel".equalsIgnoreCase(payResult)) {
                payActionListener.onGotPayResult(PayersID.ID_UPPAY, PayActionListener.CANCEL, null);
            }
        } else {
            payActionListener.onGotPayResult(PayersID.ID_UPPAY, PayActionListener.CANCEL, null);
        }
    }

    private void onGotShangQiTongPayResult(Activity activity, int resultCode) {
        PayActionListener payActionListener = PayerMediator.getInstance().getPayActionListener(activity);
        if (payActionListener == null) {
            return;
        }

        // 赋值成功和取消的逻辑与native标准收银台对齐，native代码在PayerMediator的onGotWapPayResult中
        if (resultCode == Activity.RESULT_OK) {
            payActionListener.onGotPayResult(PayersID.ID_SHANGQITONGPAY, PayActionListener.SUCCESS, null);
        } else {
            payActionListener.onGotPayResult(PayersID.ID_SHANGQITONGPAY, PayActionListener.CANCEL, null);
        }
    }

    /**
     * @param paytype 支付方式
     * @return 是否应该处理onActivityResult的结果
     */
    public static boolean shouldHandleActivityResult(String paytype) {
        return !TextUtils.isEmpty(paytype) && !blackList.contains(paytype);
    }

    public static void setPayActionListener(Activity activity, PayActionListener payActionListener) {
        PayerMediator.getInstance().setPayActionListener(activity, payActionListener);
    }

    public static void removePayActionListener(Activity activity) {
        PayerMediator.getInstance().removePayActionListener(activity);
    }
}
