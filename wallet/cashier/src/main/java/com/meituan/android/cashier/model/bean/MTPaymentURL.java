package com.meituan.android.cashier.model.bean;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;

/**
 * Created by ljj
 * Date:17/2/14
 * Time:下午5:13
 */
@JsonBean
public class MTPaymentURL implements Serializable {
    private static final long serialVersionUID = 1411397808302864142L;
    private String url;
    @SerializedName("pay_type")
    private String payType;
    @SerializedName("overload_info")
    private OverLoadInfo overLoadInfo;

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public OverLoadInfo getOverLoadInfo() {
        return overLoadInfo;
    }

    public void setOverLoadInfo(OverLoadInfo overLoadInfo) {
        this.overLoadInfo = overLoadInfo;
    }

    public PayResult getPayResultObject() {
        PayResult payResult = new PayResult();
        payResult.setPayType(payType);
        payResult.setUrl(url);
        payResult.setOverLoadInfo(overLoadInfo);
        return payResult;
    }

}
