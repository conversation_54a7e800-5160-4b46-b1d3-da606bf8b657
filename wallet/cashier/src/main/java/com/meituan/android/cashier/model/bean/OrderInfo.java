package com.meituan.android.cashier.model.bean;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;
import java.util.List;

/**
 * 订单详情信息
 * Created by <PERSON><PERSON><PERSON> on 16/9/12.
 */
@JsonBean
public class OrderInfo implements Serializable {

    private static final long serialVersionUID = 2881620820883468099L;
    @SerializedName("block_count")
    private int blockCount;

    @SerializedName("block")
    private List<OrderInfoBlock> orderInfoBlocks;

    public int getBlockCount() {
        return blockCount;
    }

    public void setBlockCount(int blockCount) {
        this.blockCount = blockCount;
    }

    public List<OrderInfoBlock> getOrderInfoBlocks() {
        CollectionUtils.removeNullElement(orderInfoBlocks);
        return orderInfoBlocks;
    }

    public void setOrderInfoBlocks(List<OrderInfoBlock> orderInfoBlocks) {
        this.orderInfoBlocks = orderInfoBlocks;
    }
}
