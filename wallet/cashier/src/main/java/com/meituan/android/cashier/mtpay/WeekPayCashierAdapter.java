package com.meituan.android.cashier.mtpay;

import static com.meituan.android.cashier.NativeStandardCashierAdapter.REQ_TAG_GO_HELLO_PAY;
import static com.meituan.android.cashier.utils.GoHelloPaySceneUtils.reportMtPaySLAStart;
import static com.meituan.android.pay.desk.component.analyse.DeskAnalyseUtils.analyseComponentDispatch;
import static com.meituan.android.pay.desk.component.analyse.DeskCatConstants.ACTION_COMPONENT_START;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.support.v4.app.FragmentActivity;
import android.text.TextUtils;

import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.bean.CashierParams;
import com.meituan.android.cashier.common.CashierConstants;
import com.meituan.android.cashier.common.CashierListener;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.common.CashierTypeConstant;
import com.meituan.android.cashier.common.ICashier;
import com.meituan.android.cashier.common.ICashierAdapter;
import com.meituan.android.cashier.common.ProductTypeConstant;
import com.meituan.android.cashier.model.bean.MTPaymentURL;
import com.meituan.android.cashier.model.params.PayParams;
import com.meituan.android.cashier.retrofit.CashierRequestService;
import com.meituan.android.cashier.retrofit.CashierRequestUtils;
import com.meituan.android.pay.common.analyse.MtPaymentStaticsUtils;
import com.meituan.android.pay.model.PayErrorCode;
import com.meituan.android.paybase.common.activity.PayBaseActivity;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.downgrading.PayHornConfigBean;
import com.meituan.android.paybase.moduleinterface.payment.PayActionListener;
import com.meituan.android.paybase.moduleinterface.payment.PayFailInfo;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.retrofit.PayException;
import com.meituan.android.paybase.utils.CashierRepeatDownGradeSwitchManager;
import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.MTPayBaseClass;
import com.meituan.android.paycommon.lib.coupon.model.Promotion;
import com.meituan.android.paycommon.lib.retrofit.PayRetrofit;
import com.meituan.android.paycommon.lib.utils.OuterBusinessParamUtils;
import com.meituan.android.paymentchannel.PayerMediator;
import com.meituan.android.paymentchannel.PayersID;
import com.sankuai.meituan.retrofit2.Call;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

@ServiceLoaderInterface(key = CashierTypeConstant.CASHIERTYPE_WEEKPAY, interfaceClass = ICashier.class)
@MTPayBaseClass
public class WeekPayCashierAdapter extends ICashierAdapter implements PayActionListener {

    private static final String TAG = "WeekPayCashier";
    private static final String VAL_DOWNGRADE_TO_BUSINESS = "downgrade_to_business";
    private static final int STATUS_FINISH = MTCashierActivity.STATUS_FINISH;
    private static final String KEY_ACTION = "action";
    private static final String PRODUCT_TYPE = ProductTypeConstant.WEEK_PAY;
    private static final String KEY_PAY_TYPE = "pay_type";

    private CashierParams mCashierParams;
    private MTCashierActivity mCashierActivity;
    private String tradeNo;
    private String payToken;
    private String merchantNo;

    private Call<MTPaymentURL> mGoHelloPayCall;

    @Override
    public <T extends FragmentActivity & CashierListener & IRequestCallback> ConsumeResult consume(T t, CashierParams cashierParams) {
        if (t == null || cashierParams == null
                || TextUtils.isEmpty(cashierParams.getTradeNo())
                || TextUtils.isEmpty(cashierParams.getPayToken())) {
            return new ConsumeResult(false);
        }
        this.mCashierParams = cashierParams;
        this.mCashierActivity = (MTCashierActivity) t;
        this.tradeNo = cashierParams.getTradeNo();
        this.payToken = cashierParams.getPayToken();
        Uri uri = cashierParams.getUri();
        if (uri != null) {
            this.merchantNo = uri.getQueryParameter(CashierConstants.ARG_MERCHANT_NO);
        }
        return new ConsumeResult(PRODUCT_TYPE.equals(getProductType(cashierParams)));
    }

    private String getProductType(CashierParams cashierParams) {
        if (cashierParams == null) {
            return "";
        }
        return cashierParams.getProductType();
    }

    @Override
    public void invoke(String cashierFrom, Map<String, Object> cashierParams) {
        MtPaymentStaticsUtils.setMerchantNo(this.merchantNo);
        MtPaymentStaticsUtils.registerCommonBusinessParams(getUniqueId());
        analyseComponentDispatch(ACTION_COMPONENT_START, "6");
        request(genPayParams());
    }

    private void request(PayParams payParams) {
        HashMap<String, String> requestParams = CashierRequestUtils.getHelloPayMap(payParams);
        requestParams.put(KEY_PAY_TYPE, PRODUCT_TYPE);
        OuterBusinessParamUtils.appendExtraParamsTogoHelloPay(mCashierActivity, requestParams);
        mGoHelloPayCall = PayRetrofit.getInstance().create(CashierRequestService.class, mCashierActivity,
                REQ_TAG_GO_HELLO_PAY).goHelloPay(requestParams);
    }

    // https://km.sankuai.com/page/341245279
    private PayParams genPayParams() {
        PayParams payParams = new PayParams();
        payParams.tradeNo = tradeNo;
        payParams.payToken = payToken;
        payParams.cashierType = "preorder-cashier";
        return payParams;
    }

    @Override
    public String getCashierType() {
        return CashierTypeConstant.CASHIERTYPE_WEEKPAY;
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {

    }

    @Override
    public void onRestoreInstanceState(Bundle savedInstanceState) {

    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        PayerMediator.getInstance().setPayActionListener(mCashierActivity, this);
        if (PayerMediator.getInstance().consumeActivityResult(mCashierActivity, requestCode, resultCode, data)) {
            LoganUtils.log("WeekPayCashierAdapter_onActivityResult_requestCode: " + requestCode);
        }
    }

    @Override
    public void onDestroy(boolean release) {
        if (mGoHelloPayCall != null && !mGoHelloPayCall.isCanceled()) {
            mGoHelloPayCall.cancel();
        }
    }

    @Override
    public void onRequestSucc(int tag, Object obj) {
        if (tag == REQ_TAG_GO_HELLO_PAY && obj != null) {
            CashierStaticsUtils.logCustomRequestSuccess("weekpay_gohellopay_succ", null, getUniqueId());
            CashierStaticsUtils.reportRequestSuccess("cashier/gohellopay", "b_pay_weekpay_gohellopay_succ_sc", null, getUniqueId());
            MTPaymentURL mtPaymentURL = (MTPaymentURL) obj;
            if (mtPaymentURL != null) {
                PayHornConfigBean.setGmDegradeFlag(mtPaymentURL.getUrl());
            }
            dealGoHelloPayResponse(mtPaymentURL);
        }
    }

    private void dealGoHelloPayResponse(MTPaymentURL mtPaymentURL) {
        if (mtPaymentURL == null) {
            return;
        }
        if (mtPaymentURL.getOverLoadInfo() != null && mtPaymentURL.getOverLoadInfo().isStatus()) { //当前后台负载过重，无法完成支付，弹出提示窗口
            openStatus(false, null);
            CashierStaticsUtils.techMis("b_pay_6f1taqcl_mv", new AnalyseUtils.InstantReportBuilder().add("type", mtPaymentURL.getPayType()).build(), getUniqueId());
            businessDegrade(null);
        } else {
            openStatus(true, null);
            //gohellopay  流量分布埋点 周卡收银台
            PayerMediator.getInstance().startPay(mCashierActivity, PayersID.ID_MEITUANPAY, mtPaymentURL.getUrl(), this.mCashierParams.getTradeNo(), this);
            reportMtPaySLAStart(mtPaymentURL, "native", getUniqueId());
        }
    }

    @Override
    public void onRequestException(int tag, Exception e) {
        // 直接降级到标准收银台
        if (tag == REQ_TAG_GO_HELLO_PAY) {
            CashierStaticsUtils.logCustomRequestFailed("weekpay_gohellopay_fail", e, getUniqueId());
            CashierStaticsUtils.reportRequestFailed("cashier/gohellopay", "b_pay_weekpay_gohellopay_fail_sc", e, getUniqueId());
            openStatus(false, null);
            if (e instanceof PayException) {
                downgradeToStandardCashier(getPreComponentFailInfo((PayException) e));
            } else {
                downgradeToStandardCashier(null);
            }
        } else {

        }
    }

    public void downgradeToStandardCashier(String info) {
        if (mCashierActivity != null) {
            mCashierActivity.onCashierBusinessDowngrade(getCashierType(), ProductTypeConstant.STANDARD_CASHIER, info);
        }
    }

    private void businessDegrade(String degradeInfo) {
        downgradeToStandardCashier(degradeInfo);
    }

    private static String getPreComponentFailInfo(PayException payException) {
        if (payException == null) {
            return null;
        }
        JSONObject jsonObject = new JSONObject();
        int errorCode = payException.getCode();
        String errorMessage = payException.getMessage();
        try {
            if (errorCode != -1) {
                jsonObject.put("pay_err_code", errorCode);
                if (!TextUtils.isEmpty(errorMessage)) {
                    jsonObject.put("pay_err_msg", errorMessage);
                }
            }
            jsonObject.put("jump_from_product", PRODUCT_TYPE);
        } catch (JSONException e) {
            LoganUtils.logError("WeekPayCashierAdapter_getPreComponentFailInfo", e.getMessage());
        }
        return jsonObject.toString();
    }

    private static String getPreComponentFailInfo(PayFailInfo payFailInfo) {
        if (payFailInfo == null) {
            return null;
        }
        JSONObject jsonObject = new JSONObject();
        int errorCode = payFailInfo.getErrorCode();
        String errorMessage = payFailInfo.getMsg();
        try {
            if (errorCode != -1) {
                jsonObject.put("pay_err_code", errorCode);
            }
            if (!TextUtils.isEmpty(errorMessage)) {
                jsonObject.put("pay_err_msg", errorMessage);
            }
            jsonObject.put("jump_from_product", PRODUCT_TYPE);
        } catch (JSONException e) {
            LoganUtils.logError("WeekPayCashierAdapter_getPreComponentFailInfo", e.getMessage());
        }
        return jsonObject.toString();
    }

    @Override
    public void onPayPreExecute(String payType) {

    }

    @Override
    public void onGotPayResult(String payType, int payResult, PayFailInfo payFailInfo) {
        if (PayersID.ID_MEITUANPAY.equals(payType)) {
            onGotMeituanPayResult(payType, payResult, payFailInfo);
        }
    }

    private void onGotMeituanPayResult(String payType, int payResult, PayFailInfo payFailInfo) {
        if (payResult == PayActionListener.SUCCESS) {
            // analysePaySucess是独立收银台特有的，不应该在周卡收银台，为避免影响数据，先删除
            Map<String, Object> reportMap = new HashMap<>();
            reportMap.put("pay_type", payType);
            reportMap.put("class", "WeekPayCashierAdapter");
            LoganUtils.log("收银台支付成功后埋点", reportMap);
            onMeituanPaySuccess(payFailInfo);
        } else if (payResult == PayActionListener.CANCEL) { //
            downgradeToStandardCashier(null);
        } else {
            if (isLoadingCancel(payFailInfo)) { // 容器加载过程中点击 back 键，取消加载
                Map<String, Object> map = new HashMap<>();
                map.put("scene", "cancel");
                openStatus(false, map);
                downgradeToStandardCashier(null);
            } else {
                if (isDegradeToStandardCashierOfBusinessDecision(payFailInfo)) { // 业务降级
                    businessDegrade(getPreComponentFailInfo(payFailInfo));
                } else {
                    payCancel();
                }
            }
        }
    }

    private void onMeituanPaySuccess(PayFailInfo failInfo) {
        if (mCashierActivity == null) {
            return;
        }
        if (CashierRepeatDownGradeSwitchManager.downGrade()) {
            mCashierActivity.setResultStatus(MTCashierActivity.STATUS_SUCCESS);
            mCashierActivity.handlePayResultAndFinish(STATUS_FINISH);
        } else {
            if (failInfo == null) {
                mCashierActivity.onCashierPaySuccess(null);
                return;
            }
            Promotion promotion = null;
            try {
                JSONObject jsonObject = new JSONObject(failInfo.getExtra());
                // pay_promotion 和 PayActivity.KEY_PAY_PROMOTION 保持一致
                promotion = GsonProvider.getInstance().fromJson(jsonObject.optString("pay_promotion"), Promotion.class);
            } catch (Exception e) {
                LoganUtils.logError("WeekPayCashierAdapter_onMeituanPaySuccess", e.getMessage());
            }
            mCashierActivity.onCashierPaySuccess(promotion);
        }
    }

    private void payCancel() {
        mCashierActivity.onCashierCancel();
    }

    private boolean isLoadingCancel(PayFailInfo payFailInfo) {
        if (payFailInfo == null) {
            return false;
        }
        return payFailInfo.getErrorCode() == PayErrorCode.BACK_CANCEL;
    }

    /**
     * 是否是降级到标准收银台--业务决策降级
     *
     * @return
     */
    private boolean isDegradeToStandardCashierOfBusinessDecision(PayFailInfo payFailInfo) {
        JSONObject object = null;
        try {
            if (payFailInfo != null && !TextUtils.isEmpty(payFailInfo.getExtra())) {
                object = new JSONObject(payFailInfo.getExtra());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        boolean downgradeToBusiness = object != null && object.has(KEY_ACTION) && TextUtils.equals(object.optString(KEY_ACTION), VAL_DOWNGRADE_TO_BUSINESS);
        return !downgradeToBusiness;
    }

    @Override
    public PayBaseActivity.ProcessType getRequestProgressType(int tag) {
        return PayBaseActivity.ProcessType.CASHIER;
    }

}
