package com.meituan.android.cashier.newrouter.cashierdialog;

import android.support.v4.app.Fragment;
import android.support.v4.app.FragmentActivity;
import android.support.v4.app.FragmentManager;
import android.text.TextUtils;

import com.meituan.android.cashier.common.ProductTypeConstant;
import com.meituan.android.cashier.dialogfragment.CardPayFunctionGuideDialogFragment;
import com.meituan.android.cashier.dialogfragment.CommonGuideFragment;
import com.meituan.android.cashier.dialogfragment.PromotionSignedGuideFragment;
import com.meituan.android.cashier.fragment.MTCashierRevisionFragment;
import com.meituan.android.cashier.model.bean.CashierPopWindowBean;
import com.meituan.android.cashier.model.bean.PopDetailInfo;
import com.meituan.android.cashier.newrouter.CashierBusinessHandler;
import com.meituan.android.cashier.newrouter.NewCashierParams;
import com.meituan.android.cashier.retrofit.CashierRequestService;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.utils.ActivityStatusChecker;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.JsonString;
import com.meituan.android.paycommon.lib.retrofit.PayRetrofit;
import com.meituan.android.paycommon.lib.utils.OuterBusinessParamUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 收银台内弹窗
 */
public class CashierViewDialogHandler extends CashierBusinessHandler implements IRequestCallback {
    private static final int REQ_TAG_SAVE_ACTION = 10001;

    public CashierViewDialogHandler(FragmentActivity activity, NewCashierParams cashierParams) {
        super(activity, cashierParams);
    }

    /**
     * 收银台内展示弹窗，后续单独抽出模块进行管理。
     * 目前有两种场景：
     * 1. 支付前引导。{@link CashierPopWindowBean#BEFORE_PAY_SCENE}
     * 2. 三方中断弹窗。{@link CashierPopWindowBean#INTERRUPT_PAY_SCENE}
     */
    public boolean showCashierPopWindow(CashierPopWindowBean cashierPopWindowBean, Fragment fragment) {
        FragmentActivity activity = getActivity();
        if (!ActivityStatusChecker.isValid(activity)
                || fragment == null
                || cashierPopWindowBean == null
                || cashierPopWindowBean.getPopDetailInfo() == null) {
            return false;
        }
        if (!(fragment instanceof MTCashierRevisionFragment)) {
            return false;
        }
        FragmentManager manager = fragment.getChildFragmentManager();
        boolean result = false;
        PopDetailInfo popDetailInfo = cashierPopWindowBean.getPopDetailInfo();
        popDetailInfo.setPopScene(cashierPopWindowBean.getPopScene());
        String style = popDetailInfo.getStyle();
        if (TextUtils.equals(PopDetailInfo.FUNCTION_STYLE, style)
                && PopDetailInfo.allowShowOldDialog(popDetailInfo)) {
            CardPayFunctionGuideDialogFragment.newInstance(cashierPopWindowBean)
                    .show(manager);
            result = true;
        } else if (TextUtils.equals(PopDetailInfo.FUNCTION_SINGED_STYLE, style)) {
            PromotionSignedGuideFragment signedGuideFragment = PromotionSignedGuideFragment.newInstance(cashierPopWindowBean);
            if (signedGuideFragment.allowShowDialog(popDetailInfo)) {
                signedGuideFragment.show(manager);
                result = true;
            }
        } else if (TextUtils.equals(PopDetailInfo.COMMON_STYLE, style)
                && CommonGuideFragment.allowShowCommonDialog(popDetailInfo)) {
            CommonGuideFragment.newInstance(cashierPopWindowBean)
                    .show(manager);
            result = true;
        }
        //引导弹窗5期需求增加支付前也上报actionInfo的功能
        sendSaveActionInfoForPopWindow(cashierPopWindowBean);
        return result;
    }

    /**
     * 收银台内展示弹窗数据上报
     *
     * @param popWindowBean 弹窗数据
     */
    private void sendSaveActionInfoForPopWindow(CashierPopWindowBean popWindowBean) {
        FragmentActivity activity = getActivity();
        if (!ActivityStatusChecker.isValid(activity)
                || popWindowBean == null
                || popWindowBean.getPopDetailInfo() == null) {
            return;
        }
        HashMap<String, Object> parametersMap = new HashMap<>();
        parametersMap.put("cashierType", ProductTypeConstant.STANDARD_CASHIER);
        parametersMap.put("action", "popup");
        parametersMap.put("tradeno", getCashierParams().getTradeNo());
        parametersMap.put("nb_platform", "android");
        parametersMap.put("halfScreenType", popWindowBean.getType());
        parametersMap.put("userActionInfo", JsonString.builder()
                .add("popupType", popWindowBean.getPopDetailInfo().getPopupType())
                .add("popupScene", popWindowBean.getPopScene()).build());
        if (activity instanceof OuterBusinessParamUtils.OuterBusinessParamInterface) {
            OuterBusinessParamUtils.appendExtraParams(
                    (OuterBusinessParamUtils.OuterBusinessParamInterface) activity, parametersMap);
        }
        Map<String, String> extendTransmissionParams = getCashierParams().getExtendTransmissionParams();
        if (!CollectionUtils.isEmpty(extendTransmissionParams)) {
            parametersMap.putAll(extendTransmissionParams);
        }
        PayRetrofit.getInstance().create(CashierRequestService.class, this, REQ_TAG_SAVE_ACTION)
                .saveActionInfo(parametersMap);
    }

    @Override
    public void onRequestSucc(int tag, Object obj) {

    }

    @Override
    public void onRequestException(int tag, Exception e) {

    }

    @Override
    public void onRequestFinal(int tag) {

    }

    @Override
    public void onRequestStart(int tag) {

    }
}
