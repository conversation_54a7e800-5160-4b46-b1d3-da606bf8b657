package com.meituan.android.cashier.newrouter;

import android.support.annotation.NonNull;

import com.meituan.android.cashier.bean.CashierRouterPreGuideHornConfig;
import com.meituan.android.cashier.common.CashierTypeConstant;
import com.meituan.android.paybase.payrouter.constants.RouterAdapterConstants;
import com.meituan.android.paybase.utils.JsonString;
import com.meituan.android.paycommon.lib.fragment.HalfPageFragment;
import com.meituan.android.payrouter.adapter.PayRouterAdapterInterface;
import com.meituan.android.payrouter.remake.router.adapter.AbstractRouterAdapter;
import com.meituan.android.payrouter.remake.router.data.CheckResult;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * 先用后付
 */
@ServiceLoaderInterface(key = RouterAdapterConstants.ROUTER_ADAPTER_PAY_DEFER_SIGN, interfaceClass = AbstractRouterAdapter.class)
public class PayDeferSignHybridRouterCashier extends CommonHybridRouterAdapter {
    private final static String CASHIER_TYPE = CashierTypeConstant.CASHIERTYPE_PAY_DEFER_SIGN;
    private final static String PREFETCH_PATH = "/mtScorepay/payDefer/inPay/homePage";

    @Override
    public CheckResult check() {
        if (CASHIER_TYPE.equals(cashierParams().getProductType())) {
            return super.check();
        }
        return CheckResult.fail("001", "productType is:" + cashierParams().getProductType());
    }

    @Override
    protected void prefetch(@NonNull CashierRouterPreGuideHornConfig cashierRouterPreGuideHornConfig, @NonNull HalfPageFragment.HalfPageFragmentConfig halfPageFragmentConfig) {
        if (cashierRouterPreGuideHornConfig.isNsf()) {
            halfPageFragmentConfig.setRequestUrl(PREFETCH_PATH);
            halfPageFragmentConfig.setRequestData(JsonString.builder()
                    .add("outer_business_data", cashierParams().getExtraData())
                    .add("ext_dim_stat", genExtDimStat())
                    .addMap(cashierParams().getExtendTransmissionParams())
                    .build());
        }
    }

    public static String getCashierType() {
        return RouterAdapterConstants.ROUTER_ADAPTER_PAY_DEFER_SIGN;
    }
}