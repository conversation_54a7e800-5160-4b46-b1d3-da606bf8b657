package com.meituan.android.cashier.exception;

import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.retrofit.PayException;

public class GoHelloPayPayExceptionHandler extends GoHelloPayAndDirectPayExceptionHandler {
    protected GoHelloPayPayExceptionHandler(MTCashierActivity activity) {
        super(activity);
    }

    @Override
    protected void techBeforeHandleException(Exception exception) {
        int errorCode = 0;
        if (exception instanceof PayException) {
            errorCode = ((PayException) exception).getCode();
        }
        AnalyseUtils.techMis("b_afd0sd11", new AnalyseUtils.MapBuilder()
                .add("pay_type", getPayType()).add("code", "" + errorCode)
                .add("message", exception.getMessage()).build());
    }

    public static void handleException(MTCashierActivity activity, Exception exception) {
        new GoHelloPayPayExceptionHandler(activity).handleException(exception);
    }
}
