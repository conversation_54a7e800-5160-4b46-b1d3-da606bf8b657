package com.meituan.android.cashier.model.bean;

import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;

@JsonBean
public class PayLaterAgreementBean implements Serializable {
    private static final long serialVersionUID = -8564075271550019154L;

    private String agreementName;
    private String agreementLink;

    public String getAgreementName() {
        return agreementName;
    }

    public void setAgreementName(String agreementName) {
        this.agreementName = agreementName;
    }

    public String getAgreementLink() {
        return agreementLink;
    }

    public void setAgreementLink(String agreementLink) {
        this.agreementLink = agreementLink;
    }

    @Override
    public String toString() {
        return "PayLaterAgreementBean{" +
                "agreementName='" + agreementName + '\'' +
                ", agreementLink='" + agreementLink + '\'' +
                '}';
    }
}
