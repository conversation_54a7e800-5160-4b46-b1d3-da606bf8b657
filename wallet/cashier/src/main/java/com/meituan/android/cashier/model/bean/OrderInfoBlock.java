package com.meituan.android.cashier.model.bean;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 16/9/12.
 */
@JsonBean
public class OrderInfoBlock implements Serializable {

    private static final long serialVersionUID = -3010449896942067559L;
    @SerializedName("type")
    private String blockType;

    @SerializedName("title")
    private String blockTitle;

    @SerializedName("content")
    private List<OrderInfoContent> orderInfoContents;

    public String getBlockType() {
        return blockType;
    }

    public void setBlockType(String blockType) {
        this.blockType = blockType;
    }

    public String getBlockTitle() {
        return blockTitle;
    }

    public void setBlockTitle(String blockTitle) {
        this.blockTitle = blockTitle;
    }

    public List<OrderInfoContent> getOrderInfoContents() {
        CollectionUtils.removeNullElement(orderInfoContents);
        return orderInfoContents;
    }

    public void setOrderInfoContents(List<OrderInfoContent> orderInfoContents) {
        this.orderInfoContents = orderInfoContents;
    }
}
