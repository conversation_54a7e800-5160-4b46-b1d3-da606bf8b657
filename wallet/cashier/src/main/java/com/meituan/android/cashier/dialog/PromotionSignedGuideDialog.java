package com.meituan.android.cashier.dialog;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.meituan.android.cashier.R;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.model.bean.CashierPopWindowBean;
import com.meituan.android.cashier.model.bean.PopDetailInfo;
import com.meituan.android.cashier.utils.NativeStandardCashierPayProcessStatistic;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.pay.common.payment.utils.PayTypeUtils;
import com.meituan.android.pay.common.promotion.bean.Icon;
import com.meituan.android.pay.desk.pack.WalletPayManager;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.dialog.BaseDialog;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.StatisticsUtils;
import com.meituan.android.paycommon.lib.utils.WebpImageLoader;
import com.meituan.android.paycommon.lib.widgets.NoDuplicateClickListener;

import java.util.HashMap;

public class PromotionSignedGuideDialog extends BaseDialog {

    private HashMap<String, Object> technologyParameters;
    private OnClickPromotionPayTypeListener mOnClickPromotionPayTypeListener;
    private PopDetailInfo mGuideInfo;
    private static final String PAYMENT_SUFFIX = " 支付"; //支付方式后缀
    private String mMainPreferentialContent; //主优惠内容
    private String mSubPreferentialContent; //副优惠内容


    public PromotionSignedGuideDialog(Context context, CashierPopWindowBean cashierPopWindowBean,
                                      OnClickPromotionPayTypeListener onClickPromotionPayTypeListener,
                                      String mainPreferentialContent, String subPreferentialContent) {
        super(context, R.style.cashier__card_pay_guide_transparent_dialog);
        if(cashierPopWindowBean != null) {
            mGuideInfo = cashierPopWindowBean.getPopDetailInfo();
        }
        mOnClickPromotionPayTypeListener = onClickPromotionPayTypeListener;
        mMainPreferentialContent = mainPreferentialContent;
        mSubPreferentialContent = subPreferentialContent;
        initView();
    }

    private void initView() {
        addBusinessParameters();
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setCanceledOnTouchOutside(false);
        setContentView(R.layout.cashier_promotion_signed_guide_dialog);
        findViewById(R.id.promotion_signed_dialog_close).setOnClickListener(v -> {
            dismiss();
            CashierStaticsUtils.logModelEvent("c_PJmoK", "b_pay_ofkpwvjx_mc", "引导使用已有支付方式弹窗-主按钮-关闭按钮",
                    technologyParameters, StatisticsUtils.EventType.CLICK, getUniqueId());
        });
        if (mGuideInfo != null) {
            TextView title = findViewById(R.id.promotion_signed_dialog_title);
            title.setText(mGuideInfo.getTitle());
            TextView main = findViewById(R.id.promotion_signed_dialog_main_preferential);
            main.setText(mMainPreferentialContent);
            TextView sub = findViewById(R.id.promotion_signed_dialog_sub_preferential);
            sub.setText(mSubPreferentialContent);
            if (TextUtils.isEmpty(mSubPreferentialContent)) {
                sub.setVisibility(View.GONE);
            }
            ImageView payIcon = findViewById(R.id.promotion_dialog_pay_icon);
            MTPayment mtPayment = mGuideInfo.getGuidePayTypeInfo();
            Icon icon;
            if (mtPayment != null && (icon = mtPayment.getIcon()) != null && !TextUtils.isEmpty(icon.getEnable())) {
                WebpImageLoader.load(icon.getEnable(),
                        payIcon,
                        R.drawable.mpay__payment_default_pic,
                        R.drawable.mpay__payment_default_pic
                );
            }
            TextView paymentName = findViewById(R.id.promotion_dialog_payment_name);
            paymentName.setText(getNameText(mtPayment) + PAYMENT_SUFFIX);
            Button bottomButton = findViewById(R.id.promotion_signed_dialog_bottom_button);
            bottomButton.setText(mGuideInfo.getGuideButton());
            bottomButton.setOnClickListener(new NoDuplicateClickListener() {
                //设置防重复点击
                @Override
                public void onSingleClick(View v) {
                    dismiss();
                    WalletPayManager.getInstance().setOpenSource(getOwnerActivity(),
                            "promotion_signed_guide_popwindow");
                    if (mOnClickPromotionPayTypeListener != null) {
                        mOnClickPromotionPayTypeListener.onClickPromotionPayType(mGuideInfo.getGuidePayTypeInfo());
                    }
                    technologyParameters.put("open_source", "promotion_signed_guide_popwindow");
                    if (mGuideInfo != null) {
                        NativeStandardCashierPayProcessStatistic.putCreditPayInfo(technologyParameters, mGuideInfo.getGuidePayTypeInfo());
                    }
                    CashierStaticsUtils.logModelEvent("c_PJmoK", "b_pay_ma3yhfn3_mc", "引导使用已有支付方式弹窗-主按钮",
                            technologyParameters, StatisticsUtils.EventType.CLICK, getUniqueId());
                    if (mGuideInfo != null) {
                        analyseClickConfirmButton(mGuideInfo.getGuidePayTypeInfo());
                    }
                }
            }.setClickInternal(1000));
        }

        technologyParameters.put("open_source", "promotion_signed_guide_popwindow");
        if (mGuideInfo != null) {
            NativeStandardCashierPayProcessStatistic.putCreditPayInfo(technologyParameters, mGuideInfo.getGuidePayTypeInfo());
        }
        CashierStaticsUtils.logModelEvent("c_PJmoK", "b_pay_ue0rpr3c_mv", "引导使用已有支付方式弹窗",
                technologyParameters, StatisticsUtils.EventType.VIEW, getUniqueId());

    }

    /**
     * 上报弹窗的业务参数
     * "pop_scene"：支付前场景或者三方中断场景
     * "style_type"：为0时，代表老样式
     * "ad_id"：广告id，旧弹窗中为兜底值"-999"
     */
    private void addBusinessParameters(){
        technologyParameters = CashierStaticsUtils.getTechnologyParameters();
        if (mGuideInfo != null && !TextUtils.isEmpty(mGuideInfo.getPopScene())) {
            technologyParameters.put("pop_scene", mGuideInfo.getPopScene());
        }
        technologyParameters.put("style_type", "0");
        technologyParameters.put("ad_id", "-999");
        if (mGuideInfo != null
                && mGuideInfo.getGuidePayTypeInfo() != null
                && !TextUtils.isEmpty(mGuideInfo.getGuidePayTypeInfo().getPayType())){
            technologyParameters.put("pay_type", mGuideInfo.getGuidePayTypeInfo().getPayType());
        }
    }

    private void analyseClickConfirmButton(MTPayment guidePayTypeInfo) {
        if (guidePayTypeInfo == null) {
            return;
        }
        if (!TextUtils.isEmpty(guidePayTypeInfo.getPayType())) {
            HashMap<String, Object> map = new AnalyseUtils.MapBuilder().add("pay_type", guidePayTypeInfo.getPayType()).build();
            LoganUtils.log("standard_cashier_mt_pay_confirm", map);
            CashierStaticsUtils.logCustom("standard_cashier_mt_pay_confirm", map, null, getUniqueId());
        }
    }

    // 支付名称拼接
    protected String getNameText(MTPayment mtPayment) {
        if (mtPayment == null) {
            return "";
        }
        String nameStr = "";
        String nameSuffix = "";
        if (!TextUtils.isEmpty(mtPayment.getName())) {
            nameStr = mtPayment.getName();
        }
        // 区分银行卡和非银行
        if (PayTypeUtils.isBankcardPay(mtPayment.getPayType())) {
            nameSuffix = getBankNameExtText(mtPayment);
        } else if (!TextUtils.isEmpty(mtPayment.getNameSuffix())) {
            nameSuffix = mtPayment.getNameSuffix();
        }
        return nameStr + nameSuffix;
    }

    private String getBankNameExtText(MTPayment mtPayment) {
        if (mtPayment != null && mtPayment.getCardInfo() != null) {
            return mtPayment.getCardInfo().getNameExt();
        }
        return "";
    }

}
