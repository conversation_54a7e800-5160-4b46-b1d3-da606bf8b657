package com.meituan.android.cashier.widget;

import android.content.Context;
import android.support.v4.content.ContextCompat;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.meituan.android.cashier.R;
import com.meituan.android.pay.common.payment.bean.FloatingLayer;
import com.meituan.android.pay.common.payment.data.IPaymentData;
import com.meituan.android.pay.common.promotion.bean.CombineLabel;
import com.meituan.android.pay.common.promotion.bean.Material;
import com.meituan.android.pay.common.promotion.utils.CombineDiscountUtils;
import com.meituan.android.pay.desk.payment.discount.DiscountCashierUtils;
import com.meituan.android.pay.desk.payment.view.DiscountView;
import com.meituan.android.paybase.utils.CashAmountArithUtils;
import com.meituan.android.paybase.utils.Strings;

import java.util.List;

import static com.meituan.android.pay.common.promotion.utils.CombineDiscountUtils.DISCOUNT;
import static com.meituan.android.pay.common.promotion.utils.CombineDiscountUtils.DISCOUNT_REWARD;
import static com.meituan.android.pay.common.promotion.utils.CombineDiscountUtils.REWARD;
import static com.meituan.android.pay.common.promotion.utils.CombineDiscountUtils.getCombineDiscount;
import static com.meituan.android.pay.common.promotion.utils.CombineDiscountUtils.getCombineReward;

/**
 * author: luo jing
 * date: 4/16/21 17:29
 * description: 底部按钮上面的支付省钱视图（收银台首页使用）
 */

public class SaveMoneyDiscountView extends LinearLayout {
    private IPaymentData iPaymentData;
    private TextView saveMoneyDiscount;
    private TextView saveMoneyBrand;
    private TextView saveMoneyHint;
    private DiscountView.OnClickDiscountDetail onClickDiscountDetail;

    public SaveMoneyDiscountView(Context context) {
        super(context);
    }

    public SaveMoneyDiscountView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public void init(IPaymentData iPaymentData) {
        this.iPaymentData = iPaymentData;
        initView();
    }

    private void initView() {
        View rootView = LayoutInflater.from(getContext()).inflate(R.layout.cashier__save_money_discount_detail,
                this);
        saveMoneyBrand = rootView.findViewById(R.id.cashier__save_money_brand);
        saveMoneyHint = rootView.findViewById(R.id.cashier__save_money_hint);
        saveMoneyDiscount = rootView.findViewById(R.id.cashier__save_money_discount);
        saveMoneyDiscount.setOnClickListener(v -> {
            if (onClickDiscountDetail != null) {
                onClickDiscountDetail.onClick();
            }
        });
        FloatingLayer floatingLayer = DiscountCashierUtils.getDiscountFloatingLayer(iPaymentData);
        if (floatingLayer != null) {
            saveMoneyBrand.setText(floatingLayer.getFirstContent());
            saveMoneyHint.setText(floatingLayer.getSecondContent());
        }
        Material material = DiscountCashierUtils.getDiscountMaterial(iPaymentData);
        if (material != null) {
            saveMoneyDiscount.setText(material.getCheckDiscountPromoText());
        }
    }

    public void refreshView(IPaymentData checkedPaymentData, float discountMoney) {
        if (getContext() != null) {
            List<CombineLabel> labels = DiscountCashierUtils.getCombineLabel(checkedPaymentData);
            String discount = Strings.getFormattedDoubleValueWithZero(discountMoney);
            Material material = DiscountCashierUtils.getDiscountMaterial(checkedPaymentData);
            if (material != null && !TextUtils.isEmpty(material.getCheckDiscountPromoText())) {
                String checkDiscountPromoText = getReplaceString(material.getCheckDiscountPromoText(),
                        FloatingLayer.FLOATING_TYPE_DISCOUNT, discount);
                saveMoneyDiscount.setText(checkDiscountPromoText);
            } else { // 兜底展示
                saveMoneyDiscount.setText("-" + getContext().getString(R.string.mpay__money_prefix) + discount);
            }
            saveMoneyDiscount.setTextColor(ContextCompat.getColor(getContext(), R.color.paybase__notice_text3));

            int style = CombineDiscountUtils.getCombineLabelStyle(labels);
            if (isShowNoSelectDiscountPrompt(style, labels)) {
                String checkDiscountPromoText = (material != null && !TextUtils.isEmpty(material.getUncheckDiscountPromoText())
                        ? material.getUncheckDiscountPromoText() : getContext().getString(R.string.mpay__uncheck_discount_promo_text));
                saveMoneyDiscount.setTextColor(ContextCompat.getColor(getContext(),
                        R.color.paybase__hint_text_color));
                saveMoneyDiscount.setText(checkDiscountPromoText);
            }

            FloatingLayer floatingLayer = DiscountCashierUtils.getDiscountFloatingLayer(checkedPaymentData);
            if (floatingLayer != null && !TextUtils.isEmpty(floatingLayer.getSecondContent())) {
                saveMoneyHint.setText(floatingLayer.getSecondContent());
                saveMoneyHint.setVisibility(View.VISIBLE);
            } else {
                saveMoneyHint.setVisibility(View.GONE);
            }
            String brandText = floatingLayer != null && !TextUtils.isEmpty(floatingLayer.getFirstContent())
                    ? floatingLayer.getFirstContent() : getContext().getString(R.string.mpay__uncheck_discount_brand);
            saveMoneyBrand.setText(brandText);
        }
    }

    /**
     * 营销浮层右侧有一种特殊的文案提示"无选中优惠"。此文案出现的时机有三个：
     * 1 聚合营销标签只有返赠，但返赠都未勾选；
     * 2 聚合营销标签只有优惠，但优惠都未勾选；
     * 3 聚合营销标签有优惠也有返赠，但优惠和返赠都未勾选。
     */
    private boolean isShowNoSelectDiscountPrompt(int combineDiscountStyle, List<CombineLabel> labels) {
        return (combineDiscountStyle == REWARD && getCombineReward(labels) == 0) // 有返赠，返赠都未勾选
                || (combineDiscountStyle == DISCOUNT && CashAmountArithUtils.compare(getCombineDiscount(labels), 0) == 0) // 有优惠，优惠都未勾选
                || (combineDiscountStyle == DISCOUNT_REWARD && getCombineReward(labels) == 0 // 有优惠和返赠，优惠和返赠未勾选
                && CashAmountArithUtils.compare(getCombineDiscount(labels), 0) == 0);
    }

    private String getReplaceString(String source, String key, String value) {
        if (source != null && source.contains(key)) {
            source = source.replace(key, value);
        }
        return source;
    }

    public void setOnClickDiscountDetail(DiscountView.OnClickDiscountDetail onClickDiscountDetail) {
        this.onClickDiscountDetail = onClickDiscountDetail;
    }
}
