package com.meituan.android.cashier.business;

import android.support.v4.app.FragmentActivity;
import android.text.TextUtils;

import com.meituan.android.cashier.newrouter.CashierBusinessHandler;
import com.meituan.android.cashier.newrouter.NewCashierParams;
import com.meituan.android.pay.utils.DiscountMonitorHelper;
import com.meituan.android.paybase.moduleinterface.payment.PayActionListener;
import com.meituan.android.paybase.moduleinterface.payment.PayFailInfo;
import com.meituan.android.paybase.utils.ActivityStatusChecker;
import com.meituan.android.paybase.utils.CashierScreenSnapShotUtil;
import com.meituan.android.paybase.utils.CatchException;
import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.paybase.utils.JsonString;
import com.meituan.android.paycommon.lib.coupon.model.Promotion;
import com.meituan.android.paymentchannel.PayerMediator;
import com.meituan.android.paymentchannel.PayersID;
import com.meituan.android.payrouter.utils.ProxyUtils;

public class MTPayHandler extends CashierBusinessHandler implements PayActionListener {
    private static final String KEY_SIGN_BANK_PAY_FAILED = "refresh_page";
    private static final String MEITUAN_PAY_FAIL = "meituanpay_fail";
    private final MTPayResultHandler resultHandler;

    public MTPayHandler(FragmentActivity activity, NewCashierParams cashierParams, MTPayResultHandler resultHandler) {
        super(activity, cashierParams);
        this.resultHandler = resultHandler;
    }

    /**
     * 启动美团支付流程
     * @param url 支付需要的参数
     */
    public void startMTPay(String payType, String url) {
        FragmentActivity activity = getActivity();
        if (ActivityStatusChecker.isValid(activity)) {
            PayerMediator.getInstance().startPay(activity, PayersID.ID_MEITUANPAY, url, getCashierParams().getTradeNo(), this);
        }
    }

    public boolean handleMTPayResult(String payType, int payResult, PayFailInfo payFailInfo) {
        if (!TextUtils.equals(PayersID.ID_MEITUANPAY, payType)) {
            return false;
        }
        if (payResult == PayActionListener.SUCCESS) {
            onMTPaySuccess(payType, payFailInfo);
        } else if (payResult == PayActionListener.CANCEL) {
            getResultHandler().onMTPayCancel();
        } else if (payResult == PayActionListener.FAIL) {
            if (payFailInfo != null && JsonString.parser(payFailInfo.getExtra()).is(KEY_SIGN_BANK_PAY_FAILED)) { // 针对KEY_SIGN_BANK_PAY_FAILED的场景，转换为fatalError进行处理
                getResultHandler().onMTPayFatalError(null);
            } else {
                getResultHandler().onMTPayFail(); // 其他的还是按照Fail进行处理
            }
        } else if (payResult == PayActionListener.OVER_TIME) {
            getResultHandler().onMTPayOverTime();
        } else if (payResult == PayActionListener.PAY_FATAL_ERROR) {
            getResultHandler().onMTPayFatalError(MEITUAN_PAY_FAIL);
        }
        return true;
    }

    private void onMTPaySuccess(String payType, PayFailInfo failInfo) {
        if (failInfo == null) {
            getResultHandler().onMTPaySuccess(null);
            return;
        }
        // TODO 这里是从PayFailInfo里拿美团支付的Promotion数据，应该放在非Fail字段下，后续需要修改
        Promotion promotion = CatchException.run(() -> GsonProvider.getInstance()
                .fromJson(JsonString.parser(failInfo.getExtra()).get("pay_promotion"), Promotion.class)).value();
        // 营销金额监控，把相关逻辑转移到DiscountMonitorHelper中了
        DiscountMonitorHelper.getInstance().saveFinalMoneyAndReport(payType,
                promotion, getCashierParams().getCashierUniqueId());
        CashierScreenSnapShotUtil.captureSnapShot(getActivity(), success -> {
            getResultHandler().onMTPaySuccess(promotion);
        });
    }

    @Override
    public void onPayPreExecute(String payType) {

    }

    @Override
    public void onGotPayResult(String payType, int payResult, PayFailInfo payFailInfo) {
        handleMTPayResult(payType, payResult, payFailInfo);
    }

    private MTPayResultHandler getResultHandler() {
        return ProxyUtils.nonNullObject(MTPayResultHandler.class, resultHandler);
    }
}
