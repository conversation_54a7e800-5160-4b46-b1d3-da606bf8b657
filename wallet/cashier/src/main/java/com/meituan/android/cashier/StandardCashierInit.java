package com.meituan.android.cashier;


import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;

import com.meituan.android.paybase.config.PayBaseConfig;
import com.meituan.android.paycommon.lib.IInitSDK;
import com.meituan.android.paymentchannel.utils.UPPayUtils;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

@ServiceLoaderInterface(key = "standardCashier", interfaceClass = IInitSDK.class)
public class StandardCashierInit implements IInitSDK {
    @Override
    public void onInit(@NonNull Context context) {
        // 该方法在App启动时会调用，建议延迟几秒再查询降低卡顿的概率。
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            try {
                if (UPPayUtils.isCheckStatusEqualNull()) {
                    UPPayUtils.startToGetSEPayInfo(PayBaseConfig.getProvider().getApplicationContext());
                }
            } catch (Throwable throwable) {
                throwable.printStackTrace();
            }
        }, 5 * 1000);
    }
}
