package com.meituan.android.cashier.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.widget.Space;
import android.text.Html;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.Display;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import com.meituan.android.cashier.R;
import com.meituan.android.cashier.model.bean.PayLaterAgreementBean;
import com.meituan.android.cashier.model.bean.PayLaterPopDetailInfoBean;
import com.meituan.android.paybase.dialog.BaseDialog;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.UriUtils;
import com.meituan.android.paycommon.lib.config.MTPayConfig;
import com.meituan.android.paycommon.lib.utils.WebpImageLoader;

import java.util.List;

public class PayLaterGuideDialog extends BaseDialog {
    private static final long TOAST_TIME = 3000;
    private boolean isAgreeCheckBox = false;
    private CheckBox agreementCheck;
    private Runnable toastRunnable;
    @SuppressLint("InflateParams")
    private  View toast;
    @SuppressLint("InflateParams")
    private final ViewGroup viewParent;
    @Nullable
    private PayLaterGuideDialog.OnClickPayLaterGuideButtonListener mOnClickPayLaterGuideButtonListener;
    @NonNull
    private final PayLaterPopDetailInfoBean mPayLaterPopDetailInfoBean;

    @SuppressLint("InflateParams")
    public PayLaterGuideDialog(Context context, @NonNull PayLaterPopDetailInfoBean payLaterPopDetailInfoBean, @Nullable PayLaterGuideDialog.OnClickPayLaterGuideButtonListener onClickPayLaterGuideButtonListener) {
        super(context, R.style.mpay__TransparentDialog);
        this.mPayLaterPopDetailInfoBean = payLaterPopDetailInfoBean;
        this.mOnClickPayLaterGuideButtonListener = onClickPayLaterGuideButtonListener;
        viewParent = (ViewGroup) LayoutInflater.from(getContext()).inflate(R.layout.cashier__paylater_guide_dialog, null);
        ViewGroup group = viewParent.findViewById(R.id.dialog_root);
        setContentView(viewParent, getViewLayoutParam());
        initView(group);
    }


    public void setOnClickPayLaterGuideButtonListener(OnClickPayLaterGuideButtonListener onClickPayLaterGuideButtonListener) {
        this.mOnClickPayLaterGuideButtonListener = onClickPayLaterGuideButtonListener;
    }

    private ViewGroup.LayoutParams getViewLayoutParam() {
        return new ViewGroup.LayoutParams(getDialogWidth(), ViewGroup.LayoutParams.WRAP_CONTENT);
    }

    private int getDialogWidth() {
        WindowManager windowManager = (WindowManager) MTPayConfig.getProvider().getApplicationContext().getSystemService(Activity.WINDOW_SERVICE);
        Display display = windowManager.getDefaultDisplay();
        return (int) (display.getWidth() * 0.827f);
    }

    private void initView(View contentView) {
        setCanceledOnTouchOutside(false);
        initHeadView(contentView);
        initDescView(contentView);
        initGuidePictureView(contentView);
        if (contentView instanceof ViewGroup) {
            if (isShowCheckBox()) {
                initAgreementView((ViewGroup) contentView);
                initButton((ViewGroup) contentView);
            } else {
                initButton((ViewGroup) contentView);
                initAgreementView((ViewGroup) contentView);
            }
        }
    }

    private void initButton(ViewGroup contentView) {
        View buttonRootView = LayoutInflater.from(getContext()).inflate(R.layout.cashier__paylater_guide_dialog_button_view, contentView);

        TextView cancel = buttonRootView.findViewById(R.id.cancel);
        cancel.setText(mPayLaterPopDetailInfoBean.getLbtn());
        cancel.setOnClickListener(view -> {
            if (isShowCheckBox()){
                removeToastCallback();
            }
            if (mOnClickPayLaterGuideButtonListener != null) {
                mOnClickPayLaterGuideButtonListener.onClickPayLaterGuideCancel();
            }
        });
        TextView ensure = contentView.findViewById(R.id.ensure);
        ensure.setText(mPayLaterPopDetailInfoBean.getRbtn());
        ensure.setOnClickListener(view -> {
            if (isShowCheckBox() && !isAgreeCheckBox) {
                showAgreementCheckToast();
            } else {
                removeToastCallback();
                if (mOnClickPayLaterGuideButtonListener != null) {
                    mOnClickPayLaterGuideButtonListener.onClickPayLaterGuideOpen();
                }
            }
        });
        TextView discountIcon = contentView.findViewById(R.id.discount_icon);
        // 设置最大长度为按钮的一半
        discountIcon.setMaxWidth((int) (getDialogWidth()/2.0-getContext().getResources().getDimensionPixelSize(R.dimen.cashier__paylater_guide_dialog_horizontal_padding)));
        if (TextUtils.isEmpty(mPayLaterPopDetailInfoBean.getPromoBubble())) {
            discountIcon.setVisibility(View.GONE);
        } else {
            discountIcon.setVisibility(View.VISIBLE);
            discountIcon.setText(mPayLaterPopDetailInfoBean.getPromoBubble());
        }
    }

    private void initAgreementView(ViewGroup contentView) {
        @SuppressLint("InflateParams")
        View agreementRootView = LayoutInflater.from(getContext()).inflate(R.layout.cashier__paylater_guide_dialog_agreement_view, contentView);
        TextView agreement = agreementRootView.findViewById(R.id.agreement);
        String agreementDesc = getContext().getResources().getString(R.string.cashier__paylater_agreement_desc);
        if (isShowCheckBox()) {
            agreementDesc = getContext().getResources().getString(R.string.cashier__paylater_agreement_desc_with_check_box);
            ViewGroup   agreementCheckRoot = agreementRootView.findViewById(R.id.cashier__paylater_guide_dialog_agreement_checkbox);
            agreementCheck = agreementRootView.findViewById(R.id.cashier__paylater_guide_dialog_agreement_checkbox_view);
            agreementCheckRoot.setVisibility(View.VISIBLE);
            agreementCheck.setVisibility(View.VISIBLE);
            agreementCheckRoot.setOnClickListener((v -> {
                this.isAgreeCheckBox =!this.isAgreeCheckBox;
                this.agreementCheck.setChecked(this.isAgreeCheckBox);
                if (this.isAgreeCheckBox){
                    this.removeToastCallback();
                    if (toast != null ) {
                        toast.setVisibility(View.GONE);
                    }
                }
            }));
        }


        List<PayLaterAgreementBean> payLaterAgreementBeans = mPayLaterPopDetailInfoBean.getAgreementList();
        if (CollectionUtils.isEmpty(payLaterAgreementBeans)) {
            return;
        }
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder(agreementDesc);
        for (PayLaterAgreementBean payLaterAgreementBean : payLaterAgreementBeans) {
            spannableStringBuilder.append(payLaterAgreementBean.getAgreementName());
            ClickableSpan clickableSpan = new ClickableSpan() {
                @Override
                public void onClick(@NonNull View view) {
                    UriUtils.open(getContext(), payLaterAgreementBean.getAgreementLink());
                }

                @Override
                public void updateDrawState(@NonNull TextPaint ds) {
                    super.updateDrawState(ds);
                    ds.setUnderlineText(false);
                }
            };
            spannableStringBuilder.setSpan(clickableSpan, spannableStringBuilder.length() - payLaterAgreementBean.getAgreementName().length(), spannableStringBuilder.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        }
        ForegroundColorSpan linkColorSpan = new ForegroundColorSpan(getContext().getResources().getColor(R.color.cashier__bg_paylater_guide_dialog_lint_color));
        spannableStringBuilder.setSpan(linkColorSpan, agreementDesc.length(), spannableStringBuilder.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);

        ForegroundColorSpan descColorSpan = new ForegroundColorSpan(getContext().getResources().getColor(R.color.cashier__bg_paylater_guide_agreement_desc_color));
        spannableStringBuilder.setSpan(descColorSpan, 0, agreementDesc.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        agreement.setText(spannableStringBuilder);
        agreement.setMovementMethod(LinkMovementMethod.getInstance());
        agreement.setHighlightColor(getContext().getResources().getColor(android.R.color.transparent));
    }

    private boolean existGuiPicture() {
        return !TextUtils.isEmpty(mPayLaterPopDetailInfoBean.getGuidePicture());
    }

    private void initGuidePictureView(View contentView) {
        ImageView guidePicture = contentView.findViewById(R.id.guide_picture);
        Space guidePicturePlaceHolder = contentView.findViewById(R.id.guide_picture_placeholder);
        ViewGroup.LayoutParams layoutParams = guidePicture.getLayoutParams();
        layoutParams.height = (int) ((getDialogWidth() - 2 * getContext().getResources().getDimensionPixelSize(R.dimen.cashier__paylater_guide_dialog_horizontal_padding)) * 8 / 31f);
        guidePicture.setLayoutParams(layoutParams);
        if (existGuiPicture()) {
            guidePicture.setVisibility(View.VISIBLE);
            guidePicturePlaceHolder.setVisibility(View.GONE);
            WebpImageLoader.load(mPayLaterPopDetailInfoBean.getGuidePicture(), guidePicture);
        } else {
            guidePicture.setVisibility(View.GONE);
            guidePicturePlaceHolder.setVisibility(View.VISIBLE);
        }
    }

    private void initDescView(View contentView) {
        TextView descTv = contentView.findViewById(R.id.desc);
        if (!TextUtils.isEmpty(mPayLaterPopDetailInfoBean.getDetail())) {
            descTv.setText(Html.fromHtml(mPayLaterPopDetailInfoBean.getDetail()));
        }
    }

    private void initHeadView(View contentView) {
        TextView titleTv = contentView.findViewById(R.id.title);
        ImageView businessLogo = contentView.findViewById(R.id.business_logo);
        if (!TextUtils.isEmpty(mPayLaterPopDetailInfoBean.getTitle())) {
            titleTv.setText(mPayLaterPopDetailInfoBean.getTitle());
        }

        View scoreContainer = contentView.findViewById(R.id.score_container);
        TextView scoreTv = contentView.findViewById(R.id.score);
        TextView scoreNameTv = contentView.findViewById(R.id.score_name);
        if (isShowBeliveScore()) {
            scoreContainer.setVisibility(View.VISIBLE);
            scoreTv.setText(String.valueOf(mPayLaterPopDetailInfoBean.getScore()));
            scoreNameTv.setText(mPayLaterPopDetailInfoBean.getScoreName());
            businessLogo.setVisibility(View.GONE);
        } else {
            scoreContainer.setVisibility(View.GONE);
            businessLogo.setVisibility(View.VISIBLE);
            WebpImageLoader.load(mPayLaterPopDetailInfoBean.getBelieveScoreBizLogo(), businessLogo);
        }
    }

    private boolean isShowBeliveScore() {
        return mPayLaterPopDetailInfoBean.getScore() > PayLaterPopDetailInfoBean.MIN_SCORE;
    }

    private boolean isShowCheckBox(){
      return mPayLaterPopDetailInfoBean.isNeedUserCheck();
    }

    @SuppressLint("InflateParams")
    private void showAgreementCheckToast() {

        if (toast != null) {
            if (toast.getVisibility() == View.GONE) {
                toast.setVisibility(View.VISIBLE);
            }
         this.removeToastCallback();
        } else {
            int[] location = new int[2];
            agreementCheck.getLocationInWindow(location);
            toast = LayoutInflater.from(getContext()).inflate(R.layout.cashier__paylater_guide_dialog_toast, null);
            int toastHeightInDp = (int) getContext().getResources().getDimensionPixelSize(R.dimen.cashier__paylater_guide_dialog_toast_height);
            toast.setY(location[1] - toastHeightInDp);
            viewParent.addView(toast, -1);
            toastRunnable = () -> {
                if (toast != null ) {
                    toast.setVisibility(View.GONE);
                }
            };
        }
        if (toast == null || toastRunnable == null) {
            return;
        }
        toast.postDelayed(toastRunnable, TOAST_TIME);
    }
    private void removeToastCallback(){
        if (toast != null && toastRunnable != null) {
            toast.removeCallbacks(toastRunnable);
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        removeToastCallback();
        toastRunnable = null;
    }

    public interface OnClickPayLaterGuideButtonListener {
        void onClickPayLaterGuideOpen();

        void onClickPayLaterGuideCancel();
    }
}

