package com.meituan.android.cashier.business;

import android.app.Activity;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.annotation.StringRes;

import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.common.R;
import com.meituan.android.paybase.common.dialog.PayDialog;
import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.retrofit.PayException;
import com.meituan.android.paycommon.lib.config.MTPayConfig;
import com.meituan.android.paycommon.lib.utils.ExceptionUtils;

import java.lang.ref.WeakReference;

public class PayExceptionHandler {
    private final WeakReference<Activity> weakActivity;

    public PayExceptionHandler(Activity activity) {
        this.weakActivity = new WeakReference<>(activity);
    }

    public void handleException(Exception exception) {
        if (exception instanceof PayException) {
            handlePayException((PayException) exception);
        } else {
            handleNonPayException(exception);
        }
    }

    protected void handlePayException(@NonNull PayException payException) {
        switch (payException.getLevel()) {
            case PayException.RESP_LEVEL_1:
                handleLevel1PayException(payException);
                break;

            case PayException.RESP_LEVEL_2:
                handleLevel2PayException(payException);
                break;

            case PayException.RESP_LEVEL_3:
                handleLevel3PayException(payException);
                break;

            case PayException.RESP_LEVEL_4:
                handleLevel4PayException(payException);
                break;

            case PayException.RESP_LEVEL_5:
                handleLevel5PayException(payException);
                break;

            case PayException.RESP_LEVEL_6:
                handleLevel6PayException(payException);
                break;

            default:
                handleLevelPayException(payException);
                break;
        }
    }

    protected void handleLevel1PayException(PayException payException) {
        showToast(payException.getMessage(), payException.getErrorCodeStr());
    }

    protected void handleLevel2PayException(PayException payException) {
        ExceptionUtils.alertAndFinish(getActivity(), payException.getMessage(), payException.getErrorCodeStr(), MTCashierActivity.class);
    }

    protected void handleLevel3PayException(PayException payException) {
        Activity activity = getActivity();
        if (activity != null) {
            new PayDialog.Builder(activity).msg(payException.getMessage()).subMsg(payException.getErrorCodeStr()).build().show();
        }
    }

    protected void handleLevel4PayException(PayException payException) {
        showToast(payException.getMessage(), payException.getErrorCodeStr());
    }

    protected void handleLevel5PayException(PayException payException) {
        showToast(payException.getMessage(), payException.getErrorCodeStr());
    }

    protected void handleLevel6PayException(PayException payException) {
        showToast(payException.getMessage(), payException.getErrorCodeStr());
    }

    protected void handleLevelPayException(PayException payException) {
        showToast(payException.getMessage(), payException.getErrorCodeStr());
    }

    protected void handleNonPayException(@Nullable Exception exception) {
        showToast(getString(R.string.paycommon__error_msg_load_later));
    }

    protected void showToast(String message) {
        ToastUtils.showSnackToast(getActivity(), message);
    }

    protected void showToast(String message, String subMessage) {
        ToastUtils.showSnackToast(getActivity(), message, subMessage);
    }

    protected Activity getActivity() {
        return weakActivity.get();
    }

    protected String getString(@StringRes int resId) {
        return MTPayConfig.getProvider().getApplicationContext().getString(resId);
    }

}
