package com.meituan.android.cashier.base.utils;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.meituan.android.cashier.R;
import com.meituan.android.cashier.common.CashierCatConstants;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.model.bean.Cashier;
import com.meituan.android.cashier.model.bean.CashierPayment;
import com.meituan.android.cashier.model.bean.CashierPopWindowBean;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.pay.common.payment.data.IBankcardData;
import com.meituan.android.pay.common.payment.data.IPaymentData;
import com.meituan.android.pay.common.payment.data.PayStatus;
import com.meituan.android.pay.common.payment.data.PayType;
import com.meituan.android.pay.common.payment.utils.PayTypeUtils;
import com.meituan.android.pay.common.promotion.bean.CombineLabel;
import com.meituan.android.pay.common.promotion.bean.ReduceInfo;
import com.meituan.android.pay.common.selectdialog.bean.WalletPaymentListPage;
import com.meituan.android.pay.desk.payment.bean.standarddesk.WalletPayment;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.common.analyse.cat.CatUtils;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.widgets.label.Label;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.meituan.android.pay.common.payment.utils.PaymentListUtils.isAbnormal;
import static com.meituan.android.pay.desk.component.analyse.DeskAnalyseUtils.reportLabel;

/**
 * author: luo jing
 * date: 2018/10/26 20:54
 * description: cashier analyse class
 */

public final class CashierAnalyseUtils {
    private static final String TAG = "CashierAnalyseUtils";
    private static final boolean DEBUG = false;

    private CashierAnalyseUtils() {

    }

    //获取信用付状态，0：不展示；1：不可用；2：可用&需签约；3：可用（无需签约）
    public static int getCreditPayStatus(Cashier cashier) {
        List<CashierPayment> list = cashier.getPaymentDataList();
        if (!CollectionUtils.isEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                CashierPayment cashierPayment = list.get(i);
                if (PayTypeUtils.isWalletPay(cashierPayment.getPayType())) {
                    List<MTPayment> mtPaymentList = cashierPayment.getRecommendPayment();
                    if (!CollectionUtils.isEmpty(mtPaymentList)) {
                        for (MTPayment mtPayment : mtPaymentList) {
                            if (PayTypeUtils.isCreditPay(mtPayment.getPayType())) {
                                if (mtPayment.getStatus() == PayStatus.NORMAL
                                        || mtPayment.getStatus() == PayStatus.ACTIVE) {
                                    if (mtPayment.getAgreement() != null) {
                                        return 2;
                                    } else {
                                        return 3;
                                    }
                                } else {
                                    return 1;
                                }
                            }
                        }
                    }
                }
            }
        }
        return 0;
    }

    // 获取信用付实名状态  用于埋点
    public static int getCreditPayAuthType(Cashier cashier) {
        List<CashierPayment> list = cashier.getPaymentDataList();
        if (!com.meituan.android.paybase.utils.CollectionUtils.isEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                CashierPayment cashierPayment = list.get(i);
                if (PayTypeUtils.isWalletPay(cashierPayment.getPayType())) {
                    List<MTPayment> mtPaymentList = cashierPayment.getRecommendPayment();
                    if (!com.meituan.android.paybase.utils.CollectionUtils.isEmpty(mtPaymentList)) {
                        for (MTPayment mtPayment : mtPaymentList) {
                            if (PayTypeUtils.isCreditPay(mtPayment.getPayType())) {
                                return mtPayment.getRealNameAuthType();
                            }
                        }
                    }
                }
            }
        }
        return -1;
    }

    // 一级支付方式和美团支付二级支付方式 用于埋点
    public static String getAllPayType(Cashier cashier) {
        StringBuilder stringBuilder = new StringBuilder();
        List<CashierPayment> list = cashier.getPaymentDataList();
        if (!com.meituan.android.paybase.utils.CollectionUtils.isEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                CashierPayment cashierPayment = list.get(i);
                stringBuilder.append(cashierPayment.getPayType());
                stringBuilder.append("|");
                if (PayTypeUtils.isWalletPay(cashierPayment.getPayType())) {
                    List<MTPayment> mtPaymentList = cashierPayment.getRecommendPayment();
                    if (!com.meituan.android.paybase.utils.CollectionUtils.isEmpty(mtPaymentList)) {
                        for (MTPayment mtPayment : mtPaymentList) {
                            stringBuilder.append(mtPayment.getPayType());
                            stringBuilder.append("|");
                        }
                    }
                }
            }
        }
        return stringBuilder.toString();
    }

    public static Map<String, Object> getPayTypeMgeLab(IPaymentData iPaymentData) {
        HashMap<String, Object> mgeLab = new HashMap<>();
        mgeLab.put("tradeNo", AnalyseUtils.getCashierTradeNo());
        mgeLab.put("platform", "android");
        mgeLab.put("pay_type", iPaymentData.getPayType());
        mgeLab.put("is_select", iPaymentData.isSelected());
        if (iPaymentData.getPaymentReduce() != null) {
            ReduceInfo reduceInfo = iPaymentData.getPaymentReduce().getNoBalanceReduceInfo();
            if (reduceInfo != null) {
                mgeLab.put("activity_id", reduceInfo.getCampaignId());
            }
        }
        ArrayList<String> labelsContent = new ArrayList<>();
        List<CombineLabel> rightLabels = iPaymentData.getRightLabels();
        if (!CollectionUtils.isEmpty(rightLabels)) {
            for (Label label : rightLabels) {
                labelsContent.add(label.getContent());
            }
        }
        List<CombineLabel> bottomLabels = iPaymentData.getBottomLabels();
        if (!CollectionUtils.isEmpty(bottomLabels)) {
            for (Label label : bottomLabels) {
                labelsContent.add(label.getContent());
            }
        }
        if (!CollectionUtils.isEmpty(labelsContent)) {
            mgeLab.put("activity_tip", labelsContent);
        }
        if (iPaymentData instanceof CashierPayment) {
            mgeLab.put("is_folded", ((CashierPayment) iPaymentData).isFolded());
        }
        return mgeLab;
    }

    public static Map<String, Object> reportCashierPayment(IPaymentData iPaymentData) {
        HashMap<String, Object> mgeLab = new HashMap<>();
        mgeLab.put("tradeNo", AnalyseUtils.getCashierTradeNo());
        mgeLab.put("platform", "android");
        mgeLab.put("pay_type", iPaymentData.getPayType());
        mgeLab.put("is_select", iPaymentData.isSelected());
        JsonArray jsonArray = new JsonArray();
        JsonObject rightLabels = reportLabel(iPaymentData.getRightLabels(), 0);
        JsonObject bottomLabels = reportLabel(iPaymentData.getBottomLabels(), 1);
        if (rightLabels != null && !rightLabels.isJsonNull()) {
            jsonArray.add(rightLabels);
        }
        if (bottomLabels != null && !bottomLabels.isJsonNull()) {
            jsonArray.add(bottomLabels);
        }
        mgeLab.put("activity", jsonArray.size() > 0 ? jsonArray.toString() : "-999");

        if (iPaymentData instanceof CashierPayment) {
            mgeLab.put("is_folded", ((CashierPayment) iPaymentData).isFolded());
        }
        return mgeLab;
    }

    public static void logDefaultSelectedPayType(Context context, Cashier cashier) {
        if (cashier == null || context == null) {
            return;
        }
        CashierPayment cashierPayment = null;
        int selectedCount = 0;
        int usableCount = 0;
        List<CashierPayment> list = cashier.getPaymentDataList();
        if (!CollectionUtils.isEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i) != null) {
                    if (PayTypeUtils.isWalletPay(list.get(i).getPayType())) {
                        List<MTPayment> mtPaymentList = list.get(i).getRecommendPayment();
                        if (!CollectionUtils.isEmpty(mtPaymentList)) {
                            for (MTPayment mtPayment : mtPaymentList) {
                                if (mtPayment.isSelected()) {
                                    selectedCount++;
                                    if (DEBUG) {
                                        Log.d(TAG, "logDefaultSelectedPayType: " + mtPayment.getPayType());
                                    }
                                }
                            }
                        }
                    } else {
                        if (list.get(i).isSelected()) {
                            selectedCount++;
                        }
                    }
                    if (!isAbnormal(list.get(i).getStatus())) {
                        usableCount++;
                    }
                    if (PayTypeUtils.isWalletPay(list.get(i).getPayType())) {
                        cashierPayment = list.get(i);
                    }
                }
            }
            if (selectedCount == 0) {
                CatUtils.logError("noSelectedPayType",
                        context.getString(R.string.cashier__no_selected_pay_type));
                AnalyseUtils.techMis("b_zsalpxsl", null);
            } else if (selectedCount > 2) {
                // 美团支付二级支付方式始终有一个是Selected，当默选第三方支付时，收银台就有两种支付方式Selected
                CatUtils.logError("multiSelectedPayTypes",
                        context.getString(R.string.cashier__multi_selected_pay_types));
                AnalyseUtils.techMis("b_pay_8c8s2vxw_mv", null);
            } else {
                // do nothing
            }

            if (usableCount == 0) {
                CatUtils.logError("allPayTypesInvalid",
                        context.getString(R.string.cashier__all_pay_types_invalid));
            }
            if (cashierPayment != null) {
                logMtPaymentStatus(cashierPayment);
            }
        }
    }

    private static void logMtPaymentStatus(CashierPayment cashierPayment) {
        if (cashierPayment != null) {
            int cashierPaymentStatus = cashierPayment.getStatus();
            if (cashierPaymentStatus == PayStatus.ERROR) {
                CatUtils.logRate(CashierCatConstants.ACTION_MT_PAY_STATUS,
                        CashierCatConstants.CODE_MT_PAY_ERROR);
            } else if (cashierPaymentStatus == PayStatus.ACTIVE) {
                CatUtils.logRate(CashierCatConstants.ACTION_MT_PAY_STATUS,
                        CashierCatConstants.CODE_MT_PAY_ACTIVE);
            } else if (cashierPaymentStatus == PayStatus.OVER_AMOUNT) {
                CatUtils.logRate(CashierCatConstants.ACTION_MT_PAY_STATUS,
                        CashierCatConstants.CODE_MT_PAY_OVER_AMOUNT);
            } else {
                CatUtils.logRate(CashierCatConstants.ACTION_MT_PAY_STATUS,
                        CashierCatConstants.CODE_MT_PAY_NORMAL);
            }
        }
    }

    /**
     * 刷新收银台时上报埋点
     *
     * @param refreshScene
     */
    public static void recordRefreshingCashier(String refreshScene, String uniqueId) {
        Map<String, Object> map = new HashMap<>(1);
        map.put("refresh_scene", refreshScene);
        CashierStaticsUtils.techMis("b_pay_4gjqy71v_sc", map, uniqueId);
    }

    /**
     * 获取余额状态
     */
    public static int getBalanceStatus(WalletPayment walletPayment) {
        if (walletPayment == null || walletPayment.getWalletPaymentListPage() == null
                || CollectionUtils.isEmpty(walletPayment.getWalletPaymentListPage().getMtPaymentList())) {
            return 0;
        }
        WalletPaymentListPage listPage = walletPayment.getWalletPaymentListPage();
        List<IBankcardData> list = listPage.getMtPaymentList();
        for (IBankcardData iBankcardData : list) {
            if (PayTypeUtils.isBalancePay(iBankcardData.getPayType())) {
                return iBankcardData.getStatus();
            }
        }
        return 0;
    }

    /**
     * 0：正常折叠；1：正常不折叠；2：不可用；3：不展示
     */
    public static int getAliPayStatus(Cashier cashier) {
        if (cashier == null || CollectionUtils.isEmpty(cashier.getPaymentDataList())) {
            return 0;
        }
        List<CashierPayment> list = cashier.getPaymentDataList();
        for (CashierPayment cashierPayment : list) {
            if (TextUtils.equals(PayType.ALIPAY_SIMPLE, cashierPayment.getPayType())) {
                if (PayStatus.NORMAL == cashierPayment.getStatus()) {
                    return cashierPayment.isFolded() ? 0 : 1;
                } else {
                    return 2;
                }
            }
        }
        return 3;
    }

    public static IBankcardData getCardPay(WalletPayment walletPayment) {
        if (walletPayment == null || walletPayment.getWalletPaymentListPage() == null
                || CollectionUtils.isEmpty(walletPayment.getWalletPaymentListPage().getMtPaymentList())) {
            return null;
        }
        WalletPaymentListPage listPage = walletPayment.getWalletPaymentListPage();
        List<IBankcardData> list = listPage.getMtPaymentList();
        for (IBankcardData iBankcardData : list) {
            if (PayTypeUtils.isNewCardPay(iBankcardData.getPayType())) {
                return iBankcardData;
            }
        }
        return null;
    }

    public static IPaymentData getRecommendCardPay(WalletPayment walletPayment) {
        if (walletPayment == null || CollectionUtils.isEmpty(walletPayment.getRecommendPayment())) {
            return null;
        }
        List<MTPayment> list = walletPayment.getRecommendPayment();
        for (MTPayment mtPayment : list) {
            if (PayTypeUtils.isNewCardPay(mtPayment.getPayType())) {
                return mtPayment;
            }
        }
        return null;
    }

    public static boolean isPromotionScene(CashierPopWindowBean cashierPopWindowBean) {
        if (cashierPopWindowBean != null) {
            return cashierPopWindowBean.getType() == CashierPopWindowBean.PROMOTION_SIGNED_PAY_GUIDE
                    || cashierPopWindowBean.getType() == CashierPopWindowBean.PROMOTION_BINDED_CARD_PAY_GUIDE;
        }
        return false;
    }
}
