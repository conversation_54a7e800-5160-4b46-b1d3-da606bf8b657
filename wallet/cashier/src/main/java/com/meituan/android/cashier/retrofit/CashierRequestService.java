package com.meituan.android.cashier.retrofit;

import com.meituan.android.cashier.model.bean.MTPaymentURL;
import com.meituan.android.cashier.model.bean.OrderResult;
import com.meituan.android.cashier.model.bean.PayResult;
import com.meituan.android.cashier.model.bean.RetainWindow;
import com.meituan.android.cashier.model.bean.RouteInfo;
import com.sankuai.meituan.retrofit2.Call;
import com.sankuai.meituan.retrofit2.http.Field;
import com.sankuai.meituan.retrofit2.http.FieldMap;
import com.sankuai.meituan.retrofit2.http.FormUrlEncoded;
import com.sankuai.meituan.retrofit2.http.POST;
import com.sankuai.meituan.retrofit2.http.Path;

import java.util.HashMap;

/**
 * Created by z<PERSON><PERSON><PERSON><PERSON> on 2017/7/12.
 */

public interface CashierRequestService {
//    @FormUrlEncoded
//    @POST("/cashier/querywxnpopen")
//    Call<WechatPayWithoutPswResult> queryWechatNoPassOpen(@Field("tradeno") String tradeNo,
//                                                          @Field("pay_token") String payToken,
//                                                          @Field("nb_source") String nbSource,
//                                                          @Field("nb_fingerprint") String fingerprint);

    @FormUrlEncoded
    @POST("/cashier/dispatcher")
    Call<RouteInfo> startRouting(@Field("tradeno") String tradeNo,
                                 @Field("pay_token") String payToken,
                                 @Field("rooted") String isRooted,
                                 @Field("installed_apps") String installedApp,
                                 @Field("callback_url") String callbackUrl,
                                 @Field("dispatcher_scene") String dispatcherScene,
                                 @Field("nb_fingerprint") String fingerprint,
                                 @Field("upsepay_type") String upsepayType,
                                 @Field("ext_param") String extParam,
                                 @Field("guide_plan_infos") String guidePlanInfos,
                                 @Field("outer_business_data") String outerBusinessData,
                                 @Field("ext_dim_stat") String extDimStat,
                                 @FieldMap HashMap<String, String> extendTransmissionParams);

    @FormUrlEncoded
    @POST("/cashier/gohellopay")
    Call<MTPaymentURL> goHelloPay(@FieldMap HashMap<String, String> map);

    @FormUrlEncoded
    @POST("{path}")
    Call<MTPaymentURL> goHelloPay(@Path(value = "path", encoded = true) String path,
                                  @FieldMap HashMap<String, String> map,
                                  @Field("nb_fingerprint") String fingerprint);

    @FormUrlEncoded
    @POST("/cashier/queryorder")
    Call<OrderResult> queryOrder(@Field("tradeno") String tradeNo,
                                 @Field("pay_token") String payToken,
                                 @Field("isauto") String isAuto,
                                 @Field("outer_business_data") String outerBusinessData,
                                 @Field("ext_dim_stat") String extDimStat,
                                 @FieldMap HashMap<String, String> extendTransmissionParams);

    @FormUrlEncoded
    @POST("/cashier/limitguidewxnpopen")
    Call<Boolean> sendWxnpAction(@Field("tradeno") String tradeNo,
                                 @Field("pay_token") String payToken,
                                 @Field("type") String type,
                                 @Field("nb_fingerprint") String fingerprint);

    @FormUrlEncoded
    @POST("/cashier/directpay")
    Call<PayResult> startDirectPay(@FieldMap HashMap<String, String> map,
                                   @Field("nb_fingerprint") String fingerprint,
                                   @Field("app_id") String appId,
                                   @Field("ext_param") String extParam,
                                   @Field("guide_plan_infos") String guidePlanInfos,
                                   @Field("cashier_type") String cashierType,
                                   @Field("outer_business_data") String outerBusinessData,
                                   @Field("ext_dim_stat") String extDimStat,
                                   @FieldMap HashMap<String, String> extendTransmissionParams);

    @FormUrlEncoded
    @POST("/cashier/actioninfo")
    Call<PayResult> saveActionInfo(@FieldMap HashMap<String, Object> map);

    @FormUrlEncoded
    @POST("{path}")
    Call<Object> startTransGuide(@Path(value = "path", encoded = true) String path,
                                 @Field("tradeno") String tradeNo,
                                 @Field("pay_token") String payToken,
                                 @Field("with_contract") String withContact,
                                 @Field("nb_fingerprint") String fingerprint);

    @FormUrlEncoded
    @POST("/cashier/retainwindow")
    Call<RetainWindow> requestRetainWindow(@Field("tradeno") String tradeNo,
                                           @Field("pay_token") String payToken,
                                           @Field("installed_apps") String installedApp,
                                           @Field("nb_fingerprint") String fingerprint,
                                           @Field("ext_param") String extParam,
                                           @Field("outer_business_data") String outerBusinessData,
                                           @Field("ext_dim_stat") String extDimStat,
                                           @FieldMap HashMap<String, String> extendTransmissionParams);
}
