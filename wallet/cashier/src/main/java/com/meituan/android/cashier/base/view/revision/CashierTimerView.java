package com.meituan.android.cashier.base.view.revision;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.meituan.android.cashier.R;

/**
 * author: luo jing
 * date: 2018/8/28 13:06
 * description: cashier timer view
 */
public class CashierTimerView extends LinearLayout implements ITimerView {
    private TextView hour1, hour2, colonHourAndMin;
    private TextView min1, min2;
    private TextView sec1, sec2;
    private TextView noRemainTime, remainTimePrefix;
    private LinearLayout remainTimeValue;

    public CashierTimerView(Context context) {
        super(context);
        init();
    }

    public CashierTimerView(Context context, String remainText) {
        super(context);
        init(remainText);
    }

    public CashierTimerView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        LayoutInflater.from(getContext()).inflate(R.layout.cashier__timer, this);
        hour1 = findViewById(R.id.remain_time_hour1);
        hour2 = findViewById(R.id.remain_time_hour2);
        colonHourAndMin = findViewById(R.id.colon_between_hour_and_min);
        min1 = findViewById(R.id.remain_time_min1);
        min2 = findViewById(R.id.remain_time_min2);
        sec1 = findViewById(R.id.remain_time_sec1);
        sec2 = findViewById(R.id.remain_time_sec2);
        noRemainTime = findViewById(R.id.cashier_no_remaining_time);
        remainTimePrefix = findViewById(R.id.cashier_remaining_time_txt);
        remainTimeValue = findViewById(R.id.cashier_remaining_time_value);
    }

    private void init(String remainText) {
        init();
        if (!TextUtils.isEmpty(remainText)) {
            remainTimePrefix.setText(remainText);
        }
    }

    @Override
    public void onTimerTick(long millisUntilFinished) {
        int seconds = (int) millisUntilFinished / 1000;
        int hour = seconds / 3600;
        seconds = seconds % 3600;
        int min = seconds / 60;
        int second = seconds % 60;

        if (hour > 0) {
            hour1.setVisibility(View.VISIBLE);
            hour2.setVisibility(View.VISIBLE);
            colonHourAndMin.setVisibility(View.VISIBLE);
            hour1.setText(String.valueOf(hour / 10));
            hour2.setText(String.valueOf(hour % 10));
        } else {
            hour1.setVisibility(View.GONE);
            hour2.setVisibility(View.GONE);
            colonHourAndMin.setVisibility(View.GONE);
        }

        min1.setText(String.valueOf(min / 10));
        min2.setText(String.valueOf(min % 10));
        sec1.setText(String.valueOf(second / 10));
        sec2.setText(String.valueOf(second % 10));
        noRemainTime.setVisibility(View.GONE);
        remainTimePrefix.setVisibility(View.VISIBLE);
        remainTimeValue.setVisibility(View.VISIBLE);
    }

    @Override
    public void onTimerFinish() {
        noRemainTime.setVisibility(View.VISIBLE);
        remainTimePrefix.setVisibility(View.GONE);
        remainTimeValue.setVisibility(View.GONE);
    }
}
