package com.meituan.android.cashier.model.bean;


import com.google.gson.annotations.SerializedName;
import com.meituan.android.pay.desk.payment.bean.standarddesk.WalletPayment;
import com.meituan.android.paybase.utils.JsonBean;

/**
 * Created by ljj
 * Date:17/2/13
 * Time:上午11:28
 */
@JsonBean
public class CashierPayment extends WalletPayment {

    private static final long serialVersionUID = 8791374637381283250L;
    /**
     * DCEP支付半屏数据列表
     */
    @SerializedName("banklist_page")
    private BankListPage bankListPage;

    @SerializedName("no_promo_info")
    private String noPromoInfo;
    /**
     * 是否支持三方中断弹窗
     */
    @SerializedName("support_interrupt")
    private boolean supportInterrupt;


    public boolean isSupportInterrupt() {
        return supportInterrupt;
    }

    public void setSupportInterrupt(boolean supportInterrupt) {
        this.supportInterrupt = supportInterrupt;
    }

    public BankListPage getBankListPage() {
        return bankListPage;
    }

    public void setBankListPage(BankListPage bankListPage) {
        this.bankListPage = bankListPage;
    }

    public String getNoPromoInfo() {
        return noPromoInfo;
    }

    public void setNoPromoInfo(String noPromoInfo) {
        this.noPromoInfo = noPromoInfo;
    }
}
