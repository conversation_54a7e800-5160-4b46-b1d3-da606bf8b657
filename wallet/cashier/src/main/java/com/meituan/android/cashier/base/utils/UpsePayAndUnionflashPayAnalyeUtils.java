package com.meituan.android.cashier.base.utils;


import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paymentchannel.PayersID;
import com.meituan.android.paymentchannel.utils.UPPayUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Android Pay 相关的埋点
 */
public class UpsePayAndUnionflashPayAnalyeUtils {
    private static boolean isNullWhenPaymentDataInit = false;

    public static void recordUpsepayStatusWhenInit() {
        isNullWhenPaymentDataInit = UPPayUtils.isCheckStatusEqualNull();
        report("recordUpsepayStatusWhenInit");
    }

    private static String getStatus() {
        boolean isError = UPPayUtils.isCheckStatusEqualError();
        boolean isNull = UPPayUtils.isCheckStatusEqualNull();
        boolean isSuccess = UPPayUtils.isCheckStatusEqualSuccess();
        String status = "";
        if (isError) {
            status = "isError";
        } else if (isSuccess) {
            status = "isSuccess";
        } else if (isNull) {
            status = "isNull";
        }
        return status;
    }


    private static void report(String scene) {
        Map<String, Object> map = new HashMap<>();
        map.put("recordUpsepayStatus", getStatus());
        map.put("recordUpsepayScene", scene);
        map.put("pay_type", PayersID.ID_UPSEPAY);
        LoganUtils.log("标准收银台Android Pay展示", map);
    }

    public static void recordUpsepayStatusWhenPay() {
        if (isNullWhenPaymentDataInit) {
            report("recordUpsepayStatusWhenPay");
        }
    }

    public static void reportUnionflashpayStatus(boolean isDynLoadSoFailed) {
        Map<String, Object> map = new HashMap<>();
        map.put("isDynLoadSoFailed", isDynLoadSoFailed ? "1" : "0");
        map.put("pay_type", PayersID.ID_UNION_FLASH_PAY);
        LoganUtils.log("标准收银台云闪付展示", map);
    }
}