package com.meituan.android.cashier.dialogfragment;

import android.app.Activity;
import android.os.Bundle;
import android.text.TextUtils;

import com.meituan.android.cashier.dialog.OnClickPromotionPayTypeListener;
import com.meituan.android.cashier.dialog.PromotionSignedGuideDialog;
import com.meituan.android.cashier.model.bean.CashierPopWindowBean;
import com.meituan.android.cashier.model.bean.PopDetailInfo;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.pay.common.payment.data.PayStatus;
import com.meituan.android.pay.common.promotion.bean.CombineLabel;
import com.meituan.android.paybase.common.fragment.MTPayBaseDialogFragment;
import com.meituan.android.paybase.dialog.BaseDialog;
import com.meituan.android.paybase.utils.CollectionUtils;

import java.util.LinkedList;
import java.util.List;


public class PromotionSignedGuideFragment extends MTPayBaseDialogFragment {
    public static final String TAG = "PromotionSignedGuideFragment";
    private static final String PARAM_PROMOTION_SIGNED_POP_WINDOW_BEAN = "promotion_signed_pop_window_bean";

    private OnClickPromotionPayTypeListener mOnClickPromotionPayTypeListener;
    private CashierPopWindowBean mCashierPopWindowBean;

    private String mMainPreferentialContent; //主优惠内容
    private String mSubPreferentialContent;  //副优惠内容
    private List<CombineLabel> combineLabelList = new LinkedList<>(); // 分类好的标签数组

    public PromotionSignedGuideFragment() {

    }

    public static PromotionSignedGuideFragment newInstance(CashierPopWindowBean cashierPopWindowBean) {
        PromotionSignedGuideFragment fragment = new PromotionSignedGuideFragment();
        Bundle bundle = new Bundle();
        if (cashierPopWindowBean != null) {
            bundle.putSerializable(PARAM_PROMOTION_SIGNED_POP_WINDOW_BEAN, cashierPopWindowBean);
        }
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected String getTAG() {
        return TAG;
    }

    @Override
    protected BaseDialog createDialog(Bundle savedInstanceState) {
        setCancelable(false);
        return new PromotionSignedGuideDialog(getContext(), mCashierPopWindowBean, mOnClickPromotionPayTypeListener, mMainPreferentialContent, mSubPreferentialContent);
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        if (getParentFragment() instanceof OnClickPromotionPayTypeListener) {
            mOnClickPromotionPayTypeListener = (OnClickPromotionPayTypeListener) getParentFragment();
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            Bundle bundle = getArguments();
            mCashierPopWindowBean = (CashierPopWindowBean) bundle.getSerializable(PARAM_PROMOTION_SIGNED_POP_WINDOW_BEAN);
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        mOnClickPromotionPayTypeListener = null;
        if(!CollectionUtils.isEmpty(combineLabelList)){
            combineLabelList.clear();
        }
    }


    public boolean allowShowDialog(PopDetailInfo popDetailInfo) {
        classifyAndCombineLabels(popDetailInfo);
        searchPreferential();
        return isStatusAllowed(popDetailInfo) && !TextUtils.isEmpty(mMainPreferentialContent);
    }

    // 分类并拼接数组
    private void classifyAndCombineLabels(PopDetailInfo popDetailInfo) {
        MTPayment mtPayment;
        List<CombineLabel> unCombineLabelList = new LinkedList<>();
        if (popDetailInfo != null && (mtPayment = popDetailInfo.getGuidePayTypeInfo()) != null) {
            List<CombineLabel> labelList = mtPayment.getLabels();
            if(CollectionUtils.isEmpty(labelList)){
                return;
            }
            for (CombineLabel combineLabel : labelList) {
                if (combineLabel == null) {
                    continue;
                }
                if (!CollectionUtils.isEmpty(combineLabel.getChildrenLabel())) {
                    // 以是否包含二级标签为区分聚合和非聚合
                    if (combineLabel.getDiscount() > 0 && !TextUtils.isEmpty(combineLabel.getContent())) {
                        // 聚合标签discount > 0 并且 content不为空才可添加
                        combineLabelList.add(combineLabel);
                    }
                } else {
                    if (!TextUtils.isEmpty(combineLabel.getContent())) {
                        // 非聚合标签content不为空才可添加
                        unCombineLabelList.add(combineLabel);
                    }
                }
            }
            combineLabelList.addAll(unCombineLabelList);// 聚合和非聚合进行拼接
        }
    }

    private void searchPreferential() {
        if (!CollectionUtils.isEmpty(combineLabelList)) {
            mMainPreferentialContent = combineLabelList.get(0).getContent();
            if (combineLabelList.size() >= 2) {
                mSubPreferentialContent = combineLabelList.get(1).getContent();
            }
        }
    }

    private boolean isStatusAllowed(PopDetailInfo popDetailInfo) {
        MTPayment mtPayment;
        if (popDetailInfo != null && (mtPayment = popDetailInfo.getGuidePayTypeInfo()) != null) {
            return mtPayment.getStatus() == PayStatus.NORMAL || mtPayment.getStatus() == PayStatus.ACTIVE;
        }
        return false;
    }

}