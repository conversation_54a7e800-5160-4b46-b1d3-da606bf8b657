package com.meituan.android.cashier.dialogfragment;

import android.app.Activity;
import android.content.DialogInterface;
import android.os.Bundle;
import android.support.annotation.Nullable;

import com.meituan.android.cashier.NativeStandardCashierAdapter;
import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.common.CashierCatConstants;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.common.ICashier;
import com.meituan.android.cashier.dialog.AutomaticPayGuideDialog;
import com.meituan.android.cashier.model.bean.Cashier;
import com.meituan.android.cashier.newrouter.NewCashierParams;
import com.meituan.android.cashier.util.CashierSLAMonitor;
import com.meituan.android.paybase.common.analyse.cat.CatConstants;
import com.meituan.android.paybase.common.analyse.cat.CatUtils;
import com.meituan.android.paybase.common.fragment.MTPayBaseDialogFragment;
import com.meituan.android.paybase.dialog.BaseDialog;
import com.meituan.android.paybase.utils.MTPayNeedToPersist;
import com.meituan.android.paycommon.lib.utils.ExceptionUtils;

/**
 * Created by ljj
 * Date:17/3/16
 * Time:上午10:14
 */

public class AutomaticPayGuideDialogFragment extends MTPayBaseDialogFragment {
    private static final String TAG = "AutomaticPayGuideDialogFragment";
    private static final String ARG_CASHIER = "cashier";
    private Cashier cashier;
    private NewCashierParams cashierParams;
    private AutomaticPayGuideDialog.OnClickGuideButtonListener onClickGuideButton;
    @MTPayNeedToPersist
    private boolean reportSLA = true;
    @Override
    protected String getTAG() {
        return TAG;
    }

    @Override
    protected BaseDialog createDialog(Bundle savedInstanceState) {
        initOnClickGuideButton();
        return new AutomaticPayGuideDialog(getContext(), cashier, onClickGuideButton);
    }

    @Override
    public void onViewStateRestored(@Nullable Bundle savedInstanceState) {
        super.onViewStateRestored(savedInstanceState);
        // 不保留活动恢复后，需重新设置 onClickGuideButton
        initOnClickGuideButton();
        if (getDialog() instanceof AutomaticPayGuideDialog) {
            ((AutomaticPayGuideDialog) getDialog()).setOnClickGuideButton(onClickGuideButton);
        }
    }

    public static AutomaticPayGuideDialogFragment newInstance(Cashier cashier) {
        AutomaticPayGuideDialogFragment automaticPayGuideDialogFragment = new AutomaticPayGuideDialogFragment();
        Bundle bundle = new Bundle();
        if (cashier != null) {
            bundle.putSerializable(ARG_CASHIER, cashier);
        }
        automaticPayGuideDialogFragment.setArguments(bundle);
        return automaticPayGuideDialogFragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            cashier = (Cashier) getArguments().getSerializable(ARG_CASHIER);
            cashierParams = (NewCashierParams) getArguments().getSerializable("CashierParams");
        }
        if (getActivity() instanceof MTCashierActivity && reportSLA) {
            reportSLA = false;
            CashierStaticsUtils.logCustom("native_standcashier_start_succ", null, null, getUniqueId());
            CashierSLAMonitor.reportStandardCashierShowSuccess(((MTCashierActivity) getActivity()).getLastResumedFeature(), getUniqueId());
            CashierSLAMonitor.reportStandardCashierFinished(getUniqueId(), CashierSLAMonitor.CASHIER_FINISHED_STATUS_SUCCESS, CashierSLAMonitor.NATIVE_STANDARD_CASHIER_SUCCESS_SCENE_AUTOMATIC_PAY_GUIDE);
        }
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        initOnClickGuideButton();
    }


    private void initOnClickGuideButton() {
        if (onClickGuideButton != null) {
            return;
        }
        if (getActivity() instanceof MTCashierActivity) {
            ICashier iCashier = ((MTCashierActivity) getActivity()).getCurrentCashier();
            if (iCashier instanceof NativeStandardCashierAdapter) {
                onClickGuideButton = (NativeStandardCashierAdapter) iCashier;
            }
        } else if (getActivity() instanceof AutomaticPayGuideDialog.OnClickGuideButtonListener) {
            onClickGuideButton = (AutomaticPayGuideDialog.OnClickGuideButtonListener) getActivity();
        }
    }

    @Override
    public void onDetach() {
        onClickGuideButton = null;
        super.onDetach();
    }

    @Override
    public void onCancel(DialogInterface dialog) {
        CatUtils.logRate(CashierCatConstants.ACTION_DISPATCHER_CASHIER,
                CatConstants.CODE_DEFAULT_OK);
        ExceptionUtils.exitSDK(getActivity(), MTCashierActivity.class);
    }

    public void setOnClickGuideButton(AutomaticPayGuideDialog.OnClickGuideButtonListener listener) {
        this.onClickGuideButton = listener;
    }
}
