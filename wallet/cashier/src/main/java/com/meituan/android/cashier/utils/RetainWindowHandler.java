package com.meituan.android.cashier.utils;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.support.annotation.NonNull;

import com.meituan.android.cashier.model.bean.RetainWindow;
import com.meituan.android.cashier.retrofit.CashierReqTagConstant;
import com.meituan.android.cashier.retrofit.CashierRequestService;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paycommon.lib.retrofit.PayRetrofit;
import com.sankuai.meituan.retrofit2.Call;

import java.lang.ref.WeakReference;
import java.util.HashMap;

public class RetainWindowHandler implements IRequestCallback {
    private static final int REQ_TAG_RETAIN_WINDOW = CashierReqTagConstant.REQ_TAG_RETAIN_WINDOW;
    private static final int REQUEST_TIME_OUT_MS = 800;
    private Call retainWindowCall;
    private RetainWindowShowListener retainWindowShowListener;
    private TimeOutHandler timeOutHandler;
    private boolean isDestroyed;
    private boolean requestedRetainWindow;

    public RetainWindowHandler(RetainWindowShowListener retainWindowShowListener) {
        this.retainWindowShowListener = retainWindowShowListener;
    }

    /**
     * 请求新挽留弹窗接口
     *
     * @param tradeNo
     * @param payToken
     * @param installedApp
     * @param fingerprint
     * @param extParam
     * @param outerBusinessData
     * @param extDimStat
     * @param extendTransmissionParams
     */
    public boolean requestRetainWindow(String tradeNo, String payToken, String installedApp,
                                       String fingerprint, String extParam, String outerBusinessData,
                                       String extDimStat, HashMap<String, String> extendTransmissionParams) {
        if (!requestedRetainWindow) {
            if (retainWindowCall == null) {
                retainWindowCall = PayRetrofit.getInstance().create(CashierRequestService.class,
                        this, REQ_TAG_RETAIN_WINDOW).requestRetainWindow(tradeNo, payToken, installedApp,
                        fingerprint, extParam, outerBusinessData, extDimStat, extendTransmissionParams);
                shutdownTimeOutTimer();
                startTimeOutTimer();
            }
            return true;
        }
        return false;
    }

    /**
     * 销毁释放资源
     */
    public void onDestroy() {
        isDestroyed = true;
        shutdownTimeOutTimer();
    }

    private void startTimeOutTimer() {
        if (timeOutHandler == null) {
            timeOutHandler = new TimeOutHandler(this);
        }
        timeOutHandler.sendEmptyMessageDelayed(1, REQUEST_TIME_OUT_MS);
    }

    private void shutdownTimeOutTimer() {
        if (timeOutHandler != null) {
            timeOutHandler.removeCallbacksAndMessages(null);
        }
    }

    private void cancelRetainWindowRequest() {
        if (retainWindowCall != null && !retainWindowCall.isCanceled()) {
            retainWindowCall.cancel();
        }
    }

    @Override
    public void onRequestSucc(int tag, Object obj) {
        if (isDestroyed) {
            return;
        }
        requestedRetainWindow = true;
        shutdownTimeOutTimer();
        if (obj instanceof RetainWindow) {
            RetainWindow retainWindow = (RetainWindow) obj;
            retainWindow.setNewRetainWindow(true);
            retainWindowShowListener.showNewRetainWindow(retainWindow);
        } else {
            retainWindowShowListener.showOldRetainWindow();
        }

    }

    @Override
    public void onRequestException(int tag, Exception e) {
        if (isDestroyed) {
            return;
        }
        requestedRetainWindow = true;
        shutdownTimeOutTimer();
        retainWindowShowListener.showOldRetainWindow();
    }

    @Override
    public void onRequestFinal(int tag) {

    }

    @Override
    public void onRequestStart(int tag) {

    }

    private static class TimeOutHandler extends Handler {
        private final WeakReference<RetainWindowHandler> weakReference;

        public TimeOutHandler(RetainWindowHandler retainWindowHandler) {
            super(Looper.getMainLooper());
            weakReference = new WeakReference<>(retainWindowHandler);
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            RetainWindowHandler retainWindowHandler = weakReference.get();
            if (retainWindowHandler != null) {
                retainWindowHandler.cancelRetainWindowRequest();
            }
        }
    }

    public interface RetainWindowShowListener {
        void showNewRetainWindow(RetainWindow retainWindow);

        void showOldRetainWindow();

    }

}
