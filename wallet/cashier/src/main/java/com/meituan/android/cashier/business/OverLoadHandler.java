package com.meituan.android.cashier.business;

import android.app.Activity;
import android.os.Handler;
import android.os.Message;
import android.support.annotation.NonNull;
import android.support.v4.app.FragmentActivity;
import android.text.TextUtils;

import com.meituan.android.cashier.model.bean.OverLoadInfo;
import com.meituan.android.cashier.newrouter.CashierBusinessHandler;
import com.meituan.android.cashier.newrouter.NewCashierParams;
import com.meituan.android.paybase.common.dialog.PayDialog;
import com.meituan.android.paybase.utils.ActivityStatusChecker;

import java.lang.ref.WeakReference;

public class OverLoadHandler extends CashierBusinessHandler {
    private static final int ALLOW_TO_SEND_REQ = 2;
    private final RequestLimitHandler requestLimitHandler;
    private boolean isOverLoading;
    private OverLoadInfo overLoadInfo;

    public OverLoadHandler(FragmentActivity activity, NewCashierParams cashierParams) {
        super(activity, cashierParams);
        this.requestLimitHandler = new RequestLimitHandler(this);
    }

    public boolean handleOverLoad(OverLoadInfo overLoadInfo) {
        if (overLoadInfo != null && overLoadInfo.isStatus()) { // 后台是否负载过重
            this.overLoadInfo = overLoadInfo;
            isOverLoading = true;
            long overLoadTimeout = overLoadInfo.getTimeout();
            if (overLoadTimeout > 0) {
                requestLimitHandler.sendEmptyMessageDelayed(ALLOW_TO_SEND_REQ, overLoadTimeout);
            }
            onOverLoading();
            return true;
        }
        return false;
    }

    public boolean isOverLoading() {
        return isOverLoading;
    }

    public void onDestroy() {
        if (requestLimitHandler != null) {
            requestLimitHandler.removeMessages(ALLOW_TO_SEND_REQ);
        }
    }

    /**
     * 展示负载过重弹窗
     */
    public void onOverLoading() {
        Activity activity = getActivity();
        if (ActivityStatusChecker.isValid(activity) && !TextUtils.isEmpty(overLoadInfo.getMessage())) {
            new PayDialog.Builder(getActivity())
                    .msg(overLoadInfo.getMessage())
                    .leftBtn(getActivity().getString(com.meituan.android.cashier.R.string.cashier__I_have_known), null)
                    .build().show();
        }
    }

    private static class RequestLimitHandler extends Handler {
        private final WeakReference<OverLoadHandler> weakReference;

        public RequestLimitHandler(OverLoadHandler overLoadHandler) {
            this.weakReference = new WeakReference<>(overLoadHandler);
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            OverLoadHandler overLoadHandler = weakReference.get();
            if (overLoadHandler != null && msg.what == ALLOW_TO_SEND_REQ) {
                overLoadHandler.isOverLoading = false;
            }
        }
    }

}
