package com.meituan.android.cashier.mtpay;

import static com.meituan.android.cashier.activity.MTCashierActivity.KEY_INSTALLED_APPS;
import static com.meituan.android.cashier.utils.GoHelloPaySceneUtils.reportMtPaySLAStart;
import static com.meituan.android.pay.common.payment.data.WalletPayParams.KEY_CASHIER_PAY_TOKEN;
import static com.meituan.android.pay.common.payment.data.WalletPayParams.KEY_HYBRID_INFO;
import static com.meituan.android.pay.common.payment.data.WalletPayParams.KEY_MTP_CASHIER_URL;
import static com.meituan.android.pay.common.payment.data.WalletPayParams.KEY_TRADE_NO;
import static com.meituan.android.pay.desk.component.analyse.DeskAnalyseUtils.analysePaySucess;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.support.v4.app.FragmentActivity;
import android.support.v4.content.LocalBroadcastManager;
import android.text.TextUtils;
import android.view.View;

import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.bean.CashierParams;
import com.meituan.android.cashier.bean.CashierProductInfo;
import com.meituan.android.cashier.bean.ClientRouterInfoBean;
import com.meituan.android.cashier.bean.ClientRouterParamBean;
import com.meituan.android.cashier.common.CashierListener;
import com.meituan.android.cashier.common.CashierTypeConstant;
import com.meituan.android.cashier.common.CashierUtil;
import com.meituan.android.cashier.common.ICashier;
import com.meituan.android.cashier.common.ICashierAdapter;
import com.meituan.android.cashier.common.ProductTypeConstant;
import com.meituan.android.cashier.model.bean.MTPaymentURL;
import com.meituan.android.cashier.retrofit.CashierRequestService;
import com.meituan.android.cashier.retrofit.CashierRequestUtils;
import com.meituan.android.neohybrid.cache.HybridParamsCache;
import com.meituan.android.pay.common.payment.data.WalletPayParams;
import com.meituan.android.pay.model.PayErrorCode;
import com.meituan.android.pay.utils.HybridHalfPageCashierStatics;
import com.meituan.android.paybase.common.activity.PayBaseActivity;
import com.meituan.android.paybase.common.analyse.cat.CatUtils;
import com.meituan.android.paybase.config.PayBaseConfig;
import com.meituan.android.paybase.downgrading.PayHornConfigBean;
import com.meituan.android.paybase.downgrading.PayHornConfigService;
import com.meituan.android.paybase.moduleinterface.payment.PayActionListener;
import com.meituan.android.paybase.moduleinterface.payment.PayFailInfo;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.retrofit.PayException;
import com.meituan.android.paybase.utils.Base64;
import com.meituan.android.paybase.utils.CashierRepeatDownGradeSwitchManager;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.paybase.utils.LocalBroadCastUtil;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.MTPayBaseClass;
import com.meituan.android.paybase.utils.SaveInstanceUtil;
import com.meituan.android.paybase.utils.SdkDataStorageUtils;
import com.meituan.android.paycommon.lib.config.MTPayConfig;
import com.meituan.android.paycommon.lib.coupon.model.Promotion;
import com.meituan.android.paycommon.lib.retrofit.PayRetrofit;
import com.meituan.android.paycommon.lib.utils.OuterBusinessParamUtils;
import com.meituan.android.paymentchannel.PayerMediator;
import com.meituan.android.paymentchannel.PayersID;
import com.sankuai.meituan.retrofit2.Call;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@ServiceLoaderInterface(key = CashierTypeConstant.CASHIERTYPE_MT_HYBRID_HALFPAGE_CASHIER, interfaceClass = ICashier.class)
@MTPayBaseClass
public class HybridPrePosedMTCashierAdapter extends ICashierAdapter implements PayActionListener {
    private static final String TAG = "MTHybridHalfPageCashier";
    private static final String VAL_DOWNGRADE_TO_BUSINESS = "downgrade_to_business";
    private static final int STATUS_FINISH = MTCashierActivity.STATUS_FINISH;
    private static final String KEY_ACTION = "action";
    private static final String PRODUCT_TYPE = "preposed-mtcashier";
    private CashierParams mCashierParams;
    private MTCashierActivity mCashierActivity;
    private Call<MTPaymentURL> mGoHelloPayCall;
    private ClientRouterInfoBean mClientRouterInfoBean;
    private boolean isOpenAsynFingerprint;
    private final HybridPrePosedMTCashierConfigManager mHybridPrePosedMTCashierConfigManager = new HybridPrePosedMTCashierConfigManager();
    // 用于接收独立收银台半页加载成功、失败的回调
    private BroadcastReceiver mBroadcastReceiver;

    @Override
    public <T extends FragmentActivity & CashierListener & IRequestCallback> ConsumeResult consume(T t, CashierParams cashierParams) {
        this.mCashierParams = cashierParams;
        this.mCashierActivity = (MTCashierActivity) t;
        if (!PRODUCT_TYPE.equals(getProductType(cashierParams))) {
            return new ConsumeResult(false, "007", "productType is not preposed-mtcashier");
        }
        return consumeInternal(cashierParams);
    }

    /**
     * 是否需要降级
     *
     * @param cashierParams
     * @return true 表示需要降级
     */
    private ConsumeResult consumeInternal(CashierParams cashierParams) {
        mClientRouterInfoBean = mHybridPrePosedMTCashierConfigManager.getHybridPrePosedMTCashierConfig(cashierParams);
        if (!isTechDowngradeAvailable(mCashierParams)) {
            return new ConsumeResult(true);
        }
        if (mClientRouterInfoBean == null) { // 1.配置为空
            return new ConsumeResult(false, "001", "config is empty");
        }
        String cashierUrl = mClientRouterInfoBean.getCashierUrl();
        if (TextUtils.isEmpty(cashierUrl)) { // 2.页面 url 为空
            return new ConsumeResult(false, "002", "cashier url is empty");
        }
        if (cashierParams.getCashierRouterInfo() == null) { // 跳转参数相关为空
            return new ConsumeResult(false, "003", "cashierRouterInfo is null");
        }
        CashierProductInfo cashierProductInfo = cashierParams.getCashierRouterInfo().getProductInfo();
        if (TextUtils.isEmpty(cashierProductInfo.getPath())) { // 跳转参数相关为空
            return new ConsumeResult(false, "004", "cashierRouterInfo.path is null");
        }
        HashMap<String, Object> nestConfigurations = mClientRouterInfoBean.getNestConfigurations();
        if (nestConfigurations == null) { // 3.不存在 nestConfigurations 时，当作不降级处理
            return new ConsumeResult(true);
        }
        boolean enableDegrade = (boolean) nestConfigurations.get("enable_degrade");
        if (enableDegrade) {
            return new ConsumeResult(false, "005", "enable_degrade");
        }
        boolean enableOfflineDegrade = (boolean) nestConfigurations.get("enable_offline_degrade");
        if (enableOfflineDegrade) { // 4.离线包降级开启，不存在匹配的离线包
            if (existOffline(cashierUrl)) {
                return new ConsumeResult(true);
            }
            return new ConsumeResult(false, "006", "offline degrade");
        }
        return new ConsumeResult(true);
    }

    private String getUrlPath(String url) {
        if (url.startsWith("http:") || url.startsWith("https:")) {
            return Uri.parse(url).getPath();
        }
        if (url.contains("?")) {
            return url.substring(0, url.indexOf('?'));
        }
        return url;
    }

    private boolean existOffline(String cashierUrl) {
        ClientRouterParamBean clientRouterParamBean = ClientRouterParamBean.createClientRouterParamBean();
        List<String> offlines = clientRouterParamBean.getGlobalOfflineHybridMtp();
        if (CollectionUtils.isEmpty(offlines)) {
            return false;
        }
        for (String offline : offlines) {
            if (TextUtils.equals(getUrlPath(offline), getUrlPath(cashierUrl))) {
                return true;
            }
        }
        return false;
    }

    private String getProductType(CashierParams cashierParams) {
        if (cashierParams == null) {
            return "";
        }
        return cashierParams.getProductType();
    }

    private void businessDegrade(String degradeInfo) {
        downgradeToStandardCashier(degradeInfo);
    }

    private boolean isTechDowngradeAvailable(CashierParams cashierParams) {
        return cashierParams.getCashierScope(getCashierType(), getUniqueId()).isDowngradeAvailable();
    }

    @Override
    public void invoke(String cashierFrom, Map<String, Object> cashierParams) {
        CashierProductInfo cashierProductInfo = this.mCashierParams.getCashierRouterInfo().getProductInfo();
        HybridHalfPageCashierStatics.registerCommonBusinessParams(commonBusinessParams(false), getUniqueId());
        HybridHalfPageCashierStatics.onSLAStart(getUniqueId());
        HashMap<String, String> map = jsonObjectToMap(cashierProductInfo.getNextReqParams());
        CashierRequestUtils.appendGuidePlans(map, getGuidePlanInfos());
        appendTransmissionParam(map);
        OuterBusinessParamUtils.appendExtraParamsTogoHelloPay(mCashierActivity, map);
        HybridHalfPageCashierStatics.reportRequestStart("cashier/gohellopay", "b_pay_cashier_gohellopay_start_sc", null, getUniqueId());
        HybridHalfPageCashierStatics.logCustomRequestStart("cashier_gohellopay_start", getUniqueId());
        PayHornConfigBean configBean = PayHornConfigService.get().getPayCashierHornConfigBean();
        String fingerprint = "";
        if (configBean != null && configBean.isAsynFingerprintSwitch()) {
            fingerprint = HybridParamsCache.getFingerprintFromCache();
            isOpenAsynFingerprint = configBean.isAsynFingerprintSwitch();
        } else {
            fingerprint = MTPayConfig.getProvider().getFingerprint();
            isOpenAsynFingerprint = false;
        }
        HashMap<String, Object> logMap = new HashMap();
        logMap.put("is_asyn_fingerprint_open", isOpenAsynFingerprint);
        LoganUtils.log("hybrid独立收银台缓存中获取指纹", logMap);
        mGoHelloPayCall = PayRetrofit.getInstance().create(CashierRequestService.class, mCashierActivity, MTHalfPageCashierReqTagConstant.REQ_TAG_GO_HELLO_PAY)
                .goHelloPay(cashierProductInfo.getPath(), appendParams(map, cashierProductInfo), fingerprint);
        initBroadcastReceiver();
    }

    private void initBroadcastReceiver() {
        final String successAction = "com.meituan.android.cashier.mtpay.loadState.success";
        final String failAction = "com.meituan.android.cashier.mtpay.loadState.fail";
        if (mBroadcastReceiver == null) {
            mBroadcastReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    if (LocalBroadCastUtil.actionEquals(mCashierActivity, failAction, intent)) {
                        openStatus(false, null);
                    } else if (LocalBroadCastUtil.actionEquals(mCashierActivity, successAction, intent)) {
                        openStatus(true, null);
                    }
                }
            };
        }
        LocalBroadCastUtil.registerBroadCast(mCashierActivity, new String[]{successAction, failAction}, mBroadcastReceiver);
    }

    private HashMap<String, Object> commonBusinessParams(boolean isSavedState) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("nb_container", "hybrid");
        final String vRoot = SdkDataStorageUtils.getDataStorageCenter(mCashierActivity).getString(MTCashierActivity.KEY_IS_ROOT, "0");
        hashMap.put("device_rooted", vRoot);
        hashMap.put("is_saved_state", isSavedState);
        if (mCashierParams != null) {
            hashMap.put("trade_no", mCashierParams.getTradeNo());
            hashMap.put("merchant_no", mCashierParams.getMerchantNo());
        }
        hashMap.put("hybrid_mtpay_verison", getNbHybridVersion());
        String url = getCashierUrl();
        hashMap.put("hybrid_current_url", url);
        hashMap.put("nb_hybrid_version", getNbHybridVersion());
        return hashMap;
    }

    /**
     * 后端不处理此数据，主要是用于传给美团支付侧
     *
     * @param alreadyParams
     */
    private void appendTransmissionParam(HashMap<String, String> alreadyParams) {
        String payExtendParamStr = alreadyParams.get(WalletPayParams.KEY_PAY_EXTEND_PARAMS);
        payExtendParamStr = CashierRequestUtils.extendPayExtendParams(payExtendParamStr, "transmission_param", generateMtpHybridInfo());
        alreadyParams.put(WalletPayParams.KEY_PAY_EXTEND_PARAMS, payExtendParamStr);
    }

    private String generateMtpHybridInfo() {
        JSONObject result = new JSONObject();
        JSONObject mtHybridInfo = new JSONObject();
        try {
            mtHybridInfo.put(KEY_CASHIER_PAY_TOKEN, this.mCashierParams.getPayToken());
            mtHybridInfo.put(KEY_TRADE_NO, this.mCashierParams.getTradeNo());
            mtHybridInfo.put(KEY_MTP_CASHIER_URL, getCashierUrl());
            mtHybridInfo.put("force_enter", !isTechDowngradeAvailable(this.mCashierParams));
            mtHybridInfo.put("app_id", getAppId());
            mtHybridInfo.put("nb_hybrid_version", getNbHybridVersion());
            mtHybridInfo.put("install_app", CashierUtil.getInstalledApps(mCashierActivity));
            final String vRoot = SdkDataStorageUtils.getDataStorageCenter(mCashierActivity).getString(MTCashierActivity.KEY_IS_ROOT, "0");
            mtHybridInfo.put("rooted", vRoot);
            result.put(KEY_HYBRID_INFO, mtHybridInfo);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result.toString();
    }

    private HashMap<String, String> appendParams(HashMap<String, String> alreadyParams, CashierProductInfo cashierProductInfo) {
        if (alreadyParams == null) {
            alreadyParams = new HashMap<>();
        }
        alreadyParams.put("tradeno", this.mCashierParams.getTradeNo());
        alreadyParams.put("pay_token", this.mCashierParams.getPayToken());
        alreadyParams.put(WalletPayParams.KEY_GUIDE_PLAN_INFOS, getGuidePlanInfos());
        alreadyParams.put("nb_hybrid_version", getNbHybridVersion());
        alreadyParams.put("submit_path", cashierProductInfo.getPath());
        alreadyParams.put("nb_container", "hybrid");
        alreadyParams.put(KEY_INSTALLED_APPS, String.valueOf(CashierUtil.getInstalledApps(mCashierActivity)));
        return alreadyParams;
    }

    private String getCashierUrl() {
        if (mClientRouterInfoBean == null) {
            return "";
        }
        return getCashierUrl(mClientRouterInfoBean);
    }

    private String getCashierUrl(ClientRouterInfoBean clientRouterInfoBean) {
        if (clientRouterInfoBean == null) {
            return null;
        }
        String cashierUrl = clientRouterInfoBean.getCashierUrl();
        if (TextUtils.isEmpty(cashierUrl)) {
            return cashierUrl;
        }
        Uri.Builder uriBuilder = Uri.parse(cashierUrl).buildUpon();
        uriBuilder.appendQueryParameter("app_pay_sdk_version", PayBaseConfig.getProvider().getPayVersion());
        uriBuilder.appendQueryParameter("device_platform", PayBaseConfig.getProvider().getPlatform());
        return uriBuilder.build().toString();
    }

    private String getNbHybridVersion() {
        String cashierUrl = getCashierUrl();
        if (TextUtils.isEmpty(cashierUrl)) {
            return null;
        }
        // 此处的正则表达式和 iOS 端一致
        String patternStr = "v([0-9]+.){2,3}[0-9]+";
        Pattern pattern = Pattern.compile(patternStr);
        Matcher m = pattern.matcher(cashierUrl);
        if (m.find()) {
            String version = m.group(0);
            if (!TextUtils.isEmpty(version)) {
                return version.replace("v", "");
            }
            return version;
        }
        return null;
    }


    public String getGuidePlanInfos() {
        if (!TextUtils.isEmpty(this.mCashierParams.getExtraData())) {
            try {
                JSONObject extraJson = new JSONObject(this.mCashierParams.getExtraData());
                return extraJson.optString("guide_plan_infos");
            } catch (JSONException e) {
                LoganUtils.logError("HybridPrePosedMTCashierAdapter_getGuidePlanInfos", e.getMessage());
            }
        }
        return "";
    }

    private HashMap<String, String> jsonObjectToMap(String jsonString) {
        HashMap<String, String> map = new HashMap<>();
        try {
            JSONObject jsonObject = new JSONObject(jsonString);
            Iterator<String> keys = jsonObject.keys();
            while (keys.hasNext()) {
                String key = keys.next();
                map.put(key, jsonObject.optString(key));
            }
        } catch (Exception e) {
            CatUtils.logError(TAG, "json 解析异常 " + this.mCashierParams.getUri().toString());
        }
        return map;
    }

    public void downgradeToStandardCashier(String info) {
        if (mCashierActivity != null) {
            mCashierActivity.onCashierBusinessDowngrade(getCashierType(), ProductTypeConstant.STANDARD_CASHIER, info);
        }
    }

    @Override
    public String getCashierType() {
        return CashierTypeConstant.CASHIERTYPE_MT_HYBRID_HALFPAGE_CASHIER;
    }

    @Override
    public PayBaseActivity.ProcessType getRequestProgressType(int tag) {
        return PayBaseActivity.ProcessType.CASHIER;
    }

    @Override
    public boolean onBackPressed() {
        return false;
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        SaveInstanceUtil.saveInstanceOfClass(this, getClass(), outState);
    }

    @Override
    public void onRestoreInstanceState(Bundle savedInstanceState) {
        SaveInstanceUtil.restoreInstanceOfClass(this, getClass(), savedInstanceState);
        HybridHalfPageCashierStatics.registerCommonBusinessParams(commonBusinessParams(true), getUniqueId());
    }

    @Override
    public void onDestroy(boolean release) {
        if (mGoHelloPayCall != null && !mGoHelloPayCall.isCanceled()) {
            mGoHelloPayCall.cancel();
        }
        HybridHalfPageCashierStatics.unRegisterCommonBusinessParams(getUniqueId());
        if (mBroadcastReceiver != null) {
            LocalBroadcastManager.getInstance(mCashierActivity).unregisterReceiver(mBroadcastReceiver);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        PayerMediator.getInstance().setPayActionListener(mCashierActivity, this);
        if (PayerMediator.getInstance().consumeActivityResult(mCashierActivity, requestCode, resultCode, data)) {
            LoganUtils.log("HybridPrePosedMTCashierAdapter_onActivityResult_requestCode: " + requestCode);
        }
    }

    @Override
    public void onStart() {

    }

    @Override
    public void onRequestSucc(int tag, Object obj) {
        if (tag == MTHalfPageCashierReqTagConstant.REQ_TAG_GO_HELLO_PAY) {
            HybridHalfPageCashierStatics.reportRequestSuccess("cashier/gohellopay", "b_pay_cashier_gohellopay_succ_sc", null, getUniqueId());
            HybridHalfPageCashierStatics.logCustomRequestSuccess("cashier_gohellopay_succ", null, getUniqueId());
            MTPaymentURL mtPaymentURL = (MTPaymentURL) obj;
            if(mtPaymentURL !=null ){
                PayHornConfigBean.setGmDegradeFlag(mtPaymentURL.getUrl());
            }
            dealGoHelloPayResponse(mtPaymentURL);
        }
    }

    private void dealGoHelloPayResponse(MTPaymentURL mtPaymentURL) {
        if (mtPaymentURL == null) {
            return;
        }
        if (mtPaymentURL.getOverLoadInfo() != null && mtPaymentURL.getOverLoadInfo().isStatus()) { //当前后台负载过重，无法完成支付，弹出提示窗口
            businessDegrade("");
        } else {
            try {
                String orderInfo = new String(Base64.decode(mtPaymentURL.getUrl()));
                JSONObject jsonObject = new JSONObject(orderInfo);
                String qdbNo = jsonObject.optString("trans_id");
                HybridHalfPageCashierStatics.appendCommonBusinessParams("qdb_no", TextUtils.isEmpty(qdbNo) ? "-999" : qdbNo, getUniqueId());
            } catch (Exception e) {
                e.printStackTrace();
            }
            PayerMediator.getInstance().startPay(mCashierActivity, PayersID.ID_MEITUANPAY, mtPaymentURL.getUrl(), this.mCashierParams.getTradeNo(), this);
            reportMtPaySLAStart(mtPaymentURL, "hybrid", getUniqueId());
        }
    }

    private String getAppId() {
        if (!TextUtils.isEmpty(this.mCashierParams.getExtraData())) {
            try {
                JSONObject extraJson = new JSONObject(this.mCashierParams.getExtraData());
                return extraJson.optString("app_id");
            } catch (JSONException e) {
                LoganUtils.logError("HybridPrePosedMTCashierAdapter_getAppId", e.getMessage());
            }
        }
        return "";
    }


    @Override
    public void onRequestException(int tag, Exception e) {
        // 直接降级到标准收银台
        if (tag == MTHalfPageCashierReqTagConstant.REQ_TAG_GO_HELLO_PAY) {
            HybridHalfPageCashierStatics.logCustomRequestFailed("cashier_gohellopay_fail", e, getUniqueId());
            HybridHalfPageCashierStatics.reportRequestFailed("cashier/gohellopay", "b_pay_cashier_gohellopay_fail_sc", e, getUniqueId());
            if (e instanceof PayException) {
                businessDegrade(getPreComponentFailInfo((PayException) e));
            } else {
                businessDegrade(null);
            }
        }
    }

    private static String getPreComponentFailInfo(PayException payException) {
        if (payException == null) {
            return null;
        }
        JSONObject jsonObject = new JSONObject();
        int errorCode = payException.getCode();
        String errorMessage = payException.getMessage();
        try {
            if (errorCode != -1) {
                jsonObject.put("pay_err_code", errorCode);
                if (!TextUtils.isEmpty(errorMessage)) {
                    jsonObject.put("pay_err_msg", errorMessage);
                }
            }
            jsonObject.put("jump_from_product", "preposed-mtcashier");
        } catch (JSONException e) {
            LoganUtils.logError("HybridPrePosedMTCashierAdapter_getPreComponentFailInfo", e.getMessage());
        }
        return jsonObject.toString();
    }

    @Override
    public void onRequestFinal(int tag) {

    }

    @Override
    public void onRequestStart(int tag) {

    }

    @Override
    public void onPayPreExecute(String payType) {

    }

    @Override
    public void onGotPayResult(String payType, int payResult, PayFailInfo payFailInfo) {
        if (PayersID.ID_MEITUANPAY.equals(payType)) {
            onGotMeituanPayResult(payType, payResult, payFailInfo);
        }
    }

    private void onMeituanPaySuccess(PayFailInfo failInfo) {
        if (mCashierActivity == null) {
            return;
        }
        if (CashierRepeatDownGradeSwitchManager.downGrade()) {
            mCashierActivity.setResultStatus(MTCashierActivity.STATUS_SUCCESS);
            mCashierActivity.handlePayResultAndFinish(STATUS_FINISH);
        } else {
            if (failInfo == null) {
                mCashierActivity.onCashierPaySuccess(null);
                return;
            }
            Promotion promotion = null;
            try {
                JSONObject jsonObject = new JSONObject(failInfo.getExtra());
                // pay_promotion 和 PayActivity.KEY_PAY_PROMOTION 保持一致
                promotion = GsonProvider.getInstance().fromJson(jsonObject.optString("pay_promotion"), Promotion.class);
            } catch (Exception e) {
                LoganUtils.logError("HybridPrePosedMTCashierAdapter_onMeituanPaySuccess", e.getMessage());
            }
            mCashierActivity.onCashierPaySuccess(promotion);
        }
    }

    /**
     * 是否是降级到标准收银台--业务决策降级
     *
     * @return
     */
    private boolean isDegradeToStandardCashierOfBusinessDecision(PayFailInfo payFailInfo) {
        JSONObject object = null;
        try {
            if (payFailInfo != null && !TextUtils.isEmpty(payFailInfo.getExtra())) {
                object = new JSONObject(payFailInfo.getExtra());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        boolean downgradeToBusiness = object != null && object.has(KEY_ACTION) && TextUtils.equals(object.optString(KEY_ACTION), VAL_DOWNGRADE_TO_BUSINESS);
        return !downgradeToBusiness;
    }

    private static String getPreComponentFailInfo(PayFailInfo payFailInfo) {
        if (payFailInfo == null) {
            return null;
        }
        JSONObject jsonObject = new JSONObject();
        int errorCode = payFailInfo.getErrorCode();
        String errorMessage = payFailInfo.getMsg();
        try {
            if (errorCode != -1) {
                jsonObject.put("pay_err_code", errorCode);
            }
            if (!TextUtils.isEmpty(errorMessage)) {
                jsonObject.put("pay_err_msg", errorMessage);
            }
            jsonObject.put("jump_from_product", "preposed-mtcashier");
        } catch (JSONException e) {
            LoganUtils.logError("HybridPrePosedMTCashierAdapter_getPreComponentFailInfo", e.getMessage());
        }
        return jsonObject.toString();
    }


    private void onGotMeituanPayResult(String payType, int payResult, PayFailInfo payFailInfo) {
        if (payResult == PayActionListener.SUCCESS) {
            analysePaySucess(getUniqueId());
            Map<String, Object> reportMap = new HashMap<>();
            reportMap.put("pay_type", payType);
            reportMap.put("class", "HybridPrePosedMTCashierAdapter");
            LoganUtils.log("收银台支付成功后埋点", reportMap);
            if (!CashierRepeatDownGradeSwitchManager.downGrade()) {
                mCashierActivity.setHalfPageMarketingBackgroundColor("#B3000000");
            }
            onMeituanPaySuccess(payFailInfo);
        } else if (payResult == PayActionListener.CANCEL) { //
            payCancel();
        } else {
            if (isLoadingCancel(payFailInfo)) { // 容器加载过程中点击 back 键，取消加载
                Map<String, Object> map = new HashMap<>();
                map.put("scene", "cancel");
                openStatus(false, map);
                payCancel();
            } else if (renderFailed(payFailInfo)) { // 技术降级
                Map<String, Object> map = new HashMap<>();
                map.put("scene", "renderFail");
                openStatus(false, map);
                String failInfo = getPreComponentFailInfo(payFailInfo);
                businessDegrade(failInfo);
            } else {
                if (isDegradeToStandardCashierOfBusinessDecision(payFailInfo)) { // 业务降级
                    businessDegrade(getPreComponentFailInfo(payFailInfo));
                } else {
                    payCancel();
                }
            }
        }
    }

    private boolean isLoadingCancel(PayFailInfo payFailInfo) {
        if (payFailInfo == null) {
            return false;
        }
        return payFailInfo.getErrorCode() == PayErrorCode.BACK_CANCEL;
    }

    @Override
    protected void onSLASuccess() {
        if (CashierRepeatDownGradeSwitchManager.downGrade()) {
            return;
        }
        try {
            View decorView = mCashierActivity.getWindow().getDecorView();
            decorView.setBackgroundColor(Color.parseColor("#B3000000"));
        } catch (Exception e) {
            LoganUtils.logError("HybridPrePosedMTCashierAdapter_onSLASuccess", e.getMessage());
        }
    }

    private boolean renderFailed(PayFailInfo payFailInfo) {
        if (payFailInfo == null) {
            return false;
        }
        return payFailInfo.getErrorCode() == PayErrorCode.HYBRID_PREPOSED_MTCASHIER_LOADING_ERROR;
    }

    private void payCancel() {
        mCashierActivity.onCashierCancel();
    }
}
