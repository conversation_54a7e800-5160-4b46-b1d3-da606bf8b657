package com.meituan.android.cashier.model.bean;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.pay.common.promotion.bean.PaymentReduce;
import com.meituan.android.paybase.utils.JsonBean;
import com.meituan.android.paybase.widgets.label.Label;

import java.io.Serializable;
import java.util.ArrayList;

@JsonBean
public class DCEPPayment implements Serializable {

    private static final long serialVersionUID = -9059062307371536320L;

    @SerializedName("pay_type")
    private String payType;

    private Icon icon;

    private String name;

    private int status;

    private boolean selected;

    @SerializedName("status_info")
    private String statusInfo;

    @SerializedName("card_info")
    private CardInfo cardInfo;

    @SerializedName("bottomlabels")
    private ArrayList<Label> bottomLabels;

    @SerializedName("discounts")
    private PaymentReduce paymentReduce;

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public Icon getIcon() {
        return icon;
    }

    public void setIcon(Icon icon) {
        this.icon = icon;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public String getStatusInfo() {
        return statusInfo;
    }

    public void setStatusInfo(String statusInfo) {
        this.statusInfo = statusInfo;
    }

    public CardInfo getCardInfo() {
        return cardInfo;
    }

    public void setCardInfo(CardInfo cardInfo) {
        this.cardInfo = cardInfo;
    }

    public ArrayList<Label> getBottomLabels() {
        return bottomLabels;
    }

    public void setBottomLabels(ArrayList<Label> bottomLabels) {
        this.bottomLabels = bottomLabels;
    }

    public PaymentReduce getPaymentReduce() {
        return paymentReduce;
    }

    public void setPaymentReduce(PaymentReduce paymentReduce) {
        this.paymentReduce = paymentReduce;
    }
}
