package com.meituan.android.cashier.base.utils;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 14-8-17.
 */
@Deprecated
public final class CollectionUtils {

    private CollectionUtils() {
    }

    /**
     * use {@link com.meituan.android.paybase.utils.CollectionUtils#isEmpty(Collection)} instead!
     *
     * @param list
     * @param <T>
     * @return
     */
    public static <T> boolean isEmpty(List<T> list) {
        return list == null || list.isEmpty();
    }

    /**
     * use {@link com.meituan.android.paybase.utils.CollectionUtils#isEmpty(Map)} instead!
     *
     * @param list
     * @param <T>
     * @return
     */
    public static <T> boolean isEmpty(Map<T, T> list) {
        return list == null || list.size() == 0;
    }
}
