package com.meituan.android.cashier.base.utils;

import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.model.bean.Cashier;
import com.meituan.android.cashier.model.bean.PayLaterPopDetailInfoBean;
import com.meituan.android.cashier.retrofit.CashierRequestUtils;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.paybase.utils.StatisticsUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.WeakHashMap;

public final class PayLaterAnalyseManager {
    public static final String PAGE_NAME = "c_pay_ejiowkr5";
    private static final Map<Object, PayLaterAnalyseManager> weakBeans = new WeakHashMap<>();

    private PayLaterPopDetailInfoBean mPayLaterPopDetailInfoBean = null;

    private PayLaterAnalyseManager() {
    }

    public static PayLaterAnalyseManager of(Object target) {
        if (target == null) {
            return new PayLaterAnalyseManager();
        }
        PayLaterAnalyseManager util = weakBeans.get(target);
        if (util == null) {
            util = new PayLaterAnalyseManager();
            weakBeans.put(target, util);
        }
        return util;
    }

    public void update(PayLaterPopDetailInfoBean payLaterPopDetailInfoBean) {
        mPayLaterPopDetailInfoBean = payLaterPopDetailInfoBean;
    }

    public void updateMap(Map<String, Object> map) {
        if (mPayLaterPopDetailInfoBean == null || mPayLaterPopDetailInfoBean.getPayLaterSubmitBean() == null) {
            return;
        }
        map.put("is_creditscore_show", mPayLaterPopDetailInfoBean.getScore() > PayLaterPopDetailInfoBean.MIN_SCORE ? 1 : 0); // 信用分展示是否展示：0-不展示；1-展示
        map.put("openStatus", mPayLaterPopDetailInfoBean.getPayLaterSubmitBean().openCreditPay()); // true/false是否开通买单
        map.put("marketDesc", mPayLaterPopDetailInfoBean.getPromoBubble()); // 营销文案
    }

    private String getPayType(Cashier cashier) {
        if (mPayLaterPopDetailInfoBean != null && mPayLaterPopDetailInfoBean.getPayLaterSubmitBean() != null && mPayLaterPopDetailInfoBean.getPayLaterSubmitBean().openCreditPay()) {
            return "creditpay";
        }
        MTPayment mtPayment = CashierRequestUtils.findSelectedMTPayment(cashier);
        if (mtPayment != null) {
            return mtPayment.getPayType();
        }
        return "";
    }

    public void onShow(Cashier cashier, String uniqueId) {
        // 弹窗展示时的埋点
        Map<String, Object> customTags = new HashMap<>();
        customTags.put("pay_type", getPayType(cashier));
        CashierStaticsUtils.logCustom("paybiz_paylater_dialog_show", customTags, null, uniqueId);
    }

    public void logEnsureBtnClickEvent(Cashier cashier, String uniqueId) {
        if (mPayLaterPopDetailInfoBean == null) {
            return;
        }
        Map<String, Object> valLab = new HashMap<>(4);
        valLab.put("button_name", mPayLaterPopDetailInfoBean.getRbtn());
        updateMap(valLab);
        CashierStaticsUtils.logModelEvent(PAGE_NAME, "b_pay_wzbw2j98_mc", "先享后付-支付前开通引导页-正向主按钮", valLab, StatisticsUtils.EventType.CLICK, uniqueId);
        // Cat 埋点
        Map<String, Object> customTags = new HashMap<>();
        customTags.put("pay_type", getPayType(cashier));
        customTags.put("button", "ensure");
        CashierStaticsUtils.logCustom("paybiz_paylater_dialog_click", customTags, null, uniqueId);
    }

    public void logCancelBtnClickEvent(Cashier cashier, String uniqueId) {
        if (mPayLaterPopDetailInfoBean == null) {
            return;
        }
        Map<String, Object> valLab = new HashMap<>(4);
        valLab.put("button_name", mPayLaterPopDetailInfoBean.getLbtn());
        updateMap(valLab);
        CashierStaticsUtils.logModelEvent(PAGE_NAME, "b_pay_k7dmbr58_mc", "先享后付-支付前开通引导页-负向主按钮", valLab, StatisticsUtils.EventType.CLICK, uniqueId);
        // Cat 埋点
        Map<String, Object> customTags = new HashMap<>();
        customTags.put("pay_type", getPayType(cashier));
        customTags.put("button", "cancel");
        CashierStaticsUtils.logCustom("paybiz_paylater_dialog_click", customTags, null, uniqueId);
    }
}