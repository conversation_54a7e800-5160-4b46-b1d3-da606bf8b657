package com.meituan.android.cashier.dialogfragment;

import android.app.Activity;
import android.os.Bundle;
import android.support.annotation.Nullable;

import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.dialog.CardPayFunctionGuideDialog;
import com.meituan.android.cashier.dialog.OnClickGuidePayTypeListener;
import com.meituan.android.cashier.model.bean.CashierPopWindowBean;
import com.meituan.android.paybase.common.fragment.MTPayBaseDialogFragment;
import com.meituan.android.paybase.dialog.BaseDialog;

import java.util.HashMap;
import java.util.Map;

public class CardPayFunctionGuideDialogFragment extends MTPayBaseDialogFragment {

    public static final String TAG = "CardPayFunctionGuideDialogFragment";
    private static final String PARAM_BIND_CARD_POP_WINDOW_BEAN = "bind_card_pop_window_bean";

    private OnClickGuidePayTypeListener mOnClickGuidePayTypeListener;
    private CashierPopWindowBean mCashierPopWindowBean;

    public static CardPayFunctionGuideDialogFragment newInstance(CashierPopWindowBean cashierPopWindowBean) {
        CardPayFunctionGuideDialogFragment fragment = new CardPayFunctionGuideDialogFragment();
        Bundle bundle = new Bundle();
        if (cashierPopWindowBean != null) {
            bundle.putSerializable(PARAM_BIND_CARD_POP_WINDOW_BEAN, cashierPopWindowBean);
        }
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected String getTAG() {
        return TAG;
    }

    @Override
    protected BaseDialog createDialog(Bundle savedInstanceState) {
        setCancelable(false);
        return new CardPayFunctionGuideDialog(getContext(), mCashierPopWindowBean, mOnClickGuidePayTypeListener);
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        if (getParentFragment() instanceof OnClickGuidePayTypeListener) {
            mOnClickGuidePayTypeListener = (OnClickGuidePayTypeListener) getParentFragment();
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            Bundle bundle = getArguments();
            mCashierPopWindowBean = (CashierPopWindowBean) bundle.getSerializable(PARAM_BIND_CARD_POP_WINDOW_BEAN);
        }
        if (savedInstanceState == null) {
            reportOnShow();
        }
    }

    private void reportOnShow() {
        Map<String, Object> customTags = new HashMap<>();
        customTags.put("style", "function_style");
        if (mCashierPopWindowBean != null && mCashierPopWindowBean.getPopDetailInfo() != null && mCashierPopWindowBean.getPopDetailInfo().getGuidePayTypeInfo() != null) {
            customTags.put("pay_type", mCashierPopWindowBean.getPopDetailInfo().getGuidePayTypeInfo().getPayType());
        }
        CashierStaticsUtils.logCustom("paybiz_bind_card_guide_dialog_show", customTags, null, getUniqueId());
    }

    @Override
    public void onDetach() {
        super.onDetach();
        mOnClickGuidePayTypeListener = null;
    }
}
