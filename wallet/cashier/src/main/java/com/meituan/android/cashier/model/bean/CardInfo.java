package com.meituan.android.cashier.model.bean;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;

@JsonBean
public class CardInfo implements Serializable {

    private static final long serialVersionUID = 6124346516489133803L;

    @SerializedName("token_id")
    private String tokenId;

    @SerializedName("name_ext")
    private String nameExt;

    public String getTokenId() {
        return tokenId;
    }

    public void setTokenId(String tokenId) {
        this.tokenId = tokenId;
    }

    public String getNameExt() {
        return nameExt;
    }

    public void setNameExt(String nameExt) {
        this.nameExt = nameExt;
    }
}
