package com.meituan.android.cashier.model.bean;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.pay.common.payment.bean.FinanceServiceBean;
import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

/**
 * 收银台信息
 * Created by liuxu on 14-7-4.
 */
@JsonBean
public class Cashier implements Serializable {
    private static final long serialVersionUID = 3601204569650363526L;
    //checked

    /**
     * 支付订单号
     */
    @SerializedName("tradeno")
    private String tradeNo;

    private String mobile;

    /**
     * 支付订单金额
     */
    @SerializedName("total_fee")
    private float totalFee;

    /**
     * 商品名称
     */
    @SerializedName("subject")
    private String orderName;

    @SerializedName("head_info")
    private String headInfo;

    /**
     * 支付超时时间
     */
    @SerializedName("expire_time")
    private int expireTime;
    /**
     * 服务器当前时间戳
     */
    @SerializedName("current_time")
    private int currentTime;

    @SerializedName("pay_info")
    private List<CashierPayment> paymentDataList;

    // 无用资源治理，采用 JsonObject 代替类
    @SerializedName("header_notice")
    private HashMap<String, Object> headNotice;


    @SerializedName("subject_url")
    private String subjectUrl;

    @SerializedName("order_info")
    private OrderInfo orderInfo;
    @SerializedName("bank_nppay_guide")
    private NoPswGuide noPswGuide;

    @SerializedName("upgrade_balancepay_guide")
    private int showBalancePayGuide;

    @SerializedName("order_txt")
    private String orderTxt;

    @SerializedName("remain_txt")
    private String remainTxt;

    @SerializedName("pay_title")
    private String payTitle;

    @SerializedName("finance_title")
    private String financeTitle;

    @SerializedName("third_pay_button")
    private String payButtonText;

    @SerializedName("finance_info")
    private List<FinanceServiceBean> financeDataList;

    public HashMap<String, Object> getHeadNotice() {
        return  headNotice;
    }

    public void setHeadNotice(HashMap<String, Object> headNotice) {
        this.headNotice = headNotice;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }


    public float getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(float totalFee) {
        this.totalFee = totalFee;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public int getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(int expireTime) {
        this.expireTime = expireTime;
    }

    public List<CashierPayment> getPaymentDataList() {
        return paymentDataList;
    }

    public void setPaymentDataList(List<CashierPayment> paymentDataList) {
        this.paymentDataList = paymentDataList;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getHeadInfo() {
        return headInfo;
    }

    public void setHeadInfo(String headInfo) {
        this.headInfo = headInfo;
    }


    public String getSubjectUrl() {
        return subjectUrl;
    }

    public void setSubjectUrl(String subjectUrl) {
        this.subjectUrl = subjectUrl;
    }

    public void setCurrentTime(int currentTime) {
        this.currentTime = currentTime;
    }

    public int getCurrentTime() {
        return currentTime;
    }


    public void setOrderInfo(OrderInfo orderInfo) {
        this.orderInfo = orderInfo;
    }

    public OrderInfo getOrderInfo() {
        return orderInfo;
    }

    public NoPswGuide getNoPswGuide() {
        return noPswGuide;
    }

    public void setNoPswGuide(NoPswGuide noPswGuide) {
        this.noPswGuide = noPswGuide;
    }

    public int getShowBalancePayGuide() {
        return showBalancePayGuide;
    }

    public void setShowBalancePayGuide(int showBalancePayGuide) {
        this.showBalancePayGuide = showBalancePayGuide;
    }

    public boolean shouldShowBalanceGuide() {
        return showBalancePayGuide == 1;
    }

    public String getOrderTxt() {
        return orderTxt;
    }

    public void setOrderTxt(String orderTxt) {
        this.orderTxt = orderTxt;
    }


    public String getRemainTxt() {
        return remainTxt;
    }

    public void setRemainTxt(String remainTxt) {
        this.remainTxt = remainTxt;
    }

    public String getPayTitle() {
        return payTitle;
    }

    public void setPayTitle(String payTitle) {
        this.payTitle = payTitle;
    }

    public String getFinanceTitle() {
        return financeTitle;
    }

    public void setFinanceTitle(String financeTitle) {
        this.financeTitle = financeTitle;
    }

    public String getPayButtonText() {
        return payButtonText;
    }

    public void setPayButtonText(String payButtonText) {
        this.payButtonText = payButtonText;
    }

    public List<FinanceServiceBean> getFinanceDataList() {
        return financeDataList;
    }

    public void setFinanceDataList(List<FinanceServiceBean> financeDataList) {
        this.financeDataList = financeDataList;
    }
}
