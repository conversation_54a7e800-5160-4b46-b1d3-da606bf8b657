package com.meituan.android.cashier.dialogfragment;

import static com.meituan.android.cashier.NativeStandardCashierAdapter.REQ_TAG_GO_HELLO_PAY;
import static com.meituan.android.cashier.retrofit.CashierRequestUtils.isMTPayOrCreditPay;

import android.app.Activity;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.android.cashier.NativeStandardCashierAdapter;
import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.base.utils.PayLaterAnalyseManager;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.common.ICashier;
import com.meituan.android.cashier.dialog.PayLaterGuideDialog;
import com.meituan.android.cashier.exception.GoHelloPayPayExceptionHandler;
import com.meituan.android.cashier.model.Constants;
import com.meituan.android.cashier.model.bean.Cashier;
import com.meituan.android.cashier.model.bean.MTPaymentURL;
import com.meituan.android.cashier.model.bean.PayLaterPopDetailInfoBean;
import com.meituan.android.cashier.model.bean.PayLaterSubmitBean;
import com.meituan.android.cashier.model.params.PayParams;
import com.meituan.android.cashier.newrouter.mtpaydialog.MTPayDialogResultHandler;
import com.meituan.android.cashier.newrouter.remake.CashierRouterConstants;
import com.meituan.android.cashier.retrofit.CashierRequestService;
import com.meituan.android.cashier.retrofit.CashierRequestUtils;
import com.meituan.android.cashier.util.CashierSLAMonitor;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.pay.common.payment.data.IPaymentData;
import com.meituan.android.pay.desk.pack.WalletPayManager;
import com.meituan.android.paybase.common.analyse.cat.CatUtils;
import com.meituan.android.paybase.common.fragment.MTPayBaseDialogFragment;
import com.meituan.android.paybase.config.PayBaseConfig;
import com.meituan.android.paybase.dialog.BaseDialog;
import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.paybase.utils.MTPayNeedToPersist;
import com.meituan.android.paycommon.lib.retrofit.PayRetrofit;
import com.meituan.android.paycommon.lib.utils.OuterBusinessParamUtils;
import com.meituan.android.payrouter.remake.router.manager.RouterManager;

import java.util.HashMap;

public class PayLaterGuideDialogFragment extends MTPayBaseDialogFragment implements IRequestCallback {

    private static final String TAG = "PayLaterGuideDialogFragment";
    private static final String ARG_BEAN = "PayLaterPopDetailInfoBean";
    private static final String ARG_TRADENO = "tradeNo";
    private static final String ARG_PAYTOKEN = "payToken";
    private static final String ARG_CASHIER = "cashier";
    private static final String ARG_GUIDE_PLANS = "guide_plans";
    private static final String ARG_DOWNGRADE_INFO = "downgrade_info";
    @MTPayNeedToPersist
    private PayLaterPopDetailInfoBean mPayLaterPopDetailInfoBean;
    @MTPayNeedToPersist
    private String mPayToken;
    @MTPayNeedToPersist
    private String mTradeNo;
    @MTPayNeedToPersist
    private String mGuidePlans; // json列表
    @MTPayNeedToPersist
    private String downgradeInfo;
    @MTPayNeedToPersist
    private Cashier mCashier;
    @MTPayNeedToPersist
    private boolean reportSLA = true;
    private GoHelloPayPayExceptionHandler mGoHelloPayExceptionHandler;
    private PayLaterGuideDialog.OnClickPayLaterGuideButtonListener mOnClickPayLaterGuideButtonListener;
    private MTPayDialogResultHandler resultHandler;

    @Override
    protected String getTAG() {
        return TAG;
    }

    @Override
    protected BaseDialog createDialog(Bundle savedInstanceState) {
        initOnClickGuideButton();
        return new PayLaterGuideDialog(getContext(), mPayLaterPopDetailInfoBean, mOnClickPayLaterGuideButtonListener);
    }

    @Override
    public void onViewStateRestored(@Nullable Bundle savedInstanceState) {
        super.onViewStateRestored(savedInstanceState);
        // 不保留活动恢复后，需重新设置 onClickGuideButton
        initOnClickGuideButton();
        if (getDialog() instanceof PayLaterGuideDialog) {
            ((PayLaterGuideDialog) getDialog()).setOnClickPayLaterGuideButtonListener(mOnClickPayLaterGuideButtonListener);
        }
    }

    public static PayLaterGuideDialogFragment newInstance(String guidePlans, String tradeNo, String payToken, @NonNull PayLaterPopDetailInfoBean payLaterPopDetailInfoBean, Cashier cashier, String downgradeInfo) {
        PayLaterGuideDialogFragment payLaterGuideDialogFragment = new PayLaterGuideDialogFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(ARG_BEAN, payLaterPopDetailInfoBean);
        bundle.putString(ARG_PAYTOKEN, payToken);
        bundle.putString(ARG_TRADENO, tradeNo);
        bundle.putSerializable(ARG_CASHIER, cashier);
        bundle.putString(ARG_GUIDE_PLANS, guidePlans);
        bundle.putString(ARG_DOWNGRADE_INFO, downgradeInfo);
        payLaterGuideDialogFragment.setArguments(bundle);
        return payLaterGuideDialogFragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            mPayLaterPopDetailInfoBean = (PayLaterPopDetailInfoBean) getArguments().getSerializable(ARG_BEAN);
            mCashier = (Cashier) getArguments().getSerializable(ARG_CASHIER);
            mPayToken = getArguments().getString(ARG_PAYTOKEN);
            mTradeNo = getArguments().getString(ARG_TRADENO);
            mGuidePlans = getArguments().getString(ARG_GUIDE_PLANS);
        }
        PayLaterAnalyseManager.of(this).update(mPayLaterPopDetailInfoBean);
        if (getStandardCashier() != null && reportSLA) {
            reportSLA = false;
            CashierStaticsUtils.logCustom("native_standcashier_start_succ", null, null, getUniqueId());
            String lastResumedFeature = Constants.UNKNOWN;
            if (getActivity() instanceof MTCashierActivity) {
                lastResumedFeature = ((MTCashierActivity) getActivity()).getLastResumedFeature();
            }
            CashierSLAMonitor.reportStandardCashierShowSuccess(lastResumedFeature, getUniqueId());
            CashierSLAMonitor.reportStandardCashierFinished(getUniqueId(), CashierSLAMonitor.CASHIER_FINISHED_STATUS_SUCCESS, CashierSLAMonitor.NATIVE_STANDARD_CASHIER_SUCCESS_SCENE_PAY_LATER_GUIDE);
        }
        if (savedInstanceState == null) {
            PayLaterAnalyseManager.of(this).onShow(mCashier, getUniqueId());
        }
        setCancelable(false);
    }

    @Override
    public void onResume() {
        PayLaterAnalyseManager.of(this).update(mPayLaterPopDetailInfoBean);
        super.onResume();
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        initOnClickGuideButton();
    }

    // https://km.sankuai.com/page/341245279
    private PayParams genPayParams() {
        PayParams params = null;
        if (mPayLaterPopDetailInfoBean.getPayLaterSubmitBean().openCreditPay()
                || mPayLaterPopDetailInfoBean.getPayLaterSubmitBean().bindNewCard()) { // 引导开通买单 或 引导新卡支付（不需要上传pay_type）
            params = CashierRequestUtils.genUniversalParams(mCashier, mTradeNo, mPayToken);
        } else {
            MTPayment mtPayment = CashierRequestUtils.findSelectedMTPayment(mCashier);
            if (mtPayment == null) {
                CatUtils.logError(getSubTag("onClickPayLaterGuideOpen"), "mtPayment == null");
            } else {
                if (getStandardCashier() != null) { // 埋点相关
                    getStandardCashier().setPayType(mtPayment.getPayType());
                } else if (resultHandler != null) {
                    resultHandler.onMTPaySelected(mtPayment.getPayType());
                } else {
                    CatUtils.logError(getSubTag("onClickPayLaterGuideOpen"), "standardCashier == null");
                }
                params = genMtPayParams(mCashier, mTradeNo, mPayToken, mtPayment);
            }
        }
        if (params != null) {
            appendGuidePlans(params, mGuidePlans);
            PayLaterSubmitBean submitBean = mPayLaterPopDetailInfoBean.getPayLaterSubmitBean();
            String ext = mPayLaterPopDetailInfoBean.getExt();
            if (!TextUtils.isEmpty(ext)) {
                submitBean.setExt(ext);
            }
            String utmSource = mPayLaterPopDetailInfoBean.getUtmSource();
            if (!TextUtils.isEmpty(utmSource)) {
                submitBean.setPromotionInfo("{\"utmSource\":\"" + utmSource + "\"}");
            }
            appendOpenWithholdInfoBefore(params, GsonProvider.getInstance().toJson(submitBean));
            if (params.walletPayParams == null) {
                params.walletPayParams = new HashMap<>();
            }
            CashierRequestUtils.setGoHelloPayExtParamToParams(params.walletPayParams, getGuideRequestNo(), getDowngradeErrorInfo());
        }
        return params;
    }


    /**
     * 获取极速支付、美团前置组件等降级的信息
     */
    private String getDowngradeErrorInfo() {
        NativeStandardCashierAdapter nativeStandardCashierAdapter = getStandardCashier();
        if (nativeStandardCashierAdapter == null) {
            return downgradeInfo != null ? downgradeInfo : "";
        }
        return nativeStandardCashierAdapter.getDowngradeErrorInfo();
    }

    private String getGuideRequestNo() {
        if (mPayLaterPopDetailInfoBean == null) {
            return "";
        }
        return mPayLaterPopDetailInfoBean.getGuideRequestNo();
    }


    private void onClickPayLaterGuideOpen() {
        // 网络请求成功后再关闭弹窗 onRequestSucc
        request(genPayParams());
    }

    private void onClickPayLaterGuideCancel() {
        NativeStandardCashierAdapter nativeStandardCashierAdapter = getStandardCashier();
        if (nativeStandardCashierAdapter != null) {
            dismissAllowingStateLoss();
            nativeStandardCashierAdapter.downgradeFromMeituanPay();
            return;
        }
        // 判断新路由处理逻辑
        if (resultHandler != null) {
            dismissAllowingStateLoss();
            resultHandler.onMTPayCancel("payLaterGuideDialog");
            return;
        }

        CatUtils.logError(getSubTag("onClickPayLaterGuideCancel"), "standardCashier == null");

    }

    private void initOnClickGuideButton() {
        if (mOnClickPayLaterGuideButtonListener != null) {
            return;
        }
        mOnClickPayLaterGuideButtonListener = new PayLaterGuideDialog.OnClickPayLaterGuideButtonListener() {
            @Override
            public void onClickPayLaterGuideOpen() {
                PayLaterGuideDialogFragment.this.onClickPayLaterGuideOpen();
                PayLaterAnalyseManager.of(this).logEnsureBtnClickEvent(mCashier, getUniqueId());
            }

            @Override
            public void onClickPayLaterGuideCancel() {
                PayLaterGuideDialogFragment.this.onClickPayLaterGuideCancel();
                PayLaterAnalyseManager.of(this).logCancelBtnClickEvent(mCashier, getUniqueId());
            }
        };
    }


    private void request(PayParams payParams) {
        HashMap<String, String> requestParams = CashierRequestUtils.getHelloPayMap(payParams);
        OuterBusinessParamUtils.appendExtraParamsTogoHelloPay((MTCashierActivity) getActivity(), requestParams);
        PayRetrofit.getInstance().create(CashierRequestService.class, this,
                REQ_TAG_GO_HELLO_PAY).goHelloPay(requestParams);
    }

    private PayParams genMtPayParams(Cashier cashier, String tradeNo, String payToken, IPaymentData checkedPaymentData) {
        PayParams payParams = CashierRequestUtils.genUniversalParams(cashier, tradeNo, payToken);
        if (checkedPaymentData != null) {
            if (isMTPayOrCreditPay(CashierRequestUtils.getWalletPayment(cashier), checkedPaymentData)) {
                payParams.walletPayParams = WalletPayManager.getInstance()
                        .appendRequestParams(getActivity(), CashierRequestUtils.getWalletPayment(cashier), checkedPaymentData, WalletPayManager.CASHIER_PARAMS);
            }
        }
        return payParams;
    }

    private void appendGuidePlans(PayParams payParams, String guidePlans) {
        CashierRequestUtils.appendGuidePlans(payParams, guidePlans);
    }

    private String getSubTag(String methodName) {
        return "PayLaterGuideDialogFragment_" + methodName;
    }


    private void appendOpenWithholdInfoBefore(PayParams payParams, String openWithholdInfoBefore) {
        payParams.openWithholdInfoBefore = openWithholdInfoBefore;
    }


    private NativeStandardCashierAdapter getStandardCashier() {
        if (getActivity() instanceof MTCashierActivity) {
            ICashier iCashier = ((MTCashierActivity) getActivity()).getCurrentCashier();
            if (iCashier instanceof NativeStandardCashierAdapter) {
                return (NativeStandardCashierAdapter) iCashier;
            }
        }
        return null;
    }


    private IRequestCallback getIRequestCallback() {
        if (getActivity() instanceof MTCashierActivity) {
            return (MTCashierActivity) getActivity();
        }
        return null;
    }


    @Override
    public void onDetach() {
        super.onDetach();
        mOnClickPayLaterGuideButtonListener = null;
    }

    @Override
    public void onRequestSucc(int tag, Object obj) {
        // 先处理新路由的流程
        if (resultHandler != null && obj instanceof MTPaymentURL) {
            resultHandler.onMTPayRequestSuccess((MTPaymentURL) obj);
            dismissAllowingStateLoss();
            return;
        }
        // 再处理旧路由的流程
        if (getIRequestCallback() != null) {
            getIRequestCallback().onRequestSucc(tag, obj);
            if (tag == REQ_TAG_GO_HELLO_PAY) {
                dismissAllowingStateLoss();
            }
        }
    }

    @Override
    public void onRequestException(int tag, Exception e) {
        if (getActivity() == null || getActivity().isFinishing()) {
            return;
        }
        if (mGoHelloPayExceptionHandler == null) {
            mGoHelloPayExceptionHandler = new GoHelloPayPayExceptionHandler((MTCashierActivity) getActivity()) {
                @Override
                protected void showToast(String message) {
                    ToastUtils.showSnackToast(getDialog(), message, "", ToastUtils.ToastType.TOAST_TYPE_COMMON, false);
                }

                @Override
                protected void showToast(String message, String subMsg) {
                    ToastUtils.showSnackToast(getDialog(), message, subMsg, ToastUtils.ToastType.TOAST_TYPE_EXCEPTION, false);
                }
            };
        }
        mGoHelloPayExceptionHandler.handleException(e);
    }

    @Override
    public void onRequestFinal(int tag) {
        if (getIRequestCallback() != null) {
            show(getActivity().getSupportFragmentManager());
            getIRequestCallback().onRequestFinal(tag);
        }
    }

    @Override
    public void onRequestStart(int tag) {
        if (getIRequestCallback() != null) {
            hideDialog();
            getIRequestCallback().onRequestStart(tag);
        }
    }

    @Override
    public String getPageName() {
        return PayLaterAnalyseManager.PAGE_NAME;
    }

    @Override
    public HashMap<String, Object> getPageProperties() {
        HashMap<String, Object> properties = super.getPageProperties();
        PayLaterAnalyseManager.of(this).updateMap(properties);
        properties.put("tradeNo", mTradeNo);
        properties.put("nb_version", PayBaseConfig.getProvider().getPayVersion());
        return properties;
    }

    private MTPayDialogResultHandler getResultHandler() {
        return resultHandler;
    }

    public void setResultHandler(MTPayDialogResultHandler resultHandler) {
        this.resultHandler = resultHandler;
    }
}