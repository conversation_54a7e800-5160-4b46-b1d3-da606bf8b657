package com.meituan.android.cashier.model.params;


import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.utils.JsonBean;
import com.meituan.android.paybase.utils.LoganUtils;

import java.io.Serializable;
import java.util.Map;

/**
 * Created by liuxu on 14-7-4.
 */
@JsonBean
public class PayParams implements Serializable, Cloneable {

    private static final long serialVersionUID = -4739964884799841680L;
    /**
     * 支付交易号
     */
    public String tradeNo;

    /**
     * 支付订单token
     */
    public String payToken;
    /**
     * 支付类型
     */
    public String payType;

    /**
     * 风控－支付密码
     */
    public String payPassword;

    /**
     * 风控－短信验证码
     */
    public String smsCode;

    public String campaignId;

    public String couponCode;

    public String cashierType;
    public int fromSelectBankCard;

    public int moneyChanged;

    public String upsepayType;
    public String openWithholdInfoBefore;

    public Map<String, String> walletPayParams;

    public String tokenId;

    /**
     * 风控弹窗数据
     */
    public String verifyPayType; // 支付方式
    public String verifyPayOrderId; // 支付单ID
    public String verifyType; // 验证方式, "0"代表风险提示弹窗（A类），"1"代表风险验证-人脸验证弹窗（B类），"2"代表风险验证-问题验证弹窗（B类）
    public String verifyResult; // 验证结果，"1"代表成功；"2"代表失败
    public String verifyToken; // 验证中心-人脸校验结果token，仅在人脸校验成功时传输
    public String uniqueId;

    @Override
    public PayParams clone() {
        PayParams payParams = null;
        try {
            payParams = (PayParams) super.clone();
        } catch (Exception e) {
            LoganUtils.logError("PayParams_clone", e.getMessage());
        }
        return payParams;
    }
}
