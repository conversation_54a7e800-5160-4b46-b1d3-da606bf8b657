package com.meituan.android.cashier.retrofit;

import android.text.TextUtils;

import com.meituan.android.cashier.model.Constants;
import com.meituan.android.cashier.model.bean.Cashier;
import com.meituan.android.cashier.model.bean.CashierPayment;
import com.meituan.android.cashier.model.params.PayParams;
import com.meituan.android.neohybrid.util.UnstableEnvDetectUtils;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.pay.common.payment.data.IBankcardData;
import com.meituan.android.pay.common.payment.data.IPaymentData;
import com.meituan.android.pay.common.payment.data.PayType;
import com.meituan.android.pay.common.payment.data.WalletPayParams;
import com.meituan.android.pay.common.payment.utils.PayTypeUtils;
import com.meituan.android.pay.common.selectdialog.IInsertPayment;
import com.meituan.android.pay.common.selectdialog.bean.WalletPaymentListPage;
import com.meituan.android.pay.desk.payment.bean.standarddesk.WalletPayment;
import com.meituan.android.paybase.common.analyse.cat.CatUtils;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.MTPaySuppressFBWarnings;
import com.meituan.android.paycommon.lib.config.MTPayConfig;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Created by zhuzijian on 2017/7/12.
 */

public final class CashierRequestUtils {
    private static final String CASHIER_TYPE_WALLET = "wallet";
    private static final String CASHIER_TYPE_COMMON = "common";

    private CashierRequestUtils() {

    }

    @MTPaySuppressFBWarnings("UWF_UNWRITTEN_PUBLIC_OR_PROTECTED_FIELD")
    public static HashMap<String, String> getHelloPayMap(PayParams payParams) {
        if (payParams == null) {
            return null;
        }
        HashMap<String, String> map = new HashMap<>();
        map.put("tradeno", payParams.tradeNo);
        map.put("pay_token", payParams.payToken);
        map.put("from_mt_cashier", String.valueOf(1));
        map.put("cashier_type", payParams.cashierType);
        map.put("money_changed", String.valueOf(payParams.moneyChanged));
        // 1 表示true
        map.put("from_select_bankcard", String.valueOf(payParams.fromSelectBankCard));
        if (!TextUtils.isEmpty(payParams.openWithholdInfoBefore)) {
            map.put("openWithholdInfoBefore", payParams.openWithholdInfoBefore);
        }
        if (!CollectionUtils.isEmpty(payParams.walletPayParams)) {
            map.putAll(payParams.walletPayParams);
        }
        putExtDimStat(map);
        return map;
    }

    @MTPaySuppressFBWarnings("UWF_UNWRITTEN_PUBLIC_OR_PROTECTED_FIELD")
    public static HashMap<String, String> getDirectPayMap(PayParams payParams, String imsi) {
        if (payParams == null) {
            return null;
        }
        HashMap<String, String> map = new HashMap<>();
        map.put("tradeno", payParams.tradeNo);
        map.put("pay_token", payParams.payToken);
        map.put("pay_type", payParams.payType);
        if (!TextUtils.isEmpty(payParams.campaignId)) {
            map.put("campaign_id", payParams.campaignId);
        }
        if (!TextUtils.isEmpty(payParams.couponCode)) {
            map.put("cashticket_code", payParams.couponCode);
        }
        if (!TextUtils.isEmpty(payParams.upsepayType)) {
            map.put("upsepay_type", payParams.upsepayType);
        }
        if (!TextUtils.isEmpty(payParams.tokenId)) {
            map.put("token_id", payParams.tokenId);
        }
        if (!TextUtils.isEmpty(payParams.verifyPayType)) {
            map.put("verify_pay_type", payParams.verifyPayType);
        }
        if (!TextUtils.isEmpty(payParams.verifyPayOrderId)) {
            map.put("verify_pay_order_id", payParams.verifyPayOrderId);
        }
        if (!TextUtils.isEmpty(payParams.verifyType)) {
            map.put("verify_type", payParams.verifyType);
        }
        if (!TextUtils.isEmpty(payParams.verifyResult)) {
            map.put("verify_result", payParams.verifyResult);
        }
        if (!TextUtils.isEmpty(payParams.verifyToken)) {
            map.put("verify_token", payParams.verifyToken);
        }
        return map;
    }

    @MTPaySuppressFBWarnings("UWF_UNWRITTEN_PUBLIC_OR_PROTECTED_FIELD")
    public static PayParams genUniversalParams(Cashier cashier, String tradeNo, String payToken) {
        PayParams payParams = new PayParams();
        payParams.tradeNo = tradeNo;
        payParams.payToken = payToken;
        payParams.cashierType = CashierRequestUtils.getCashierType(cashier);
        return payParams;
    }

    public static WalletPayment getWalletPayment(Cashier cashier) {
        if (cashier == null) {
            return null;
        }
        List<CashierPayment> list = cashier.getPaymentDataList();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        for (int i = 0; i < list.size(); i++) {
            CashierPayment cashierPayment = list.get(i);
            if (cashierPayment != null && PayTypeUtils.isWalletPay(cashierPayment.getPayType())) {
                return cashierPayment;
            }
        }
        return null;
    }

    /**
     * 判断当前支付方式是否为美团支付，判断条件为 iPaymentData 是否为 walletPayment 中的数据
     */
    public static boolean isMTPayOrCreditPay(WalletPayment walletPayment, IPaymentData iPaymentData) {
        if (iPaymentData == null) {
            return false;
        }
        // 判断月付支付方式
        if (TextUtils.equals(PayType.CREDIT_PAY, iPaymentData.getPayType())) {
            return true;
        }
        if (walletPayment != null) {
            String payType = iPaymentData.getPayType();
            List<MTPayment> recommendPayment = walletPayment.getRecommendPayment();
            if (isContainSpecificPayType(recommendPayment, payType)) {
                return true;
            }
            WalletPaymentListPage walletPaymentListPage = walletPayment.getWalletPaymentListPage();
            if (walletPaymentListPage != null) {
                List<IBankcardData> mtPaymentList = walletPaymentListPage.getMtPaymentList();
                if (isContainSpecificPayType(mtPaymentList, payType)) {
                    return true;
                }
                List<IInsertPayment> insertPaymentsList = walletPaymentListPage.getInsertPaymentsList();
                for (IInsertPayment iInsertPayment : insertPaymentsList) {
                    List<IBankcardData> list = iInsertPayment.getMtMorePaymentList();
                    if (isContainSpecificPayType(list, payType)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private static boolean isContainSpecificPayType(List collection, String specificPayType) {
        if (CollectionUtils.isEmpty(collection) || TextUtils.isEmpty(specificPayType)) {
            return false;
        }
        for (Object o : collection) {
            if (o instanceof MTPayment) {
                if (TextUtils.equals(((MTPayment) o).getPayType(), specificPayType)) {
                    return true;
                }
            } else if (o instanceof IBankcardData) {
                if (TextUtils.equals(((IBankcardData) o).getPayType(), specificPayType)) {
                    return true;
                }
            }
        }
        return false;
    }

    public static String getCashierType(Cashier cashier) {
        List<CashierPayment> list = cashier.getPaymentDataList();
        if (!CollectionUtils.isEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                final IPaymentData iPaymentData = list.get(i);
                if (PayTypeUtils.isWalletPay(iPaymentData.getPayType())) {
                    return CASHIER_TYPE_WALLET;
                }
            }
        }
        return CASHIER_TYPE_COMMON;
    }

    /**
     * 找到第一个选中的美团支付方式
     *
     * @return
     */
    public static MTPayment findSelectedMTPayment(Cashier cashier) {
        WalletPayment walletPayment = CashierRequestUtils.getWalletPayment(cashier);
        if (walletPayment == null) {
            return null;
        }
        List<MTPayment> recommendPayment = walletPayment.getRecommendPayment();
        if (CollectionUtils.isEmpty(recommendPayment)) {
            return null;
        }
        for (MTPayment mtPayment : recommendPayment) {
            if (mtPayment.isSelected()) {
                return mtPayment;
            }
        }
        return null;
    }

    public static void appendGuidePlans(PayParams payParams, String guidePlans) {
        payParams.walletPayParams = appendGuidePlans(payParams.walletPayParams, guidePlans);
    }

    public static String extendPayExtendParams(String payExtendParamStr, String key, String appendValue) {
        JSONObject payExtendParamObj;
        if (TextUtils.isEmpty(payExtendParamStr)) {
            payExtendParamObj = new JSONObject();
        } else {
            try {
                payExtendParamObj = new JSONObject(payExtendParamStr);
            } catch (JSONException e) {
                payExtendParamObj = new JSONObject();
                CatUtils.logError(getSubTag("extendPayExtendParams"), "payExtendParamStr 解析错误");
                LoganUtils.logError("CashierRequestUtils_extendPayExtendParams", e.getMessage());
            }
        }
        try {
            payExtendParamObj.put(key, appendValue);
        } catch (JSONException e) {
            LoganUtils.logError("CashierRequestUtils_extendPayExtendParams_put", e.getMessage());
            CatUtils.logError(getSubTag("extendPayExtendParams"), "put 错误");
        }
        return payExtendParamObj.toString();
    }


    public static Map<String, String> appendGuidePlans(Map<String, String> params, String guidePlans) {
        if (params == null) {
            params = new HashMap<>();
        }
        JSONObject payExtendParamObj;
        if (CollectionUtils.isEmpty(params)) {
            payExtendParamObj = new JSONObject();
        } else {
            String payExtendParamStr = params.get(WalletPayParams.KEY_PAY_EXTEND_PARAMS);
            if (TextUtils.isEmpty(payExtendParamStr)) {
                payExtendParamObj = new JSONObject();
            } else {
                try {
                    payExtendParamObj = new JSONObject(payExtendParamStr);
                } catch (JSONException e) {
                    payExtendParamObj = new JSONObject();
                    CatUtils.logError(getSubTag("appendGuidePlans"), "payExtendParamStr 解析错误");
                    LoganUtils.logError("CashierRequestUtils_appendGuidePlans", e.getMessage());
                }
            }
        }
        try {
            payExtendParamObj.put(Constants.KEY_GUIDE_PLANS, guidePlans);
        } catch (JSONException e) {
            LoganUtils.logError("CashierRequestUtils_appendGuidePlans_put", e.getMessage());
            CatUtils.logError(getSubTag("appendGuidePlans"), "put 错误");
        }
        params.put(WalletPayParams.KEY_PAY_EXTEND_PARAMS, payExtendParamObj.toString());
        return params;
    }

    public static Map<String, String> appendTransmissionParams(Map<String, String> params, String transmissionParam) {
        if (params == null) {
            params = new HashMap<>();
        }
        String payExtendParamStr = params.get(WalletPayParams.KEY_PAY_EXTEND_PARAMS);
        payExtendParamStr = extendPayExtendParams(payExtendParamStr, "transmission_param", transmissionParam);
        params.put(WalletPayParams.KEY_PAY_EXTEND_PARAMS, payExtendParamStr);
        return params;
    }

    private static String getSubTag(String tag) {
        return "CashierRequestUtils_" + tag;
    }


    // gohellopay中添加极速支付失败原因（第三方支付在direct pay中传递）、guideRequestNo
    public static void setGoHelloPayExtParamToParams(Map<String, String> payParams, String guideRequestNo, String downgradeErrorInfo) {
        if (payParams == null) {
            return;
        }
        appendToExtParams(payParams, downgradeErrorInfo);

        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(Constants.KEY_GUIDE_REQUEST_NO, guideRequestNo);
            appendToExtParams(payParams, jsonObject.toString());
        } catch (JSONException e) {
            LoganUtils.logError("CashierRequestUtils_setGoHelloPayExtParamToParams", e.getMessage());
        }
    }

    private static void appendToExtParams(Map<String, String> payParams, String jsonParam) {
        if (!TextUtils.isEmpty(jsonParam)) {
            String extParam = payParams.get(Constants.KEY_EXT_PARAM);
            if (TextUtils.isEmpty(extParam)) {
                payParams.put(Constants.KEY_EXT_PARAM, jsonParam);
            } else {
                // 如果这个ext_param已经赋值过，则进行追加合并
                try {
                    JSONObject oldExtParam = new JSONObject(extParam);
                    JSONObject newExtParam = new JSONObject(jsonParam);
                    payParams.put(Constants.KEY_EXT_PARAM, mergeJSONObject(oldExtParam, newExtParam));
                } catch (JSONException e) {
                    LoganUtils.logError("CashierRequestUtils_appendToExtParams", e.getMessage());
                }
            }
        }
    }

    // 合并两个JSONObject
    private static String mergeJSONObject(JSONObject oldJson, JSONObject newJson) {
        Iterator<String> iterator = newJson.keys();
        while (iterator.hasNext()) {
            String key = iterator.next();
            Object value = null;
            try {
                value = newJson.get(key);
                oldJson.put(key, value);
            } catch (JSONException e) {
                LoganUtils.logError("CashierRequestUtils_mergeJSONObject", e.getMessage());
            }
        }
        return oldJson.toString();
    }

    /**
     * 增加低质量参数
     *
     * @param map
     */
    private static void putExtDimStat(HashMap<String, String> map) {
        if (map.containsKey(WalletPayParams.KEY_EXT_DIM_STAT)) {
            String extDimStat = map.get(WalletPayParams.KEY_EXT_DIM_STAT);
            try {
                JSONObject json = new JSONObject(extDimStat);
                map.put(WalletPayParams.KEY_EXT_DIM_STAT, getEnvData(json));
            } catch (JSONException e) {
                LoganUtils.logError("CashierRequestUtils_putExtDimStat", e.getMessage());
            }
        } else {
            JSONObject json = new JSONObject();
            map.put(WalletPayParams.KEY_EXT_DIM_STAT, getEnvData(json));
        }
    }

    private static String getEnvData(JSONObject json) {
        try {
            json.put("good_network", UnstableEnvDetectUtils.hasGoodNetwork(MTPayConfig.getProvider().getApplicationContext()));
            json.put("sys_version_available", UnstableEnvDetectUtils.isSystemVersionEnabled());
        } catch (JSONException e) {
            LoganUtils.logError("CashierRequestUtils_getEnvData", e.getMessage());
        }
        return json.toString();
    }

}
