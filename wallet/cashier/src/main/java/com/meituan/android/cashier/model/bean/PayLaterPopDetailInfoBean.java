package com.meituan.android.cashier.model.bean;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;
import java.util.List;

/**
 * 文档地址 https://km.sankuai.com/page/328950112
 */
@JsonBean
public class PayLaterPopDetailInfoBean implements Serializable {

    private static final long serialVersionUID = 6561534117838257057L;
    public static final int MIN_SCORE = 0;
    private String title;
    private String detail;
    @SerializedName("lbtnText")
    private String lbtn;
    @SerializedName("rbtnText")
    private String rbtn;
    @SerializedName("beliveName")
    private String scoreName;
    @SerializedName("beliveScore")
    private int score;
    @SerializedName("marketingLink")
    private String guidePicture;
    @SerializedName("buttonStyleText")
    private String promoBubble;
    @SerializedName("agreementList")
    private List<PayLaterAgreementBean> agreementList;
    @SerializedName("submitData")
    private PayLaterSubmitBean payLaterSubmitBean;
    @SerializedName("guideRequestNo")
    private String guideRequestNo;

    @SerializedName("believeScoreBizLogo")
    private String believeScoreBizLogo;

    @SerializedName("needUserCheck")
    private boolean needUserCheck;

    // 用户有营销时返回
    @SerializedName("utmSource")
    private String utmSource;

    // 扩展字段，后续有迭代时，在此基础上增加
    @SerializedName("ext")
    private String ext;

    public boolean isNeedUserCheck() {
        return needUserCheck;
    }

    public void setNeedUserCheck(boolean needUserCheck) {
        this.needUserCheck = needUserCheck;
    }



    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getLbtn() {
        return lbtn;
    }

    public void setLbtn(String lbtn) {
        this.lbtn = lbtn;
    }

    public String getRbtn() {
        return rbtn;
    }

    public void setRbtn(String rbtn) {
        this.rbtn = rbtn;
    }

    public String getScoreName() {
        return scoreName;
    }

    public void setScoreName(String scoreName) {
        this.scoreName = scoreName;
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }

    public String getGuidePicture() {
        return guidePicture;
    }

    public void setGuidePicture(String guidePicture) {
        this.guidePicture = guidePicture;
    }

    public List<PayLaterAgreementBean> getAgreementList() {
        return agreementList;
    }

    public void setAgreementList(List<PayLaterAgreementBean> agreementList) {
        this.agreementList = agreementList;
    }

    public PayLaterSubmitBean getPayLaterSubmitBean() {
        return payLaterSubmitBean;
    }

    public void setPayLaterSubmitBean(PayLaterSubmitBean payLaterSubmitBean) {
        this.payLaterSubmitBean = payLaterSubmitBean;
    }

    public String getPromoBubble() {
        return promoBubble;
    }

    public void setPromoBubble(String promoBubble) {
        this.promoBubble = promoBubble;
    }


    public String getGuideRequestNo() {
        return guideRequestNo;
    }

    public void setGuideRequestNo(String guideRequestNo) {
        this.guideRequestNo = guideRequestNo;
    }

    public String getBelieveScoreBizLogo() {
        return believeScoreBizLogo;
    }

    public void setBelieveScoreBizLogo(String believeScoreBizLogo) {
        this.believeScoreBizLogo = believeScoreBizLogo;
    }

    public String getUtmSource() {
        return utmSource;
    }

    public void setUtmSource(String utmSource) {
        this.utmSource = utmSource;
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }
}
