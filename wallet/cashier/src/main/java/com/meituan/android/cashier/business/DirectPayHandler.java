package com.meituan.android.cashier.business;

import android.app.Activity;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.meituan.android.cashier.R;
import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.model.bean.PayResult;
import com.meituan.android.paybase.common.dialog.PayDialog;
import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.retrofit.PayException;
import com.meituan.android.paybase.utils.ActivityStatusChecker;
import com.meituan.android.paycommon.lib.coupon.model.Promotion;
import com.meituan.android.paycommon.lib.utils.ExceptionUtils;
import com.meituan.android.payrouter.utils.ProxyUtils;

/**
 * 包含以下功能：
 * 1. 处理网络错误
 * 2. 处理通用业务错误
 * 3. 判断过载以及业务成功，并调用业务层方法
 * 4. 处理Promotion的超时逻辑
 */
public class DirectPayHandler extends PayExceptionHandler {
    private final OverLoadHandler overLoadHandler;
    private final DirectPayResultHandler resultHandler;

    public DirectPayHandler(Activity activity, OverLoadHandler overLoadHandler, DirectPayResultHandler resultHandler) {
        super(activity);
        this.overLoadHandler = overLoadHandler;
        this.resultHandler = resultHandler;
    }

    /**
     * @param payResult directPay的响应结果
     */
    public void onResponse(PayResult payResult) {
        if (payResult == null) {
            return;
        }
        // 如果OverLoad处理了，则不处理支付相关逻辑
        if (!overLoadHandler.handleOverLoad(payResult.getOverLoadInfo())) {
            // 如果没有OverLoad，则处理Promotion以及支付相关逻辑
            Promotion promotion = payResult.getPromotion();
            if (promotion != null) {
                promotion.setEffectiveTimestamp(System.currentTimeMillis());
            }
            // 处理支付相关逻辑
            getResultHandler().onDirectPaySuccess(payResult.getPayType(), payResult.getUrl(), promotion);
        }
    }

    @Override
    protected void handlePayException(@NonNull PayException payException) {
        Activity activity = getActivity();
        if (!ActivityStatusChecker.isValid(activity)) {
            return;
        }
        int errorCode = payException.getCode();
        String errorMessage = payException.getMessage();
        if (errorCode == CashierErrorConstants.NEED_VERIFY_SMS_CODE) {
            showToast(getString(R.string.cashier_common__error_msg_pay_later));
        } else if (errorCode == CashierErrorConstants.ALREADY_PAYED) {
            new PayDialog.Builder(activity)
                    .msg(errorMessage)
                    .subMsg(payException.getErrorCodeStr())
                    .rightBtn(PayDialog.DEFAULT_BUTTON_TEXT, (dialog) -> getResultHandler().onAlreadyPayed())
                    .build().show();
        } else {
            ExceptionUtils.handleException(activity, payException, MTCashierActivity.class);
        }
    }

    @Override
    protected void handleNonPayException(@Nullable Exception exception) {
        ToastUtils.showSnackToast(getActivity(), R.string.cashier__error_msg_pay_later);
    }

    private DirectPayResultHandler getResultHandler() {
        return ProxyUtils.nonNullObject(DirectPayResultHandler.class, resultHandler);
    }
}
