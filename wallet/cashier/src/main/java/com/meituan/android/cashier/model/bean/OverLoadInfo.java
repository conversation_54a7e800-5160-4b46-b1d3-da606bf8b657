package com.meituan.android.cashier.model.bean;

import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;

/**
 * Created by ljj
 * Date:16/1/21
 * Time:上午11:28
 */
@JsonBean
public class OverLoadInfo implements Serializable {
    private static final long serialVersionUID = -7170119658884950012L;
    //checked
    private boolean status;
    private long timeout;
    private String message;

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public long getTimeout() {
        return timeout;
    }

    public void setTimeout(long timeout) {
        this.timeout = timeout;
    }

    public static boolean isOverLoad(OverLoadInfo info) {
        return info != null && info.isStatus();
    }
}
