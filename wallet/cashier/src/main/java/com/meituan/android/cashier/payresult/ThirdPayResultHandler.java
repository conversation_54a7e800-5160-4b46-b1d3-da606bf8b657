package com.meituan.android.cashier.payresult;

import android.text.TextUtils;

import com.meituan.android.cashier.NativeStandardCashierAdapter;
import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.common.CashierListener;
import com.meituan.android.cashier.common.ICashier;
import com.meituan.android.cashier.model.bean.OrderResult;
import com.meituan.android.cashier.retrofit.CashierReqTagConstant;
import com.meituan.android.cashier.retrofit.CashierRequestService;
import com.meituan.android.common.sniffer.annotation.SnifferThrow;
import com.meituan.android.pay.activity.PayActivity;
import com.meituan.android.paybase.common.activity.PayBaseActivity;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.moduleinterface.payment.PayActionListener;
import com.meituan.android.paybase.moduleinterface.payment.PayFailInfo;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.utils.CashierScreenSnapShotUtil;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paycommon.lib.retrofit.PayRetrofit;
import com.meituan.android.paymentchannel.PayersID;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * 处理第三方支付的返回结果
 */
public class ThirdPayResultHandler implements IRequestCallback {
    private static final int REQ_TAG_THIRD_PAY_FAIL_QUERY = CashierReqTagConstant.REQ_TAG_THIRD_PAY_FAIL_QUERY;
    private final CashierListener mCashierListener;
    private final PayBaseActivity mAvtivity;
    private String mQueryOrderScene; //埋点统计订单查询接口的scene
    private final String mTradeNo;
    private final String mPayToken;
    private final String mExtraData;
    private final String mExtraStatics;
    private ICashier interruptCashier; //处理三方中断的cashier ，目前只有标准收银台
    private String failMsg;

    public ThirdPayResultHandler(CashierListener cashierListener, PayBaseActivity mAvtivity, String tradeNo, String payToken, String extraData, String extraStatics) {
        this.mCashierListener = cashierListener;
        this.mAvtivity = mAvtivity;
        this.mTradeNo = tradeNo;
        this.mPayToken = payToken;
        this.mExtraData = extraData;
        this.mExtraStatics = extraStatics;
    }

    /**
     * 微信支付会在标准收银台start 的时候请求一次，是确保切到微信App但是直接从后台进入前台时没有走onGotPayResult 也能拿结果
     *
     * @param queryOrderScene
     */
    public void queryOrder(String queryOrderScene, HashMap<String, String> extendTransmissionParams) {
        this.mQueryOrderScene = queryOrderScene;
        PayRetrofit.getInstance().create(CashierRequestService.class, this, REQ_TAG_THIRD_PAY_FAIL_QUERY)
                .queryOrder(mTradeNo, mPayToken, "1", mExtraData, getExtDimStat(), extendTransmissionParams);
    }

    private String getExtDimStat() {
        if (TextUtils.isEmpty(mExtraStatics)) {
            return "";
        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("outer_business_statics", mExtraStatics);
        } catch (Exception e) {
            LoganUtils.logError("ThirdPayResultHandler_getExtDimStat", e.getMessage());
        }
        return jsonObject.toString();
    }

    //这里拿到了第三方支付的结果
    public void onGotPayResult(String payType, int payResult, PayFailInfo failInfo, String uniqueId, HashMap<String, String> extendTransmissionParams) {
        if (payResult == PayActionListener.SUCCESS) {
            if (TextUtils.equals(payType, PayersID.ID_ALIPAYHK_APP)) {
                // AlipayHK 结果不感知状态，需要 queryOrder 查询支付状态
                queryOrder("AlipayHK 支付", extendTransmissionParams);
            } else {
                Map<String, Object> reportMap = new HashMap<>();
                reportMap.put("pay_type", payType);
                reportMap.put("class", "ThirdPayResultHandler");
                LoganUtils.log("收银台支付成功后埋点", reportMap);
                CashierScreenSnapShotUtil.captureSnapShot(mAvtivity, success -> {
                    mCashierListener.onCashierPaySuccess(null);
                });
            }
        } else if (payResult == PayActionListener.CANCEL) {
            if (mAvtivity instanceof MTCashierActivity) {
                //与ios同步，微信，支付宝，安卓pay,云闪付 除返回成功外的结果查一下queryOrder
                if (TextUtils.equals(payType, PayersID.ID_ALIPAY_MINI)
                        || TextUtils.equals(payType, PayersID.ID_UNION_FLASH_PAY)
                        || TextUtils.equals(payType, PayersID.ID_UPSEPAY)
                        || TextUtils.equals(payType, PayersID.ID_ALIPAYWAP)
                        || TextUtils.equals(payType, PayersID.ID_WEIXINPAY)
                        || TextUtils.equals(payType, PayersID.ID_UPPAY)
                        || TextUtils.equals(payType, PayersID.ID_ALIPAYHK_APP)) {
                    // 目前云闪付和安卓pay的回调共用ID_UPPAY方式，所以不能下线ID_UPPAY判断
                    queryOrder("第三方支付失败", extendTransmissionParams);
                } else {
                    ((MTCashierActivity) mAvtivity).onPayCancel();
                }
            } else if (mAvtivity instanceof PayActivity) {
                mCashierListener.onCashierCancel();
            }
        } else if (payResult == PayActionListener.FAIL) {
            failMsg = failInfo != null ? failInfo.getMsg() : "";
            switch (payType) {
                case PayersID.ID_ALIPAY_MINI:
                    reportAliPayFail();
                    queryOrder("支付宝支付失败", extendTransmissionParams);
                    break;
                case PayersID.ID_WEIXINPAY:
                    reportWeixinPayFail();
                    queryOrder("微信支付失败", extendTransmissionParams);
                    break;
                case PayersID.ID_UPSEPAY:
                case PayersID.ID_UNION_FLASH_PAY:
                case PayersID.ID_ALIPAYWAP:
                case PayersID.ID_UPPAY:
                case PayersID.ID_ALIPAYHK_APP:
                    // 目前云闪付和安卓pay的回调共用ID_UPPAY方式，所以不能下线ID_UPPAY判断
                    reportOtherPayFail();
                    queryOrder("三方支付失败", extendTransmissionParams);
                    break;
                default:
                    reportOtherPayFail();
                    mCashierListener.onCashierPayFail(failMsg);
                    break;
            }
        }
    }

    @Override
    public void onRequestSucc(int tag, Object obj) {
        if (obj == null || isDestroyed()) {
            if (obj == null) {
                AnalyseUtils.techMis("b_pay_pfjic30w_mv", new AnalyseUtils.MapBuilder()
                        .add("scene", "o == null")
                        .add("tag", tag + "")
                        .build());
            } else {
                AnalyseUtils.techMis("b_pay_pfjic30w_mv", new AnalyseUtils.MapBuilder()
                        .add("scene", "isDestroyed")
                        .add("tag", tag + "")
                        .build());
            }
            return;
        }
        AnalyseUtils.techMis("b_ruzoirdm", new AnalyseUtils.MapBuilder()
                .add("scene", mQueryOrderScene).build());
        OrderResult result = (OrderResult) obj;
        if (result.isResult()) {
            CashierScreenSnapShotUtil.captureSnapShot(mAvtivity, success -> {
                mCashierListener.onCashierPaySuccess(null);
            });
        } else {
            if (!TextUtils.isEmpty(failMsg)) {
                ToastUtils.showSnackToast(mAvtivity, failMsg, true);
            }
            LoganUtils.logError("ThirdPayResultHandler_onRequestSucc", failMsg);
            //查询queryOrder返回失败后，标准收银台判断会走三方中断弹窗逻辑，其他收银台不处理
            if (interruptCashier instanceof NativeStandardCashierAdapter) {
                ((NativeStandardCashierAdapter) interruptCashier).handleThirdPay();
            }
            if (mAvtivity != null && mAvtivity instanceof MTCashierActivity) {
                ((MTCashierActivity) mAvtivity).setCouponOutOfDate(false);
            }
        }
    }

    @Override
    public void onRequestException(int tag, Exception e) {
        AnalyseUtils.techMis("b_pay_v3zwwi9x_mv", null);
        if (mAvtivity != null && mAvtivity instanceof MTCashierActivity) {
            ((MTCashierActivity) mAvtivity).setCouponOutOfDate(false);
        }
    }

    @Override
    public void onRequestFinal(int tag) {
        mAvtivity.hideProgress();
    }

    @Override
    public void onRequestStart(int tag) {
        mAvtivity.showMTProgress(true, PayBaseActivity.ProcessType.CASHIER, null);
    }

    private boolean isDestroyed() {
        return mAvtivity != null && (mAvtivity.isFinishing() || mAvtivity.isActivityDestroyed());
    }

    @SnifferThrow(module = "meituan_payment_cashier_other_fail", describe = "other pay fail")
    private void reportOtherPayFail() {
        // 防止编译内联，也可以互相印证
        LoganUtils.log("ThirdPayResultHandler_meituan_payment_cashier_other_fail");
    }

    @SnifferThrow(module = "meituan_payment_cashier_weixin_fail", describe = "weixin pay fail")
    private void reportWeixinPayFail() {
        // 防止编译内联，也可以互相印证
        LoganUtils.log("ThirdPayResultHandler_meituan_payment_cashier_weixin_fail");
    }

    @SnifferThrow(module = "meituan_payment_cashier_ali_fail", describe = "ali pay fail")
    private void reportAliPayFail() {
        // 防止编译内联，也可以互相印证
        LoganUtils.log("ThirdPayResultHandler_meituan_payment_cashier_ali_fail");
    }

    public void setHandleInterruptCashier(ICashier standardCashier) {
        this.interruptCashier = standardCashier;
    }
}
