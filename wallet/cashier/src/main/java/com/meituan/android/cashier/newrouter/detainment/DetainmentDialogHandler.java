package com.meituan.android.cashier.newrouter.detainment;

import android.app.Dialog;
import android.support.v4.app.FragmentActivity;
import android.support.v4.content.ContextCompat;
import android.text.TextUtils;

import com.meituan.android.cashier.R;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.model.bean.Cashier;
import com.meituan.android.cashier.model.bean.CashierPayment;
import com.meituan.android.cashier.model.bean.RetainWindow;
import com.meituan.android.cashier.model.bean.SubmitData;
import com.meituan.android.cashier.newrouter.CashierBusinessHandler;
import com.meituan.android.cashier.newrouter.NewCashierParams;
import com.meituan.android.cashier.utils.RetainWindowHandler;
import com.meituan.android.pay.common.payment.data.IBankcardData;
import com.meituan.android.pay.common.payment.utils.PayTypeUtils;
import com.meituan.android.pay.desk.pack.WalletPayManager;
import com.meituan.android.paybase.common.dialog.PayDialog;
import com.meituan.android.paybase.dialog.BasePayDialog;
import com.meituan.android.paybase.utils.ActivityStatusChecker;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.MapUtils;
import com.meituan.android.paybase.utils.StatisticsUtils;
import com.meituan.android.paycommon.lib.abtest.CommonABTestManager;
import com.meituan.android.paycommon.lib.config.MTPayConfig;

import java.util.Map;

public class DetainmentDialogHandler extends CashierBusinessHandler implements RetainWindowHandler.RetainWindowShowListener {
    private final DetainmentDialogResultHandler resultHandler;
    private RetainWindow retainWindow;

    private Dialog detainmentDialog;
    private final RetainWindowHandler retainWindowHandler;
    private boolean detainmentDialogShowed = false;

    public DetainmentDialogHandler(FragmentActivity activity, NewCashierParams cashierParams, DetainmentDialogResultHandler resultHandler) {
        super(activity, cashierParams);
        this.resultHandler = resultHandler;
        this.retainWindowHandler = new RetainWindowHandler(this);
    }

    public DetainmentDialogHandler setRetainWindow(RetainWindow retainWindow) {
        this.retainWindow = retainWindow;
        return this;
    }

    public boolean show() {
        if (detainmentDialog != null && detainmentDialog.isShowing()) {
            return true;
        }
        // 使用新的挽留弹窗样式
        if (CommonABTestManager.isNewRetainWindow()) {
            NewCashierParams cashierParams = getCashierParams();
            return retainWindowHandler.requestRetainWindow(
                    cashierParams.getTradeNo(),
                    cashierParams.getPayToken(),
                    String.valueOf(cashierParams.getInstalledApps()),
                    MTPayConfig.getProvider().getFingerprint(),
                    cashierParams.getDowngradeInfo(),
                    cashierParams.getExtraData(),
                    cashierParams.getExtDimStat(),
                    cashierParams.getExtendTransmissionParams());
        }
        // 使用旧的挽留弹窗样式
        return showRetainWindow(retainWindow);
    }

    public IBankcardData searchMTPayment(Cashier cashier, SubmitData submitData) {
        CashierPayment cashierPayment = null;
        if (cashier != null && !CollectionUtils.isEmpty(cashier.getPaymentDataList())) {
            for (CashierPayment payment : cashier.getPaymentDataList()) {
                if (PayTypeUtils.isWalletPay(payment.getPayType())) {
                    cashierPayment = payment;
                    break;
                }
            }
            if (cashierPayment != null && cashierPayment.getWalletPaymentListPage() != null
                    && !CollectionUtils.isEmpty(cashierPayment.getWalletPaymentListPage().getMtPaymentList())) {
                for (IBankcardData mtPayment : cashierPayment.getWalletPaymentListPage().getMtPaymentList()) {
                    if (submitData == null) {
                        break;
                    }
                    if ((PayTypeUtils.isNewCardPay(submitData.getPayType()) && TextUtils.equals(submitData.getPayType(), mtPayment.getPayType()))
                            || (PayTypeUtils.isSelectNewCardPay(submitData.getPayType())
                            && TextUtils.equals(submitData.getPayType(), mtPayment.getPayType())
                            && TextUtils.equals(submitData.getBankType(), mtPayment.getBankType()))) {
                        return mtPayment;
                    }
                }
            }
        }
        return null;
    }

    public void onDestroy() {
        notifyDialogDismiss();
        retainWindowHandler.onDestroy();
    }

    private boolean showRetainWindow(RetainWindow retainWindow) {
        FragmentActivity activity = getActivity();
        if (!ActivityStatusChecker.isValid(activity) || !RetainWindow.isValid(retainWindow)) {
            return false;
        }
        if (retainWindow.isDefaultRetainType()) {
            if (TextUtils.equals(CommonABTestManager.getDetainmentDialogGroupType(), CommonABTestManager.GROUP_TYPE_B)) {
                return showDetainmentDialog(retainWindow);
            }
            getResultHandler().onDetainmentDialogCanceled();
            return true;
        } else if (retainWindow.isAlipayRetainType()
                || retainWindow.isBankselectpayRetainType()
                || retainWindow.isCardpayRetainType()) {
            return showDetainmentDialog(retainWindow);
        }
        return false;
    }

    private boolean showDetainmentDialog(RetainWindow retainWindow) {
        if (detainmentDialogShowed) {
            return false;
        }
        BasePayDialog.Builder builder = new PayDialog.Builder(getActivity())
                .title(retainWindow.getTitle())
                .msg(retainWindow.getDetail())
                .leftBtn(retainWindow.getLeftButton(), dialog -> {
                    getResultHandler().onDetainmentDialogCanceled();
                    notifyDialogDismiss();
                })
                .rightBtn(retainWindow.getRightButton(), dialog -> {
                    WalletPayManager.getInstance().setEntry(getActivity(), WalletPayManager.ENTRY_FROM_CASHIER_DETAINMENT_DIALOG);
                    SubmitData submitData = retainWindow.getSubmitData();
                    if (submitData == null) {
                        return;
                    }
                    if (retainWindow.isAlipayRetainType()) {
                        getResultHandler().onSelectedThirdPay(submitData);
                    }
                    if ((retainWindow.isCardpayRetainType() || retainWindow.isBankselectpayRetainType())) {
                        getResultHandler().onSelectedMTPay(submitData);
                    }
                    notifyDialogDismiss();
                })
                .rightBtnColor(ContextCompat.getColor(getActivity(), R.color.cashier__color));

        if (retainWindow.isAlipayRetainType() || retainWindow.isCardpayRetainType() || retainWindow.isBankselectpayRetainType()) {
            builder.closeIcon(true, dialog -> {
                Map<String, Object> valLab = MapUtils.builder().add("type", retainWindow.getStaticsRetainType()).build();
                CashierStaticsUtils.reportSystemCheck("b_pay_9uefqi3m_mc", valLab, getCashierParams().getCashierUniqueId());
                CashierStaticsUtils.logModelEvent("c_PJmoK", "b_pay_b92ieqdb_mc", "离开收银台挽留弹窗-关闭",
                        valLab, StatisticsUtils.EventType.CLICK, getCashierParams().getCashierUniqueId());
            });
        }
        detainmentDialog = builder.build();
        detainmentDialog.show();
        detainmentDialogShowed = true;
        return true;
    }

    private void notifyDialogDismiss() {
        if (detainmentDialog != null) {
            detainmentDialog.dismiss();
            detainmentDialog = null;
        }
    }

    private DetainmentDialogResultHandler getResultHandler() {
        return resultHandler;
    }

    @Override
    public void showNewRetainWindow(RetainWindow retainWindow) {
        showRetainWindow(retainWindow);
    }

    @Override
    public void showOldRetainWindow() {
        showRetainWindow(retainWindow);
    }
}
