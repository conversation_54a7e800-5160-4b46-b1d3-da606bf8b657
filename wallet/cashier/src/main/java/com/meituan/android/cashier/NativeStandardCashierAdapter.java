package com.meituan.android.cashier;

import static com.meituan.android.cashier.activity.MTCashierActivity.ERROR_CODE_DEFAULT;
import static com.meituan.android.cashier.activity.MTCashierActivity.ERROR_MSG_UNKNOWN_DISPATCHER_EXCEPTION;
import static com.meituan.android.cashier.fragment.MTCashierRevisionFragment.isBindCardPayGuideInfoComplete;
import static com.meituan.android.cashier.utils.GoHelloPaySceneUtils.reportMtPaySLAStart;
import static com.meituan.android.pay.common.payment.data.WalletPayParams.KEY_LAUNCH_URL;
import static com.meituan.android.pay.utils.DiscountMonitorHelper.DISCOUNT_MONEY_MONITOR_KEY;
import static com.meituan.android.pay.utils.DiscountMonitorHelper.FINAL_MONEY_MONITOR_KEY;
import static com.meituan.android.pay.utils.DiscountMonitorHelper.ORDER_MONEY_MONITOR_KEY;

import android.app.Activity;
import android.app.Dialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.support.v4.app.Fragment;
import android.support.v4.app.FragmentActivity;
import android.support.v4.app.FragmentManager;
import android.support.v4.content.ContextCompat;
import android.support.v4.content.LocalBroadcastManager;
import android.text.TextUtils;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.base.utils.CashierAnalyseUtils;
import com.meituan.android.cashier.bean.CashierParams;
import com.meituan.android.cashier.common.CashierCatConstants;
import com.meituan.android.cashier.common.CashierConstants;
import com.meituan.android.cashier.common.CashierListener;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.common.CashierTypeConstant;
import com.meituan.android.cashier.common.ICashier;
import com.meituan.android.cashier.common.ICashierAdapter;
import com.meituan.android.cashier.common.ProductTypeConstant;
import com.meituan.android.cashier.dialog.AutomaticPayGuideDialog;
import com.meituan.android.cashier.dialogfragment.AutomaticPayGuideDialogFragment;
import com.meituan.android.cashier.dialogfragment.CardPayFunctionGuideDialogFragment;
import com.meituan.android.cashier.dialogfragment.CommonGuideFragment;
import com.meituan.android.cashier.dialogfragment.PayLaterGuideDialogFragment;
import com.meituan.android.cashier.dialogfragment.PromotionSignedGuideFragment;
import com.meituan.android.cashier.exception.GoHelloPayPayExceptionHandler;
import com.meituan.android.cashier.fragment.MTCashierRevisionFragment;
import com.meituan.android.cashier.fragment.TimerCallbacks;
import com.meituan.android.cashier.model.Constants;
import com.meituan.android.cashier.model.PayErrorCode;
import com.meituan.android.cashier.model.bean.Cashier;
import com.meituan.android.cashier.model.bean.CashierPayment;
import com.meituan.android.cashier.model.bean.CashierPopWindowBean;
import com.meituan.android.cashier.model.bean.MTPaymentURL;
import com.meituan.android.cashier.model.bean.OrderResult;
import com.meituan.android.cashier.model.bean.OverLoadInfo;
import com.meituan.android.cashier.model.bean.PayLaterPopDetailInfoBean;
import com.meituan.android.cashier.model.bean.PopDetailInfo;
import com.meituan.android.cashier.model.bean.RetainWindow;
import com.meituan.android.cashier.model.bean.RouteInfo;
import com.meituan.android.cashier.model.bean.SubmitData;
import com.meituan.android.cashier.payresult.DirectPayResultHandler;
import com.meituan.android.cashier.payresult.ThirdPayResultHandler;
import com.meituan.android.cashier.retrofit.CashierReqTagConstant;
import com.meituan.android.cashier.retrofit.CashierRequestService;
import com.meituan.android.cashier.util.CashierSLAMonitor;
import com.meituan.android.cashier.utils.RetainWindowHandler;
import com.meituan.android.cipstorage.CIPStorageCenter;
import com.meituan.android.common.sniffer.annotation.SnifferThrow;
import com.meituan.android.neohybrid.neo.http.NeoRequestCallback;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.pay.common.payment.data.IBankcardData;
import com.meituan.android.pay.common.payment.data.IPaymentData;
import com.meituan.android.pay.common.payment.utils.PayTypeUtils;
import com.meituan.android.pay.desk.pack.WalletPayManager;
import com.meituan.android.pay.utils.DiscountMonitorHelper;
import com.meituan.android.paybase.asynctask.ConcurrentTask;
import com.meituan.android.paybase.common.activity.PayBaseActivity;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.common.analyse.cat.CatConstants;
import com.meituan.android.paybase.common.analyse.cat.CatUtils;
import com.meituan.android.paybase.common.dialog.PayDialog;
import com.meituan.android.paybase.config.PayBaseConfig;
import com.meituan.android.paybase.dialog.BasePayDialog;
import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.downgrading.DowngradingService;
import com.meituan.android.paybase.downgrading.PayHornConfigBean;
import com.meituan.android.paybase.moduleinterface.payment.PayActionListener;
import com.meituan.android.paybase.moduleinterface.payment.PayFailInfo;
import com.meituan.android.paybase.moduleinterface.payment.PrePayerExecute;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.retrofit.PayException;
import com.meituan.android.paybase.utils.Base64;
import com.meituan.android.paybase.utils.CashierRepeatDownGradeSwitchManager;
import com.meituan.android.paybase.utils.CashierScreenSnapShotUtil;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.paybase.utils.LocalBroadCastUtil;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.MTPayBaseClass;
import com.meituan.android.paybase.utils.MTPayNeedToPersist;
import com.meituan.android.paybase.utils.MTPaySuppressFBWarnings;
import com.meituan.android.paybase.utils.SaveInstanceUtil;
import com.meituan.android.paybase.utils.SdkDataStorageUtils;
import com.meituan.android.paybase.utils.StatisticsUtils;
import com.meituan.android.paybase.utils.SystemInfoUtils;
import com.meituan.android.paybase.utils.UriUtils;
import com.meituan.android.paycommon.lib.abtest.CommonABTestManager;
import com.meituan.android.paycommon.lib.config.MTPayConfig;
import com.meituan.android.paycommon.lib.coupon.model.Promotion;
import com.meituan.android.paycommon.lib.retrofit.PayRetrofit;
import com.meituan.android.paycommon.lib.utils.ExceptionUtils;
import com.meituan.android.paycommon.lib.utils.OuterBusinessParamUtils;
import com.meituan.android.paymentchannel.PayerMediator;
import com.meituan.android.paymentchannel.PayersID;
import com.meituan.android.paymentchannel.utils.PaymentInstallInfoUtils;
import com.meituan.android.paymentchannel.utils.UPPayUtils;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.lang.ref.WeakReference;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@ServiceLoaderInterface(key = CashierTypeConstant.CASHIERTYPE_NATIVE_STANDARD_CASHIER, interfaceClass = ICashier.class)
@MTPayBaseClass
public class NativeStandardCashierAdapter extends ICashierAdapter implements
        AutomaticPayGuideDialog.OnClickGuideButtonListener,
        TimerCallbacks, PayActionListener, PrePayerExecute,
        RetainWindowHandler.RetainWindowShowListener {
    // 同 BalanceInsufficientGuideFragment.ACTION_REFRESH_CASHIER
    private static final String ACTION_REFRESH_CASHIER = "com.meituan.android.cashier.standardCashier.refresh";
    private static final String GO_HELLO_PAY_DEFAULT_PATH = "/cashier/gohellopay";
    public static final int STATUS_FINISH = MTCashierActivity.STATUS_FINISH;
    public static final int REQ_TAG_PAY_ORDER = CashierReqTagConstant.REQ_TAG_PAY_ORDER;
    public static final int REQ_TAG_SAVE_ACTION = 10001;
    private static final int ALLOW_TO_SEND_REQ = 2;
    public static final int REQ_TAG_GO_HELLO_PAY = CashierReqTagConstant.REQ_TAG_GO_HELLO_PAY;
    public static final int REQ_TAG_GUIDE_GO_HELLO_PAY = CashierReqTagConstant.REQ_TAG_GUIDE_GO_HELLO_PAY;
    private static final int REQ_TAG_ROUTE = CashierReqTagConstant.REQ_TAG_ROUTE;
    public static final int REQ_TAG_EXCEED_TIME_LIMIT = CashierReqTagConstant.REQ_TAG_EXCEED_TIME_LIMIT;
    private static final String KEY_COMMON = "common";
    private static final String TECH_TAG = MTCashierActivity.TECH_TAG; //打点相关的页面标示（是吗？表示怀疑）
    private static final String MEITUAN_PAY_FAIL = "meituanpay_fail";
    private static final String KEY_SIGN_BANK_PAY_FAILED = "refresh_page";
    public static final String KEY_INSTALLED_APPS = MTCashierActivity.KEY_INSTALLED_APPS;
    public static final String KEY_IS_ROOT = MTCashierActivity.KEY_IS_ROOT;
    private String callbackUrl;
    /**
     * 商户号 Id，在调用收银台时传递
     */
    private String mMchId;
    private boolean isTimeUp;
    private boolean isDestroyed;
    @MTPayNeedToPersist
    private String payType; //埋点统计支付方式
    private OverLoadInfo overLoadInfo;
    private String tipsForSuspendPaying;
    private boolean sendRequest = true;
    private boolean fromNoPasswordGuide = false;
    @MTPayNeedToPersist
    private boolean fromPayLaterGuide = false;
    private CashierHandler handler;

    @MTPayNeedToPersist
    private boolean isInThirdPay = false;
    private Boolean isRoot;
    private boolean reportBackPressed;
    private boolean retryHomeRequest;
    private boolean reportRetrySuccess;
    private boolean isRefresh; //收银台兜底刷新

    private FragmentActivity mActivity;
    private IRequestCallback mIRequestCallback;
    private CashierListener mCashierListener;
    private String tradeNo;
    private String payToken;
    private String extraData;
    private Uri uri;
    @MTPayNeedToPersist
    private String cashierFrom;
    private final int fragmentId = R.id.content;
    private Dialog mDetainmentDialog;
    @MTPayNeedToPersist
    private boolean mDetainmentDialogShowed;
    @MTPayNeedToPersist
    private int mDetainmentDialogShowTimes;
    private String mAppId;
    @MTPayNeedToPersist
    private RouteInfo mRouteInfo;
    private CashierPopWindowBean mCashierPopWindowBean;
    private String guidePlanInfos = "";
    private String mDowngradeErrorInfo;
    private DirectPayResultHandler mDirectPayResultHandler;
    private ThirdPayResultHandler mThirdPayResultHandler;
    private boolean needRefreshWhenBackFromMTPay;
    private RefreshBroadCastReceiver mRefreshBroadCastReceiver;
    @MTPayNeedToPersist
    private boolean hasShowThirdInterrupt = false; //三方中断防打扰，一次支付只弹一次
    private String mExtraStatics;
    private CashierParams mCashierParams;
    @MTPayNeedToPersist
    private String finalFeeText;
    @MTPayNeedToPersist
    private int installedApps;
    private RetainWindowHandler retainWindowHandler;

    @Override
    public <T extends FragmentActivity & CashierListener & IRequestCallback> ConsumeResult consume(T t, CashierParams cashierParams) {
        this.mCashierParams = cashierParams;
        this.uri = cashierParams.getUri();
        this.callbackUrl = cashierParams.getCallbackUrl();
        this.tradeNo = cashierParams.getTradeNo();
        this.payToken = cashierParams.getPayToken();
        this.mActivity = t;
        this.extraData = cashierParams.getExtraData();
        this.mExtraStatics = cashierParams.getExtraStatics();
        this.mDowngradeErrorInfo = cashierParams.getDowngradeInfo();
        this.mCashierListener = t;
        this.mIRequestCallback = t;
        mDirectPayResultHandler = new DirectPayResultHandler(overLoadInfo -> {
            NativeStandardCashierAdapter.this.overLoadInfo = overLoadInfo;
            processSuspendPaying(mActivity);
        }, this, (MTCashierActivity) mActivity, mCashierListener, tradeNo);
        hasShowThirdInterrupt = false;
        mThirdPayResultHandler = new ThirdPayResultHandler(mCashierListener, (MTCashierActivity) mActivity, tradeNo, payToken, extraData, mExtraStatics);
        mThirdPayResultHandler.setHandleInterruptCashier(this);
        return new ConsumeResult(true);
    }


    /**
     * 是否处理三方中断
     * 如果处理，则直接展示弹窗
     * 判断条件：
     * 前端防打扰：一次支付周期，仅弹一次
     * 含有三方中断弹窗数据： pop_window中pop_scene =interruptPay
     * 选中的支付方式（getPayType）支付类型支持三方中断弹窗（support_interrupt =true)
     *
     * @return 是否展示三方中断弹窗
     */
    public void handleThirdPay() {
        //前端防打扰
        if (hasShowThirdInterrupt || isDestroyed) {
            return;
        }
        if (mRouteInfo == null || mRouteInfo.getCashierPopWindowBean() == null || !TextUtils.equals("interruptPay", mRouteInfo.getCashierPopWindowBean().getPopScene())) {
            return;
        }
        if (mRouteInfo.getCashierInfo() != null) {
            Cashier cashier = getCashier(mRouteInfo);
            if (cashier == null || CollectionUtils.isEmpty(cashier.getPaymentDataList())) {
                return;
            }
            //获取当前支付方式的类型
            String selectedPayType = getPayType();
            if (TextUtils.isEmpty(selectedPayType)) {
                return;
            }
            //看当前的支付类型是否支持三方中断
            List<CashierPayment> paymentDataList = cashier.getPaymentDataList();
            for (CashierPayment cashierPayment : paymentDataList) {
                if (cashierPayment != null && cashierPayment.getPayType() != null && TextUtils.equals(cashierPayment.getPayType(), selectedPayType) && cashierPayment.isSupportInterrupt()) {
                    if (mRouteInfo.getCashierPopWindowBean().getPopDetailInfo() == null || mActivity == null || mActivity.getSupportFragmentManager() == null) {
                        return;
                    }
                    Fragment fragment = mActivity.getSupportFragmentManager().findFragmentById(fragmentId);
                    if (fragment instanceof MTCashierRevisionFragment && isBindCardPayGuideInfoComplete(mRouteInfo.getCashierPopWindowBean(), CashierPopWindowBean.INTERRUPT_PAY_SCENE)) {
                        hasShowThirdInterrupt = showPopWindow(mRouteInfo.getCashierPopWindowBean(), fragment.getChildFragmentManager());
                    }
                }
            }
        }
    }

    /**
     * 展示三方中断弹窗或者支付前引导弹窗，通过getPopScene做区分，支付前引导是beforePay， 三方中断是interruptPay
     *
     * @param cashierPopWindowBean
     */
    public boolean showPopWindow(CashierPopWindowBean cashierPopWindowBean, FragmentManager manager) {
        if (isDestroyed || cashierPopWindowBean == null || cashierPopWindowBean.getPopDetailInfo() == null || manager == null) {
            return false;
        }
        boolean result = false;
        PopDetailInfo popDetailInfo = cashierPopWindowBean.getPopDetailInfo();
        popDetailInfo.setPopScene(cashierPopWindowBean.getPopScene());
        String style = popDetailInfo.getStyle();
        if (TextUtils.equals(PopDetailInfo.FUNCTION_STYLE, style)
                && allowShowOldDialog(popDetailInfo)) {
            CardPayFunctionGuideDialogFragment.newInstance(cashierPopWindowBean)
                    .show(manager);
            result = true;
        } else if (TextUtils.equals(PopDetailInfo.FUNCTION_SINGED_STYLE, style)) {
            PromotionSignedGuideFragment signedGuideFragment = PromotionSignedGuideFragment.newInstance(cashierPopWindowBean);
            if (signedGuideFragment.allowShowDialog(popDetailInfo)) {
                signedGuideFragment.show(manager);
                result = true;
            }
        } else if (TextUtils.equals(PopDetailInfo.COMMON_STYLE, style)
                && CommonGuideFragment.allowShowCommonDialog(popDetailInfo)) {
            CommonGuideFragment.newInstance(cashierPopWindowBean)
                    .show(manager);
            result = true;
        }
        //引导弹窗5期需求增加支付前也上报actionInfo的功能
        this.saveActionInfo(cashierPopWindowBean);
        return result;
    }

    /**
     * 校验旧拉新弹窗"sub_title"的完整性，因为旧拉新弹窗除了完整性校验以外，还必传"sub_title"
     *
     * @param popDetailInfo
     * @return
     */
    private boolean allowShowOldDialog(PopDetailInfo popDetailInfo) {
        if (popDetailInfo == null) {
            return false;
        }
        return !TextUtils.isEmpty(popDetailInfo.getSubtitle());
    }

    /**
     * 将用户行为信息推送给算法
     *
     * @param popWindowBean
     */
    private void saveActionInfo(CashierPopWindowBean popWindowBean) {
        if (isDestroyed || popWindowBean == null || popWindowBean.getPopDetailInfo() == null) {
            return;
        }
        HashMap<String, Object> parametersMap = new HashMap<>();
        parametersMap.put("cashierType", ProductTypeConstant.STANDARD_CASHIER);
        parametersMap.put("action", "popup");
        parametersMap.put("tradeno", tradeNo);
        parametersMap.put("nb_platform", "android");
        parametersMap.put("halfScreenType", popWindowBean.getType());
        JsonObject object = new JsonObject();
        object.addProperty("popupType", popWindowBean.getPopDetailInfo().getPopupType());
        object.addProperty("popupScene", popWindowBean.getPopScene());
        parametersMap.put("userActionInfo", object.toString());
        OuterBusinessParamUtils.appendExtraParams((MTCashierActivity) mActivity, parametersMap);
        if (!CollectionUtils.isEmpty(getExtendTransmissionParams())) {
            parametersMap.putAll(getExtendTransmissionParams());
        }
        PayRetrofit.getInstance().create(CashierRequestService.class, this, REQ_TAG_SAVE_ACTION)
                .saveActionInfo(parametersMap);
    }

    @Override
    public void invoke(String cashierFrom, Map<String, Object> cashierParams) {
        CashierStaticsUtils.logCustom("native_standcashier_start", null, null, getUniqueId());
        CashierSLAMonitor.setStartTime(System.currentTimeMillis());
        CashierStaticsUtils.reportModelEventWithViewEvent("c_PJmoK", "b_pay_p3cw2gqv_mv", "", null, getUniqueId());
        LoganUtils.log("业务方调起收银台");
        if (!((MTCashierActivity) mActivity).paramsCheck(true)) {
            onSLAFailed(MTCashierActivity.ERROR_CODE_ILLEGAL_PAY_TOKEN + "", "tradeNo or token is null");
            return;
        }
        this.cashierFrom = cashierFrom;

        initData();
        HashMap<String, Object> logMap = new HashMap<>();
        logMap.put("uri:", uri != null ? uri.toString() : "");
        LoganUtils.log("收银台调起参数检查", logMap);
        reportBackPressed = true;
        retryHomeRequest = true;
        openCommonCashier();
        registerRefreshCashierBroadCastReceiver();
        DiscountMonitorHelper.getInstance().registerProcess();
    }

    /**
     * 注册刷新收银台的广播事件
     * 收银台接收到刷新事件后，从美团支付返回后，即开始刷新收银台。
     */
    private void registerRefreshCashierBroadCastReceiver() {
        LocalBroadCastUtil.registerBroadCast(mActivity, ACTION_REFRESH_CASHIER, getRefreshBroadCastReceiver());
    }

    private RefreshBroadCastReceiver getRefreshBroadCastReceiver() {
        if (mRefreshBroadCastReceiver == null) {
            mRefreshBroadCastReceiver = new RefreshBroadCastReceiver(mActivity, this);
        }
        return mRefreshBroadCastReceiver;
    }

    private void unregisterRefreshCashierBroadCastReceiver() {
        LocalBroadcastManager.getInstance(mActivity).unregisterReceiver(getRefreshBroadCastReceiver());
    }

    public String getExtraData() {
        return extraData;
    }

    public String getExtraStatics() {
        return mExtraStatics;
    }

    public String getAppId() {
        return this.mAppId;
    }

    public String getMchId() {
        return this.mMchId;
    }

    public HashMap<String, String> getExtendTransmissionParams() {
        return mCashierParams.getExtendTransmissionParams();
    }

    @Override
    public void showNewRetainWindow(RetainWindow retainWindow) {
        readyShowDetainmentDialog(retainWindow);
    }

    @Override
    public void showOldRetainWindow() {
        readyShowDetainmentDialog(getRetainWindow());
    }

    private static class RefreshBroadCastReceiver extends BroadcastReceiver {
        private final WeakReference<NativeStandardCashierAdapter> standardCashierWeakReference;
        private final WeakReference<Activity> activityWeakReference;

        public RefreshBroadCastReceiver(Activity activity, NativeStandardCashierAdapter nativeStandardCashierAdapter) {
            standardCashierWeakReference = new WeakReference<>(nativeStandardCashierAdapter);
            activityWeakReference = new WeakReference<>(activity);
        }

        @Override
        public void onReceive(Context context, Intent intent) {
            if (activityWeakReference.get() == null) {
                return;
            }
            if (LocalBroadCastUtil.actionEquals(activityWeakReference.get(), ACTION_REFRESH_CASHIER, intent)) {
                NativeStandardCashierAdapter nativeStandardCashierAdapter = standardCashierWeakReference.get();
                if (nativeStandardCashierAdapter != null) {
                    nativeStandardCashierAdapter.refreshWhenBackFromMTPay(true);
                }
            }
        }
    }

    private void initData() {
        if (uri != null) {
            mMchId = uri.getQueryParameter(CashierConstants.ARG_MERCHANT_NO);
        }
        if (!TextUtils.isEmpty(extraData)) {
            try {
                JSONObject extraJson = new JSONObject(extraData);
                mAppId = extraJson.optString("app_id");
                guidePlanInfos = extraJson.optString("guide_plan_infos");
            } catch (JSONException e) {
                LoganUtils.logError("NativeStandardCashierAdapter_initData", e.getMessage());
            }
        }
        handler = new CashierHandler(this);
        retainWindowHandler = new RetainWindowHandler(this);
        PayerMediator.getInstance().setPayActionListener(mActivity, this);
    }

    private void openCommonCashier() {
        startHomeRequest(null, false);
        DowngradingService.get().load(mActivity);
        loadMTCashierFragment();
    }

    private void loadMTCashierFragment() {
        Fragment fragment = mActivity.getSupportFragmentManager().findFragmentById(fragmentId);
        if (fragment instanceof MTCashierRevisionFragment) {
            ((MTCashierRevisionFragment) fragment).init(null, null, null, mMchId, null, null, isRefresh);
        } else {
            initMTCashierFragment();
        }
    }

    private void initMTCashierFragment() {
        MTCashierRevisionFragment fragment = new MTCashierRevisionFragment();
        mActivity.getSupportFragmentManager().beginTransaction().replace(fragmentId, fragment)
                .commitAllowingStateLoss();
    }

    private String getExtDimStat() {
        if (TextUtils.isEmpty(mExtraStatics)) {
            return "";
        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("outer_business_statics", mExtraStatics);
        } catch (Exception e) {
            LoganUtils.logError("NativeStandardCashierAdapter_getExtDimStat", e.getMessage());
        }
        return jsonObject.toString();
    }

    private boolean isRoot() {
        if (isRoot == null) {
            CIPStorageCenter dataStorage = SdkDataStorageUtils.getDataStorageCenter(mActivity);
            final String dIsRoot = "-1";
            final String vRoot = dataStorage.getString(KEY_IS_ROOT, dIsRoot);
            isRoot = TextUtils.equals("1", vRoot);
        }
        return isRoot;
    }

    @MTPaySuppressFBWarnings("SIC_INNER_SHOULD_BE_STATIC_ANON")
    public void startHomeRequest(final String dispatcherScene, boolean refresh) {
        LoganUtils.log("dispatcher发起请求", new AnalyseUtils.InstantReportBuilder().addTradeNo().build());
        isRefresh = refresh;
        final int dValue = -1;
        final String dIsRoot = "-1";
        final CIPStorageCenter[] dataStorage = {SdkDataStorageUtils.getDataStorageCenter(mActivity)};
        installedApps = dataStorage[0].getInteger(KEY_INSTALLED_APPS, dValue);
        final String vRoot = dataStorage[0].getString(KEY_IS_ROOT, dIsRoot);
        final String seType = UPPayUtils.loadSepayType(mActivity.getApplicationContext());
        HashMap<String, Object> logMap = new HashMap();
        logMap.put("recordStep", getClass().getName() + " request_start");
        LoganUtils.log("CASHIER_TTI_RECORD", logMap);
        if (installedApps != dValue && !TextUtils.equals(dIsRoot, vRoot)) {
            PayRetrofit.getInstance().create(CashierRequestService.class, mIRequestCallback, REQ_TAG_ROUTE)
                    .startRouting(tradeNo, payToken, vRoot, installedApps + "", callbackUrl, dispatcherScene,
                            MTPayConfig.getProvider().getFingerprint(), seType,
                            getDowngradeErrorInfo(), guidePlanInfos, extraData, getExtDimStat(),
                            getExtendTransmissionParams());
            isRoot = TextUtils.equals("1", vRoot);
        }

        new ConcurrentTask<String, Integer, Integer>() {
            @Override
            protected Integer doInBackground(String... params) {
                String root = SystemInfoUtils.isRoot() ? "1" : "0";
                int installAPP = PaymentInstallInfoUtils.getInstalledApps(mActivity.getApplicationContext());
                if (dataStorage[0] == null) {
                    dataStorage[0] = SdkDataStorageUtils.getDataStorageCenter(mActivity.getApplicationContext());
                }
                if (dataStorage[0] != null) {
                    dataStorage[0].setString(KEY_IS_ROOT, root);
                    dataStorage[0].setInteger(KEY_INSTALLED_APPS, installAPP);
                }
                return installAPP;
            }

            @Override
            protected void onPostExecute(Integer integer) {
                if (installedApps == dValue || TextUtils.equals(dIsRoot, vRoot)) {
                    installedApps = integer;
                    String root = dataStorage[0].getString(KEY_IS_ROOT, dIsRoot);
                    PayRetrofit.getInstance().create(CashierRequestService.class, mIRequestCallback, REQ_TAG_ROUTE)
                            .startRouting(tradeNo, payToken, root, installedApps + "", callbackUrl, dispatcherScene,
                                    MTPayConfig.getProvider().getFingerprint(), seType,
                                    getDowngradeErrorInfo(), guidePlanInfos, extraData, getExtDimStat(),
                                    getExtendTransmissionParams());
                    isRoot = TextUtils.equals("1", root);
                }
            }
        }.exe();
        startToGetSEPayInfo();
    }

    private NeoRequestCallback callback;

    private void resetNeoCallback() {
        callback = null;
    }

    private void startToGetSEPayInfo() {
        if (UPPayUtils.isCheckStatusEqualNull()) {
            UPPayUtils.startToGetSEPayInfo(PayBaseConfig.getProvider().getApplicationContext());
        }
    }

    public String getDowngradeErrorInfo() {
        return mDowngradeErrorInfo;
    }

    public String getGuidePlanInfos() {
        return guidePlanInfos;
    }

    public String getGuideRequestNo() {
        if (mRouteInfo == null) {
            return "";
        }
        CashierPopWindowBean cashierPopWindowBean = mRouteInfo.getCashierPopWindowBean();
        if (cashierPopWindowBean == null) {
            return "";
        }
        PayLaterPopDetailInfoBean payLaterPopDetailInfoBean = cashierPopWindowBean.getPayLaterPopDetailInfoBean();
        if (payLaterPopDetailInfoBean == null) {
            return "";
        }
        return payLaterPopDetailInfoBean.getGuideRequestNo();
    }

    /**
     * 从 meituanpay 返回后，是否需要刷新收银台
     *
     * @param needRefreshWhenBackFromMTPay
     */
    public void refreshWhenBackFromMTPay(boolean needRefreshWhenBackFromMTPay) {
        this.needRefreshWhenBackFromMTPay = needRefreshWhenBackFromMTPay;
    }

    private static class CashierHandler extends Handler {
        WeakReference<NativeStandardCashierAdapter> weakReference;

        public CashierHandler(NativeStandardCashierAdapter nativeStandardCashierAdapter) {
            weakReference = new WeakReference<>(nativeStandardCashierAdapter);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == ALLOW_TO_SEND_REQ) {
                NativeStandardCashierAdapter nativeStandardCashierAdapter = weakReference.get();
                if (nativeStandardCashierAdapter != null && !nativeStandardCashierAdapter.isFinishing()) {
                    nativeStandardCashierAdapter.sendRequest = true;
                    removeMessages(ALLOW_TO_SEND_REQ);
                }
            }
        }
    }

    private boolean isFinishing() {
        return mActivity.isFinishing();
    }


    @Override
    public void onClickGuideOpen(String url) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        String requestInfo;
        HashMap<String, String> requestInfoMap;
        String launchUrl;
        try {
            requestInfo = new String(Base64.decode(url));
            requestInfoMap = getOrderInfoMap(requestInfo);
            launchUrl = requestInfoMap.get(KEY_LAUNCH_URL);
            if (TextUtils.isEmpty(launchUrl)) {
                launchUrl = GO_HELLO_PAY_DEFAULT_PATH;
            }
            OuterBusinessParamUtils.appendExtraParamsTogoHelloPay((MTCashierActivity) mActivity, requestInfoMap);
            PayRetrofit.getInstance().create(CashierRequestService.class, mIRequestCallback, REQ_TAG_GUIDE_GO_HELLO_PAY)
                    .goHelloPay(launchUrl, requestInfoMap, MTPayConfig.getProvider().getFingerprint());
        } catch (IOException e) {
            LoganUtils.logError("NativeStandardCashierAdapter_onClickGuideOpen", e.getMessage());
        }
    }

    private HashMap<String, String> getOrderInfoMap(String orderInfo) {
        JsonObject orderInfoJson = new JsonParser().parse(orderInfo).getAsJsonObject();
        HashMap<String, String> orderInfoMap = new HashMap<>();
        if (orderInfoJson != null) {
            for (Map.Entry<String, JsonElement> entry : orderInfoJson.entrySet()) {
                if (entry.getValue().isJsonArray()) {
                    orderInfoMap.put(entry.getKey(), entry.getValue().toString());
                } else if (entry.getValue().isJsonObject()) {
                    orderInfoMap.put(entry.getKey(), entry.getValue().toString());
                } else if (entry.getValue().isJsonPrimitive()) {
                    orderInfoMap.put(entry.getKey(), entry.getValue().getAsString());
                }
            }
        }
        return orderInfoMap;
    }

    @Override
    public void onClickGuideCancel(Cashier cashier) {
        showMTCashierFragment(cashier);
    }

    private void showMTCashierFragment(Cashier cashier) {
        Fragment fragment = mActivity.getSupportFragmentManager().findFragmentById(fragmentId);
        if (fragment instanceof MTCashierRevisionFragment) {
            if (isRefresh) {
                isRefresh = false;
                CashierAnalyseUtils.recordRefreshingCashier("total", getUniqueId());
                MTCashierRevisionFragment cashierFragment = new MTCashierRevisionFragment();
                //刷新收银台不展示拉新弹窗
                cashierFragment.init(tradeNo, payToken, cashier, mMchId, mAppId, null, true);
                mActivity.getSupportFragmentManager().beginTransaction()
                        .replace(fragmentId, cashierFragment).commitAllowingStateLoss();
            } else {
                ((MTCashierRevisionFragment) fragment).init(tradeNo, payToken, cashier, mMchId, mAppId, mCashierPopWindowBean, false);
            }
        }
    }

    @Override
    public void onTimerFinish() {
        isTimeUp = true;
        if (mActivity.hasWindowFocus()) {
            dealTimeUp();
        }
        dismissDetainmentDialog();
    }

    /**
     * 处理timeup情况
     */
    private void dealTimeUp() {
        isTimeUp = false;
        PayRetrofit.getInstance().create(CashierRequestService.class, mIRequestCallback, REQ_TAG_EXCEED_TIME_LIMIT).queryOrder(
                tradeNo, payToken, "1", extraData, getExtDimStat(), getExtendTransmissionParams());
        CatUtils.logRate(CashierCatConstants.ACTION_CASHIER_OVERTIME, CatConstants.CODE_DEFAULT_OK);
    }

    @Override
    public void onRequestException(int i, Exception e) {
        int errorCode = 0;
        int level = 0;
        switch (i) {
            case REQ_TAG_ROUTE:
                if (retryHomeRequest && !(e instanceof PayException)) {
                    retryHomeRequest = false;
                    reportRetrySuccess = true;
                    AnalyseUtils.techMis("b_pay_w0yqzlx3_mv", null);
                    startHomeRequest(null, false);
                    return;
                }
                reportRetrySuccess = false;
                reportBackPressed = false;
                if (e instanceof PayException) {
                    errorCode = ((PayException) e).getCode();
                    level = ((PayException) e).getLevel();
                }
                CashierStaticsUtils.reportSystemCheck("b_aAh3p", new AnalyseUtils.InstantReportBuilder()
                        .addTradeNo().add("code", String.valueOf(errorCode))
                        .add("message", e.getMessage()).add("level", "" + level).build(), getUniqueId());
                // 0 非极速支付降级影响的重复支付.  1为极速支付降级造成重复支付
                String degrade = "0";
                if (errorCode == PayErrorCode.ALREADY_PAYED && CashierTypeConstant.CASHIERTYPE_ONE_CLICK.equals(cashierFrom)) {
                    degrade = "1";
                }
                onSLAFailed(errorCode + "", "degrade is " + degrade);
                CatUtils.logRate(CashierCatConstants.ACTION_DISPATCHER_CASHIER,
                        errorCode == 0 ? CatConstants.CODE_DEFAULT_ERROR : errorCode);
                String msg = e instanceof PayException ? e.getMessage() : mActivity.getString(R.string.paycommon__error_msg_load_later);
                if (errorCode == PayErrorCode.ALREADY_PAYED) {
                    ((MTCashierActivity) mActivity).setResultStatus(MTCashierActivity.STATUS_SUCCESS);
                    ((MTCashierActivity) mActivity).setErrorCodeAndErrorMessage(String.valueOf(errorCode), msg);
                    new PayDialog.Builder(mActivity)
                            .msg(e.getMessage())
                            .subMsg(((PayException) e).getErrorCodeStr())
                            .rightBtn(PayDialog.DEFAULT_BUTTON_TEXT, (dialog) -> ((MTCashierActivity) mActivity).handlePayResultAndFinish(STATUS_FINISH))
                            .build().show();
                    CashierSLAMonitor.reportStandardCashierFinished(getUniqueId(), CashierSLAMonitor.CASHIER_FINISHED_STATUS_ALREADY_PAYED, CashierSLAMonitor.NATIVE_STANDARD_CASHIER_SUCCESS_SCENE_NORMAL);
                } else if (e instanceof PayException) {
                    ((MTCashierActivity) mActivity).setResultStatus(MTCashierActivity.STATUS_FAIL);
                    ((MTCashierActivity) mActivity).setErrorCodeAndErrorMessage(String.valueOf(errorCode), msg);
                    ExceptionUtils.alertAndFinish(mActivity, msg, ((PayException) e).getErrorCodeStr(), MTCashierActivity.class);
                    CashierSLAMonitor.reportStandardCashierFinished(getUniqueId(), CashierSLAMonitor.CASHIER_FINISHED_STATUS_FAILED, CashierSLAMonitor.NATIVE_STANDARD_CASHIER_SUCCESS_SCENE_NORMAL);
                } else {
                    ((MTCashierActivity) mActivity).setResultStatus(MTCashierActivity.STATUS_FAIL);
                    ((MTCashierActivity) mActivity).setErrorCodeAndErrorMessage(ERROR_CODE_DEFAULT, ERROR_MSG_UNKNOWN_DISPATCHER_EXCEPTION);
                    ExceptionUtils.alertAndFinish(mActivity, msg, "", MTCashierActivity.class);
                    AnalyseUtils.techMis("b_pay_mqk1w1xy_mv", new AnalyseUtils.MapBuilder()
                            .add("scene", mActivity.getLocalClassName() + "_onRequestException")
                            .add("message", e.getMessage()).build());
                    CashierSLAMonitor.reportStandardCashierFinished(getUniqueId(), CashierSLAMonitor.CASHIER_FINISHED_STATUS_NET_ERROR, CashierSLAMonitor.NATIVE_STANDARD_CASHIER_SUCCESS_SCENE_NORMAL);
                }
                break;
            case REQ_TAG_GUIDE_GO_HELLO_PAY:
                AnalyseUtils.techMis("b_pay_lfo8h91o_mv", null);
                String message;
                if (e instanceof PayException && !TextUtils.isEmpty(e.getMessage())) {
                    message = e.getMessage();
                } else {
                    message = mActivity.getString(R.string.cashier__pay_error_msg_try_later);
                }
                ExceptionUtils.alertAndFinish(mActivity, message, null, MTCashierActivity.class);
                break;
            case REQ_TAG_GO_HELLO_PAY:
                GoHelloPayPayExceptionHandler.handleException((MTCashierActivity) mActivity, e);
                break;
            case REQ_TAG_PAY_ORDER:
                mDirectPayResultHandler.onRequestException(i, e);
                break;
            case REQ_TAG_EXCEED_TIME_LIMIT:
                dismissDetainmentDialog();
                AnalyseUtils.techMis("b_pay_v3zwwi9x_mv", null);
                CashierStaticsUtils.reportSystemCheck("b_bbmRU", new AnalyseUtils.InstantReportBuilder().addTradeNo().build(), getUniqueId());
                new PayDialog.Builder(mActivity)
                        .msg(mActivity.getString(R.string.cashier__pay_timeout_content))
                        .leftBtn(mActivity.getString(R.string.cashier__pay_timeout_btn), (dialog) -> {
                            LoganUtils.log("NativeStandardCashierAdapter_onRequestException: " + mActivity.getString(R.string.cashier__pay_timeout_content));
                            mCashierListener.onCashierCancel();
                        })
                        .build().show();
                break;
            default:
                break;
        }
    }


    @Override
    public void onRequestFinal(int i) {
        retryHomeRequest = false;
    }


    @Override
    public void onDestroy(boolean release) {
        dismissDetainmentDialog();
        mDetainmentDialog = null;
        isDestroyed = true;
        if (handler != null) {
            handler.removeMessages(ALLOW_TO_SEND_REQ);
        }
        PayerMediator.getInstance().removePayActionListener(mActivity);
        mDirectPayResultHandler.onDestroy();
        if (retainWindowHandler != null) {
            retainWindowHandler.onDestroy();
        }
        unregisterRefreshCashierBroadCastReceiver();
        DiscountMonitorHelper.getInstance().unRegisterProcess();
        if (release) {
            Fragment fragment = mActivity.getSupportFragmentManager().findFragmentById(fragmentId);
            if (fragment instanceof MTCashierRevisionFragment) {
                mActivity.getSupportFragmentManager().beginTransaction().remove(fragment).commitNowAllowingStateLoss();
            }
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (PayerMediator.getInstance().consumeActivityResult(mActivity, requestCode, resultCode, data)) {
            LoganUtils.log("NativeStandardCashierAdapter_onActivityResult_requestCode: " + requestCode);
        }
    }

    private void onCashierPaySuccess(Promotion promotion) {
        CashierScreenSnapShotUtil.captureSnapShot(mActivity, success -> {
            Fragment fragment = mActivity.getSupportFragmentManager().findFragmentById(fragmentId);
            if (fragment != null) {
                mActivity.getSupportFragmentManager().beginTransaction().remove(fragment).commitNowAllowingStateLoss();
            }
            if (mCashierListener != null) {
                mCashierListener.onCashierPaySuccess(promotion);
            }
        });
        if (promotion != null && !TextUtils.isEmpty(finalFeeText)) {
            promotion.setFinalFeeText(finalFeeText);
        }
    }

    @MTPaySuppressFBWarnings("SF_SWITCH_FALLTHROUGH")
    @Override
    public void onRequestSucc(int i, Object o) {
        if (o == null || isDestroyed) {
            if (o == null) {
                AnalyseUtils.techMis("b_pay_pfjic30w_mv", new AnalyseUtils.MapBuilder()
                        .add("scene", "o == null")
                        .add("tag", i + "")
                        .build());
            } else {
                AnalyseUtils.techMis("b_pay_pfjic30w_mv", new AnalyseUtils.MapBuilder()
                        .add("scene", "isDestroyed")
                        .add("tag", i + "")
                        .build());
            }
            return;
        }
        switch (i) {
            case REQ_TAG_PAY_ORDER://第三方支付tag
                mDirectPayResultHandler.onRequestSucc(i, o);
                break;
            case REQ_TAG_ROUTE:
                if (reportRetrySuccess) {
                    reportRetrySuccess = false;
                    AnalyseUtils.techMis("b_pay_4km995m2_mv", null);
                }
                reportBackPressed = false;
                // dispatch接口获取数据成功后设置导航栏与状态栏颜色，避免在透明界面的时候更改状态栏颜色
                ((MTCashierActivity) mActivity).setActionBarAndStatusBarBackground();
                HashMap<String, Object> logMap = new HashMap();
                logMap.put("recordStep", getClass().getName() + " request_success");
                LoganUtils.log("CASHIER_TTI_RECORD", logMap);
                LoganUtils.log("dispatcher返回成功");

                RouteInfo info = (RouteInfo) o;
                this.mRouteInfo = info;
                this.mCashierPopWindowBean = null;
                fromPayLaterGuide = false;
                if (info.getCashierPopWindowBean() != null) {
                    switch (info.getCashierPopWindowBean().getType()) {
                        case CashierPopWindowBean.STOP_PAYMENT_GUIDE:
                            if (!isRefresh) {
                                CashierStaticsUtils.logCustom("native_standcashier_start_succ", null, null, getUniqueId());
                                String lastResumedFeature = Constants.UNKNOWN;
                                if (mActivity instanceof MTCashierActivity) {
                                    lastResumedFeature = ((MTCashierActivity) mActivity).getLastResumedFeature();
                                }
                                CashierSLAMonitor.reportStandardCashierShowSuccess(lastResumedFeature, getUniqueId());
                                CashierSLAMonitor.reportStandardCashierFinished(getUniqueId(), CashierSLAMonitor.CASHIER_FINISHED_STATUS_SUCCESS, CashierSLAMonitor.NATIVE_STANDARD_CASHIER_SUCCESS_SCENE_STOP_PAYMENT_GUIDE);
                            }
                            showBlockPayWindow(info.getCashierPopWindowBean().getPopDetailInfo());
                            return;
                        case CashierPopWindowBean.POPWINDOW_PAYLATER_GUIDE:
                            showPayLaterWindow(info.getCashierPopWindowBean());
                            return;
                        case CashierPopWindowBean.BIND_CARD_PAY_GUIDE:
                        case CashierPopWindowBean.CREDIT_PAY_GUIDE:
                        case CashierPopWindowBean.PROMOTION_SIGNED_PAY_GUIDE:
                        case CashierPopWindowBean.PROMOTION_BINDED_CARD_PAY_GUIDE:
                            mCashierPopWindowBean = info.getCashierPopWindowBean();
                            break;
                        default:
                            break;
                    }
                }
                processDispatcher(info);
                break;
            case REQ_TAG_GUIDE_GO_HELLO_PAY:
                AnalyseUtils.techMis("b_pay_sk31olhm_mv", null);
                fromNoPasswordGuide = true;
            case REQ_TAG_GO_HELLO_PAY:
                Map<String, Object> reportMap = new HashMap<>();
                reportMap.put("pay_type", payType);
                LoganUtils.log("gohellopay返回成功", reportMap);
                MTPaymentURL mtPaymentURL = (MTPaymentURL) o;
                if (mtPaymentURL != null) {
                    PayHornConfigBean.setGmDegradeFlag(mtPaymentURL.getUrl());
                }
                dealGoHelloPayResponse(mtPaymentURL);
                break;
            case REQ_TAG_EXCEED_TIME_LIMIT:
                dismissDetainmentDialog();
                AnalyseUtils.techMis("b_ruzoirdm", new AnalyseUtils.MapBuilder()
                        .add("scene", "订单超时").build());
                OrderResult result2 = (OrderResult) o;
                if (result2.isResult()) {
                    onCashierPaySuccess(null);
                } else {
                    CashierStaticsUtils.reportSystemCheck("b_bbmRU", new AnalyseUtils.InstantReportBuilder().addTradeNo().build(), getUniqueId());
                    new PayDialog.Builder(mActivity)
                            .msg(mActivity.getString(R.string.cashier__pay_timeout_content))
                            .leftBtn(mActivity.getString(R.string.cashier__pay_timeout_btn), (dialog) -> {
                                LoganUtils.log("NativeStandardCashierAdapter_onRequestSucc: " + mActivity.getString(R.string.cashier__pay_timeout_content));
                                mCashierListener.onCashierCancel();
                            })
                            .build().show();
                }
                break;
            default:
                break;
        }
    }

    /**
     * 先享后付弹窗进入美团支付后，
     * 美团支付取消或失败都降级到标准收银台
     */
    public void downgradeFromMeituanPay() {
        CashierStaticsUtils.logCustom("pop_cashier_cancel", null, null, getUniqueId());
        CashierStaticsUtils.reportSystemCheck("b_pay_pop_cashier_cancel_sc", null, getUniqueId());
        fromPayLaterGuide = false; // 已经降级，则将 fromNoPasswordGuide 复位
        if (mRouteInfo == null) { // 做一个技术兜底，防止不保留活动等未知因素导致的 info 为空
            startHomeRequest(null, false);
            return;
        }
        if (mActivity != null) {
            // 加一个 loading 动画，降低突兀感
            ((MTCashierActivity) mActivity).showMTProgress(true, PayBaseActivity.ProcessType.CASHIER, null);
            new Handler().postDelayed(() -> {
                if (mActivity != null) {
                    ((MTCashierActivity) mActivity).hideProgress();
                    processDispatcher(mRouteInfo);
                }
            }, 200);
        }
    }

    private RetainWindow getRetainWindow() {
        if (mRouteInfo == null) {
            return null;
        }
        return mRouteInfo.getRetainWindow();
    }

    private void processDispatcher(RouteInfo info) {
        if (!TextUtils.isEmpty(info.getRootDesc()) && isRoot()) {
            ToastUtils.showSnackToast(mActivity, info.getRootDesc(), true);
        }
        // 设置abtest分组
        CommonABTestManager.setAbTestGroup(info.getAbTestGroup());
        // native收银台降级为i版收银台
        if (!TextUtils.isEmpty(info.getUrl())) {
            CatUtils.logRate(CashierCatConstants.ACTION_DISPATCHER_CASHIER,
                    CatConstants.CODE_DEFAULT_OK);
            mCashierParams.setWebCashierUrl(info.getUrl());
            ((MTCashierActivity) mActivity).onCashierTechDowngrade(CashierTypeConstant.CASHIERTYPE_NATIVE_STANDARD_CASHIER,
                    CashierTypeConstant.CASHIERTYPE_ICASHIER, info.getUrl());
            // 统计降级i版收银台的次数
            CatUtils.logRate(CashierCatConstants.ACTION_DEGRADE_I, CatConstants.CODE_DEFAULT_OK);
            AnalyseUtils.techMis("b_pay_hga93ht6_mv", null);
            return;
        }
        if (info.getCashierInfo() != null) {
            onGotCashierInfo(getCashier(info));
        } else {
            startHomeRequest(null, false);
        }
    }


    public Cashier getCashier(RouteInfo info) {
        if (info == null || info.getCashierInfo() == null) {
            return null;
        }
        if (TextUtils.equals(KEY_COMMON, info.getCashierType())) {
            return info.getCashierInfo().getCommon();
        } else {
            return info.getCashierInfo().getWallet();
        }
    }

    public Cashier getCashier() {
        return getCashier(mRouteInfo);
    }

    private void showPayLaterWindow(CashierPopWindowBean cashierPopWindowBean) {
        PayLaterPopDetailInfoBean payLaterPopDetailInfoBean = cashierPopWindowBean.getPayLaterPopDetailInfoBean();
        if (payLaterPopDetailInfoBean == null) {
            processDispatcher(mRouteInfo);
            return;
        }
        fromPayLaterGuide = true;
        PayLaterGuideDialogFragment payLaterGuideDialogFragment = PayLaterGuideDialogFragment.newInstance(guidePlanInfos, tradeNo, payToken, payLaterPopDetailInfoBean, getCashier(mRouteInfo), null);
        payLaterGuideDialogFragment.show(mActivity.getSupportFragmentManager());
        openStatus(true, null);
    }


    private void showBlockPayWindow(PopDetailInfo popDetailInfo) {
        String errorMsg = popDetailInfo.getDetail();
        ((MTCashierActivity) mActivity).setErrorCodeAndErrorMessage(ERROR_CODE_DEFAULT, errorMsg);
        new PayDialog.Builder(mActivity)
                .msg(errorMsg)
                .canceledOnTouchOutside(false)
                .cancelable(false)
                .leftBtn(popDetailInfo.getLeftBtn(), dialog -> {
                    CashierStaticsUtils.logModelEvent("c_pay_jjckzxmj", "b_pay_v5l55ue3_mc", "解止付申诉弹窗-终止支付", null, StatisticsUtils.EventType.CLICK, getUniqueId());
                    dialog.cancel();
                    mCashierListener.onCashierPayFail("");
                })
                .rightBtn(popDetailInfo.getRightBtn(), dialog -> {
                    CashierStaticsUtils.logModelEvent("c_pay_jjckzxmj", "b_pay_2royud7a_mc", "解止付申诉弹窗-申请解除限制", null, StatisticsUtils.EventType.CLICK, getUniqueId());
                    dialog.cancel();
                    mCashierListener.onCashierPayFail("");
                    UriUtils.open(mActivity, popDetailInfo.getRedirectUrl(), false);
                })
                .build().show();
        // 弹框曝光埋点
        AnalyseUtils.logPV("StandardCashier", "c_pay_jjckzxmj", new AnalyseUtils.MapBuilder().
                add("nb_version", PayBaseConfig.getProvider().getPayVersion()).
                add("tradeNo", tradeNo).
                build());
    }


    private void dealGoHelloPayResponse(MTPaymentURL mtPaymentURL) {
        if (mtPaymentURL == null) {
            return;
        }
        if (mtPaymentURL.getOverLoadInfo() != null && mtPaymentURL.getOverLoadInfo().isStatus()) { //当前后台负载过重，无法完成支付，弹出提示窗口
            CashierStaticsUtils.techMis("b_pay_6f1taqcl_mv", new AnalyseUtils.InstantReportBuilder().add("type", mtPaymentURL.getPayType()).build(), getUniqueId());
            overLoadInfo = mtPaymentURL.getOverLoadInfo();
            processSuspendPaying(mActivity);
        } else {
            reportMtPaySLAStart(mtPaymentURL, "native", getUniqueId());
            PayerMediator.getInstance().startPay(mActivity, PayersID.ID_MEITUANPAY, mtPaymentURL.getUrl(), tradeNo, this);
        }
    }

    private void onSLAFailed(String code, String message) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("code", code);
        hashMap.put("message", message);
        openStatus(false, hashMap);
    }

    private void onGotCashierInfo(Cashier cashier) {
        if (cashier == null) {
            onSLAFailed("" + CashierCatConstants.CODE_CASHIER_IS_NULL, "");
            CatUtils.logRate(CashierCatConstants.ACTION_DISPATCHER_CASHIER,
                    CashierCatConstants.CODE_CASHIER_IS_NULL);
            CatUtils.logError("cashierStartError", mActivity.getString(R.string.cashier__start_error));
            return;
        }
        if (cashier.getNoPswGuide() != null && !TextUtils.isEmpty(cashier.getNoPswGuide().getSubmitUrl())) {
            AutomaticPayGuideDialogFragment automaticPayGuideDialogFragment = AutomaticPayGuideDialogFragment.newInstance(cashier);
            automaticPayGuideDialogFragment.show(mActivity.getSupportFragmentManager());
            openStatus(true, null);
            CatUtils.logError("cashierStartError", mActivity.getString(R.string.cashier__start_error));
            AnalyseUtils.techMis("b_pay_t25yp8xz_mv", null);
            return;
        }
        showMTCashierFragment(cashier);
    }


    private void onMeituanPaySuccess(String payType, PayFailInfo failInfo) {
        if (CashierRepeatDownGradeSwitchManager.downGrade()) {
            if (mActivity == null) {
                return;
            }
            ((MTCashierActivity) mActivity).setResultStatus(MTCashierActivity.STATUS_SUCCESS);
            ((MTCashierActivity) mActivity).handlePayResultAndFinish(STATUS_FINISH);
        } else {
            if (mCashierListener == null) {
                return;
            }
            if (failInfo == null) {
                onCashierPaySuccess(null);
                return;
            }
            Promotion promotion = null;
            try {
                JSONObject jsonObject = new JSONObject(failInfo.getExtra());
                // pay_promotion 和 PayActivity.KEY_PAY_PROMOTION 保持一致
                promotion = GsonProvider.getInstance().fromJson(jsonObject.optString("pay_promotion"), Promotion.class);
            } catch (Exception e) {
                LoganUtils.logError("NativeStandardCashierAdapter_onMeituanPaySuccess", e.getMessage());
            }
            saveFinalMoneyAndReport(payType, promotion);
            onCashierPaySuccess(promotion);
        }
    }

    /**
     * 保存并上报营销相关金额
     *
     * @param payType   支付类型
     * @param promotion 优惠
     */
    private void saveFinalMoneyAndReport(String payType, Promotion promotion) {
        try {
            if (!DiscountMonitorHelper.getInstance().isMonitorSwitch() || promotion == null) {
                return;
            }
            DiscountMonitorHelper.getInstance().saveFinalMoney(promotion);
            BigDecimal order_money = DiscountMonitorHelper.getInstance().get(ORDER_MONEY_MONITOR_KEY);
            BigDecimal discount_money = DiscountMonitorHelper.getInstance().get(DISCOUNT_MONEY_MONITOR_KEY);
            BigDecimal final_money = DiscountMonitorHelper.getInstance().get(FINAL_MONEY_MONITOR_KEY);
            if (order_money == null || discount_money == null || final_money == null) {
                return;
            }
            int result = order_money.subtract(discount_money).compareTo(final_money);
            if (result != 0) {
                HashMap<String, Object> map = new AnalyseUtils.MapBuilder()
                        .add("pay_type", payType)
                        .add("original_money", order_money.floatValue())
                        .add("front_final_money", order_money.subtract(discount_money).floatValue())
                        .add("final_money", final_money.floatValue())
                        .build();
                CashierStaticsUtils.reportSystemCheck("b_pay_trade_amount_exception_sc", map, getUniqueId());
                CashierStaticsUtils.logCustom("trade_amount_exception", map, null, getUniqueId());
            }
        } catch (Exception e) {
            LoganUtils.logError("NativeStandardCashierAdapter_saveFinalMoneyAndReport", e.getMessage());
        }
    }

    private void onGotMeituanPayResult(String payType, int payResult, PayFailInfo failInfo) {
        if (payResult == PayActionListener.SUCCESS) {
            Map<String, Object> reportMap = new HashMap<>();
            reportMap.put("pay_type", payType);
            reportMap.put("class", "NativeStandardCashierAdapter");
            LoganUtils.log("收银台支付成功后埋点", reportMap);
            onMeituanPaySuccess(payType, failInfo);
        } else if (payResult == PayActionListener.CANCEL) {
            if (fromNoPasswordGuide) {
                mCashierListener.onCashierCancel();
            } else if (fromPayLaterGuide) {
                downgradeFromMeituanPay();
            } else if (needRefreshWhenBackFromMTPay) {
                needRefreshWhenBackFromMTPay = false;
                startHomeRequest(null, true);
            }
        } else if (payResult == PayActionListener.FAIL) {
            try {
                if (failInfo != null && !TextUtils.isEmpty(failInfo.getExtra())) {
                    JSONObject object = new JSONObject(failInfo.getExtra());
                    if (object.has(KEY_SIGN_BANK_PAY_FAILED) && object.getBoolean(KEY_SIGN_BANK_PAY_FAILED)) {
                        startHomeRequest(null, true);
                        CashierAnalyseUtils.recordRefreshingCashier("KEY_SIGN_BANK_PAY_FAILED", getUniqueId());
                    }
                } else {
                    reportMeituanPayFail();
                    if (fromNoPasswordGuide) {
                        mCashierListener.onCashierCancel(); //PayActivity的payFailed和payCancel都是FAIL状态，此时暂且直接关闭吧
                    } else if (fromPayLaterGuide) {
                        downgradeFromMeituanPay();
                    } else if (needRefreshWhenBackFromMTPay) {
                        needRefreshWhenBackFromMTPay = false;
                        startHomeRequest(null, true);
                    }
                }
            } catch (JSONException e) {
                LoganUtils.logError("NativeStandardCashierAdapter_onGotMeituanPayResult", e.getMessage());
            }
        } else if (payResult == PayActionListener.OVER_TIME) {
            mCashierListener.onCashierPayFail("");
        } else if (payResult == PayActionListener.PAY_FATAL_ERROR) {
            startHomeRequest(MEITUAN_PAY_FAIL, true);
            CashierAnalyseUtils.recordRefreshingCashier("PAY_FATAL_ERROR", getUniqueId());
        }
    }

    @Override
    public void onGotPayResult(String payType, int payResult, PayFailInfo failInfo) {
        if (mActivity.isFinishing()) {
            return;
        }
        isInThirdPay = false;
        if (PayersID.ID_MEITUANPAY.equals(payType)) {
            onGotMeituanPayResult(payType, payResult, failInfo);
        } else {
            if (mThirdPayResultHandler != null) {
                mThirdPayResultHandler.onGotPayResult(payType, payResult, failInfo, getUniqueId(), getExtendTransmissionParams());
            }
        }
    }

    @Override
    public void onRequestStart(int i) {
        // 参考 getRequestProgressType
    }

    private void setInThirdPayStatus(boolean status) {
        isInThirdPay = status;
    }

    @SnifferThrow(module = "meituan_payment_cashier_weixin_fail", describe = "weixin pay fail")
    private void reportWeixinPayFail() {
        // 防止编译内联，也可以互相印证
        LoganUtils.log("NativeStandardCashierAdapter_meituan_payment_cashier_weixin_fail");
    }

    @SnifferThrow(module = "meituan_payment_cashier_ali_fail", describe = "ali pay fail")
    private void reportAliPayFail() {
        // 防止编译内联，也可以互相印证
        LoganUtils.log("NativeStandardCashierAdapter_meituan_payment_cashier_ali_fail");
    }

    @SnifferThrow(module = "meituan_payment_cashier_meituan_fail", describe = "meituan pay fail")
    private void reportMeituanPayFail() {
        // 防止编译内联，也可以互相印证
        LoganUtils.log("NativeStandardCashierAdapter_meituan_payment_cashier_meituan_fail");
    }

    @SnifferThrow(module = "meituan_payment_cashier_other_fail", describe = "other pay fail")
    private void reportOtherPayFail() {
        // 防止编译内联，也可以互相印证
        LoganUtils.log("NativeStandardCashierAdapter_meituan_payment_cashier_other_fail");
    }
    @Override
    public void preJumpIntoThirdPaySDK() {
        setInThirdPayStatus(true);
    }

    @Override
    public void onPayPreExecute(String s) {

    }

    @Override
    public void onStart() {
    }

    /**
     * 查询三方支付结果，目前只有微信用
     */
    public void queryThirdPayOrder() {
        if (isInThirdPay) {
            isInThirdPay = false;
            if (mThirdPayResultHandler != null) {
                mThirdPayResultHandler.queryOrder("第三方支付结果", getExtendTransmissionParams());
            }
        }
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        if (hasFocus && isTimeUp) {
            dealTimeUp();
        }
    }

    public void processSuspendPaying(Activity activity) {
        sendRequest = false;
        if (overLoadInfo != null) {
            tipsForSuspendPaying = overLoadInfo.getMessage();
            if (overLoadInfo.getTimeout() > 0) {
                handler.sendEmptyMessageDelayed(ALLOW_TO_SEND_REQ, overLoadInfo.getTimeout());
            }
        }
        new PayDialog.Builder(activity)
                .msg(tipsForSuspendPaying)
                .leftBtn(mActivity.getString(R.string.cashier__I_have_known), null)
                .build().show();
        LoganUtils.log("NativeStandardCashierAdapter_processSuspendPaying: " + tipsForSuspendPaying);
    }

    public boolean canSendRequest() {
        return sendRequest;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getPayType() {
        return payType;
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        SaveInstanceUtil.saveInstanceOfClass(this, getClass(), outState);
    }

    @Override
    public void onRestoreInstanceState(Bundle savedInstanceState) {
        SaveInstanceUtil.restoreInstanceOfClass(this, getClass(), savedInstanceState);
        initData();
        if (restoreInstanceStateError()) {
            ((MTCashierActivity) mActivity).crashReport("onRestoreInstanceState_standardcashier", getCashierType());
        } else {
            loadMTCashierFragment();
        }
    }


    /**
     * 恢复数据出现异常（理论上不会出现数据恢复异常）
     *
     * @return
     */
    private boolean restoreInstanceStateError() {
        return "null".equalsIgnoreCase(tradeNo) || TextUtils.isEmpty(tradeNo) ||
                TextUtils.isEmpty(payToken) || "null".equalsIgnoreCase(payToken);
    }

    @Override
    public final boolean onBackPressed() {
        if (reportBackPressed) {
            AnalyseUtils.techMis("b_pay_bsmbner4_mc", null);
            reportBackPressed = false;
        }
        Fragment fragment = mActivity.getSupportFragmentManager().findFragmentById(fragmentId);
        if (fragment instanceof MTCashierRevisionFragment) {
            AnalyseUtils.techMis("b_pay_32l25h89_mc", new AnalyseUtils.MapBuilder()
                    .add("tradeNo", AnalyseUtils.getCashierTradeNo())
                    .add("payType", payType).build());
            return handleDetainmentDialog();
        }
        return false;
    }

    private boolean handleDetainmentDialog() {
        if (mDetainmentDialog != null && mDetainmentDialog.isShowing()) {
            // 如果挽留弹窗展示中，消费返回事件。防止快速多次点击返回按钮出现直接退出问题
            return true;
        } else if (CommonABTestManager.isNewRetainWindow()) {
            // 新的挽留弹窗接口,请求cashier/retainwindow接口
            return retainWindowHandler.requestRetainWindow(tradeNo, payToken,
                    installedApps + "", MTPayConfig.getProvider().getFingerprint(),
                    getDowngradeErrorInfo(), extraData, getExtDimStat(), getExtendTransmissionParams());
        } else {
            // 显示cashier/dispatcher接口的挽留弹窗
            return readyShowDetainmentDialog(getRetainWindow());
        }
    }

    private boolean readyShowDetainmentDialog(RetainWindow retainWindow) {
        if (retainWindow == null) {
            return false;
        }
        if (retainWindow.isDefaultRetainType()) {
            return commonDetainmentDialog(retainWindow);
        } else if (retainWindow.isAlipayRetainType() || retainWindow.isBankselectpayRetainType()
                || retainWindow.isCardpayRetainType()) {
            return showSpecialDetainmentDialog(retainWindow);
        } else {
            return false;
        }
    }

    /**
     * 支付宝/绑卡挽留弹框
     */
    private boolean showSpecialDetainmentDialog(RetainWindow mRetainWindow) {
        if (isDetainmentDialogDataComplete(mRetainWindow) && !mDetainmentDialogShowed) {
            showDetainmentDialog(mRetainWindow, "");
            mDetainmentDialogShowed = true;
            return true;
        } else {
            return false;
        }
    }

    /**
     * 普通挽留弹框
     */
    private boolean commonDetainmentDialog(RetainWindow mRetainWindow) {
        if (TextUtils.equals(CommonABTestManager.getDetainmentDialogGroupType(),
                CommonABTestManager.GROUP_TYPE_B)) {
            if (!mDetainmentDialogShowed && isDetainmentDialogDataComplete(mRetainWindow)) {
                mDetainmentDialogShowed = true;
                showDetainmentDialog(mRetainWindow, "single");
                return true;
            } else {
                mCashierListener.onCashierCancel();
                return false;
            }
        } else {
            mCashierListener.onCashierCancel();
            return false;
        }
    }

    private boolean isDetainmentDialogDataComplete(RetainWindow retainWindow) {
        return retainWindow != null
                && !TextUtils.isEmpty(retainWindow.getTitle())
                && !TextUtils.isEmpty(retainWindow.getDetail())
                && !TextUtils.isEmpty(retainWindow.getLeftButton())
                && !TextUtils.isEmpty(retainWindow.getRightButton());
    }

    private void showDetainmentDialog(RetainWindow retainWindow, String abTestGroupType) {
        if (isDestroyed || (mActivity != null && mActivity.isFinishing())) {
            return;
        }
        mDetainmentDialogShowTimes++;
        if (mDetainmentDialog == null) {
            String debugPrefix = "";
            String detailSuffix = "";
            BasePayDialog.Builder builder = new PayDialog.Builder(mActivity)
                    .title(debugPrefix + retainWindow.getTitle())
                    .msg(retainWindow.getDetail() + detailSuffix)
                    .leftBtn(retainWindow.getLeftButton(), dialog -> {
                        HashMap<String, Object> map = new AnalyseUtils.MapBuilder()
                                .add("times", String.valueOf(mDetainmentDialogShowTimes))
                                .add("user_class", abTestGroupType)
                                .add("type", retainWindow.getStaticsRetainType())
                                .add("button_name", retainWindow.getLeftButton())
                                .add("ai_type", "normal")
                                .build();
                        CashierStaticsUtils.logModelEvent("c_PJmoK", "b_pay_26e34k6d_mc", "离开收银台挽留弹窗-点击确认离开",
                                map, StatisticsUtils.EventType.CLICK, getUniqueId());
                        CashierStaticsUtils.reportSystemCheck("b_pay_zgza8o6s_mc", map, getUniqueId());
                        mCashierListener.onCashierCancel();
                        dismissDetainmentDialog();
                    })
                    .rightBtn(retainWindow.getRightButton(), dialog -> {
                        WalletPayManager.getInstance().setEntry(mActivity,
                                WalletPayManager.ENTRY_FROM_CASHIER_DETAINMENT_DIALOG);
                        if (retainWindow.isAlipayRetainType() && retainWindow.getSubmitData() != null) {
                            startDirectPay(retainWindow);
                        }
                        if ((retainWindow.isCardpayRetainType() || retainWindow.isBankselectpayRetainType())
                                && (retainWindow.getSubmitData() != null)) {
                            startCardPay(searchMTPayment(retainWindow.getSubmitData()));
                        }
                        HashMap<String, Object> map = new AnalyseUtils.MapBuilder()
                                .add("times", String.valueOf(mDetainmentDialogShowTimes))
                                .add("user_class", abTestGroupType)
                                .add("type", retainWindow.getStaticsRetainType())
                                .add("button_name", retainWindow.getRightButton())
                                .add("ai_type", "normal")
                                .build();
                        CashierStaticsUtils.reportSystemCheck("b_pay_7nugc1pd_mc", map, getUniqueId());
                        CashierStaticsUtils.logModelEvent("c_PJmoK", "b_pay_n3c198vr_mc", "离开收银台挽留弹窗-点击继续支付",
                                map, StatisticsUtils.EventType.CLICK, getUniqueId());
                        analyseClickConfirmButton(retainWindow);
                        dismissDetainmentDialog();
                    })
                    .rightBtnColor(ContextCompat.getColor(mActivity, R.color.cashier__color));

            if (retainWindow.isAlipayRetainType() || retainWindow.isCardpayRetainType() || retainWindow.isBankselectpayRetainType()) {
                builder.closeIcon(true, dialog -> {
                    // 弹框右上角X号
                    HashMap<String, Object> map = new AnalyseUtils.MapBuilder()
                            .add("type", retainWindow.getStaticsRetainType())
                            .add("ai_type", "normal")
                            .build();
                    CashierStaticsUtils.reportSystemCheck("b_pay_9uefqi3m_mc", map, getUniqueId());
                    CashierStaticsUtils.logModelEvent("c_PJmoK", "b_pay_b92ieqdb_mc", "离开收银台挽留弹窗-关闭",
                            map, StatisticsUtils.EventType.CLICK, getUniqueId());
                });
            }
            mDetainmentDialog = builder.build();
        }
        mDetainmentDialog.show();
        HashMap<String, Object> map = new AnalyseUtils.MapBuilder()
                .add("times", String.valueOf(mDetainmentDialogShowTimes))
                .add("user_class", abTestGroupType)
                .add("type", retainWindow.getStaticsRetainType())
                .add("ai_type", "normal")
                .build();
        CashierStaticsUtils.logModelEvent("c_PJmoK", "b_pay_n7gadotk_mv", "离开收银台挽留弹窗",
                map, StatisticsUtils.EventType.VIEW, getUniqueId());
        CashierStaticsUtils.reportSystemCheck("b_pay_849q03f1_mv", map, getUniqueId());
    }

    private void analyseClickConfirmButton(RetainWindow retainWindow) {
        if (retainWindow != null && retainWindow.getSubmitData() != null) {
            IBankcardData iBankcardData = searchMTPayment(retainWindow.getSubmitData());
            if (iBankcardData != null && !TextUtils.isEmpty(iBankcardData.getPayType())) {
                CashierStaticsUtils.reportSystemCheck("b_pay_n3c198vr_mc",
                        new AnalyseUtils.MapBuilder().add("pay_type", iBankcardData.getPayType()).build(), getUniqueId());
            }
        }
    }

    private void dismissDetainmentDialog() {
        if (mDetainmentDialog != null) {
            mDetainmentDialog.dismiss();
            mDetainmentDialog = null;
        }
    }

    private void startDirectPay(RetainWindow retainWindow) {
        MTCashierRevisionFragment fragment = getCashierFragment();
        if (fragment != null) {
            HashMap<String, String> param = new HashMap<>();
            param.put("tradeno", tradeNo);
            param.put("pay_token", payToken);
            param.put("pay_type", retainWindow.getSubmitData().getPayType());
            if (retainWindow.isNewRetainWindow() && retainWindow.isAlipayRetainType()) {
                param.put("payScene", retainWindow.getSubmitData().getPayScene());
                param.put("paySceneParams", retainWindow.getSubmitData().getPaySceneParams());
            }

            fragment.startDirectPay(param);
        }
    }

    private void startCardPay(IBankcardData mtPayment) {
        MTCashierRevisionFragment fragment = getCashierFragment();
        if (fragment != null && mtPayment instanceof MTPayment) {
            fragment.payOrder((IPaymentData) mtPayment);
        }
    }

    private IBankcardData searchMTPayment(SubmitData submitData) {
        Cashier cashier = getCashier(mRouteInfo);
        CashierPayment cashierPayment = null;
        if (cashier != null && !CollectionUtils.isEmpty(cashier.getPaymentDataList())) {
            for (CashierPayment payment : cashier.getPaymentDataList()) {
                if (PayTypeUtils.isWalletPay(payment.getPayType())) {
                    cashierPayment = payment;
                    break;
                }
            }
            if (cashierPayment != null && cashierPayment.getWalletPaymentListPage() != null
                    && !CollectionUtils.isEmpty(cashierPayment.getWalletPaymentListPage().getMtPaymentList())) {
                for (IBankcardData mtPayment : cashierPayment.getWalletPaymentListPage().getMtPaymentList()) {
                    if (submitData == null) {
                        break;
                    }
                    if ((PayTypeUtils.isNewCardPay(submitData.getPayType()) && TextUtils.equals(submitData.getPayType(), mtPayment.getPayType()))
                            || (PayTypeUtils.isSelectNewCardPay(submitData.getPayType())
                            && TextUtils.equals(submitData.getPayType(), mtPayment.getPayType())
                            && TextUtils.equals(submitData.getBankType(), mtPayment.getBankType()))) {
                        return mtPayment;
                    }
                }
            }
        }
        return null;
    }

    private MTCashierRevisionFragment getCashierFragment() {
        Fragment fragment = mActivity.getSupportFragmentManager().findFragmentById(fragmentId);
        if (fragment instanceof MTCashierRevisionFragment) {
            return (MTCashierRevisionFragment) fragment;
        } else {
            return null;
        }
    }

    @Override
    public String getCashierType() {
        return CashierTypeConstant.CASHIERTYPE_NATIVE_STANDARD_CASHIER;
    }

    @Override
    public PayBaseActivity.ProcessType getRequestProgressType(int tag) {
        if (tag != REQ_TAG_GUIDE_GO_HELLO_PAY) {
            if (isRefresh) {
                return PayBaseActivity.ProcessType.DEFAULT;
            } else {
                return PayBaseActivity.ProcessType.CASHIER;
            }
        }
        return null;
    }


    public void setFinalFeeText(String finalFeeText) {
        this.finalFeeText = finalFeeText;
    }
}
