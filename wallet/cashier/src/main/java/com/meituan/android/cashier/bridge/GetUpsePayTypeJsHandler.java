package com.meituan.android.cashier.bridge;

import android.content.Context;

import com.dianping.titans.js.BridgeManager;
import com.meituan.android.paybase.config.PayBaseConfig;
import com.meituan.android.paybase.moduleinterface.FinanceJsHandler;
import com.meituan.android.paymentchannel.utils.UPPayUtils;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import org.json.JSONException;
import org.json.JSONObject;

@ServiceLoaderInterface(key = "pay.getUpsePayType", interfaceClass = FinanceJsHandler.class)
public class GetUpsePayTypeJsHandler extends HybridBusinessJsHandler implements FinanceJsHandler {

    private static final String ARG_ACTION = "action";
    private static final String ACTION_GET_SAVED_TYPE = "get_saved_type";
    private static final String ACTION_GET_CURRENT_TYPE = "get_current_type";

    @Override
    public void exec() {
        super.exec();
        if (jsBean() != null && jsBean().argsJson != null &&
                jsHost() != null && jsHost().getContext() != null) {
            String action = jsBean().argsJson.optString(ARG_ACTION);
            Context context = jsHost().getContext();
            if (ACTION_GET_SAVED_TYPE.equals(action)) {
                String type = UPPayUtils.loadSepayType(context);
                startToGetSEPayInfo();
                jsCallbackResult(type, false);
            } else if (ACTION_GET_CURRENT_TYPE.equals(action)) {
                jsCallbackResult(UPPayUtils.getSepayType(), true);
            } else {
                jsCallbackPayError();
            }
        } else {
            jsCallbackPayError();
        }
    }

    private void startToGetSEPayInfo() {
        if (UPPayUtils.isCheckStatusEqualNull()) {
            UPPayUtils.startToGetSEPayInfo(PayBaseConfig.getProvider().getApplicationContext());
        }
    }

    private void jsCallbackResult(String seType, boolean shouldAddPayStatus) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("status", "success");
            JSONObject data = new JSONObject();
            data.put("upsepay_type", seType);
            if (shouldAddPayStatus) {
                data.put("upsepay_status", UPPayUtils.isCheckStatusEqualError() ? 0 : 1);
            }
            jsonObject.put("data", data);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        jsCallback(jsonObject);
    }

    @Override
    public int jsHandlerType() {
        return BridgeManager.FOR_COMMON;
    }

    @Override
    public String getSignature() {
        return "ICiKe/qe+/en9iyJIbU7587eJVB1eJzDtf0ySwZ4lbwQtauarEDOZVk5PWy9/wqADKG14aaee/uRMBwWDLbw2A==";
    }

    @Override
    public Class<?> getHandlerClass() {
        return getClass();
    }

    @Override
    public String getMethodName() {
        return "pay.getUpsePayType";
    }

    @Override
    public String getName() {
        return "pay.getUpsePayType";
    }
}
