package com.meituan.android.cashier.fragment;

import static com.meituan.android.cashier.NativeStandardCashierAdapter.REQ_TAG_GO_HELLO_PAY;
import static com.meituan.android.cashier.NativeStandardCashierAdapter.REQ_TAG_PAY_ORDER;
import static com.meituan.android.cashier.base.utils.CashierAnalyseUtils.getCreditPayStatus;
import static com.meituan.android.cashier.retrofit.CashierRequestUtils.getWalletPayment;
import static com.meituan.android.cashier.retrofit.CashierRequestUtils.isMTPayOrCreditPay;
import static com.meituan.android.pay.common.payment.utils.PaymentListUtils.isAbnormal;
import static com.meituan.android.pay.common.payment.utils.PaymentListUtils.isPaymentAbnormal;
import static com.meituan.android.pay.common.payment.utils.PaymentListUtils.isSupportBalanceCombine;
import static com.meituan.android.pay.common.payment.utils.PaymentUtils.isInstallmentedCoBrandedCard;
import static com.meituan.android.pay.common.payment.utils.PaymentUtils.isRefreshPeriod;
import static com.meituan.android.pay.desk.component.analyse.DeskAnalyseUtils.analyseComponentDispatch;
import static com.meituan.android.pay.desk.component.analyse.DeskCatConstants.ACTION_COMPONENT_START;
import static com.meituan.android.pay.desk.component.fragment.NewCombineLabelDetailDialogFragment.STANDARD_CASHIER_ENTRY;
import static com.meituan.android.pay.desk.payment.report.PayDeskCatConstants.ACTION_REFRESH_INSTALLMENT;
import static com.meituan.android.pay.desk.payment.report.PayDeskCatConstants.CODE_REFRESH_INSTALLMENT_DATA_ERROR;
import static com.meituan.android.pay.desk.payment.report.PayDeskCatConstants.CODE_REFRESH_INSTALLMENT_EXCEPTION;

import android.content.Context;
import android.content.Intent;
import android.graphics.Rect;
import android.os.Bundle;
import android.os.Handler;
import android.support.annotation.Nullable;
import android.support.v4.app.Fragment;
import android.support.v4.content.LocalBroadcastManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.LinearLayout;
import android.widget.ScrollView;

import com.meituan.android.cashier.NativeStandardCashierAdapter;
import com.meituan.android.cashier.R;
import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.base.utils.CashierAnalyseUtils;
import com.meituan.android.cashier.base.utils.UpsePayAndUnionflashPayAnalyeUtils;
import com.meituan.android.cashier.base.view.revision.CashierOrderInfoView;
import com.meituan.android.cashier.base.view.revision.CashierTimerView;
import com.meituan.android.cashier.base.view.revision.IOrderInfoView;
import com.meituan.android.cashier.base.view.revision.ITimerView;
import com.meituan.android.cashier.base.view.revision.RemainingCountDownTimer;
import com.meituan.android.cashier.common.CashierCatConstants;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.common.ICashier;
import com.meituan.android.cashier.dialog.OnClickCommonDialogGuideButtonListener;
import com.meituan.android.cashier.dialog.OnClickGuidePayTypeListener;
import com.meituan.android.cashier.dialog.OnClickPromotionPayTypeListener;
import com.meituan.android.cashier.dialogfragment.CardPayFunctionGuideDialogFragment;
import com.meituan.android.cashier.dialogfragment.DCEPDialogFragment;
import com.meituan.android.cashier.dialogfragment.PromotionSignedGuideFragment;
import com.meituan.android.cashier.dialogfragment.RiskDialogFragment;
import com.meituan.android.cashier.model.Constants;
import com.meituan.android.cashier.model.bean.Cashier;
import com.meituan.android.cashier.model.bean.CashierPayment;
import com.meituan.android.cashier.model.bean.CashierPopWindowBean;
import com.meituan.android.cashier.model.bean.PayResult;
import com.meituan.android.cashier.model.bean.PopDetailInfo;
import com.meituan.android.cashier.model.bean.PopUp;
import com.meituan.android.cashier.model.params.PayParams;
import com.meituan.android.cashier.newrouter.NSCHandlerProxy;
import com.meituan.android.cashier.retrofit.CashierRequestService;
import com.meituan.android.cashier.retrofit.CashierRequestUtils;
import com.meituan.android.cashier.util.CashierSLAMonitor;
import com.meituan.android.cashier.utils.NativeStandardCashierPayProcessStatistic;
import com.meituan.android.cashier.utils.ViewOnInitLayoutObserver;
import com.meituan.android.cashier.widget.CashierBrandView;
import com.meituan.android.cashier.widget.CashierMarketingGuideFloatView;
import com.meituan.android.cashier.widget.NSCAutoScroller;
import com.meituan.android.cashier.widget.NSCScrollView;
import com.meituan.android.cashier.widget.NativeStandardCashierAreaView;
import com.meituan.android.cashier.widget.PaymentViewStatus;
import com.meituan.android.cashier.widget.PaymentViewUtils;
import com.meituan.android.cashier.widget.SaveMoneyDiscountView;
import com.meituan.android.pay.common.analyse.MtPaySceneUtils;
import com.meituan.android.pay.common.payment.bean.FinanceServiceBean;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.pay.common.payment.bean.Payment;
import com.meituan.android.pay.common.payment.data.IBankcardData;
import com.meituan.android.pay.common.payment.data.IPaymentData;
import com.meituan.android.pay.common.payment.data.PayStatus;
import com.meituan.android.pay.common.payment.data.PayType;
import com.meituan.android.pay.common.payment.utils.PayConstantsMediator;
import com.meituan.android.pay.common.payment.utils.PayTypeUtils;
import com.meituan.android.pay.common.payment.utils.PaymentUtils;
import com.meituan.android.pay.common.promotion.bean.Agreement;
import com.meituan.android.pay.common.promotion.bean.LabelAbTest;
import com.meituan.android.pay.common.promotion.bean.Material;
import com.meituan.android.pay.common.promotion.bean.PayLabel;
import com.meituan.android.pay.common.promotion.bean.PaymentReduce;
import com.meituan.android.pay.common.promotion.bean.PromotionRefreshment;
import com.meituan.android.pay.common.promotion.bean.ReduceInfo;
import com.meituan.android.pay.common.selectdialog.bean.WalletPaymentListPage;
import com.meituan.android.pay.common.selectdialog.view.SelectBankDialog;
import com.meituan.android.pay.desk.component.fragment.CombineLabelDetailDialogFragment;
import com.meituan.android.pay.desk.component.fragment.NewCombineLabelDetailDialogFragment;
import com.meituan.android.pay.desk.component.fragment.OnCombineDialogCloseListener;
import com.meituan.android.pay.desk.pack.IPaymentInnerClick;
import com.meituan.android.pay.desk.pack.WalletPayArea;
import com.meituan.android.pay.desk.pack.WalletPayManager;
import com.meituan.android.pay.desk.payment.bean.standarddesk.RefreshInstallment;
import com.meituan.android.pay.desk.payment.bean.standarddesk.WalletPayment;
import com.meituan.android.pay.desk.payment.discount.DiscountCashierUtils;
import com.meituan.android.pay.desk.payment.report.WalletPayAreaAnalyseUtils;
import com.meituan.android.pay.desk.payment.view.BasePaymentView;
import com.meituan.android.pay.desk.payment.view.DiscountView;
import com.meituan.android.pay.utils.CreditOpenUtils;
import com.meituan.android.pay.utils.DiscountMonitorHelper;
import com.meituan.android.paybase.activity.BaseActivity;
import com.meituan.android.paybase.common.activity.PayBaseActivity;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.common.analyse.cat.CatConstants;
import com.meituan.android.paybase.common.analyse.cat.CatUtils;
import com.meituan.android.paybase.common.fragment.PayBaseFragment;
import com.meituan.android.paybase.common.utils.BrandUtils;
import com.meituan.android.paybase.config.PayBaseConfig;
import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.downgrading.DowngradingService;
import com.meituan.android.paybase.downgrading.PayHornConfigBean;
import com.meituan.android.paybase.downgrading.PayHornConfigService;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.retrofit.PayException;
import com.meituan.android.paybase.utils.CashAmountArithUtils;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.MTPayNeedToPersist;
import com.meituan.android.paybase.utils.MTPaySuppressFBWarnings;
import com.meituan.android.paybase.utils.RomUtils;
import com.meituan.android.paybase.utils.StatisticsUtils;
import com.meituan.android.paybase.utils.SystemInfoUtils;
import com.meituan.android.paybase.utils.TransferUtils;
import com.meituan.android.paybase.utils.UriUtils;
import com.meituan.android.paybase.widgets.ProgressButton;
import com.meituan.android.paybase.widgets.notice.NoticeView;
import com.meituan.android.paycommon.lib.config.MTPayConfig;
import com.meituan.android.paycommon.lib.config.MTPayProvider;
import com.meituan.android.paycommon.lib.fragment.HalfPageFragment;
import com.meituan.android.paycommon.lib.retrofit.PayRetrofit;
import com.meituan.android.paycommon.lib.utils.ExceptionUtils;
import com.meituan.android.paycommon.lib.utils.OuterBusinessParamUtils;
import com.meituan.android.paycommon.lib.utils.UpsePayAndUnionflashPayUtils;
import com.meituan.android.paycommon.lib.utils.ViewUtils;
import com.meituan.android.paycommon.lib.widgets.NoDuplicateClickListener;
import com.meituan.android.paykeqing.utils.MapUtils;
import com.meituan.android.paymentchannel.PayersID;
import com.meituan.android.paymentchannel.utils.AlipayHKUtils;
import com.meituan.android.paymentchannel.utils.UPPayUtils;
import com.meituan.metrics.Metrics;

import org.json.JSONException;
import org.json.JSONObject;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * author: luo jing
 * date: 2018/7/10 11:54
 * description: cashier revision
 */
public class MTCashierRevisionFragment extends PayBaseFragment implements WalletPayArea.OnPaymentClickListener,
        SelectBankDialog.SelectedBankOnCancelListener, IRequestCallback,
        ViewTreeObserver.OnGlobalLayoutListener, IPaymentInnerClick, OnClickGuidePayTypeListener, OnClickPromotionPayTypeListener, OnClickCommonDialogGuideButtonListener {
    private static final String TAG = "MTCashierRevisionFrag";
    private static final boolean DEBUG = false;
    private static final String TECH_TAG = "MTCashierRevisionFragment"; //打点相关的页面标示

    /**
     * 标准收银台月付开通请求码
     */
    private static final int CREDIT_OPEN_HOMEBUTTON = 888;      //收银台首页
    private static final int CREDIT_OPEN_MARKETBUTTON = 555;    //营销浮层
    private static final int CREDIT_OPEN_SELECTDIALOG = 333;    //切卡弹窗

    /**
     * 埋点所用的scene码
     */
    private static final int CREDIT_OPEN_HOMEBUTTON_SCENE = 8;      //收银台首页scene
    private static final int CREDIT_OPEN_MARKETBUTTON_SCENE = 5;    //营销浮层scene
    private static final int CREDIT_OPEN_SELECTDIALOG_SCENE = 3;    //切卡弹窗scene


    @MTPayNeedToPersist
    private MTPayment mCreditPaymentData;
    //月付新开通流程回来后需要直接支付，保留各入口处月付的数据
    private int mPayMoneyChanged = 0;
    private RemainingCountDownTimer<ITimerView> remainingCountDownTimer;
    @Nullable
    private NativeStandardCashierAdapter nativeStandardCashierAdapter;
    private NSCHandlerProxy handlerProxy;
    //当前选中的支付方式，也就是首页checkbox选中的方式
    private IPaymentData checkedPaymentData;

    @MTPayNeedToPersist
    private String tradeNo;
    @MTPayNeedToPersist
    private String payToken;

    @MTPayNeedToPersist
    private boolean logPvDelayed = false;

    @MTPayNeedToPersist
    public boolean showMoreDiscounts = false;
    /**
     * 商户号 Id
     */
    private String mMchId;
    private String mAppId;
    /**
     * firstLevelPaymentIndex，页面销毁前，选中的一级支付方式索引
     * secondLevelPaymentIndex，页面销毁前，选中的二级支付方式，比如美团支付品牌下的尝鲜、买单等
     */
    @MTPayNeedToPersist
    private int firstLevelPaymentIndex = -1;
    @MTPayNeedToPersist
    private int secondLevelPaymentIndex = -1;
    @MTPayNeedToPersist
    private int financeIndex = -1;
    private ProgressButton confirmButton;
    private PayParams payParams;
    @MTPayNeedToPersist
    private PayParams verifyParams;
    private int selectBankTimes;
    private NSCScrollView scrollView;
    /**
     * 上报信息
     */
    private Map<String, Object> properties;
    // 是否第一次加载成功，用于统计收银台可用性
    @MTPayNeedToPersist
    private boolean isFirstVisible = true;
    // 品宣view
    private CashierBrandView mCashierBrandView;
    private CashierMarketingGuideFloatView mCashierMarketingGuideFloatView;
    // 刷新分期成功标志
    @MTPayNeedToPersist
    private boolean isRefreshInstallmentFinish = true;
    @MTPayNeedToPersist
    private boolean isRefresh;
    private TranslateAnimation discountViewEnterAnim;
    private TranslateAnimation discountViewExitAnim;
    private Animation.AnimationListener discountViewExitAnimListener;
    private TranslateAnimation marketingFloatViewEnterAnim;
    private TranslateAnimation marketingFloatViewExitAnim;
    private Animation.AnimationListener marketingFloatViewExitAnimListener;
    private NativeStandardCashierAreaView mStandardCashierAreaView;
    // 月付、银行卡埋点 https://km.sankuai.com/page/1220395926
    private final NativeStandardCashierPayProcessStatistic mNativeStandardCashierPayProcessStatistic = new NativeStandardCashierPayProcessStatistic();
    private Cashier cashier;
    // 勾选的支付方式是否为第一个支付方式的标识
    @MTPayNeedToPersist
    private boolean isFirstCheckBox = false;
    private ViewTreeObserver.OnScrollChangedListener onScrollChangedListener;
    private String routerTrace;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        HashMap<String, Object> logMap = new HashMap();
        logMap.put("recordStep", getClass().getName() + " onCreate");
        LoganUtils.log("CASHIER_TTI_RECORD", logMap);
        super.onCreate(savedInstanceState);
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        initStandardCashier();
    }

    private Cashier getCashier() {
        if (this.cashier != null) {
            return this.cashier;
        }
        initStandardCashier();
        if (handlerProxy != null) {
            this.cashier = handlerProxy.getCashier();
        } else if (nativeStandardCashierAdapter != null) {
            this.cashier = nativeStandardCashierAdapter.getCashier();
        }
        return this.cashier;
    }

    private void initStandardCashier() {
        if (nativeStandardCashierAdapter != null || handlerProxy != null) {
            return;
        }
        if (getActivity() instanceof MTCashierActivity) {
            ICashier iCashier = ((MTCashierActivity) getActivity()).getCurrentCashier();
            if (iCashier instanceof NativeStandardCashierAdapter) {
                nativeStandardCashierAdapter = (NativeStandardCashierAdapter) iCashier;
            }
        }
    }

    @Override
    public void onViewStateRestored(@Nullable Bundle savedInstanceState) {
        super.onViewStateRestored(savedInstanceState);
        initStandardCashier();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        HashMap<String, Object> logMap = new HashMap();
        logMap.put("recordStep", getClass().getName() + " onCreateView");
        LoganUtils.log("CASHIER_TTI_RECORD", logMap);
        return inflater.inflate(R.layout.cashier__fragment_revision, container, false);
    }

    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        HashMap<String, Object> logMap = new HashMap();
        logMap.put("recordStep", getClass().getName() + " onViewCreated");
        LoganUtils.log("CASHIER_TTI_RECORD", logMap);
        super.onViewCreated(view, savedInstanceState);
        init(tradeNo, payToken, getCashier(), getMchId(), getAppId(), null, isRefresh);
    }

    private String getAppId() {
        if (TextUtils.isEmpty(mAppId)) {
            initStandardCashier();
            if (handlerProxy != null) {
                this.mAppId = handlerProxy.getAppId();
            } else if (nativeStandardCashierAdapter != null) {
                this.mAppId = nativeStandardCashierAdapter.getAppId();
            }
        }
        return this.mAppId;
    }

    private String getMchId() {
        if (TextUtils.isEmpty(mMchId)) {
            initStandardCashier();
            if (handlerProxy != null) {
                this.mMchId = handlerProxy.getMchId();
            } else if (nativeStandardCashierAdapter != null) {
                this.mMchId = nativeStandardCashierAdapter.getMchId();
            }
        }
        return this.mMchId;
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        saveCheckedPaymentIndex();
        super.onSaveInstanceState(outState);
    }

    @Override
    public void onStart() {
        HashMap<String, Object> logMap = new HashMap();
        logMap.put("recordStep", getClass().getName() + " onStart");
        LoganUtils.log("CASHIER_TTI_RECORD", logMap);

        super.onStart();
        refreshCashierView();
        CashierStaticsUtils.reportModelEventWithViewEvent(getPageName(), "b_SsoHH", "POP", null, getUniqueId());
    }

    @Override
    public void onResume() {
        HashMap<String, Object> logMap = new HashMap();
        logMap.put("recordStep", getClass().getName() + " onResume");
        LoganUtils.log("CASHIER_TTI_RECORD", logMap);

        if (!pagePropertiesAvailable()) {
            logPvDelayed = true;
        }
        // isRefreshInstallmentFinish为false，说明前一次网络请求没有完成。onResume时重新拉取最新数据
        if (checkedPaymentData instanceof MTPayment && !isRefreshInstallmentFinish) {
            MTPayment mtPayment = (MTPayment) checkedPaymentData;
            if (isInstallmentedCoBrandedCard(mtPayment) || PayTypeUtils.isCreditPay(checkedPaymentData.getPayType())) {
                refreshInstallmentRequest(getExtendTransmissionParams());
            }
        }

        //从微信回前台后查结果，resume处调用是防止透明Activity触发了standcashier的start方法
        if (handlerProxy != null) {
            handlerProxy.queryThirdPayOrder();
        } else if (nativeStandardCashierAdapter != null) {
            nativeStandardCashierAdapter.queryThirdPayOrder();
        }
        super.onResume();
    }

    private HashMap<String, String> getExtendTransmissionParams() {
        initStandardCashier();
        if (handlerProxy != null) {
            handlerProxy.getExtendTransmissionParams();
        } else if (nativeStandardCashierAdapter != null) {
            return nativeStandardCashierAdapter.getExtendTransmissionParams();
        }
        return new HashMap<>();
    }

    private void logPv() {
        String cid = this.getPageName();
        AnalyseUtils.logPV(this.mPageInfoKey, cid, this.getPageProperties());
    }

    @Override
    protected boolean inManualMode() {
        return true;
    }

    public void init(String tradeNo, String payToken, Cashier cashier, String mchId, String appId, CashierPopWindowBean cashierPopWindowBean, boolean isRefresh) {
        this.isRefresh = isRefresh;
        initStandardCashier();
        if (!TextUtils.isEmpty(mchId)) {
            this.mMchId = mchId;
        }
        if (!TextUtils.isEmpty(tradeNo) && !TextUtils.isEmpty(payToken) && cashier != null) {
            // 初始化 mNativeStandardCashierPayProcessStatistic
            mNativeStandardCashierPayProcessStatistic.setCashier(cashier);
            mNativeStandardCashierPayProcessStatistic.setMerchantNo(mchId);
            this.tradeNo = tradeNo;
            this.payToken = payToken;
            this.cashier = cashier;
            this.mAppId = appId;
            if (getActivity() instanceof PayBaseActivity) {
                ((PayBaseActivity) getActivity()).hideProgress();
                ((PayBaseActivity) getActivity()).cleanProgressCount();
            }
            if (getView() != null) {
                initPaymentData();
                confirmButton = getView().findViewById(R.id.btn_cashier_pay_confirm);
                HashMap<String, Object> startLogMap = new HashMap();
                startLogMap.put("recordStep", getClass().getName() + " init_start");
                LoganUtils.log("CASHIER_TTI_RECORD", startLogMap);
                if (isFirstVisible) {
                    isFirstVisible = false;
                    if (handlerProxy != null) {
                        // SLA
                        if (!isRefresh) {
                            CashierSLAMonitor.reportStandardCashierFinished(getUniqueId(),
                                    CashierSLAMonitor.CASHIER_FINISHED_STATUS_SUCCESS,
                                    CashierSLAMonitor.NATIVE_STANDARD_CASHIER_SUCCESS_SCENE_NORMAL);
                        }
                    } else if (nativeStandardCashierAdapter != null) {
                        nativeStandardCashierAdapter.openStatus(true, null);
                        if (!isRefresh) {
                            CashierStaticsUtils.logCustom("native_standcashier_start_succ", null, null, getUniqueId());
                            String lastResumeFeature = Constants.UNKNOWN;
                            if (getActivity() instanceof MTCashierActivity) {
                                lastResumeFeature = ((MTCashierActivity) getActivity()).getLastResumedFeature();
                            }
                            CashierSLAMonitor.reportStandardCashierShowSuccess(lastResumeFeature, getUniqueId());
                            CashierSLAMonitor.reportStandardCashierFinished(getUniqueId(), CashierSLAMonitor.CASHIER_FINISHED_STATUS_SUCCESS, CashierSLAMonitor.NATIVE_STANDARD_CASHIER_SUCCESS_SCENE_NORMAL);
                        }
                    }
                    CatUtils.logRate(CashierCatConstants.ACTION_DISPATCHER_CASHIER,
                            CatConstants.CODE_DEFAULT_OK);

                    if (logPvDelayed) {
                        logPvDelayed = false;
                        logPv();
                    }
                    if (isBindCardPayGuideInfoComplete(cashierPopWindowBean, CashierPopWindowBean.BEFORE_PAY_SCENE)) {
                        if (handlerProxy != null) {
                            handlerProxy.showPopWindow(cashierPopWindowBean, this);
                        } else if (nativeStandardCashierAdapter != null) {
                            nativeStandardCashierAdapter.showPopWindow(cashierPopWindowBean, getChildFragmentManager());
                        }
                    }
                }
                getView().setVisibility(View.VISIBLE);
                getView().getViewTreeObserver().addOnGlobalLayoutListener(this);
                showActionBar();
                new Handler().post(this::initConfirmButton);
                // 展示订单详情
                showOrderArea();
                // 支付方式界面的绘制
                initPayment();
                // 底部界面的绘制
                initBottomView();
                // 刷新当前选中的支付方式
                refreshCashierView();
                HashMap<String, Object> endLogMap = new HashMap();
                endLogMap.put("recordStep", "end");
                LoganUtils.log("CASHIER_TTI_RECORD", endLogMap);
                // 自动滚动到默认勾选项
                observeOnLayoutAndAutoScroll();
                // 监听金融服务区的可见性
                reportFinanceViewMVEvent();
                if (handlerProxy != null) {
                    String trace = handlerProxy.adapterContext().trace();
                    CashierSLAMonitor.notifyRouterLoadEnd(trace, "native_standard_cashier");
                }
            } else {
                CatUtils.logError("cashierShowError", MTPayConfig.getProvider().getApplicationContext().getString(R.string.cashier__show_error));
            }
        } else {
            // 预加载机制，先new一个Fragment，此时无数据，将视图隐藏；有tradeNo、payToken之后，再将视图展示出来
            if (getView() != null) {
                getView().setVisibility(View.INVISIBLE);
            } else {
                CatUtils.logError("cashierShowError", getString(R.string.cashier__show_error));
            }
            initActionBar();
        }
    }

    /**
     * 初始化收银台首页时，增加自动滚动到勾选项的功能。
     */
    private void observeOnLayoutAndAutoScroll() {
        if (scrollView == null || mStandardCashierAreaView == null) {
            return;
        }
        ViewOnInitLayoutObserver.subscribeOn(mStandardCashierAreaView, parent -> {
            List<BasePaymentView> paymentViews = PaymentViewUtils.findAllPaymentViews(mStandardCashierAreaView);
            BasePaymentView selectedPaymentView = PaymentViewUtils.findSelectedPaymentView(paymentViews);
            if (isFirstCheckBox || selectedPaymentView == null) {
                reportOnFirstScreen(paymentViews, false);
                return;
            }
            NSCAutoScroller.builder()
                    .setScrollView(scrollView)
                    .setScrolledView(selectedPaymentView)
                    .suitableRate(2 / 3f) // 2/3 表示selectedPaymentView适合展示在container（ScrollView）的2/3以上的区域。
                    .targetRate(1 / 3f) // 1/3 表示如果selectedPaymentView没有在适合展示的区域时，需要滑动到container（ScrollView）的1/3处的区域。
                    .setScrollEndListener(() -> reportOnFirstScreen(paymentViews, true)) // 执行滑动逻辑上报
                    .setNoScrolledListener(() -> reportOnFirstScreen(paymentViews, false)) // 不执行滑动逻辑上报
                    .execute();
        });
    }

    private void reportOnFirstScreen(List<BasePaymentView> paymentViews, boolean scrolled) {
        View bottomMask = getView() != null ? getView().findViewById(R.id.cashier_bottom_layout) : null;
        List<PaymentViewStatus> statusList = PaymentViewUtils.calculatePaymentViewStatus(paymentViews, scrollView, bottomMask);
        CashierStaticsUtils.reportModelEventWithViewEvent("c_PJmoK", "b_pay_arx7ldkp_mv", "标准收银台曝光支付方式",
                MapUtils.builder().add("pay_info", CashierStaticsUtils.convertListToArray(statusList)).add("slide_type", scrolled ? "1" : "0").build(), getUniqueId());

    }

    /**
     * 上报金融服务区曝光事件，仅当view在屏幕露出1px以上时上报
     */
    private void reportFinanceViewMVEvent() {
        if (scrollView != null && scrollView.getViewTreeObserver() != null
                && mStandardCashierAreaView != null && mStandardCashierAreaView.getFinanceAreaView() != null) {
            onScrollChangedListener = new ViewTreeObserver.OnScrollChangedListener() {
                @Override
                public void onScrollChanged() {
                    Rect scrollBounds = new Rect();
                    View financeAreaView = mStandardCashierAreaView.getFinanceAreaView();
                    boolean isFinanceAreaViewVisible = financeAreaView.getLocalVisibleRect(scrollBounds);
                    if (isFinanceAreaViewVisible) {
                        // 月付断直连需求新埋点-金融服务区域曝光事件，page_style = 1代表新样式，native只有新样式
                        CashierStaticsUtils.reportModelEventWithViewEvent("c_PJmoK", "b_pay_0w8ylr65_mv", "金融服务区域曝光",
                                new AnalyseUtils.MapBuilder().add("page_style", "1").add("utm_source", "-999").build(), getUniqueId());
                        // 上报以后取消监听
                        scrollView.getViewTreeObserver().removeOnScrollChangedListener(onScrollChangedListener);
                    }
                }
            };
            scrollView.getViewTreeObserver().addOnScrollChangedListener(onScrollChangedListener);
        }
    }

    //仅校验新旧样式弹窗共有的必传项
    public static boolean isBindCardPayGuideInfoComplete(CashierPopWindowBean cashierPopWindowBean, String popScene) {
        if (cashierPopWindowBean != null
                && cashierPopWindowBean.getPopDetailInfo() != null
                && TextUtils.equals(popScene, cashierPopWindowBean.getPopScene())) {
            PopDetailInfo popDetailInfo = cashierPopWindowBean.getPopDetailInfo();
            boolean complete;
            switch (cashierPopWindowBean.getType()) {
                case CashierPopWindowBean.BIND_CARD_PAY_GUIDE:
                case CashierPopWindowBean.CREDIT_PAY_GUIDE:
                case CashierPopWindowBean.PROMOTION_SIGNED_PAY_GUIDE:
                case CashierPopWindowBean.PROMOTION_BINDED_CARD_PAY_GUIDE:
                    complete = !TextUtils.isEmpty(popDetailInfo.getTitle())
                            && !TextUtils.isEmpty(popDetailInfo.getGuideButton())
                            && !TextUtils.isEmpty(popDetailInfo.getStyle())
                            && popDetailInfo.getGuidePayTypeInfo() != null;
                    if (!complete) {
                        logNotCompleteError(cashierPopWindowBean);
                    }
                    return complete;
                default:
                    return false;
            }
        }
        return false;
    }

    private static void logNotCompleteError(CashierPopWindowBean cashierPopWindowBean) {
        String errorSubTag = "bindCardGuideInfoError";
        String errorMsg = "收银台引导弹窗数据异常";
        if (cashierPopWindowBean.getType() == CashierPopWindowBean.BIND_CARD_PAY_GUIDE) {
            errorMsg += "，异常类型为拉新绑卡";
        } else if (cashierPopWindowBean.getType() == CashierPopWindowBean.CREDIT_PAY_GUIDE) {
            errorMsg += "，异常类型为拉新月付";
        } else if (cashierPopWindowBean.getType() == CashierPopWindowBean.PROMOTION_SIGNED_PAY_GUIDE) {
            errorMsg += "，异常类型为促活美团支付（A类）";
        } else if (cashierPopWindowBean.getType() == CashierPopWindowBean.PROMOTION_BINDED_CARD_PAY_GUIDE) {
            errorMsg += "，异常类型为促活绑多卡（B类）";
        }
        if (TextUtils.equals(cashierPopWindowBean.getPopScene(), CashierPopWindowBean.BEFORE_PAY_SCENE)) {
            errorMsg += "，场景为支付前";
        } else if (TextUtils.equals(cashierPopWindowBean.getPopScene(), CashierPopWindowBean.INTERRUPT_PAY_SCENE)) {
            errorMsg += "，场景为三方中断";
        }
        CatUtils.logError(errorSubTag, errorMsg);
    }

    @Nullable
    private IPaymentData getDefaultPayment() {
        Cashier cashier = getCashier();
        // 在支付方式区寻找默认勾选项
        List<CashierPayment> paymentList = cashier.getPaymentDataList();
        if (!CollectionUtils.isEmpty(paymentList)) {
            for (int i = 0; i < paymentList.size(); i++) {
                CashierPayment cashierPayment = paymentList.get(i);
                if (DEBUG) {
                    Log.d(TAG, "getDefaultPayment: " + cashierPayment.getPayType() + " : "
                            + cashierPayment.isSelected());
                }
                if (PayTypeUtils.isWalletPay(cashierPayment.getPayType()) && cashierPayment.isSelected()) {
                    IPaymentData iPaymentData = WalletPayManager.getInstance()
                            .getDefaultPayment(cashierPayment);
                    if (iPaymentData != null) {
                        return iPaymentData;
                    }
                } else {
                    if (cashierPayment.isSelected()) {
                        return cashierPayment;
                    }
                }
            }
        }
        // 在金融服务区寻找默认勾选项
        List<FinanceServiceBean> financeList = cashier.getFinanceDataList();
        if (!CollectionUtils.isEmpty(financeList)) {
            for (int i = 0; i < financeList.size(); i++) {
                FinanceServiceBean financeServiceBean = financeList.get(i);
                if (DEBUG) {
                    Log.d(TAG, "getDefaultFinance: " + financeServiceBean.getPayType() + " : "
                            + financeServiceBean.isSelected());
                }
                if (financeServiceBean.isSelected()) {
                    return financeServiceBean;
                }
            }
        }
        return null;
    }

    @Nullable
    private IPaymentData recoverCheckedPayment() {
        Cashier cashier = getCashier();
        // 在支付方式区寻找勾选项
        List<CashierPayment> paymentList = cashier.getPaymentDataList();
        if (!CollectionUtils.isEmpty(paymentList) && firstLevelPaymentIndex > -1
                && firstLevelPaymentIndex < paymentList.size()) {
            CashierPayment cashierPayment = paymentList.get(firstLevelPaymentIndex);
            if (PayTypeUtils.isWalletPay(cashierPayment.getPayType())) {
                List<MTPayment> recommendPayment = cashierPayment.getRecommendPayment();
                if (!CollectionUtils.isEmpty(recommendPayment)
                        && secondLevelPaymentIndex > -1
                        && secondLevelPaymentIndex < recommendPayment.size()) {
                    return recommendPayment.get(secondLevelPaymentIndex);
                }
            } else {
                return cashierPayment;
            }
        }
        // 在金融服务区寻找勾选项
        List<FinanceServiceBean> financeList = cashier.getFinanceDataList();
        if (!CollectionUtils.isEmpty(financeList) && financeIndex > -1
                && financeIndex < financeList.size()) {
            return financeList.get(financeIndex);
        }
        return null;
    }

    private void saveCheckedPaymentIndex() {
        Cashier cashier = getCashier();
        if (cashier != null) {
            // 在支付方式区寻找勾选项
            List<CashierPayment> paymentList = cashier.getPaymentDataList();
            if (!CollectionUtils.isEmpty(paymentList)) {
                for (int i = 0; i < paymentList.size(); i++) {
                    CashierPayment cashierPayment = paymentList.get(i);
                    if (cashierPayment == checkedPaymentData) {
                        firstLevelPaymentIndex = i;
                    } else if (PayTypeUtils.isWalletPay(cashierPayment.getPayType())) {
                        List<MTPayment> recommendPayment = cashierPayment.getRecommendPayment();
                        if (!CollectionUtils.isEmpty(recommendPayment)) {
                            for (int j = 0; j < recommendPayment.size(); j++) {
                                if (recommendPayment.get(j) == checkedPaymentData) {
                                    firstLevelPaymentIndex = i;
                                    secondLevelPaymentIndex = j;
                                }
                            }
                        }
                    }
                }
            }
            // 在金融服务区寻找勾选项
            List<FinanceServiceBean> financeList = cashier.getFinanceDataList();
            if (!CollectionUtils.isEmpty(financeList)) {
                for (int i = 0; i < financeList.size(); i++) {
                    FinanceServiceBean financeServiceBean = financeList.get(i);
                    if (financeServiceBean == checkedPaymentData) {
                        financeIndex = i;
                    }
                }
            }
        }
    }

    // 该方法使用 standardCashier 或者 getActivity() 时需要空判断
    private void initPaymentData() {
        Cashier cashier = getCashier();
        checkedPaymentData = recoverCheckedPayment();
        IPaymentData removedUpsepay = checkAndModifyUpsepay(cashier);
        IPaymentData removeUnionflashpay = checkAndModifyUnionflashpay(cashier);
        if (DEBUG) {
            String str = (checkedPaymentData == null ? "recover null" : checkedPaymentData.getPayType());
            Log.d(TAG, "initPaymentData: " + str);
        }

        if (checkedPaymentData != null) { // 不保留活动后恢复界面
            // 如果当前选中的支付方式是被删除的Android Pay或者云闪付支付方式，则默认选择第一种支付方式
            if (checkedPaymentData == removedUpsepay || checkedPaymentData == removeUnionflashpay) {
                checkedPaymentData = findFirstPayment();
            }
        } else {
            if ((removedUpsepay != null && removedUpsepay.isSelected())
                    || (removeUnionflashpay != null && removeUnionflashpay.isSelected())) {
                // 如果默选支付方式是被删除的Android Pay或者云闪付支付方式，则默认选择第一种支付方式
                checkedPaymentData = findFirstPayment();
            } else {
                checkedPaymentData = getDefaultPayment();
            }
        }

        // 如果checkedPaymentData的状态不是0（正常）或者2（活动），就默认选择第一种支付方式
        if (checkedPaymentData != null && !isNormalOrActive()) {
            checkedPaymentData = findFirstPayment();
        }
        if (checkedPaymentData == null) {
            checkedPaymentData = findFirstPayment();
            CatUtils.logError("noDefaultPayType",
                    getString(R.string.cashier__no_default_pay_type));
            CashierStaticsUtils.logCustom("paybiz_cashier_no_selected_payment", null, null, getUniqueId());
        }
        if (checkedPaymentData != null) {
            String payType = checkedPaymentData.getPayType();
            if (handlerProxy != null) {
                handlerProxy.setPayType(payType);
            } else if (nativeStandardCashierAdapter != null) {
                nativeStandardCashierAdapter.setPayType(payType);
            }
            HashMap<String, Object> map = new AnalyseUtils.MapBuilder()
                    .add("nb_version", PayBaseConfig.getProvider().getPayVersion())
                    .add("tradeNo", tradeNo)
                    .add("merchant_no", getMchId())
                    .add("default_pay_type", payType)
                    .build();
            CashierStaticsUtils.techMis("b_pay_ddse35tm_mv", map, getUniqueId());
            CashierStaticsUtils.logModelEvent(getPageName(), "b_pay_6wu70o9w_mv",
                    "收银台默选的支付方式上报", map, StatisticsUtils.EventType.VIEW, getUniqueId());
        }

        CashierAnalyseUtils.logDefaultSelectedPayType(getContext(), cashier);
    }

    private boolean isNormalOrActive() {
        if (checkedPaymentData == null) {
            return false;
        }
        int status = checkedPaymentData.getStatus();
        return status == PayStatus.NORMAL || status == PayStatus.ACTIVE;
    }

    private IPaymentData findFirstPayment() {
        Cashier cashier = getCashier();
        isFirstCheckBox = true;
        List<CashierPayment> paymentList = cashier.getPaymentDataList();
        if (!CollectionUtils.isEmpty(paymentList)) {
            for (int i = 0; i < paymentList.size(); i++) {
                CashierPayment cashierPayment = paymentList.get(i);
                if (DEBUG) {
                    Log.d(TAG, "findFirstPayment: " + cashierPayment.getPayType() + " : "
                            + cashierPayment.isSelected());
                }
                if (PayTypeUtils.isWalletPay(cashierPayment.getPayType())) {
                    IPaymentData iPaymentData = WalletPayManager.getInstance().findFirstRecommendPayment(cashierPayment);
                    if (iPaymentData != null) {
                        return iPaymentData;
                    }
                } else {
                    return cashierPayment;
                }
            }
        }
        return null;
    }


    // 该方法使用 standardCashier 或者 getActivity() 时需要空判断
    private void initActionBar() {
        if (getActivity() == null) {
            return;
        }
        android.support.v7.app.ActionBar bar = ((BaseActivity) getActivity()).getSupportActionBar();
        if (bar != null) {
            bar.hide();
        }
    }

    // 该方法使用 standardCashier 或者 getActivity() 时需要空判断
    private void showActionBar() {
        if (getActivity() == null) {
            return;
        }
        android.support.v7.app.ActionBar bar = ((BaseActivity) getActivity()).getSupportActionBar();
        if (bar != null) {
            bar.show();
        }
        Cashier cashier = getCashier();
        if (TextUtils.isEmpty(cashier.getOrderTxt())) {
            ((MTCashierActivity) getActivity()).setActionBarTitle(R.string.cashier__payinfo_title);
        } else {
            ((MTCashierActivity) getActivity()).setActionBarTitle(cashier.getOrderTxt());
        }
    }

    private void showOrderArea() {
        Cashier cashier = getCashier();
        showHeadNotice(cashier.getHeadNotice());
        showRemainingTime(cashier.getExpireTime(), cashier.getCurrentTime());
        showOrderInfo();
    }

    private void showHeadNotice(Map<String, Object> headNotice) {
        if (headNotice == null || getView() == null) {
            return;
        }
        NoticeView noticeView = getView().findViewById(R.id.notice_layout);
        noticeView.setStyle(NoticeView.NoticeStyle.ROUND_ORANGE);
        String content = "";
        if (headNotice.get("content") != null) {
            content = String.valueOf(headNotice.get("content"));
        }
        String url = "";
        if (headNotice.get("url") != null) {
            url = String.valueOf(headNotice.get("url"));
        }
        if (!TextUtils.isEmpty(content)) {
            HashMap<String, Object> map = new AnalyseUtils.MapBuilder().
                    add("scene", "收银台首页小黄条").
                    add("link", content).build();
            AnalyseUtils.logModelEvent("b_aZuNd", "显示协议", map, AnalyseUtils.EventType.VIEW, -1);
            noticeView.setText(content);
            noticeView.setVisibility(View.VISIBLE);
            if (!TextUtils.isEmpty(url)) {
                String finalUrl = url;
                noticeView.setOnClickListener(v -> {
                    AnalyseUtils.logModelEvent("b_hxOEn", "点击协议", map,
                            AnalyseUtils.EventType.CLICK, -1);
                    UriUtils.open(getActivity(), finalUrl);
                });
            }
        } else {
            noticeView.setVisibility(View.GONE);
        }
    }

    private void showRemainingTime(int expireTime, int currentTime) {
        if (getView() == null) {
            return;
        }
        LinearLayout timerLayout = getView().findViewById(R.id.layout_cashier_remaining_time);
        if (expireTime <= 0) {
            timerLayout.setVisibility(View.GONE);
        } else {
            CashierTimerView timerView = new CashierTimerView(getContext(), getCashier().getRemainTxt());
            timerLayout.addView(timerView);
            //倒计时已经开启了，不再判断过期时间（针对会重新createView的情况）
            if (remainingCountDownTimer == null) {
                long remainingTime = expireTime - currentTime; //单位是秒
                if (remainingTime <= 0) { // 超时了，直接弹dialog
                    if (handlerProxy != null) {
                        handlerProxy.onOrderTimeout();
                    } else if (nativeStandardCashierAdapter != null) {
                        nativeStandardCashierAdapter.onTimerFinish();
                    }
                } else {
                    //不保留活动会让倒计时重新开始，不处理，与改版前一致
                    remainingCountDownTimer = new RemainingCountDownTimer<>(timerView,
                            remainingTime * 1000, 1000, () -> {
                        removeDialogFragments(); //移除 DialogFragment，否则无法触发超时逻辑
                        remainingCountDownTimer.cancel();
                        remainingCountDownTimer = null;
                        if (handlerProxy != null) {
                            handlerProxy.onOrderTimeout();
                        } else if (nativeStandardCashierAdapter != null) {
                            nativeStandardCashierAdapter.onTimerFinish();
                        }
                    });
                    remainingCountDownTimer.start();
                }
            }
        }
    }

    private void removeDialogFragments() {
        Fragment cardPayFunctionGuideDialogFragment = getChildFragmentManager()
                .findFragmentByTag(CardPayFunctionGuideDialogFragment.TAG);
        if (cardPayFunctionGuideDialogFragment instanceof CardPayFunctionGuideDialogFragment) {
            ((CardPayFunctionGuideDialogFragment) cardPayFunctionGuideDialogFragment).dismissAllowingStateLoss();
        }
        Fragment promotionSignedGuideFragment = getChildFragmentManager()
                .findFragmentByTag(PromotionSignedGuideFragment.TAG);
        if (promotionSignedGuideFragment instanceof PromotionSignedGuideFragment) {
            ((PromotionSignedGuideFragment) promotionSignedGuideFragment).dismissAllowingStateLoss();
        }
        Fragment dcepDialogFragment = getChildFragmentManager()
                .findFragmentByTag(DCEPDialogFragment.TAG);
        if (dcepDialogFragment instanceof DCEPDialogFragment) {
            ((DCEPDialogFragment) dcepDialogFragment).dismissAllowingStateLoss();
        }
    }

    private void showOrderInfo() {
        if (getView() == null) {
            return;
        }
        LinearLayout businessInfoLayout = getView().findViewById(R.id.layout_business_info);
        CashierOrderInfoView orderInfoView = new CashierOrderInfoView(getContext());
        Cashier cashier = getCashier();
        orderInfoView.init(cashier);
        orderInfoView.refreshView(calculateTotalFee(checkedPaymentData).floatValue());
        businessInfoLayout.addView(orderInfoView);

        // 订单信息和营销浮层都与优惠金额有关，两部分的展示、刷新逻辑放在一起，便于统一处理
        LinearLayout parent = getView().findViewById(R.id.cashier__discount_view);
        showDiscountFloatingLayer(parent, checkedPaymentData);
        DiscountMonitorHelper.getInstance().saveOriginMoney(getCashier() == null ? -1 : getCashier().getTotalFee());
    }

    private void initConfirmButton() {
        if (getView() == null) {
            return;
        }
        confirmButton = getView().findViewById(R.id.btn_cashier_pay_confirm);
        // 如果默选是第三方支付的情况下，初始化按钮文案
        if (!isMTPayOrCreditPay(getWalletPay(), checkedPaymentData)) {
            if (!TextUtils.isEmpty(getCashier().getPayButtonText())) {
                confirmButton.setText(getCashier().getPayButtonText());
            }
        }
        confirmButton.setOnClickListener(new NoDuplicateClickListener() {
            @Override
            public void onSingleClick(View v) {
                onClickConfirmButton(checkedPaymentData, CreditOpenUtils.STANDARD_HOMEBUTTON_SUFFIX);
            }
        }.setClickInternal(1000));
        getView().findViewById(R.id.view_bottom_blank).setOnClickListener(new NoDuplicateClickListener() {
            @Override
            public void onSingleClick(View v) {
                onClickConfirmButton(checkedPaymentData, CreditOpenUtils.STANDARD_HOMEBUTTON_SUFFIX);
            }
        }.setClickInternal(1000));

        int bg = ViewUtils.checkProviderResource(MTPayProvider.ResourceId.CASHIER__SUBBTN_BG);
        if (bg >= 0) {
            confirmButton.setBackgroundResource(bg);
        }
        int textResource = ViewUtils.checkProviderResource(MTPayProvider.ResourceId.
                CASHIER__SUBBTN_TEXT_COLOR);
        if (textResource >= 0) {
            confirmButton.setTextColor(getResources().getColor(textResource));
        }
    }

    private void renderPayment() {
        Cashier cashier = getCashier();
        if (getView() == null || cashier == null) {
            return;
        }
        if (mStandardCashierAreaView == null) {
            mStandardCashierAreaView = getView().findViewById(R.id.cashier__pay_type);
            mStandardCashierAreaView.setMTPaymentInnerClick(this);
            mStandardCashierAreaView.setCreditInnerClick(this);
            mStandardCashierAreaView.setOnMTPaymentClick(this);
            mStandardCashierAreaView.setOnThirdPaymentClickListener(this::onClickCashierPayment);
            mStandardCashierAreaView.setOnCreditClickListener(this::onClickCashierPayment);
        }
        mStandardCashierAreaView.init(cashier, this);
    }

    private void initPayment() {
        if (getView() == null) {
            return;
        }
        // 修改跳屏问题：https://km.sankuai.com/page/426037779
        scrollView = getView().findViewById(R.id.cashier_scroll_layout);
        scrollView.setScrollable(true);
        renderPayment();
    }


    private BigDecimal calculateTotalFee(IPaymentData checkedPaymentData) {
        Cashier cashier = getCashier();
        BigDecimal payMoney = BigDecimal.valueOf(cashier != null ? cashier.getTotalFee() : 0);
        // 优惠金额
        BigDecimal discount = getDiscountMoney(checkedPaymentData, shouldShowNewCombineDialog());
        // 待支付金额
        payMoney = CashAmountArithUtils.subtract(payMoney, discount);
        //支付金额= 订单金额- 活动金额（活动+现金券）；参加活动至少支付0.01
        if (CashAmountArithUtils.compare(payMoney, 0) <= 0) {
            payMoney = BigDecimal.valueOf(0.01);
        }
        return payMoney;
    }

    private BigDecimal getDiscountMoney(IPaymentData checkedPaymentData, boolean useRealDiscount) {
        Cashier cashier = getCashier();
        return DiscountCashierUtils.getCashierDiscount(getWalletPayment(cashier), checkedPaymentData, useRealDiscount);
    }

    /**
     * 计算返赠的数量
     *
     * @param checkedPaymentData
     * @return
     */
    private int getRewardCount(IPaymentData checkedPaymentData) {
        Cashier cashier = getCashier();
        return DiscountCashierUtils.getCashierCombineRewardCount(getWalletPayment(cashier), checkedPaymentData);
    }

    /**
     * 确认按钮提示文案，默认为"确认支付"
     *
     * @param isCreditPayNoPwd 该字段是给买单免密升级协议使用，如果用户是开通买单免密则获取另一个字段
     * @return
     */
    private String getPayTip(boolean isCreditPayNoPwd) {
        String tip = "";
        if (checkedPaymentData instanceof MTPayment) {
            if (isCreditPayNoPwd) {
                tip = ((MTPayment) checkedPaymentData).getCreditPayNoPwdButonText();
            } else {
                tip = ((MTPayment) checkedPaymentData).getPayButonText();
            }
        }
        if (TextUtils.isEmpty(tip)) {
            tip = getString(R.string.cashier__pay_confirm);
        }
        return tip;
    }

    @Override
    public void onPaymentClick(IPaymentData selectedPayment) {
        onClickCashierPayment(selectedPayment);
    }

    private void onClickConfirmButton(IPaymentData checkedPaymentData, String openSource) {
        if (handlerProxy != null) {
            if (handlerProxy.canSendRequest()) {
                processClickConfirmButton(checkedPaymentData, openSource);
            } else {
                handlerProxy.processSuspendPaying();
            }
        } else if (nativeStandardCashierAdapter != null) {
            if (nativeStandardCashierAdapter.canSendRequest()) {
                processClickConfirmButton(checkedPaymentData, openSource);
            } else {
                nativeStandardCashierAdapter.processSuspendPaying(getActivity());
            }
        }

    }

    private void processClickConfirmButton(IPaymentData checkedPaymentData, String openSource) {
        if (checkedPaymentData == null) {
            ToastUtils.showSnackToast(getActivity(), R.string.cashier__choose_pay_type);
        } else {
            MTPayment creditPayment;
            if (checkedPaymentData instanceof MTPayment
                    && CreditOpenUtils.isNeedEnterNewOpenCredit(creditPayment = (MTPayment) checkedPaymentData)) {
                //若能进入新的月付开通流程
                mNativeStandardCashierPayProcessStatistic.logOnHomePay(checkedPaymentData, openSource, getUniqueId());
                mCreditPaymentData = creditPayment;
                HalfPageFragment.HalfPageFragmentConfig halfPageFragmentConfig
                        = new HalfPageFragment.HalfPageFragmentConfig("credit_half_page", getCreditPayOpenUrl(CreditOpenUtils.STANDARD_HOMEBUTTON_SUFFIX), creditPayment.getCreditPayOpenInfo().getData(), CREDIT_OPEN_HOMEBUTTON);
                halfPageFragmentConfig.setTunnelExtraData(HalfPageFragment.getTunnelExtraData((MTCashierActivity) getActivity()));
                HalfPageFragment.openHalfPage(this, halfPageFragmentConfig);
                CashierStaticsUtils.reportSystemCheck("b_pay_credit_open_leave_cashier_sc",
                        new AnalyseUtils.MapBuilder().add("url", getCreditPayOpenUrl(CreditOpenUtils.STANDARD_HOMEBUTTON_SUFFIX)).add("scene", CREDIT_OPEN_HOMEBUTTON_SCENE).build(), getUniqueId());
                return;
            }
            // 继续走老逻辑 如果显示的确认checkbox并且没有选中则提示用户
            if (PayTypeUtils.isCreditPay(checkedPaymentData.getPayType()) && mCashierBrandView != null && mCashierBrandView.isShowAgreementCheckBox()) {
                if (!mCashierBrandView.checkAgreementIsSelected()) {
                    ToastUtils.showSnackToast(getActivity(), mCashierBrandView.getBrandAgreement().getUnCheckedTip());
                    return;
                }
            }
            startPay(checkedPaymentData, openSource);
        }
    }

    private void startPay(IPaymentData paymentData, String openSource) {
        //Logan
        String payType = paymentData.getPayType();
        LoganUtils.log("点击确认按钮后网络请求", new AnalyseUtils.InstantReportBuilder().addTradeNo()
                .add("pay_type", payType).add("entrance", "clickbutton").build());
        WalletPayManager.getInstance().setOpenSource(getActivity(), openSource);
        mNativeStandardCashierPayProcessStatistic.logOnHomePay(paymentData, openSource, getUniqueId());
        analyseClickConfirmButton(payType);
        // 标识点击确认按钮
        WalletPayManager.getInstance().setEntry(getActivity(), WalletPayManager.ENTRY_FROM_CASHIER_BUTTON);
        payOrder(paymentData);
    }

    private void analyseClickConfirmButton(String payType) {
        HashMap<String, Object> map = new AnalyseUtils.MapBuilder().add("pay_type", payType).build();
        LoganUtils.log("standard_cashier_mt_pay_confirm", map);
        CashierStaticsUtils.logCustom("standard_cashier_mt_pay_confirm", map, null, getUniqueId());
    }

    @MTPaySuppressFBWarnings("SIC_INNER_SHOULD_BE_STATIC_ANON")
    private void onClickCashierPayment(IPaymentData selectedPayment) {
        if (selectedPayment == null) {
            return;
        }
        Map<String, Object> map = CashierAnalyseUtils.getPayTypeMgeLab(selectedPayment);
        Cashier cashier = getCashier();
        map.put("creditPay_status", getCreditPayStatus(cashier));
        map.put("merchant_no", getMchId());
        AnalyseUtils.logModelEvent("b_6u1yatb7", getString(R.string.cashier__mge_act_click_pay_type),
                map, AnalyseUtils.EventType.CLICK, -1);
        // 上报美团支付支付方式相关信息
        WalletPayAreaAnalyseUtils.reportClickMtPayment(selectedPayment);
        //支付方式没变，就不刷新了
        if (selectedPayment != checkedPaymentData) {
            switchCashierPayment(selectedPayment, CreditOpenUtils.STANDARD_HOMEBUTTON_SUFFIX);
        }
    }

    // 切换支付方式后，界面刷新统一使用收口的方法。
    private void switchCashierPayment(IPaymentData iPaymentData, String openSource) {
        mNativeStandardCashierPayProcessStatistic.logOnHomeSwitchPayment(iPaymentData, openSource, getUniqueId());
        CashierStaticsUtils.logModelEvent(getPageName(), "b_0G11Q", "切换支付方式",
                new AnalyseUtils.InstantReportBuilder()
                        .addTradeNo()
                        .add("merchant_no", getMchId())
                        .add("pay_type", iPaymentData.getPayType())
                        .add("status", String.valueOf(iPaymentData.getStatus()))
                        .build(), StatisticsUtils.EventType.CLICK, getUniqueId());
        if (handlerProxy != null) {
            handlerProxy.setPayType(iPaymentData.getPayType());
        } else if (nativeStandardCashierAdapter != null) {
            nativeStandardCashierAdapter.setPayType(iPaymentData.getPayType());
        }
        checkedPaymentData = iPaymentData;
        // 品宣view的刷新依赖 checkedPaymentData 选中的数据是否有协议和品宣文案，所有一定要在该数据更新后调用
        refreshBrandView();
        // 刷新是否展示营销按钮
        refreshMarketingFloatButton(checkedPaymentData);
        refreshCashierView();
    }

    @MTPaySuppressFBWarnings("NP_NULL_ON_SOME_PATH")
    private CashierPayment getWalletPay() {
        // 理论上cashier不会为空。但挽留弹窗有少量cashier为空导致的Crash，此处尝试修复加强一下逻辑判断
        Cashier cashier = getCashier();
        if (!CollectionUtils.isEmpty(cashier.getPaymentDataList())) {
            for (CashierPayment cashierPayment : cashier.getPaymentDataList()) {
                if (PayTypeUtils.isWalletPay(cashierPayment.getPayType())) {
                    return cashierPayment;
                }
            }
        }
        return null;
    }

    public PayParams genRiskVerifyParams() {
        if (getRiskVerifyParams() != null) {
            PayParams verifyParams = getRiskVerifyParams();
            payParams.verifyPayType = verifyParams.verifyPayType;
            payParams.verifyPayOrderId = verifyParams.verifyPayOrderId;
            payParams.verifyType = verifyParams.verifyType;
            payParams.verifyResult = verifyParams.verifyResult;
            payParams.verifyToken = verifyParams.verifyToken;
        }
        return payParams;
    }

    public PayParams getRiskVerifyParams() {
        return verifyParams;
    }

    public void setRiskVerifyParams(String verifyPayType, String verifyPayOrderId, String verifyType, String verifyResult, String verifyToken) {
        verifyParams = new PayParams();
        if (!TextUtils.isEmpty(verifyPayType)) {
            verifyParams.verifyPayType = verifyPayType;
        }
        if (!TextUtils.isEmpty(verifyPayOrderId)) {
            verifyParams.verifyPayOrderId = verifyPayOrderId;
        }
        if (!TextUtils.isEmpty(verifyType)) {
            verifyParams.verifyType = verifyType;
        }
        if (!TextUtils.isEmpty(verifyResult)) {
            verifyParams.verifyResult = verifyResult;
        }
        if (!TextUtils.isEmpty(verifyToken)) {
            verifyParams.verifyToken = verifyToken;
        }
    }

    public void clearRiskVerifyParams() {
        verifyParams = null;
    }

    private PayParams genPayParams(IPaymentData checkedPaymentData) {
        Cashier cashier = getCashier();
        payParams = CashierRequestUtils.genUniversalParams(cashier, tradeNo, payToken);
        if (checkedPaymentData != null) {
            if (PayType.RISK_VERIFY_THIRD_PAY.contains(checkedPaymentData.getPayType())) {
                payParams = genRiskVerifyParams();
            }
            if (isMTPayOrCreditPay(getWalletPay(), checkedPaymentData)) {
                payParams.walletPayParams = WalletPayManager.getInstance()
                        .appendRequestParams(getActivity(), getWalletPayment(cashier), checkedPaymentData, WalletPayManager.CASHIER_PARAMS);
                setGoHelloPayExtParamToParams(payParams.walletPayParams);
                appendGuidePlans(payParams);
            } else {
                //第三方透传活动id
                putReduceParams(checkedPaymentData.getPaymentReduce());
                payParams.payType = checkedPaymentData.getPayType();
            }
            if (TextUtils.equals(PayersID.ID_UPSEPAY, payParams.payType)) {
                String seType = UPPayUtils.getSepayType();
                if (!TextUtils.isEmpty(seType)) {
                    payParams.upsepayType = seType;
                }
            }
        }
        return payParams;
    }

    private void appendGuidePlans(PayParams payParams) {
        CashierRequestUtils.appendGuidePlans(payParams, getGuidePlanInfos());
    }


    private String getGuidePlanInfos() {
        if (handlerProxy != null) {
            return handlerProxy.getGuidePlanInfos();
        } else if (nativeStandardCashierAdapter != null) {
            return nativeStandardCashierAdapter.getGuidePlanInfos();
        }
        return "";
    }

    private int getSceneCode(int requestCode) {
        if (requestCode == CREDIT_OPEN_HOMEBUTTON) {
            return CREDIT_OPEN_HOMEBUTTON_SCENE;
        }
        if (requestCode == CREDIT_OPEN_MARKETBUTTON) {
            return CREDIT_OPEN_MARKETBUTTON_SCENE;
        }
        if (requestCode == CREDIT_OPEN_SELECTDIALOG) {
            return CREDIT_OPEN_SELECTDIALOG_SCENE;
        }
        return -1;
    }

    //根据requestCode判断是否是月付开通场景
    private boolean isCreditOpenScene(int requestCode) {
        return requestCode == CREDIT_OPEN_HOMEBUTTON || requestCode == CREDIT_OPEN_MARKETBUTTON || requestCode == CREDIT_OPEN_SELECTDIALOG;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (isCreditOpenScene(requestCode)) {
            //如果是月付开通场景
            HalfPageFragment.onActivityResult(resultCode, data, new HalfPageFragment.HalfPageListener() {
                @Override
                public void onLoadFail(int errorCode, String errorMessage) {
                    // ToastUtils.showSnackToast(getActivity(), getString(R.string.mpay__open_credit_pay_error));
                    CashierStaticsUtils.reportSystemCheck("b_pay_credit_open_back_to_cashier_sc",
                            new AnalyseUtils.MapBuilder().add("errorCode", errorCode)
                                    .add("errorMessage", errorMessage).add("scene", getSceneCode(requestCode))
                                    .add("url", getCreditPayOpenUrl(requestCode))
                                    .build(), getUniqueId());
                }

                @Override
                public void onSuccess(@Nullable String result) {
                    dealCreditPayOpenResult(result, requestCode);
                    CashierStaticsUtils.reportSystemCheck("b_pay_credit_open_back_to_cashier_sc",
                            new AnalyseUtils.MapBuilder().add("result", result).add("scene", getSceneCode(requestCode))
                                    .add("url", getCreditPayOpenUrl(requestCode))
                                    .build(), getUniqueId());
                }
            });
        }
    }

    private String getCreditPayOpenUrl(String scene) {
        return CreditOpenUtils.getCreditOpenFinalURL(getActivity(), mCreditPaymentData.getCreditPayOpenInfo().getUrl(), scene, "");
    }


    private String getCreditPayOpenUrl(int requestCode) {
        String scene = "";
        if (requestCode == CREDIT_OPEN_SELECTDIALOG) {
            scene = CreditOpenUtils.STANDARD_SELECTDIALOG_SUFFIX;
        } else if (requestCode == CREDIT_OPEN_MARKETBUTTON) {
            scene = CreditOpenUtils.STANDARD_MARKETBUTTON_SUFFIX;
        } else if (requestCode == CREDIT_OPEN_HOMEBUTTON) {
            scene = CreditOpenUtils.STANDARD_HOMEBUTTON_SUFFIX;
        }
        return getCreditPayOpenUrl(scene);
    }

    //统一处理月付开通结果
    private void dealCreditPayOpenResult(String result, int requestCode) {
        if (!TextUtils.isEmpty(result)) {
            try {
                JSONObject jsonObject = new JSONObject(result);
                int openResult = jsonObject.getInt("fd_maidan_opened_status");
                switch (requestCode) {
                    case CREDIT_OPEN_HOMEBUTTON:
                        dealCreditPayOpenFromHomeButton(openResult, CreditOpenUtils.STANDARD_HOMEBUTTON_SUFFIX);
                        break;
                    case CREDIT_OPEN_MARKETBUTTON:
                        dealCreditPayOpenFromMarketButton(openResult, CreditOpenUtils.STANDARD_MARKETBUTTON_SUFFIX);
                        break;
                    case CREDIT_OPEN_SELECTDIALOG:
                        dealCreditPayOpenFromSelectDialog(openResult);
                        break;
                    default:
                        break;
                }
            } catch (JSONException e) {
                LoganUtils.logError("MTCashierRevisionFragment_dealCreditPayOpenResult", e.getMessage());
            }
        }
    }

    //处理首页直接开通月付回调
    private void dealCreditPayOpenFromHomeButton(int openResult, String openSource) {
        if (openResult == CreditOpenUtils.OPEN_SUCCESS) { //成功
            LocalBroadcastManager.getInstance(getContext()).sendBroadcast(new Intent("com.meituan.android.cashier.standardCashier.refresh"));
            // 发起支付网络请求之前，生成月付开通已成功的参数。请求时带上。
            CreditOpenUtils.setCreditOpenVerifyScene();
            startPay(mCreditPaymentData, openSource);
        } else if (openResult == CreditOpenUtils.OPEN_REFUSE) { //失败
            ToastUtils.showSnackToast(getActivity(), getString(R.string.mpay__open_credit_pay_fail));
        } else {
            //do nothing
        }
    }

    //处理营销浮层开通月付回调
    private void dealCreditPayOpenFromMarketButton(int openResult, String openSource) {
        dealCreditPayOpenFromHomeButton(openResult, openSource);
    }

    //处理切卡弹窗开通月付回调
    private void dealCreditPayOpenFromSelectDialog(int openResult) {
        if (openResult == CreditOpenUtils.OPEN_SUCCESS) { //成功
            LocalBroadcastManager.getInstance(getContext()).sendBroadcast(new Intent("com.meituan.android.cashier.standardCashier.refresh"));
            // 发起支付网络请求之前，生成月付开通已成功的参数。请求时带上。
            CreditOpenUtils.setCreditOpenVerifyScene();
            goHelloPayWhenChangedSelectedBank(mCreditPaymentData, null, mPayMoneyChanged);
        } else if (openResult == CreditOpenUtils.OPEN_REFUSE) { //失败
            ToastUtils.showSnackToast(getActivity(), getString(R.string.mpay__open_credit_pay_fail));
        } else {
            //do nothing
        }
    }

    // 发起支付请求后，需要重置中间变量，参考 MTCashierRevisionFragment_onRequestStart
    public void payOrder(IPaymentData checkedPaymentData) {
        boolean isMtPay = isMTPayOrCreditPay(getWalletPay(), checkedPaymentData);
        payParams = genPayParams(checkedPaymentData);
        if (checkedPaymentData != null && handlerProxy != null) {
            handlerProxy.setPayType(checkedPaymentData.getPayType());
        } else if (checkedPaymentData != null && nativeStandardCashierAdapter != null) {
            nativeStandardCashierAdapter.setPayType(checkedPaymentData.getPayType());
        }
        payParams.moneyChanged = 0;
        //美团支付，包括组合支付（储值卡）、尝鲜支付、分期支付、信用付
        if (isMtPay) {
            CashierStaticsUtils.techMis("b_pay_2qmi5hr1_mv", new AnalyseUtils.InstantReportBuilder().build(), getUniqueId());
            HashMap<String, String> requestParams = CashierRequestUtils.getHelloPayMap(payParams);
            OuterBusinessParamUtils.appendExtraParamsTogoHelloPay((MTCashierActivity) getActivity(), requestParams);
            WalletPayManager.appendOpenSource(requestParams, WalletPayManager.getInstance().getOpenSource(getActivity()));
            PayRetrofit.getInstance().create(CashierRequestService.class, this,
                    REQ_TAG_GO_HELLO_PAY).goHelloPay(requestParams);
            logMgeShowSelectBankDialog();
        } else if (checkedPaymentData != null && TextUtils.equals(PayersID.ID_DCEP_PAY, checkedPaymentData.getPayType())) {
            if (checkedPaymentData instanceof CashierPayment) {
                CashierPayment cashierPayment = (CashierPayment) checkedPaymentData;
                if (cashierPayment.getBankListPage() != null
                        && !CollectionUtils.isEmpty(cashierPayment.getBankListPage().getPaymentList())) {
                    payParams.uniqueId = getUniqueId();
                    DCEPDialogFragment dcepDialogFragment = DCEPDialogFragment
                            .newInstance(tradeNo, ((CashierPayment) checkedPaymentData).getBankListPage(),
                                    payParams.clone(), getAppId() == null ? "" : getAppId(),
                                    getDowngradeErrorInfo(), getExtraStatics(), getExtraData(), getExtendTransmissionParams());
                    dcepDialogFragment.show(getChildFragmentManager());
                } else {
                    ToastUtils.showSnackToast(getActivity(), R.string.cashier__dcep_data_error);
                }
            } else {
                ToastUtils.showSnackToast(getActivity(), R.string.cashier__dcep_data_error);
            }
        } else {
            //第三方支付
            startDirectPay(CashierRequestUtils.getDirectPayMap(payParams, SystemInfoUtils.getIMSI(getActivity())));
        }
    }

    private String getExtraData() {
        initStandardCashier();
        if (handlerProxy != null) {
            return handlerProxy.getExtraData();
        } else if (nativeStandardCashierAdapter != null) {
            return nativeStandardCashierAdapter.getExtraData();
        }
        return "";
    }

    private String getExtraStatics() {
        initStandardCashier();
        if (handlerProxy != null) {
            return handlerProxy.getExtraStatics();
        } else if (nativeStandardCashierAdapter != null) {
            return nativeStandardCashierAdapter.getExtraStatics();
        }
        return "";
    }

    private void clearVerifyParams() {
        payParams.verifyPayType = "";
        payParams.verifyPayOrderId = "";
        payParams.verifyType = "";
        payParams.verifyResult = "";
        payParams.verifyToken = "";
    }

    public void startDirectPay(HashMap<String, String> param) {
        if (CollectionUtils.isEmpty(param)) {
            return;
        }
        if (!PayType.RISK_VERIFY_THIRD_PAY.contains(param.get("pay_type"))) {
            clearVerifyParams();
        }
        // 如果是 AlipayHK 需要追加 pay_success_url
        if (PayType.ALIPAYHK_APP.equals(param.get("pay_type"))) {
            String alipayHKBackScheme = AlipayHKUtils.getAlipayHKBackScheme(getActivity());
            if (!TextUtils.isEmpty(alipayHKBackScheme)) {
                param.put("pay_success_url", alipayHKBackScheme);
            }
        }
        PayRetrofit.getInstance().create(CashierRequestService.class, this, REQ_TAG_PAY_ORDER).
                startDirectPay(param,
                        MTPayConfig.getProvider().getFingerprint(),
                        getAppId() == null ? "" : getAppId(),
                        getDirectPayExtParam(), getGuidePlanInfos(), "",
                        getExtraData(), getExtDimStat(), getExtendTransmissionParams());
    }

    private String getExtDimStat() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("outer_business_statics", getExtraStatics());
        } catch (Exception e) {
            LoganUtils.logError("MTCashierRevisionFragment_getExtDimStat", e.getMessage());
        }
        return jsonObject.toString();
    }

    private String getDirectPayExtParam() {
        String guideRequestNo = "";
        if (handlerProxy != null) {
            guideRequestNo = handlerProxy.getGuideRequestNo();
        } else if (nativeStandardCashierAdapter != null) {
            guideRequestNo = nativeStandardCashierAdapter.getGuideRequestNo();
        }
        if (TextUtils.isEmpty(guideRequestNo)) {
            return getDowngradeErrorInfo();
        }
        String downgradeErrorInfo = getDowngradeErrorInfo();
        JSONObject jsonObject;
        try {
            if (TextUtils.isEmpty(downgradeErrorInfo)) {
                jsonObject = new JSONObject();
            } else {
                jsonObject = new JSONObject(downgradeErrorInfo);
            }
        } catch (JSONException e) {
            e.printStackTrace();
            jsonObject = new JSONObject();
        }
        try {
            jsonObject.put(Constants.KEY_GUIDE_REQUEST_NO, guideRequestNo);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject.toString();
    }

    private void putReduceParams(PaymentReduce reduce) {
        if (reduce != null && payParams != null) {
            ReduceInfo reduceInfo = reduce.getNoBalanceReduceInfo();
            if (reduceInfo != null) {
                payParams.campaignId = reduceInfo.getCampaignId();
                payParams.couponCode = reduceInfo.getCashTicketId();
            }
        }
    }

    @Override
    public void onRequestSucc(int tag, Object obj) {
        if (tag == REQ_TAG_PAY_ORDER && obj instanceof PayResult) {
            PayResult payResult = (PayResult) obj;
            if (isShowRiskDialogFragment(payResult)) {
                RiskDialogFragment.newInstance(payResult).show(getChildFragmentManager());
                return;
            }
        }
        if (tag == WalletPayManager.REQ_TAG_REFRESH_INSTALLMENT) {
            // MTHalfPageCreditPaymentView.refreshInstallmentRequest 和此处代码一致
            if (obj instanceof RefreshInstallment) {
                isRefreshInstallmentFinish = true;
                RefreshInstallment refreshInstallment = (RefreshInstallment) obj;
                if (checkedPaymentData instanceof MTPayment) {
                    MTPayment mtPayment = (MTPayment) checkedPaymentData;
                    Cashier cashier = getCashier();
                    WalletPayManager.getInstance().updateInstallmentData(refreshInstallment, getWalletPayment(cashier), mtPayment);
                }
                refreshCashierView();
                CatUtils.logRate(ACTION_REFRESH_INSTALLMENT, CatConstants.CODE_DEFAULT_OK);
                AnalyseUtils.techMis("b_pay_d3xt3vs4_mv", new AnalyseUtils.MapBuilder()
                        .add("is_support_period", refreshInstallment.getIsSupportInstallment())
                        .build());
                if (DEBUG) {
                    MTPayment mtPayment = (MTPayment) checkedPaymentData;
                    Log.d(TAG, "onRequestSucc: checkedPayment " + mtPayment.getInstallment());
                }
            } else {
                ToastUtils.showSnackToast(getActivity(), getString(R.string.mpay__installment_network_error));
                CatUtils.logRate(ACTION_REFRESH_INSTALLMENT, CODE_REFRESH_INSTALLMENT_DATA_ERROR);
                CatUtils.logError("installmentRequestSuccessRefreshFail",
                        getString(R.string.mpay__installment_refresh_fail));
                AnalyseUtils.techMis("b_pay_x3wmmjai_mv", new AnalyseUtils.MapBuilder()
                        .add("message", getString(R.string.mpay__installment_refresh_fail)).build());
            }
            return;
        }
        if (handlerProxy != null) {
            handlerProxy.onRequestSucc(tag, obj);
        } else if (nativeStandardCashierAdapter != null) {
            nativeStandardCashierAdapter.onRequestSucc(tag, obj);
        }
    }

    @Override
    public void onRequestException(int tag, Exception e) {
        if (tag == WalletPayManager.REQ_TAG_REFRESH_INSTALLMENT) {
            // MTHalfPageCreditPaymentView.refreshInstallmentRequest 和此处代码一致
            if (e instanceof PayException) {
                ExceptionUtils.handleException(getActivity(), e, null);
                CatUtils.logRate(ACTION_REFRESH_INSTALLMENT, ((PayException) e).getCode());
                AnalyseUtils.techMis("b_pay_x3wmmjai_mv", new AnalyseUtils.MapBuilder()
                        .add("message", ((PayException) e).getMessage())
                        .add("code", ((PayException) e).getCode())
                        .add("level", ((PayException) e).getLevel())
                        .build());
            } else {
                ToastUtils.showSnackToast(getActivity(), getString(R.string.mpay__installment_network_error));
                CatUtils.logRate(ACTION_REFRESH_INSTALLMENT, CODE_REFRESH_INSTALLMENT_EXCEPTION);
                AnalyseUtils.techMis("b_pay_x3wmmjai_mv", new AnalyseUtils.MapBuilder()
                        .add("message", e.getMessage())
                        .build());
            }
            return;
        }
        if (handlerProxy != null) {
            handlerProxy.onRequestException(tag, e);
        } else if (nativeStandardCashierAdapter != null) {
            nativeStandardCashierAdapter.onRequestException(tag, e);
        }
    }

    @Override
    public void onRequestFinal(int tag) {
        if (confirmButton.isLoading()) {
            confirmButton.stop();
        }
        hideProgress();
        if (checkedPaymentData != null && handlerProxy != null) {
            handlerProxy.setPayType(checkedPaymentData.getPayType());
        } else if (checkedPaymentData != null && nativeStandardCashierAdapter != null) {
            nativeStandardCashierAdapter.setPayType(checkedPaymentData.getPayType());
        }
        if (tag == WalletPayManager.REQ_TAG_REFRESH_INSTALLMENT) {
            // MTHalfPageCreditPaymentView.refreshInstallmentRequest 和此处代码一致
            AnalyseUtils.techMis("b_pay_5ejlvgw8_mc", new AnalyseUtils.MapBuilder()
                    .add("consume_time", System.currentTimeMillis() - WalletPayManager.getInstance()
                            .getRequestTakeUpTime())
                    .build());
        }
    }

    @Override
    public void onRequestStart(int tag) {
        if (REQ_TAG_PAY_ORDER == tag || REQ_TAG_GO_HELLO_PAY == tag) {
            // 发起支付请求时重置营销数据
            if (getActivity() != null) {
                ((MTCashierActivity) getActivity()).setPromotion(null);
            }
            if (getActivity() != null && !getActivity().isFinishing() && confirmButton != null) {
                PayHornConfigBean configBean = PayHornConfigService.get().getPayCashierHornConfigBean();
                //华为系统并且打开禁止绘制开关的时候不进行绘制
                if (!(RomUtils.isForbidDrawPhone() && (configBean != null) && configBean.isHwNotDrawPointSwitch())) {
                    confirmButton.start();
                }
            }
        } else if (WalletPayManager.REQ_TAG_REFRESH_INSTALLMENT == tag) {
            // MTHalfPageCreditPaymentView.refreshInstallmentRequest 和此处代码一致
            showProgress(true);
            // 加载框不能取消
            if (getActivity() != null && ((PayBaseActivity) getActivity()).getMtProgressDialog() != null) {
                ((PayBaseActivity) getActivity()).getMtProgressDialog().setCancelable(false);
            }
        } else {
            showProgress(BrandUtils.isHelloBrand());
        }
    }


    @MTPaySuppressFBWarnings("FE_FLOATING_POINT_EQUALITY")
    @Override
    public void onSelected(IBankcardData selectedPayment) {
        if (isAdded() && !isDetached()) {
            if (checkedPaymentData == null) {
                return;
            }
            if (isPaymentAbnormal(selectedPayment)) {
                return;
            }
            MTPayment mtPayment = null;
            int payMoneyChanged = 0;
            if (selectedPayment instanceof Payment && checkedPaymentData instanceof MTPayment) {
                mtPayment = (MTPayment) checkedPaymentData;
            } else if (selectedPayment instanceof MTPayment) {
                mtPayment = (MTPayment) selectedPayment;
                BigDecimal payMoney = calculateTotalFee(mtPayment);
                if (Math.abs(CashAmountArithUtils.subtract(payMoney, calculateTotalFee(checkedPaymentData)).floatValue()) > 0.0001) {
                    payMoneyChanged = 1;
                }
            }
            if (mtPayment != null) {
                DiscountMonitorHelper.getInstance().saveDiscountMoney(mtPayment);
                if (selectedPayment instanceof MTPayment) {
                    if (CreditOpenUtils.isNeedEnterNewOpenCredit(mtPayment)) {
                        mNativeStandardCashierPayProcessStatistic.logOnHomePay((MTPayment) selectedPayment, CreditOpenUtils.STANDARD_SELECTDIALOG_SUFFIX, getUniqueId());
                        //若需要进入新的月付开通流程
                        mCreditPaymentData = mtPayment;
                        mPayMoneyChanged = payMoneyChanged;
                        HalfPageFragment.HalfPageFragmentConfig halfPageFragmentConfig
                                = new HalfPageFragment.HalfPageFragmentConfig("credit_half_page", getCreditPayOpenUrl(CreditOpenUtils.STANDARD_SELECTDIALOG_SUFFIX), mtPayment.getCreditPayOpenInfo().getData(), CREDIT_OPEN_SELECTDIALOG);
                        halfPageFragmentConfig.setTunnelExtraData(HalfPageFragment.getTunnelExtraData((MTCashierActivity) getActivity()));
                        HalfPageFragment.openHalfPage(this, halfPageFragmentConfig);
                        CashierStaticsUtils.reportSystemCheck("b_pay_credit_open_leave_cashier_sc",
                                new AnalyseUtils.MapBuilder().add("url", getCreditPayOpenUrl(CreditOpenUtils.STANDARD_SELECTDIALOG_SUFFIX)).add("scene", CREDIT_OPEN_SELECTDIALOG_SCENE).build(), getUniqueId());
                        return;
                    }
                    goHelloPayWhenChangedSelectedBank(mtPayment, null, payMoneyChanged);
                } else {
                    goHelloPayWhenChangedSelectedBank(mtPayment, selectedPayment, payMoneyChanged);
                }
            }
        }
    }


    /***
     * @param checkedPaymentData  收银台首页选中的支付方式
     * @param bankcardData 切卡弹窗选中的支付方式
     * @param payMoneyChanged 价格是否有变
     */
    private void goHelloPayWhenChangedSelectedBank(IPaymentData checkedPaymentData,
                                                   IBankcardData bankcardData, int payMoneyChanged) {
        Cashier cashier = getCashier();
        PayParams payParams = CashierRequestUtils.genUniversalParams(cashier, tradeNo, payToken);

        if (checkedPaymentData != null && handlerProxy != null) {
            handlerProxy.setPayType(checkedPaymentData.getPayType());
        } else if (checkedPaymentData != null && nativeStandardCashierAdapter != null) {
            nativeStandardCashierAdapter.setPayType(checkedPaymentData.getPayType());
        }
        WalletPayManager.getInstance().setEntry(getActivity(), WalletPayManager.ENTRY_FROM_CASHIER_CHANGE_PAYTYPE);
        // 如果一级支付方式不为空，则拼接一级支付方式参数
        if (checkedPaymentData != null) {
            payParams.walletPayParams = WalletPayManager.getInstance()
                    .appendRequestParams(getActivity(), getWalletPayment(cashier), checkedPaymentData,
                            WalletPayManager.CASHIER_SELECT_BANK_DIALOG_PARAMS);
            setGoHelloPayExtParamToParams(payParams.walletPayParams);
            appendGuidePlans(payParams);
        }
        // 如果美团卡组合的银行卡（切卡弹窗选中的支付方式）不为空，则更新组合（切卡弹窗选中的支付方式）参数
        if (bankcardData != null) {
            WalletPayManager.getInstance().updateBankcardParams(getActivity(), checkedPaymentData, bankcardData,
                    payParams.walletPayParams);
        }
        payParams.moneyChanged = payMoneyChanged;
        payParams.fromSelectBankCard = 1;
        //埋点
        String payType = checkedPaymentData != null ? checkedPaymentData.getPayType() : "";
        CashierStaticsUtils.techMis("b_pay_2qmi5hr1_mv", new AnalyseUtils.InstantReportBuilder().addTradeNo().
                add("pay_type", payType).add("entrance", "bankcardview").build(), getUniqueId());
        WalletPayManager.getInstance().setOpenSource(getActivity(), CreditOpenUtils.STANDARD_SELECTDIALOG_SUFFIX);
        mNativeStandardCashierPayProcessStatistic.logOnHomePay(checkedPaymentData, CreditOpenUtils.STANDARD_SELECTDIALOG_SUFFIX, getUniqueId());
        analyseClickConfirmButton(payType);

        // 发起支付请求后，需要重置中间变量，参考 MTCashierRevisionFragment_onRequestStart
        HashMap<String, String> requestParams = CashierRequestUtils.getHelloPayMap(payParams);
        OuterBusinessParamUtils.appendExtraParamsTogoHelloPay((MTCashierActivity) getActivity(), requestParams);
        WalletPayManager.appendOpenSource(requestParams, CreditOpenUtils.STANDARD_SELECTDIALOG_SUFFIX);
        PayRetrofit.getInstance().create(CashierRequestService.class, this, REQ_TAG_GO_HELLO_PAY).
                goHelloPay(requestParams);
        logMgeShowSelectBankDialog();
        analyseComponentDispatch(ACTION_COMPONENT_START, MtPaySceneUtils.CASHIER_PAY);
    }

    @Override
    public void onClose() {
        if (isAdded() && !isDetached()) {
            AnalyseUtils.logModelEvent("b_2c5n632e", "点击关闭切卡弹窗", null,
                    AnalyseUtils.EventType.CLICK, -1);
        }
    }

    @Override
    public void onGlobalLayout() {
        if (getView() != null) {
            scrollView.setOnTouchListener(new TouchListener());
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.JELLY_BEAN) {
                getView().getViewTreeObserver().removeOnGlobalLayoutListener(this);
            } else {
                getView().getViewTreeObserver().removeGlobalOnLayoutListener(this);
            }
        }
    }

    public void onCreditOpened(MTPayment guidePayTypeInfo) {
        initStandardCashier();
        if (handlerProxy != null) {
            handlerProxy.refreshWhenBackFromMTPay(true);
        } else if (nativeStandardCashierAdapter != null) {
            nativeStandardCashierAdapter.refreshWhenBackFromMTPay(true);
        }
        this.onClickGuidePayType(guidePayTypeInfo);
    }

    @Override
    public void onClickGuidePayType(MTPayment guidePayTypeInfo) {
        if (guidePayTypeInfo != null) {
            WalletPayManager.getInstance().setEntry(getActivity(), WalletPayManager.ENTRY_FROM_CASHIER_PROMO_GUIDE);
            payOrder(guidePayTypeInfo);
        }
    }

    @Override
    public void onClickPromotionPayType(MTPayment guidePayTypeInfo) {
        if (guidePayTypeInfo != null) {
            payOrder(guidePayTypeInfo);
        }
    }

    @Override
    public void onClickCommonDialogGuideButton(MTPayment guidePayTypeInfo, CashierPopWindowBean cashierPopWindowBean) {
        if (guidePayTypeInfo != null && cashierPopWindowBean != null) {
            int type = cashierPopWindowBean.getType();
            if (type == CashierPopWindowBean.BIND_CARD_PAY_GUIDE
                    || type == CashierPopWindowBean.CREDIT_PAY_GUIDE
                    || type == CashierPopWindowBean.PROMOTION_BINDED_CARD_PAY_GUIDE) {
                WalletPayManager.getInstance().setEntry(getActivity(), WalletPayManager.ENTRY_FROM_CASHIER_PROMO_GUIDE);
            }
            payOrder(guidePayTypeInfo);
        }
    }


    private static class TouchListener implements View.OnTouchListener {
        int scrollY = 0;
        boolean isScrolled = false;

        @Override
        public boolean onTouch(View v, MotionEvent event) {
            if (v instanceof ScrollView) {
                // 如果用户触摸屏幕，则停止自动滚动特效
                ((NSCScrollView) v).setScrollable(false);
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        scrollY = v.getScrollY();
                        break;
                    case MotionEvent.ACTION_MOVE:
                        if (!isScrolled && v.getScrollY() != scrollY) {
                            isScrolled = true;
                            Metrics.getInstance().startCustomFPS(MTCashierRevisionFragment.class.getName());
                        }
                        break;
                    case MotionEvent.ACTION_UP:
                        if (isScrolled) {
                            AnalyseUtils.logModelEvent("b_bWJBC", "滑动展示支付方式",
                                    new AnalyseUtils.MapBuilder().add("IS_BOTTOM", "TRUE").
                                            build(), AnalyseUtils.EventType.VIEW, -1);
                            isScrolled = false;
                            Metrics.getInstance().stopCustomFPS(MTCashierRevisionFragment.class.getName());
                        }
                        break;
                    default:
                        break;
                }
            }
            return false;
        }

    }


    private void refreshPayment() {
        if (!isAdded() || getView() == null) {
            return;
        }
        if (mStandardCashierAreaView != null) {
            Cashier cashier = getCashier();
            mStandardCashierAreaView.refreshPaymentView(checkedPaymentData, cashier);
        }
    }

    private void refreshConfirmButton() {
        if (getView() == null) {
            return;
        }
        Button confirmBtn = getView().findViewById(R.id.btn_cashier_pay_confirm);
        confirmBtn.setEnabled(checkedPaymentData != null
                && !isAbnormal(checkedPaymentData.getStatus()));
        confirmBtn.setText(getPayTip(isCreditPayNoPwd()));
    }

    private void refreshOrderInfo(IPaymentData checkedPaymentData) {
        if (!isAdded() || getView() == null) {
            return;
        }
        LinearLayout businessInfoLayout = getView().findViewById(R.id.layout_business_info);
        for (int i = 0; i < businessInfoLayout.getChildCount(); i++) {
            if (businessInfoLayout.getChildAt(i) instanceof IOrderInfoView) {
                BigDecimal checkedPaymentTotalFee = calculateTotalFee(checkedPaymentData);
                ((IOrderInfoView) businessInfoLayout.getChildAt(i)).refreshView(checkedPaymentTotalFee.floatValue());
                if (handlerProxy != null) {
                    handlerProxy.setFinalFeeText(checkedPaymentTotalFee.toString());
                } else if (nativeStandardCashierAdapter != null) {
                    nativeStandardCashierAdapter.setFinalFeeText(checkedPaymentTotalFee.toString());
                }
            }
        }

        refreshDiscountFloatingLayer(checkedPaymentData);
    }

    private void refreshCashierView() {
        refreshPayment();
        refreshConfirmButton();
        refreshOrderInfo(checkedPaymentData);
        DiscountMonitorHelper.getInstance().saveDiscountMoney(checkedPaymentData);
    }

    private void logMgeShowSelectBankDialog() {
        HashMap<String, Object> map = new AnalyseUtils.MapBuilder().build();
        map.put("change_tab_times", selectBankTimes);
        if (checkedPaymentData != null) {
            map.put("cc_pay_type", checkedPaymentData.getPayType());
        }
        AnalyseUtils.logModelEvent("c_PJmoK", "b_zhwml51d", "收银台首页点击切卡", map,
                AnalyseUtils.EventType.CLICK, -1);
    }

    @Override
    public String getPageName() {
        return "c_PJmoK";
    }

    public void onStop() {
        CashierStaticsUtils.reportModelEventWithViewEvent(getPageName(), "b_Zdp0X", "CLOSE", null, getUniqueId());
        super.onStop();
    }

    @Override
    public void onDetach() {
        if (remainingCountDownTimer != null) {
            remainingCountDownTimer.cancel();
            remainingCountDownTimer = null;
        }
        selectBankTimes = 0;
        nativeStandardCashierAdapter = null;
        handlerProxy = null;
        clearRiskVerifyParams();
        WalletPayManager.getInstance().destroy();
        if (scrollView != null && scrollView.getViewTreeObserver() != null) {
            scrollView.getViewTreeObserver().removeOnScrollChangedListener(onScrollChangedListener);
        }
        super.onDetach();
    }

    private boolean pagePropertiesAvailable() {
        Cashier cashier = getCashier();
        return cashier != null;
    }

    @Override
    public HashMap<String, Object> getPageProperties() {
        HashMap<String, Object> parentProperties = super.getPageProperties();
        if (!pagePropertiesAvailable()) {
            return parentProperties;
        }
        if (properties == null) {
            properties = mNativeStandardCashierPayProcessStatistic.getHomePVProperties(checkedPaymentData);
        }
        //合并两个map
        if (!CollectionUtils.isEmpty(properties)) {
            parentProperties.putAll(properties);
        }
        return parentProperties;
    }

    /**
     * 如果本地检测不支持Android Pay，则删除对应的支付方式
     * https://km.sankuai.com/collabpage/1780647746
     *
     * @param cashier
     * @return
     */
    private IPaymentData checkAndModifyUpsepay(Cashier cashier) {
        // 该方法使用 standardCashier 或者 getActivity() 时需要空判断
        if (cashier == null) {
            return null;
        }
        List<CashierPayment> payments = cashier.getPaymentDataList();
        if (payments == null) {
            return null;
        }
        IPaymentData removedIPaymentData = null;
        Iterator<CashierPayment> iterator = payments.iterator();
        while (iterator.hasNext()) {
            CashierPayment cashierPayment = iterator.next();
            // 检测Android Pay
            if (TextUtils.equals(cashierPayment.getPayType(), PayersID.ID_UPSEPAY)) {
                if (hideUpsePay()) {
                    iterator.remove();
                    removedIPaymentData = cashierPayment;
                }
                UpsePayAndUnionflashPayAnalyeUtils.recordUpsepayStatusWhenInit();
                UPPayUtils.reportStatus();
                break;
            }
        }
        return removedIPaymentData;
    }

    /**
     * 如果本地检测云闪付相关的so文件没有加载成功，则删除对应的支付方式
     * https://km.sankuai.com/page/1354312664
     *
     * @param cashier
     * @return
     */
    private IPaymentData checkAndModifyUnionflashpay(Cashier cashier) {
        // 该方法使用 standardCashier 或者 getActivity() 时需要空判断
        if (cashier == null) {
            return null;
        }
        List<CashierPayment> payments = cashier.getPaymentDataList();
        if (payments == null) {
            return null;
        }
        IPaymentData removedIPaymentData = null;
        Iterator<CashierPayment> iterator = payments.iterator();
        while (iterator.hasNext()) {
            // 检测云闪付
            CashierPayment cashierPayment = iterator.next();
            if (TextUtils.equals(cashierPayment.getPayType(), PayersID.ID_UNION_FLASH_PAY)) {
                if (UpsePayAndUnionflashPayUtils.isDynLoadSoFailed()) {
                    iterator.remove();
                    removedIPaymentData = cashierPayment;
                }
                UpsePayAndUnionflashPayAnalyeUtils.reportUnionflashpayStatus(UpsePayAndUnionflashPayUtils.isDynLoadSoFailed());
                break;
            }
        }
        return removedIPaymentData;
    }

    /**
     * 如果Android Pay的状态未返回或者返回的失败状态，则隐藏Android Pay的支付方式
     *
     * @return
     */
    private boolean hideUpsePay() {
        if (TextUtils.equals("a", DowngradingService.get().getStrategy("android_pay_show_category"))) {
            return UPPayUtils.isCheckStatusEqualError();
        }
        return UPPayUtils.isCheckStatusEqualError() || UPPayUtils.isCheckStatusEqualNull();
    }

    /**
     * 获取极速支付、美团支付前置组件等降级的信息
     */
    private String getDowngradeErrorInfo() {
        if (handlerProxy != null) {
            return handlerProxy.getDowngradeErrorInfo();
        } else if (nativeStandardCashierAdapter != null) {
            return nativeStandardCashierAdapter.getDowngradeErrorInfo();
        }
        return "";
    }


    // gohellopay中添加极速支付失败原因（第三方支付在direct pay中传递）、guideRequestNo
    private void setGoHelloPayExtParamToParams(Map<String, String> payParams) {
        CashierRequestUtils.setGoHelloPayExtParamToParams(payParams, getGuideRequestNo(), getDowngradeErrorInfo());
    }


    private String getGuideRequestNo() {

        if (handlerProxy != null) {
            return handlerProxy.getGuideRequestNo();
        } else if (nativeStandardCashierAdapter != null) {
            return nativeStandardCashierAdapter.getGuideRequestNo();
        }
        return "";
    }

    /**
     * 美团支付契约接口实现
     */
    @Override
    public void onClickAllPayment(View view) {
        selectBankTimes++;
        //将我们的订单号值告诉中介
        Cashier cashier = getCashier();
        if (cashier != null && !TextUtils.isEmpty(cashier.getTradeNo())) {
            PayConstantsMediator.setPayId(PayConstantsMediator.PayIdType.TRADE_ID, cashier.getTradeNo());
        }
    }

    @Override
    public void onClickPointSwitch(IPaymentData iPaymentData, CompoundButton buttonView, boolean isChecked) {
        if (iPaymentData != null) {
            refreshCashierView();
        }
        Cashier cashier = getCashier();
        if (cashier != null) {
            //上报点击事件
            AnalyseUtils.logModelEvent("b_2f7hj0y4", "",
                    new AnalyseUtils.MapBuilder().add("userid", PayBaseConfig.getProvider().getUserId())
                            .add("tradeno", cashier.getTradeNo())
                            .add("switch_result", isChecked ? "on" : "off").build(),
                    AnalyseUtils.EventType.CLICK, -1);
        }
    }

    @Override
    public void onUpdateAgreement(View view, CompoundButton buttonView, boolean isChecked) {
        refreshConfirmButton();
    }

    @Override
    public void onChangeCombineBank(View view) {
        selectBankTimes++;
        //将我们的订单号值告诉中介
        Cashier cashier = getCashier();
        if (cashier != null && !TextUtils.isEmpty(cashier.getTradeNo())) {
            PayConstantsMediator.setPayId(PayConstantsMediator.PayIdType.TRADE_ID, cashier.getTradeNo());
        }
        AnalyseUtils.techMis("b_pay_bvs8nppu_mc", null);
        refreshConfirmButton();
    }

    @Override
    public void onClickNewCardAd(View view) {
        selectBankTimes++;
        //将我们的订单号值告诉中介
        Cashier cashier = getCashier();
        if (cashier != null && !TextUtils.isEmpty(cashier.getTradeNo())) {
            PayConstantsMediator.setPayId(PayConstantsMediator.PayIdType.TRADE_ID, cashier.getTradeNo());
        }
    }

    @Override
    public void onClickDeductSwitch(View view, CompoundButton buttonView, boolean isChecked) {
        Cashier cashier = getCashier();
        WalletPayment walletPayment = getWalletPayment(cashier);
        if (walletPayment != null) {
            if (walletPayment.getBalanceCombineDeduct() != null) { // 余额组合支付视图开关状态
                if (checkedPaymentData instanceof MTPayment
                        && isSupportBalanceCombine(checkedPaymentData)) {
                    walletPayment.getBalanceCombineDeduct().setSwitchOn(isChecked);
                }
                refreshCashierView();
            }
        }
    }

    /**
     * 分期的支付方式选择不同期数回调
     *
     * @param mtPayment 被选中的支付方式,直接使用回调的支付方式
     */
    @Override
    public void onClickPeriodItem(MTPayment mtPayment) {
        if (mtPayment != null) {
            switchCashierPayment(mtPayment, CreditOpenUtils.STANDARD_HOMEBUTTON_SUFFIX);
        }
    }

    // 刷新底部的界面，包括营销按钮与买单的品宣文案。只有第一次进入初始化与切换支付方式的情况才进行刷新
    private void initBottomView() {
        // 初始化底部品宣view
        initBrandView();
        if (checkedPaymentData != null) {
            initMarketingFloatViewAnim();
            // 营销按钮的初始化
            refreshMarketingFloatButton(checkedPaymentData);
            // 品宣文案的刷新
            refreshBrandView();
        }
    }

    private void initMarketingFloatViewAnim() {
        marketingFloatViewEnterAnim = new TranslateAnimation(
                Animation.RELATIVE_TO_SELF, 0.0f,
                Animation.RELATIVE_TO_SELF, 0.0f,
                Animation.RELATIVE_TO_SELF, 1f,
                Animation.RELATIVE_TO_SELF, 0.0f);
        marketingFloatViewEnterAnim.setDuration(150);
        marketingFloatViewExitAnim = new TranslateAnimation(
                Animation.RELATIVE_TO_SELF, 0.0f,
                Animation.RELATIVE_TO_SELF, 0.0f,
                Animation.RELATIVE_TO_SELF, 0.0f,
                Animation.RELATIVE_TO_SELF, 1f);
        marketingFloatViewExitAnim.setDuration(150);
        marketingFloatViewExitAnimListener = new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                if (mCashierMarketingGuideFloatView != null) {
                    mCashierMarketingGuideFloatView.setVisibility(View.GONE);
                }
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        };
        marketingFloatViewExitAnim.setAnimationListener(marketingFloatViewExitAnimListener);
    }

    // 检查当前支付方式是否买单，并且免密开关是打开状态
    private boolean isCreditPayNoPwd() {
        if (checkedPaymentData instanceof MTPayment) {
            MTPayment mtPayment = (MTPayment) checkedPaymentData;
            if (PayTypeUtils.isCreditPay(mtPayment.getPayType())
                    && (mtPayment.getUpdateAgreement() != null &&
                    mtPayment.getUpdateAgreement().isChecked())) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    // 设置品宣协议与文案的数据
    private void updateBrandViewData() {

        Agreement mBrandAgreement = null;
        String mBrandText = "";
        if (checkedPaymentData instanceof MTPayment) {
            MTPayment mtPayment = (MTPayment) checkedPaymentData;
            if (CreditOpenUtils.isNeedEnterNewOpenCredit(mtPayment)) {
                //当前需要进入新的月付开通流程，则不设置协议数据
            } else {
                mBrandAgreement = mtPayment.getAgreement();
                mBrandText = mtPayment.getBrandText();
            }
        } else {
            // 第三方支付目前没有返回该数据类型所有肯定没有品宣布局
        }
        if (mCashierBrandView != null) {
            mCashierBrandView.setBrandData(mBrandAgreement, mBrandText);
        }
    }

    // 初始化品宣view
    private void initBrandView() {
        if (mCashierBrandView == null) {
            mCashierBrandView = new CashierBrandView(getActivity());
            mCashierBrandView.setActivity(getActivity());
        }
        mCashierBrandView.setVisibility(View.GONE);
        LinearLayout mBrandLayout = getView().findViewById(R.id.remind_layout);
        mBrandLayout.removeAllViews();
        mBrandLayout.addView(mCashierBrandView);
    }

    // 更新品宣的数据并在界面上进行展示
    private void refreshBrandView() {
        updateBrandViewData();
        // 根据品宣文案数据变化进行刷新
        if (mCashierBrandView != null) {
            mCashierBrandView.refresh();
        }
    }

    private void refreshDiscountFloatingLayer(IPaymentData checkedPaymentData) {
        if (getView() != null) {
            LinearLayout discountLayout = getView().findViewById(R.id.cashier__discount_view);
            boolean isShow = isShowDiscountFloatingLayer(checkedPaymentData);
            if (isShow) {
                if (mCashierMarketingGuideFloatView != null
                        && mCashierMarketingGuideFloatView.getVisibility() == View.VISIBLE) {
                    mCashierMarketingGuideFloatView.setVisibility(View.GONE);
                    mCashierMarketingGuideFloatView.clearAnimation();
                } else if (discountLayout.getVisibility() == View.VISIBLE) {
                    if (discountLayout.getAnimation() != null) {
                        discountLayout.getAnimation().setAnimationListener(null);
                        discountLayout.clearAnimation();
                    }
                } else if (discountLayout.getVisibility() == View.GONE) {
                    if (discountViewEnterAnim != null) {
                        discountLayout.startAnimation(discountViewEnterAnim);
                    }
                }
                if (isSaveMoneyStyleView(checkedPaymentData)) {
                    refreshSaveMoneyView();
                } else {
                    refreshDiscountView();
                }
                discountLayout.setVisibility(View.VISIBLE);
            } else {
                if (discountLayout.getVisibility() == View.VISIBLE) {
                    if (discountViewExitAnim != null) {
                        discountViewExitAnim.setAnimationListener(discountViewExitAnimListener);
                        discountLayout.startAnimation(discountViewExitAnim);
                    } else {
                        discountLayout.setVisibility(View.GONE);
                    }
                }
            }
        }
    }

    private void refreshDiscountView() {
        if (getView() != null) {
            DiscountView discountView = getView().findViewById(R.id.mpay__discount_view);
            if (discountView != null) {
                discountView.setAllViewVisibility(View.VISIBLE);
                discountView.init(DiscountCashierUtils.getDiscountFloatingLayer(checkedPaymentData));
                discountView.refreshView(getDiscountMoney(checkedPaymentData, shouldShowNewCombineDialog()).floatValue(), getRewardCount(checkedPaymentData));
                int paddingTop = 10;
                // 第三方支付，隐藏CheckBox
                if (!isMTPayOrCreditPay(getWalletPay(), checkedPaymentData)) {
                    discountView.hideRule();
                }
                discountView.setLayoutPadding(0, TransferUtils.dip2px(getContext(), paddingTop), 0, 0);
            } else {
                showDiscountView(getView().findViewById(R.id.cashier__discount_view));
            }
        }
    }

    private void refreshSaveMoneyView() {
        if (getView() != null) {
            SaveMoneyDiscountView saveMoneyView = getView().findViewById(R.id.mpay__save_money_view);
            if (saveMoneyView != null) {
                saveMoneyView.refreshView(checkedPaymentData, getDiscountMoney(checkedPaymentData, shouldShowNewCombineDialog()).floatValue());
            } else {
                showSaveMoneyView(getView().findViewById(R.id.cashier__discount_view));
            }
        }
    }

    /**
     * 营销浮层展示条件：
     * 1 选中的是美团支付；2 不需要绑卡；3 is_use_combine为true 4 有children标签
     * 或者
     * 1 选中的是第三方支付；2 is_use_combine为true；3 有优惠
     *
     * @return 是否在确认区域展示营销浮层
     */
    private boolean isShowDiscountFloatingLayer(IPaymentData checkedPaymentData) {
        if (checkedPaymentData != null) {
            if (checkedPaymentData instanceof MTPayment) {
                MTPayment mtPayment = (MTPayment) checkedPaymentData;
                boolean hasBrandView = !CreditOpenUtils.isNeedEnterNewOpenCredit(mtPayment) && mtPayment.getAgreement() != null
                        && !TextUtils.isEmpty(mtPayment.getBrandText());  // 如果不是月付新开通流程，并且有买单品宣，优先展示
                if (!hasBrandView && isMTPayOrCreditPay(getWalletPay(), checkedPaymentData)) {
                    // 新卡、已签约未绑卡、活动银行不展示营销浮层
                    if (PayTypeUtils.isNeedBindCardPay(checkedPaymentData.getPayType())) {
                        return false;
                    }
                    // 美团支付，是实验组、有优惠展示
                    return DiscountCashierUtils.hasCashierCombineLabelChildren(checkedPaymentData);
                }

            } else { // 第三方支付，有优惠展示
                BigDecimal discount = getDiscountMoney(checkedPaymentData, false);
                return CashAmountArithUtils.compare(discount, 0) > 0;
            }
        }
        return false;
    }

    /**
     * @return 风险弹窗数据是否完整
     */
    private boolean isShowRiskDialogFragment(PayResult payResult) {
        if (payResult == null || TextUtils.isEmpty(payResult.getPayType()) || payResult.getPopUp() == null) {
            return false;
        }
        PopUp popUp = payResult.getPopUp();
        if (TextUtils.isEmpty(popUp.getType())
                || TextUtils.isEmpty(popUp.getSubtype())
                || TextUtils.isEmpty(popUp.getTitle())
                || TextUtils.isEmpty(popUp.getBody())
                || TextUtils.isEmpty(popUp.getConfirmButton())
                || TextUtils.isEmpty(popUp.getCancelButton())
                || TextUtils.isEmpty(popUp.getOrderId())
                || (TextUtils.equals(popUp.getType(), "riskVerify") && TextUtils.isEmpty(popUp.getUrl()))) {
            return false;
        }
        return true;
    }

    /**
     * 权益浮层的展示
     */
    public void showDiscountFloatingLayer(LinearLayout parent, IPaymentData checkedPaymentData) {
        if (isSaveMoneyStyleView(checkedPaymentData)) { //美团支付且命中"省钱"新样式
            showSaveMoneyView(parent);
        } else {
            showDiscountView(parent);
        }
    }

    private boolean isSaveMoneyStyleView(IPaymentData checkedPaymentData) {
        Cashier cashier = getCashier();
        return checkedPaymentData instanceof MTPayment
                && DiscountCashierUtils.isCashierSaveMoneyStyle(getWalletPayment(cashier));
    }

    public void showSaveMoneyView(LinearLayout parent) {
        SaveMoneyDiscountView saveMoneyView = new SaveMoneyDiscountView(getContext());
        saveMoneyView.init(checkedPaymentData);

        saveMoneyView.refreshView(checkedPaymentData, getDiscountMoney(checkedPaymentData, shouldShowNewCombineDialog()).floatValue());
        saveMoneyView.setId(R.id.mpay__save_money_view);
        saveMoneyView.setOnClickDiscountDetail(this::clickDiscountDetail);
        if (parent != null) {
            parent.removeAllViews();
            parent.addView(saveMoneyView);
            initDiscountViewAnim();
            if (checkedPaymentData != null) {
                Cashier cashier = getCashier();
                CashierStaticsUtils.reportModelEventWithViewEvent(getPageName(), "b_pay_kfk8cezg_mv", "营销浮层",
                        new AnalyseUtils.MapBuilder().add("tradeNo", cashier.getTradeNo())
                                .add("pay_type", checkedPaymentData.getPayType())
                                .build(), getUniqueId());
            }
        }
    }

    public void showDiscountView(LinearLayout parent) {
        DiscountView discountView = new DiscountView(getContext());
        discountView.init(DiscountCashierUtils.getDiscountFloatingLayer(checkedPaymentData));
        discountView.setId(R.id.mpay__discount_view);
        discountView.setOnClickDiscountDetail(this::clickDiscountDetail);
        discountView.refreshView(getDiscountMoney(checkedPaymentData, shouldShowNewCombineDialog()).floatValue(), getRewardCount(checkedPaymentData));
        int paddingTop = 10;
        // 第三方支付，隐藏CheckBox
        if (!isMTPayOrCreditPay(getWalletPay(), checkedPaymentData)) {
            discountView.hideRule();
        }
        discountView.setLayoutPadding(0, TransferUtils.dip2px(getContext(), paddingTop), 0, 0);
        if (parent != null) {
            parent.removeAllViews();
            parent.addView(discountView);
            initDiscountViewAnim();
            if (checkedPaymentData != null) {
                Cashier cashier = getCashier();
                CashierStaticsUtils.reportModelEventWithViewEvent(getPageName(), "b_pay_kfk8cezg_mv", "营销浮层",
                        new AnalyseUtils.MapBuilder().add("tradeNo", cashier.getTradeNo())
                                .add("pay_type", checkedPaymentData.getPayType())
                                .build(), getUniqueId());
            }
        }
    }


    /**
     * 是否命中新的权益聚合页面
     *
     * @return 影响标准收银台首页和支付密码弹窗上订单金额和营销浮层金额的计算
     * true  使用real_discount
     * false 使用discount
     */
    public boolean shouldShowNewCombineDialog() {
        WalletPaymentListPage walletPaymentListPage;
        LabelAbTest labelAbTest;
        if (getWalletPayment(cashier) != null
                && (walletPaymentListPage = getWalletPayment(cashier).getWalletPaymentListPage()) != null
                && (labelAbTest = walletPaymentListPage.getLabelAbTest()) != null) {
            return labelAbTest.isShiftMultiCoupon();
        }
        return false;
    }

    private void clickDiscountDetail() {
        ArrayList<PayLabel> labels = DiscountCashierUtils.getCashierCombineLabelPage(checkedPaymentData);
        if (!CollectionUtils.isEmpty(labels)) {
            Material material = DiscountCashierUtils.getDiscountMaterial(checkedPaymentData);
            if (shouldShowNewCombineDialog()) {
                BigDecimal lastDiscount = getDiscountMoney(checkedPaymentData, true);
                // 新权益聚合页弹窗标题使用material -> combine_title字段
                NewCombineLabelDetailDialogFragment newDialogFragment = NewCombineLabelDetailDialogFragment.newInstance(tradeNo,
                        CashierRequestUtils.getCashierType(cashier), labels, (MTPayment) checkedPaymentData, showMoreDiscounts, STANDARD_CASHIER_ENTRY, getExtendTransmissionParams(), material);
                newDialogFragment.show(getActivity().getSupportFragmentManager());
                newDialogFragment.setOnCloseListener((OnCombineDialogCloseListener) (promotionRefreshment, payLabels, showMoreDiscounts) -> {
                    setShowMoreDiscounts(showMoreDiscounts);
                    BigDecimal currentDiscount = getDiscountMoney(checkedPaymentData, true);
                    if (promotionRefreshment != null && checkedPaymentData instanceof MTPayment
                            && isRefreshPeriod(checkedPaymentData, lastDiscount, currentDiscount)) {
                        //执行月付或者联名卡的刷新逻辑
                        RefreshInstallment refreshInstallment = createInstallment(promotionRefreshment, checkedPaymentData);
                        WalletPayManager.getInstance().updateInstallmentData(refreshInstallment,
                                getWalletPayment(cashier), (MTPayment) checkedPaymentData);
                    }
                    //刷新标准收银台首页视图
                    refreshCashierView();
                });
            } else {
                CombineLabelDetailDialogFragment dialogFragment = CombineLabelDetailDialogFragment
                        .newInstance(labels, material);
                dialogFragment.show(getActivity().getSupportFragmentManager());
                BigDecimal lastDiscount = getDiscountMoney(checkedPaymentData, false);
                dialogFragment.setOnCloseListener((payLabels) -> {
                    DiscountCashierUtils.updateCashierCombineLabelPage(checkedPaymentData, payLabels);
                    refreshCashierView();
                    // 判断营销金额上是否发生变化
                    BigDecimal currentDiscount = null;
                    if (PayTypeUtils.isCreditPay(checkedPaymentData.getPayType()) || isInstallmentedCoBrandedCard(checkedPaymentData)) {
                        currentDiscount = getDiscountMoney(checkedPaymentData, false);
                    }

                    if (PaymentUtils.isRefreshPeriod(checkedPaymentData, lastDiscount, currentDiscount)) {
                        refreshInstallmentRequest(getExtendTransmissionParams());
                    }
                });
            }

        }
        if (checkedPaymentData != null) {
            Cashier cashier = getCashier();
            AnalyseUtils.logModelEvent("b_pay_8qr7x85y_mc", "营销浮层-查看规则和明细",
                    new AnalyseUtils.MapBuilder().add("tradeNo", cashier.getTradeNo())
                            .add("pay_type", checkedPaymentData.getPayType())
                            .build(), AnalyseUtils.EventType.CLICK);
        }
    }

    private RefreshInstallment createInstallment(PromotionRefreshment promotionRefreshment, IPaymentData checkedPaymentData) {
        if (promotionRefreshment != null) {
            RefreshInstallment refreshInstallment = new RefreshInstallment();
            refreshInstallment.setPayType(promotionRefreshment.getPayType());
            refreshInstallment.setIsSupportInstallment(promotionRefreshment.getIsSupportInstallment());
            refreshInstallment.setUnsupportedInstallmentReason(promotionRefreshment.getUnsupportedInstallmentReason());
            refreshInstallment.setInstallment(promotionRefreshment.getInstallmentInfo());
            refreshInstallment.setInstallmentRateDescBean(promotionRefreshment.getInstallmentRateDescBean());
            refreshInstallment.setCommonAgreement(promotionRefreshment.getCommonAgreement());
            // 月付和联名卡刷新，保持首页和切卡页的营销标签一致
            refreshInstallment.setLabels(checkedPaymentData.getBottomLabels());
            return refreshInstallment;
        }
        return null;
    }

    private void initDiscountViewAnim() {
        discountViewEnterAnim = new TranslateAnimation(
                Animation.RELATIVE_TO_SELF, 0.0f,
                Animation.RELATIVE_TO_SELF, 0.0f,
                Animation.RELATIVE_TO_SELF, 1f,
                Animation.RELATIVE_TO_SELF, 0.0f);
        discountViewEnterAnim.setDuration(150);
        discountViewExitAnim = new TranslateAnimation(
                Animation.RELATIVE_TO_SELF, 0.0f,
                Animation.RELATIVE_TO_SELF, 0.0f,
                Animation.RELATIVE_TO_SELF, 0.0f,
                Animation.RELATIVE_TO_SELF, 1f);
        discountViewExitAnim.setDuration(150);
        discountViewExitAnimListener = new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                if (getView() != null) {
                    LinearLayout discountLayout = getView().findViewById(R.id.cashier__discount_view);
                    if (discountLayout != null) {
                        discountLayout.setVisibility(View.GONE);
                    }
                }
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        };
        discountViewExitAnim.setAnimationListener(discountViewExitAnimListener);
    }

    private CashierMarketingGuideFloatView getCashierMarketingFloatView() {
        if (getView() == null) {
            return null;
        }
        if (mCashierMarketingGuideFloatView != null) {
            return mCashierMarketingGuideFloatView;
        }
        CashierMarketingGuideFloatView cashierMarketingGuideFloatView = getView().findViewById(R.id.cashier_discount_guide);
        Cashier cashier = getCashier();
        cashierMarketingGuideFloatView.init(cashier, new CashierMarketingGuideFloatView.OnTapMarketingPaymentListener() {
            @Override
            public void onTap(MTPayment marketingPayment) {
                if (CreditOpenUtils.isNeedEnterNewOpenCredit(marketingPayment)) {
                    //若需要进入新的月付开通流程
                    mCreditPaymentData = marketingPayment;
                    HalfPageFragment.HalfPageFragmentConfig halfPageFragmentConfig =
                            new HalfPageFragment.HalfPageFragmentConfig("credit_half_page", getCreditPayOpenUrl(CreditOpenUtils.STANDARD_MARKETBUTTON_SUFFIX), marketingPayment.getCreditPayOpenInfo().getData(), CREDIT_OPEN_MARKETBUTTON);
                    halfPageFragmentConfig.setTunnelExtraData(HalfPageFragment.getTunnelExtraData((MTCashierActivity) getActivity()));
                    HalfPageFragment.openHalfPage(MTCashierRevisionFragment.this, halfPageFragmentConfig);
                    CashierStaticsUtils.reportSystemCheck("b_pay_credit_open_leave_cashier_sc",
                            new AnalyseUtils.MapBuilder().add("url", getCreditPayOpenUrl(CreditOpenUtils.STANDARD_MARKETBUTTON_SUFFIX)).add("scene", CREDIT_OPEN_MARKETBUTTON_SCENE).build(), getUniqueId());
                    return;
                }
                switchCashierPayment(marketingPayment, CreditOpenUtils.STANDARD_MARKETBUTTON_SUFFIX);
                //点击营销引导浮层后交互变更仅针对银行卡首绑场景，包括通用新卡和活动银行新卡绑定
                if (PayTypeUtils.isNewCardPay(marketingPayment.getPayType()) || PayTypeUtils.isSelectNewCardPay(marketingPayment.getPayType())) {
                    payOrder(marketingPayment);
                }
            }

            @Override
            public void onVisible(boolean visible) {
                if (getView() == null || mCashierMarketingGuideFloatView == null) {
                    return;
                }
                CashierMarketingGuideFloatView floatView = mCashierMarketingGuideFloatView;
                if (visible) {
                    LinearLayout discountLayout = getView().findViewById(R.id.cashier__discount_view);
                    if (discountLayout.getVisibility() == View.VISIBLE) {
                        discountLayout.setVisibility(View.GONE);
                        discountLayout.clearAnimation();
                    } else if (floatView.getVisibility() == View.VISIBLE) {
                        if (floatView.getAnimation() != null) {
                            floatView.getAnimation().setAnimationListener(null);
                            floatView.clearAnimation();
                        }
                    } else if (floatView.getVisibility() == View.GONE) {
                        if (marketingFloatViewEnterAnim != null) {
                            floatView.startAnimation(marketingFloatViewEnterAnim);
                        }
                    }
                    floatView.setVisibility(View.VISIBLE);
                } else {
                    if (floatView.getVisibility() == View.VISIBLE) {
                        if (marketingFloatViewExitAnim != null) {
                            marketingFloatViewExitAnim.setAnimationListener(marketingFloatViewExitAnimListener);
                            floatView.startAnimation(marketingFloatViewExitAnim);
                        } else {
                            floatView.setVisibility(View.GONE);
                        }
                    }
                }
            }
        });
        this.mCashierMarketingGuideFloatView = cashierMarketingGuideFloatView;
        return cashierMarketingGuideFloatView;
    }

    private void refreshMarketingFloatButton(IPaymentData checkedPaymentData) {
        if (getView() == null) {
            return;
        }
        CashierMarketingGuideFloatView cashierMarketingGuideFloatView = getCashierMarketingFloatView();
        if (cashierMarketingGuideFloatView != null) {
            cashierMarketingGuideFloatView.refresh(checkedPaymentData);
        }
    }

    private void refreshInstallmentRequest(HashMap<String, String> extendTransmissionParams) {
        WalletPayManager.getInstance().refreshInstallmentRequest(getActivity(), this, tradeNo,
                payToken, checkedPaymentData, false, getExtraData(), getExtDimStat(), extendTransmissionParams);
        isRefreshInstallmentFinish = false;
    }

    public boolean isShowMoreDiscounts() {
        return showMoreDiscounts;
    }

    public void setShowMoreDiscounts(boolean showMoreDiscounts) {
        this.showMoreDiscounts = showMoreDiscounts;
    }

    // 获取 NativeStandardCashierFragmentProxy 的实现类，其实是NativeStandardCashierHandler.
    // 如果旧版路由的adapter已经获取到了，就不再获取了
    public void setNSCHandlerProxy(NSCHandlerProxy proxy) {
        this.handlerProxy = proxy;
    }
}
