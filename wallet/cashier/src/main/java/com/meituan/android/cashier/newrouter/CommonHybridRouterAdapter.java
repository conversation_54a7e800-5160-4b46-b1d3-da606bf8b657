package com.meituan.android.cashier.newrouter;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.content.LocalBroadcastManager;
import android.text.TextUtils;

import com.meituan.android.cashier.bean.CashierParams;
import com.meituan.android.cashier.bean.CashierRouterPreGuideHornConfig;
import com.meituan.android.cashier.common.CashierRouterHornService;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.common.CashierTypeConstant;
import com.meituan.android.cashier.common.ProductTypeConstant;
import com.meituan.android.cashier.newrouter.remake.CashierRouterAdapter;
import com.meituan.android.cashier.newrouter.remake.CashierRouterConstants;
import com.meituan.android.cashier.newrouter.remake.CashierRouterHelper;
import com.meituan.android.cashier.utils.DelayPayUtils;
import com.meituan.android.neohybrid.init.HybridSDK;
import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.utils.CatchException;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.paybase.utils.JsonString;
import com.meituan.android.paybase.utils.MTPayNeedToPersist;
import com.meituan.android.paybase.utils.RecycledHandler;
import com.meituan.android.paycommon.lib.coupon.model.Promotion;
import com.meituan.android.paycommon.lib.fragment.HalfPageFragment;
import com.meituan.android.payrouter.remake.modules.load.data.LoadData;
import com.meituan.android.payrouter.remake.result.RouterResult;
import com.meituan.android.payrouter.remake.router.adapter.AbstractRouterAdapter;
import com.meituan.android.payrouter.remake.router.data.CheckResult;
import com.meituan.android.payrouter.remake.router.data.InvokeInfo;
import com.meituan.android.payrouter.remake.router.manager.RouterManager;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;

/**
 * 通用的hybrid 收银台
 */
@ServiceLoaderInterface(key = CashierRouterConstants.ADAPTER_TYPE_COMMON_HYBRID_CASHIER, interfaceClass = AbstractRouterAdapter.class)
public class CommonHybridRouterAdapter extends CashierRouterAdapter {
    private static final int REQUEST_CODE_COMMON_HYBRID_PAGE = 101;
    private static final String ACTION_DOWNGRADE = "downgrade";
    private static final String ACTION_FINISH = "finish";
    private static final String STATUS_SUCCESS = "success";
    private static final String STATUS_FAIL = "fail";
    private static final String STATUS_CANCEL = "cancel";

    // 业务用配置信息
    @MTPayNeedToPersist
    private CashierRouterPreGuideHornConfig mCashierRouterPreGuideHornConfig;
    // 支付结果额外信息
    private String payResultExtra;
    // 获取抵达率埋点
    private BroadcastReceiver mSLABroadcastReceiver;

    @Override
    public void onDestroy() {
        super.onDestroy();
        RecycledHandler.of(this).recycle();
        unRegisterSLABroadCastReceiver();
    }

    @Override
    public CheckResult check() {
        // 业务层配置检查
        if (!CashierParams.checkValid(cashierParams())) {
            return CheckResult.fail("001", "cashierParams check failed");
        }
        String cashierType = cashierParams().getProductType();
        return existHornConfig(CashierRouterHornService.getInstance().getCashierRouterPreGuideHornConfigList(), cashierType)
                ? CheckResult.success() : CheckResult.fail("002", "horn not exist");
    }

    /**
     * 1.判断 cashier_type 是否匹配
     * 2.判断 merchantNo 是否匹配，如果 CashierRouterPreGuideHornConfig 没有 supportedMerchantNos，则不进行此项条件的匹配
     *
     * @param cashierRouterPreGuideHornConfigs 配置列表
     * @param cashierType                      收银台类型
     * @return 是否存在对应的Horn配置
     */
    private boolean existHornConfig(List<CashierRouterPreGuideHornConfig> cashierRouterPreGuideHornConfigs, String cashierType) {
        if (CollectionUtils.isEmpty(cashierRouterPreGuideHornConfigs)) {
            return false;
        }
        if (TextUtils.isEmpty(cashierType)) {
            return false;
        }
        for (CashierRouterPreGuideHornConfig cashierRouterPreGuideHornConfig : cashierRouterPreGuideHornConfigs) {
            if (cashierRouterPreGuideHornConfig == null) {
                continue;
            }
            if (TextUtils.equals(cashierRouterPreGuideHornConfig.getCashierType(), cashierType)) {
                this.mCashierRouterPreGuideHornConfig = cashierRouterPreGuideHornConfig;
                break;
            }
        }
        return this.mCashierRouterPreGuideHornConfig != null && !TextUtils.isEmpty(mCashierRouterPreGuideHornConfig.getUrl());
    }

    @Override
    public void invoke(InvokeInfo info) {
        super.invoke(info);
        registerSLABroadCastReceiver(mCashierRouterPreGuideHornConfig.getCashierType());
        LoadData loadData = new LoadData(trace());
        loadData.activity().setIntent(genHalfPageConfig());
        loadData.activity().setRequestCode(REQUEST_CODE_COMMON_HYBRID_PAGE);
        load(loadData);
    }

    @Override
    public void finish(RouterResult result) {
        super.finish(result);
        Intent intent = result.getData();
        if (intent != null) {
            intent.putExtra("isDarkColorBackground", true); // 半页容器场景下使用的是深色的背景色
            intent.putExtra("pay_result_extra", payResultExtra); // 半页容器场景下需要处理的支付结果信息
        }
    }

    private Intent genHalfPageConfig() {
        String url = mCashierRouterPreGuideHornConfig.getUrl();
        if (!url.startsWith("https://") && !url.startsWith("http://")) {
            url = HybridSDK.getHost() + mCashierRouterPreGuideHornConfig.getUrl();
        }
        url = url.trim();
        HalfPageFragment.HalfPageFragmentConfig halfPageFragmentConfig = new HalfPageFragment.HalfPageFragmentConfig(
                mCashierRouterPreGuideHornConfig.getCashierType(), url, "", REQUEST_CODE_COMMON_HYBRID_PAGE);
        halfPageFragmentConfig.setTunnelExtraData(JsonString.builder()
                .add("tradeno", cashierParams().getTradeNo())
                .add("pay_token", cashierParams().getPayToken())
                .add("extra_statics", cashierParams().getExtraStatics())
                .add("extra_data", cashierParams().getExtraData())
                .add("merchant_no", cashierParams().getMerchantNo())
                .add("nb_container", "hybrid")
                .add("nextReqParams", CatchException.run(() -> cashierParams().getCashierRouterInfo().getProductInfo().getNextReqParams()).value())
                .add("degradeInfo", cashierParams().getDowngradeInfo())
                .add("promotion_degrade_switch", "open") // 开关固化
                .add("use_new_cashier_callback", "1") // 开关固化
                .addMap(cashierParams().getExtendTransmissionParams())
                .build());
        halfPageFragmentConfig.setLoadingTimeOut(String.valueOf(mCashierRouterPreGuideHornConfig.getLoadingTimeOut()));
        halfPageFragmentConfig.setBackgroundColor(mCashierRouterPreGuideHornConfig.getBackgroundColor());
        prefetch(mCashierRouterPreGuideHornConfig, halfPageFragmentConfig);
        DelayPayUtils.prefetch(mCashierRouterPreGuideHornConfig, halfPageFragmentConfig, cashierParams());
        return HalfPageFragment.getOpenHalfPageIntent(halfPageFragmentConfig);
    }

    protected void prefetch(@NonNull CashierRouterPreGuideHornConfig commonCashierBusinessHornConfig, @NonNull HalfPageFragment.HalfPageFragmentConfig halfPageFragmentConfig) {
        if (commonCashierBusinessHornConfig.isNsf()) {
            halfPageFragmentConfig.setRequestUrl(commonCashierBusinessHornConfig.getNsfUrl());
            halfPageFragmentConfig.setRequestData(JsonString.builder()
                    .add("tradeno", cashierParams().getTradeNo())
                    .add("pay_token", cashierParams().getPayToken())
                    .add("cashier_type", cashierParams().getProductType())
                    .add("outer_business_data", cashierParams().getExtraData())
                    .add("ext_dim_stat", genExtDimStat())
                    .add("ext_param", CatchException.run(() -> JsonString.builder().add("nextReqParams",
                            cashierParams().getCashierRouterInfo().getProductInfo().getNextReqParams()).build()).value())
                    .addMap(cashierParams().getExtendTransmissionParams())
                    .build());
        }
    }

    public boolean onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == REQUEST_CODE_COMMON_HYBRID_PAGE) {
            HalfPageFragment.onActivityResult(resultCode, data, new HalfPageFragment.HalfPageListener() {
                @Override
                public void onLoadFail(int errorCode, String errorMessage) {
                    if (mCashierRouterPreGuideHornConfig == null) {
                        // 原本逻辑：支付失败。修改后：降级。
                        CashierRouterHelper.from(CommonHybridRouterAdapter.this).downgrade();
                        return;
                    }
                    String renderErrorAction = mCashierRouterPreGuideHornConfig.getRenderErrorAction();
                    String renderErrorToast = mCashierRouterPreGuideHornConfig.getRenderErrorToast();
                    if (TextUtils.isEmpty(renderErrorToast)) {
                        handleTechDowngrade(renderErrorAction);
                    } else {
                        ToastUtils.showSnackToast(getActivity(), renderErrorToast, false);
                        // 等 toast 消失后再进入收银台。否则网络请求的 loading 动画会覆盖 toast 信息
                        // 1500ms 是参考的 SnackbarManager.SHORT_DURATION_MS，虽然是硬编码，但是这个时间不会变动
                        RecycledHandler.of(this).postDelayed(() -> handleTechDowngrade(renderErrorAction), 1500);
                    }
                }

                @Override
                public void onSuccess(@Nullable String result) {
                    if (TextUtils.isEmpty(result)) {
                        // 按照支付成功处理
                        CashierRouterHelper.from(CommonHybridRouterAdapter.this).success().finish();
                        CashierStaticsUtils.logCustom("paybiz_pay_later_result_is_illegal", null, null, cashierUniqueId());
                        return;
                    }
                    try {
                        //noinspection DataFlowIssue
                        JSONObject jsonObject = new JSONObject(result);
                        String action = jsonObject.optString("action");
                        if (TextUtils.equals(ACTION_DOWNGRADE, action)) {
                            handleBusinessDowngrade(jsonObject);
                        } else if (TextUtils.equals(ACTION_FINISH, action)) {
                            handlePayFinish(jsonObject);
                        } else {
                            HashMap<String, Object> hashMap = new HashMap<>();
                            hashMap.put("action", action);
                            CashierStaticsUtils.logCustom("paybiz_pay_later_result_action_is_not_defined", hashMap, null, cashierUniqueId());
                        }
                    } catch (Exception e) {
                        // 按照支付成功处理
                        CashierRouterHelper.from(CommonHybridRouterAdapter.this).success().finish();
                        CashierStaticsUtils.logCustom("paybiz_pay_later_result_is_illegal", null, null, cashierUniqueId());
                    }
                }
            });
            return true;
        }
        return false;
    }

    private void handlePayFinish(@NonNull JSONObject jsonObject) {
        String status = jsonObject.optString("status");
        this.payResultExtra = jsonObject.optString("pay_result_extra");
        // 处理Promotion相关逻辑
        Promotion promotion = CatchException.run(() -> {
            JSONObject promotionJSONObject = jsonObject.optJSONObject("promotion");
            return promotionJSONObject != null ? GsonProvider.getInstance()
                    .fromJson(promotionJSONObject.toString(), Promotion.class) : null;
        }).value();
        if (TextUtils.equals(STATUS_SUCCESS, status)) {
            CashierRouterHelper.from(this).success().promotion(promotion).finish();
        } else if (TextUtils.equals(STATUS_FAIL, status)) {
            CashierRouterHelper.from(this).fail().message("").finish();
        } else if (TextUtils.equals(STATUS_CANCEL, status)) {
            CashierRouterHelper.from(this).cancel().finish();
        } else {
            CashierRouterHelper.from(this).cancel().finish();
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("status", status);
            CashierStaticsUtils.logCustom("paybiz_pay_later_result_status_is_not_defined", hashMap, null, cashierUniqueId());
        }
    }

    private void handleBusinessDowngrade(@NonNull JSONObject jsonObject) {
        String destCashierType = jsonObject.optString("dest_cashier_type");
        String sourceCashierType = jsonObject.optString("source_cashier_type");
        String downgradeInfo = jsonObject.optString("downgrade_info");
        this.payResultExtra = jsonObject.optString("pay_result_extra");
        if (TextUtils.isEmpty(destCashierType)) {
            final String bid = "b_pay_5l3pq2aw_sc";
            HashMap<String, Object> valLabs = new HashMap<>();
            valLabs.put("scene", "PreGuideCashier_handleDowngrade");
            CashierStaticsUtils.reportSystemCheck(bid, valLabs, cashierUniqueId());
            CashierStaticsUtils.logCustom("paybiz_pay_later_result_dest_cashier_empty", null, null, cashierUniqueId());
        }
        String destProductType = ProductTypeConstant.STANDARD_CASHIER;
        if (TextUtils.equals(destCashierType, CashierTypeConstant.CASHIERTYPE_MT_HYBRID_HALFPAGE_CASHIER)) {
            destProductType = ProductTypeConstant.PREPOSED_MTCASHIER;
        }
        // 降级处理，设置目标收银台类型、源收银台类型和降级信息。
        CashierRouterHelper.from(this).destProductType(destProductType).info(downgradeInfo).downgrade();
    }

    private void handleTechDowngrade(String destAdapterType) {
        String destProductType = ProductTypeConstant.STANDARD_CASHIER;
        if (TextUtils.equals(destAdapterType, CashierTypeConstant.CASHIERTYPE_MT_HYBRID_HALFPAGE_CASHIER)) {
            destProductType = ProductTypeConstant.PREPOSED_MTCASHIER;
        }
        String downgradeInfo = CatchException.run(() -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("jump_from_product", cashierParams().getProductType() + "_fail");
            jsonObject.put("passive_downgrade", "1"); // 标明当前场景为被动降级的额外字段，1代表被动降级，0代表主动降级
            return jsonObject.toString();
        }).value();
        CashierRouterHelper.from(this).destProductType(destProductType).info(downgradeInfo).message("timeout").downgrade();
    }

    private void registerSLABroadCastReceiver(String targetScene) {
        if (mSLABroadcastReceiver == null) {
            mSLABroadcastReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    unRegisterSLABroadCastReceiver();
                    RouterManager.notifier(trace()).notifyLoadSuccess(targetScene);
                }
            };
        }
        IntentFilter intentFilter = new IntentFilter("com.meituan.android.paycommon.lib.fragment.HalfPageFragment_" + targetScene);
        LocalBroadcastManager.getInstance(getActivity()).registerReceiver(mSLABroadcastReceiver, intentFilter);
    }

    private void unRegisterSLABroadCastReceiver() {
        if (mSLABroadcastReceiver != null) {
            LocalBroadcastManager.getInstance(getActivity()).unregisterReceiver(mSLABroadcastReceiver);
        }
    }
}
