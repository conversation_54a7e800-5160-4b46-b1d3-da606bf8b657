package com.meituan.android.cashier.model.bean;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;

/**
 * https://km.sankuai.com/page/328950112
 */
@JsonBean
public class PayLaterSubmitBean implements Serializable {
    private static final long serialVersionUID = -5056293054808302880L;

    @SerializedName("deductType")
    private int planType;
    @SerializedName("planId")
    private long planId;
    @SerializedName("guideScene")
    private int guidScene; // 0:不做其他引导 1:引导开通买单 2:引导绑定新卡支付
    @SerializedName("signMerchantNo")
    private String signMerchantNo;
    @SerializedName("promotionInfo")
    private String promotionInfo; // jsonString 格式，目前只有 utmSource 一个字段，如"{\"utmSource\": \"xxxxx\"}"
    @SerializedName("ext")
    private String ext;

    public int getPlanType() {
        return planType;
    }

    public void setPlanType(int planType) {
        this.planType = planType;
    }

    public long getPlanId() {
        return planId;
    }

    public void setPlanId(long planId) {
        this.planId = planId;
    }

    public int getGuidScene() {
        return guidScene;
    }

    public void setGuidScene(int guidScene) {
        this.guidScene = guidScene;
    }

    public String getSignMerchantNo() {
        return signMerchantNo;
    }

    public void setSignMerchantNo(String signMerchantNo) {
        this.signMerchantNo = signMerchantNo;
    }

    /**
     * 是否开通买单
     * @return true 表示需开通买单
     */
    public boolean openCreditPay() {
        return guidScene == 1;
    }

    /**
     * 是否绑定新卡支付
     * @return true 表示需绑定新卡支付
     */
    public boolean bindNewCard() {
        return guidScene == 2;
    }

    public String getPromotionInfo() {
        return promotionInfo;
    }

    public void setPromotionInfo(String promotionInfo) {
        this.promotionInfo = promotionInfo;
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }
}
