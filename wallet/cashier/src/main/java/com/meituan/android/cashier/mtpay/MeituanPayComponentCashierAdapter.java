package com.meituan.android.cashier.mtpay;

import static com.meituan.android.paybase.moduleinterface.picasso.coupondialog.CouponDialogConstants.REQ_RESULT_PAGE;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.support.v4.app.FragmentActivity;
import android.text.TextUtils;

import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.bean.CashierParams;
import com.meituan.android.cashier.common.CashierListener;
import com.meituan.android.cashier.common.CashierTypeConstant;
import com.meituan.android.cashier.common.CashierUtil;
import com.meituan.android.cashier.common.ICashier;
import com.meituan.android.cashier.common.ICashierAdapter;
import com.meituan.android.paybase.moduleinterface.payment.PayActionListener;
import com.meituan.android.paybase.moduleinterface.payment.PayFailInfo;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.MTPayBaseClass;
import com.meituan.android.paybase.utils.UriUtils;
import com.meituan.android.paymentchannel.PayerMediator;
import com.meituan.android.paymentchannel.PayersID;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import org.json.JSONObject;

import java.util.Map;

/**
 * 美团支付组件，跑腿充值业务使用
 */
@ServiceLoaderInterface(key = CashierTypeConstant.CASHIERTYPE_MT_COMPONENT_CASHIER, interfaceClass = ICashier.class)
@MTPayBaseClass
public class MeituanPayComponentCashierAdapter extends ICashierAdapter implements PayActionListener {
    private MTCashierActivity mtCashierActivity;
    private CashierParams mCashierParams;

    /**
     * 是否是美团支付组件
     * @param uri
     * @return
     */
    private static boolean isMeituanPayComponent(Uri uri) {
        return CashierUtil.isMeituanPayConponent(uri);
    }

    @Override
    public <T extends FragmentActivity & CashierListener & IRequestCallback> ConsumeResult consume(T t, CashierParams cashierParams) {
        this.mCashierParams = cashierParams;
        this.mtCashierActivity = (MTCashierActivity) t;
        return new ConsumeResult(isMeituanPayComponent(cashierParams.getUri()));
    }

    @Override
    public void invoke(String cashierFrom, Map<String, Object> cashierParams) {
        Uri.Builder uriBuilder = mCashierParams.getUri().buildUpon();
        Uri uri = uriBuilder.scheme("meituanpayment").authority("meituanpay").path("launch").build();
        PayerMediator.getInstance().startPay(mtCashierActivity, PayersID.ID_MEITUANPAY_COMPONENT, uri.toString(), "", this);
        openStatus(true, null);
    }

    @Override
    public String getCashierType() {
        return CashierTypeConstant.CASHIERTYPE_MT_COMPONENT_CASHIER;
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {

    }

    @Override
    public void onRestoreInstanceState(Bundle savedInstanceState) {

    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == REQ_RESULT_PAGE) { // 直接退出
            if (mtCashierActivity != null) {
                mtCashierActivity.onCashierPaySuccess(null);
            }
        } else {
            PayerMediator.getInstance().consumeActivityResult(mtCashierActivity, requestCode, resultCode, data);
        }
    }

    @Override
    public void onPayPreExecute(String payType) {

    }

    @Override
    public void onGotPayResult(String payType, int payResult, PayFailInfo payFailInfo) {
        if (mtCashierActivity == null) {
            return;
        }
        if (TextUtils.equals(payType, PayersID.ID_MEITUANPAY)) {
            if (payResult == PayActionListener.SUCCESS) {
                if (payFailInfo == null) {
                    mtCashierActivity.onCashierPaySuccess(null);
                } else {
                    String extra = payFailInfo.getExtra();
                    if (TextUtils.isEmpty(extra)) {
                        mtCashierActivity.onCashierPaySuccess(null);
                        return;
                    }
                    try {
                        JSONObject jsonObject = new JSONObject(extra);
                        // pay_result_url 和 PayActivity.KEY_PAY_PROMOTION 保持一致
                        String callBackUrl = jsonObject.optString("pay_result_url");
                        if (!TextUtils.isEmpty(callBackUrl)) {
                            UriUtils.openForResult(mtCashierActivity, callBackUrl, REQ_RESULT_PAGE);
                        }
                    } catch (Exception e) {
                        LoganUtils.logError("MeituanPayComponentCashierAdapter_onGotPayResult", e.getMessage());
                    }
                }
            } else if (payResult == PayActionListener.CANCEL) {
                mtCashierActivity.onCashierCancel();
            } else {
                mtCashierActivity.onCashierPayFail("");
            }
        }
    }
}
