package com.meituan.android.cashier.payresult;

import android.os.CountDownTimer;
import android.text.TextUtils;

import com.meituan.android.cashier.R;
import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.base.utils.UpsePayAndUnionflashPayAnalyeUtils;
import com.meituan.android.cashier.common.CashierCatConstants;
import com.meituan.android.cashier.common.CashierListener;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.model.PayErrorCode;
import com.meituan.android.cashier.model.bean.OverLoadInfo;
import com.meituan.android.cashier.model.bean.PayResult;
import com.meituan.android.pay.activity.PayActivity;
import com.meituan.android.paybase.common.activity.PayBaseActivity;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.common.analyse.cat.CatConstants;
import com.meituan.android.paybase.common.analyse.cat.CatUtils;
import com.meituan.android.paybase.common.dialog.PayDialog;
import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.moduleinterface.payment.PayActionListener;
import com.meituan.android.paybase.retrofit.PayException;
import com.meituan.android.paybase.utils.CashierScreenSnapShotUtil;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paycommon.lib.coupon.model.Promotion;
import com.meituan.android.paycommon.lib.utils.ExceptionUtils;
import com.meituan.android.paymentchannel.PayerMediator;
import com.meituan.android.paymentchannel.PayersID;

/**
 * 用于处理 directPay 的回调
 * 该类中除 mCountDownTimer 外，不要保存其他临时状态
 */
public class DirectPayResultHandler {
    private static final String TECH_TAG = MTCashierActivity.TECH_TAG;
    private PayActionListener mPayActionListener;
    private PayBaseActivity mActivity;
    private CashierListener mCashierListener;
    private CountDownTimer mCountDownTimer;
    private ThirdPayResultHandlerListener mThirdPayResultHandlerListener;
    private String mTradeNo;

    public DirectPayResultHandler(ThirdPayResultHandlerListener thirdPayResultHandlerListener, PayActionListener mPayActionListener, PayBaseActivity mActivity, CashierListener mCashierListener, String mTradeNo) {
        this.mThirdPayResultHandlerListener = thirdPayResultHandlerListener;
        this.mPayActionListener = mPayActionListener;
        this.mActivity = mActivity;
        this.mCashierListener = mCashierListener;
        this.mTradeNo = mTradeNo;
    }


    public void onRequestException(int i, Exception e) {
        int errorCode = 0;
        int level = 0;
        if (e instanceof PayException) {
            errorCode = ((PayException) e).getCode();
            level = ((PayException) e).getLevel();
            if (errorCode == PayErrorCode.NEED_VERIFY_SMS_CODE) {
                AnalyseUtils.techMis("b_pay_vqzyehjz_mv", null);
            }
        }
        CashierStaticsUtils.reportSystemCheck("b_21iwgx7m", new AnalyseUtils.MapBuilder()
                .add("code", "" + errorCode).add("message", e.getMessage())
                .add("level", "" + level).build(), getUniqueId());
        CatUtils.logRate(CashierCatConstants.ACTION_RESPONSE_DIRECTPAY, errorCode);
        onGotPayException(e);
    }

    private String getUniqueId() {
        if (mActivity != null) {
            return mActivity.getUniqueId();
        }
        return "";
    }

    public void onRequestSucc(int i, Object o) {
        if (o == null || isFinishing()) {
            if (o == null) {
                AnalyseUtils.techMis("b_pay_pfjic30w_mv", new AnalyseUtils.MapBuilder()
                        .add("scene", "o == null")
                        .add("tag", i + "")
                        .build());
            } else {
                AnalyseUtils.techMis("b_pay_pfjic30w_mv", new AnalyseUtils.MapBuilder()
                        .add("scene", "isDestroyed")
                        .add("tag", i + "")
                        .build());
            }
            return;
        }
        PayResult payResult = (PayResult) o;
        Promotion promotion = payResult.getPromotion();
        if (promotion != null) {
            AnalyseUtils.techMis("b_pay_dn9s8rnr_mv", null);
        }
        CashierStaticsUtils.techMis("b_5jx1qb72", null, getUniqueId());
        long millisInFuture = 0;
        if (promotion != null) {
            millisInFuture = promotion.getWindowTimeout() * 1000L;
        }
        if (mActivity instanceof MTCashierActivity) {
            ((MTCashierActivity) mActivity).setPromotion(promotion);
        } else if (mActivity instanceof PayActivity) {
            ((PayActivity) mActivity).setPromotion(promotion);
        }
        //directpay接口针对微信／支付宝支付进行预发券，携带有效期WindowTimeout。
        // 如果用户在有效期内完成支付，则给用户展示优惠券；否则，不予展示。
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
        }
        mCountDownTimer = new CountDownTimer(millisInFuture, 1000L) {
            @Override
            public void onTick(long millisUntilFinished) {

            }

            @Override
            public void onFinish() {
                if (mActivity instanceof MTCashierActivity) {
                    ((MTCashierActivity) mActivity).setCouponOutOfDate(true);

                } else if (mActivity instanceof PayActivity) {
                    ((PayActivity) mActivity).setCouponOutOfDate(true);
                }
                CatUtils.logRate(CashierCatConstants.ACTION_PROMOTION_ALERT_OVERTIME,
                        CatConstants.CODE_DEFAULT_OK);
            }
        }.start();

        CatUtils.logRate(CashierCatConstants.ACTION_RESPONSE_DIRECTPAY, CatConstants.CODE_DEFAULT_OK);
        dealWithPayResult(payResult);
    }

    private void dealWithPayResult(PayResult payResult) {
        if (payResult.getOverLoadInfo() != null && payResult.getOverLoadInfo().isStatus()) { //当前后台负载过重，无法完成支付，弹出提示窗口
            AnalyseUtils.techMis("b_pay_2bqf1335_mv", null);
            mThirdPayResultHandlerListener.processSuspendPaying(payResult.getOverLoadInfo());
        } else {
            startPayWithPayer(payResult);
        }
    }

    private void startPayWithPayer(PayResult payResult) {
        String payType = payResult.getPayType();
        PayerMediator.getInstance().startPay(mActivity, payType, payResult.getUrl(), mTradeNo, mPayActionListener);
        if (TextUtils.equals(payType, PayersID.ID_UPSEPAY)) {
            UpsePayAndUnionflashPayAnalyeUtils.recordUpsepayStatusWhenPay();
        }
    }

    private void dealPayException(PayException e) {
        int errorCode = e.getCode();
        String errorMsg = e.getMessage();
        LoganUtils.logError("DirectPayResultHandler_dealPayException", errorMsg + errorCode);
        switch (errorCode) {
            case PayErrorCode.NEED_VERIFY_SMS_CODE: //进入短信验证页面
                ToastUtils.showSnackToast(mActivity, R.string.cashier__error_msg_pay_later);
                break;
            case PayErrorCode.ALREADY_PAYED:
                new PayDialog.Builder(mActivity)
                        .msg(e.getMessage())
                        .subMsg(e.getErrorCodeStr())
                        .rightBtn(PayDialog.DEFAULT_BUTTON_TEXT, (dialog) -> {
                            CashierScreenSnapShotUtil.captureSnapShot(mActivity, success -> mCashierListener.onCashierPaySuccess(null));
                        })
                        .build().show();
                break;
            default:
                ExceptionUtils.handleException(mActivity, e, MTCashierActivity.class);
                break;
        }
    }


    private void onGotPayException(Exception e) {
        if (e instanceof PayException) {
            dealPayException((PayException) e);
        } else {
            ToastUtils.showSnackToast(mActivity, R.string.cashier__error_msg_pay_later);
            LoganUtils.logError("DirectPayResultHandler_onGotPayException", mActivity.getString(R.string.cashier__error_msg_pay_later));
        }
    }

    public void onDestroy() {
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
            mCountDownTimer = null;
        }
    }

    private boolean isFinishing() {
        return mActivity.isFinishing() || mActivity.isActivityDestroyed();
    }

    public interface ThirdPayResultHandlerListener {
        void processSuspendPaying(OverLoadInfo overLoadInfo);
    }
}
