package com.meituan.android.cashier.preorder;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.app.FragmentActivity;
import android.support.v4.content.LocalBroadcastManager;
import android.support.v4.view.ViewCompat;
import android.text.TextUtils;
import android.view.View;

import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.bean.CashierParams;
import com.meituan.android.cashier.bean.CashierRouterPreGuideHornConfig;
import com.meituan.android.cashier.common.CashierListener;
import com.meituan.android.cashier.common.CashierRouterHornService;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.common.CashierTypeConstant;
import com.meituan.android.cashier.common.ICashier;
import com.meituan.android.cashier.common.ICashierAdapter;
import com.meituan.android.cashier.common.ProductTypeConstant;
import com.meituan.android.neohybrid.init.HybridSDK;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.MTPayNeedToPersist;
import com.meituan.android.paycommon.lib.coupon.model.Promotion;
import com.meituan.android.paycommon.lib.fragment.HalfPageFragment;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 老路由通用美支流程前置收银台，目前支持的类型：美支前置收银台，月付拉新收银台，极速支付错误引导月付拉新收银台等
 */
@ServiceLoaderInterface(key = CashierTypeConstant.CASHIERTYPE_PREORDER_CASHIER, interfaceClass = ICashier.class)
public class CommonHalfPageCashierAdapter extends ICashierAdapter {
    private static final String ACTION_DOWNGRADE = "downgrade";
    private static final String ACTION_FINISH = "finish";
    private static final String STATUS_SUCCESS = "success";
    private static final String STATUS_FAIL = "fail";
    private static final String STATUS_CANCEL = "cancel";
    private static final int REQUEST_CODE = 1010;
    private static final String ERROR_HORN_ILLEGAL_CODE = "111";
    @MTPayNeedToPersist
    private CashierRouterPreGuideHornConfig mCommonCashierBusinessHornConfig;
    private MTCashierActivity mtCashierActivity;
    private CashierParams mCashierParams;
    private BroadcastReceiver mSLABroadcastReceiver;
    private Drawable mDecorViewBackground;

    @Override
    public <T extends FragmentActivity & CashierListener & IRequestCallback> ConsumeResult consume(T t, CashierParams cashierParams) {
        this.mCommonCashierBusinessHornConfig = getHornConfig(CashierRouterHornService.getInstance().getCashierRouterPreGuideHornConfigList(), cashierParams.getProductType());
        this.mtCashierActivity = (MTCashierActivity) t;
        this.mCashierParams = cashierParams;
        boolean isHornConfigLegal = isHornConfigLegal();
        return isHornConfigLegal ? new ConsumeResult(true) : new ConsumeResult(false, ERROR_HORN_ILLEGAL_CODE, "horn not legal");

    }


    /**
     * 获取当前收银台对应 horn 中的业务配置
     *
     * @param cashierHornConfigs horn 业务配置列表
     * @param cashierType        收银台类型
     */
    private CashierRouterPreGuideHornConfig getHornConfig(List<CashierRouterPreGuideHornConfig> cashierHornConfigs, String cashierType) {
        if (CollectionUtils.isEmpty(cashierHornConfigs) || TextUtils.isEmpty(cashierType)) {
            return null;
        }
        for (CashierRouterPreGuideHornConfig cashierHornConfig : cashierHornConfigs) {
            if (cashierHornConfig == null) {
                continue;
            }
            if (TextUtils.equals(cashierHornConfig.getCashierType(), cashierType)) {
                return cashierHornConfig;
            }
        }
        return null;
    }

    /**
     * 判断前置验证收银台的 horn 配置合法性
     */
    private boolean isHornConfigLegal() {
        return this.mCommonCashierBusinessHornConfig != null && !TextUtils.isEmpty(mCommonCashierBusinessHornConfig.getUrl());
    }

    @Override
    public void invoke(String cashierFrom, Map<String, Object> cashierParams) {
        CashierRouterPreGuideHornConfig commonCashierBusinessHornConfig = this.mCommonCashierBusinessHornConfig;
        String url = commonCashierBusinessHornConfig.getUrl().trim();
        if (!url.startsWith("https://") && !url.startsWith("http://")) {
            url = HybridSDK.getHost() + commonCashierBusinessHornConfig.getUrl();
        }
        HalfPageFragment.HalfPageFragmentConfig halfPageFragmentConfig = new HalfPageFragment.HalfPageFragmentConfig(
                commonCashierBusinessHornConfig.getCashierType(), url, "", REQUEST_CODE);
        halfPageFragmentConfig.setTunnelExtraData(getTunnelData());
        halfPageFragmentConfig.setLoadingTimeOut(String.valueOf(commonCashierBusinessHornConfig.getLoadingTimeOut()));
        halfPageFragmentConfig.setBackgroundColor(commonCashierBusinessHornConfig.getBackgroundColor());
        prefetch(commonCashierBusinessHornConfig, halfPageFragmentConfig);
        registerSLABroadCastReceiver(commonCashierBusinessHornConfig.getCashierType());
        HalfPageFragment.openHalfPage(mtCashierActivity, halfPageFragmentConfig);
    }

    /**
     * 不同收银台通过配置决定是否走预请求以及走预请求的 url，但需要注意不同收银台参数差异问题
     *
     * @param commonCashierBusinessHornConfig 不同收银台对应的业务配置
     * @param halfPageFragmentConfig          本页容器配置
     */
    protected void prefetch(@NonNull CashierRouterPreGuideHornConfig commonCashierBusinessHornConfig, @NonNull HalfPageFragment.HalfPageFragmentConfig halfPageFragmentConfig) {
        if (commonCashierBusinessHornConfig.isNsf()) {
            halfPageFragmentConfig.setRequestUrl(commonCashierBusinessHornConfig.getNsfUrl());
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("tradeno", this.mCashierParams.getTradeNo());
                jsonObject.put("pay_token", this.mCashierParams.getPayToken());
                jsonObject.put("cashier_type", this.mCashierParams.getProductType());

                // 该参数用来透传业务方传入参数
                jsonObject.put("outer_business_data", this.mCashierParams.getExtraData());
                // 该参数用来透传后端下发的参数，为了兼容不同收银台参数上的差异，与后端约定不同收银台的参数都放在nextReqParams中，前端只做透传即可。
                JSONObject nextParams = new JSONObject();
                nextParams.put("nextReqParams", this.mCashierParams.getCashierRouterInfo().getProductInfo().getNextReqParams());
                jsonObject.put("ext_param", nextParams.toString());

                HashMap<String, String> extendTransmissionParams = this.mCashierParams.getExtendTransmissionParams();
                if (!CollectionUtils.isEmpty(extendTransmissionParams)) {
                    for (Map.Entry<String, String> entry : extendTransmissionParams.entrySet()) {
                        jsonObject.put(entry.getKey(), entry.getValue());
                    }
                }
                String extraStatics = getExtraStatics();
                if (!TextUtils.isEmpty(extraStatics)) {
                    jsonObject.put("ext_dim_stat", extraStatics);
                }
            } catch (Exception e) {
                LoganUtils.logError("CommonHalfPageCashierAdapter_prefetch", e.getMessage());
            }
            halfPageFragmentConfig.setRequestData(jsonObject.toString());
        }
    }


    /**
     * 将业务方传入数据转换塞入参数隧道
     *
     * @return jsonString
     */
    private String getTunnelData() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("tradeno", mCashierParams.getTradeNo());
            jsonObject.put("extra_statics", mCashierParams.getExtraStatics());
            jsonObject.put("extra_data", mCashierParams.getExtraData());
            jsonObject.put("merchant_no", mCashierParams.getMerchantNo());
            jsonObject.put("pay_token", mCashierParams.getPayToken());
            jsonObject.put("nb_container", "hybrid");
            if (mCashierParams.getCashierRouterInfo() != null && mCashierParams.getCashierRouterInfo().getProductInfo() != null) {
                jsonObject.put("nextReqParams", mCashierParams.getCashierRouterInfo().getProductInfo().getNextReqParams());
            }
            jsonObject.put("degradeInfo", mCashierParams.getDowngradeInfo());
            HashMap<String, String> hashMap = mCashierParams.getExtendTransmissionParams();
            if (!CollectionUtils.isEmpty(hashMap)) {
                for (Map.Entry<String, String> entry : hashMap.entrySet()) {
                    jsonObject.put(entry.getKey(), entry.getValue());
                }
            }
        } catch (Exception e) {
            LoganUtils.logError("CommonHalfPageCashierAdapter_getTunnelData", e.getMessage());
        }
        return jsonObject.toString();
    }

    protected String getExtraStatics() {
        if (TextUtils.isEmpty(mCashierParams.getExtraStatics())) {
            return "";
        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("outer_business_statics", mCashierParams.getExtraStatics());
        } catch (Exception e) {
            LoganUtils.logError("CommonHalfPageCashierAdapter_getExtraStatics", e.getMessage());
        }
        return jsonObject.toString();
    }

    private void registerSLABroadCastReceiver(String targetScene) {
        if (mSLABroadcastReceiver == null) {
            mSLABroadcastReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    openStatus(true, null);
                    unRegisterSLABroadCastReceiver();
                }
            };
        }
        IntentFilter intentFilter = new IntentFilter("com.meituan.android.paycommon.lib.fragment.HalfPageFragment_" + targetScene);
        LocalBroadcastManager.getInstance(mtCashierActivity).registerReceiver(mSLABroadcastReceiver, intentFilter);
    }

    private void unRegisterSLABroadCastReceiver() {
        if (mSLABroadcastReceiver != null) {
            LocalBroadcastManager.getInstance(mtCashierActivity).unregisterReceiver(mSLABroadcastReceiver);
        }
    }

    @Override
    public String getCashierType() {
        return CashierTypeConstant.CASHIERTYPE_PREORDER_CASHIER;
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {

    }

    @Override
    public void onRestoreInstanceState(Bundle savedInstanceState) {

    }

    @Override
    public void onDestroy(boolean release) {
        super.onDestroy(release);
        if (mDecorViewBackground != null && !mtCashierActivity.isFinishing()) {
            View decorView = mtCashierActivity.getWindow().getDecorView();
            ViewCompat.setBackground(decorView, mDecorViewBackground);
        }
        unRegisterSLABroadCastReceiver();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == REQUEST_CODE) {
            HalfPageFragment.onActivityResult(resultCode, data, new HalfPageFragment.HalfPageListener() {
                @Override
                public void onLoadFail(int errorCode, String errorMessage) {
                    handleTechDowngrade(errorCode, errorMessage);
                }

                @Override
                public void onSuccess(@Nullable String result) {
                    if (TextUtils.isEmpty(result)) {
                        // 按照支付成功处理
                        mtCashierActivity.onCashierPaySuccess(null);
                        HashMap<String, Object> logMap = new HashMap();
                        logMap.put("action", "result_empty");
                        LoganUtils.log("前置验证收银台事件", logMap);
                        return;
                    }
                    try {
                        JSONObject jsonObject = new JSONObject(result);
                        String action = jsonObject.optString("action");
                        if (TextUtils.equals(ACTION_DOWNGRADE, action)) {
                            handleBusinessDowngrade(jsonObject);
                        } else if (TextUtils.equals(ACTION_FINISH, action)) {
                            handlePayFinish(jsonObject);
                        } else {
                            HashMap<String, Object> logMap = new HashMap();
                            logMap.put("action", "action_illegal");
                            logMap.put("info", result);
                            LoganUtils.log("前置验证收银台事件", logMap);
                        }
                    } catch (Exception e) {
                        // 按照支付成功处理
                        mtCashierActivity.onCashierPaySuccess(null);
                        HashMap<String, Object> logMap = new HashMap();
                        logMap.put("action", "result_illegal");
                        logMap.put("info", result);
                        LoganUtils.log("前置验证收银台事件", logMap);
                    }
                }
            });
        }
    }


    /**
     * 业务降级：
     * 1.主动关闭收银台
     * 2.营销不一致弹窗
     * 3.支付失败或者取消
     */
    private void handleBusinessDowngrade(JSONObject jsonObject) {
        if (jsonObject == null) {
            return;
        }
        String sourceCashierType = jsonObject.optString("source_cashier_type");
        String downgradeInfo = jsonObject.optString("downgrade_info");
        String payResultExtra = jsonObject.optString("pay_result_extra");
        mtCashierActivity.setPayResultExtra(payResultExtra);
        mtCashierActivity.onCashierBusinessDowngrade(sourceCashierType, ProductTypeConstant.STANDARD_CASHIER, downgradeInfo);
        HashMap<String, Object> logMap = new HashMap();
        logMap.put("action", "business_downgrade");
        logMap.put("info", jsonObject);
        LoganUtils.log("前置验证收银台事件", logMap);
    }

    /**
     * 技术降级：
     * 1.loading 超时
     * 2.渲染失败
     */
    private void handleTechDowngrade(int errorCode, String errorMessage) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("jump_from_product", mCashierParams.getProductType() + "_fail");
            // 标明当前场景为被动降级的额外字段，1代表被动降级，0代表主动降级
            jsonObject.put("passive_downgrade", "1");
        } catch (Exception e) {
            LoganUtils.logError("CommonHalfPageCashierAdapter_handleTechDowngrade", e.getMessage());
        }
        mtCashierActivity.onCashierBusinessDowngrade(mCashierParams.getProductType(), ProductTypeConstant.STANDARD_CASHIER, jsonObject.toString());
        HashMap<String, Object> logMap = new HashMap();
        logMap.put("action", "technical_downgrade");
        logMap.put("errCode", errorCode);
        logMap.put("errMsg", errorMessage);
        LoganUtils.log("前置验证收银台事件", logMap);
    }

    /**
     * 处理支付结果
     *
     * @param jsonObject 支付结果
     */
    private void handlePayFinish(JSONObject jsonObject) {
        if (jsonObject == null) {
            return;
        }
        String status = jsonObject.optString("status");
        String payResultExtra = jsonObject.optString("pay_result_extra");
        Promotion promotion = null;
        try {
            JSONObject promotionJSONObject = jsonObject.optJSONObject("promotion");
            if (promotionJSONObject != null) {
                promotion = GsonProvider.getInstance().fromJson(promotionJSONObject.toString(), Promotion.class);
            }
        } catch (Exception e) {
            LoganUtils.logError("CommonHalfPageCashierAdapter_handlePayFinish", e.getMessage());
        }
        mtCashierActivity.setPayResultExtra(payResultExtra);
        if (TextUtils.equals(STATUS_SUCCESS, status)) {
            mtCashierActivity.onCashierPaySuccess(promotion);
        } else if (TextUtils.equals(STATUS_FAIL, status)) {
            mtCashierActivity.onCashierPayFail("");
        } else if (TextUtils.equals(STATUS_CANCEL, status)) {
            mtCashierActivity.onCashierCancel();
        } else {
            HashMap<String, Object> logMap = new HashMap();
            logMap.put("action", "status_illegal");
            logMap.put("info", jsonObject);
            LoganUtils.log("前置验证收银台事件", logMap);
        }
    }
}
