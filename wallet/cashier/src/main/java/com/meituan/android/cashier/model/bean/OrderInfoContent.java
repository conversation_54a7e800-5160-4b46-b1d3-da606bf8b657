package com.meituan.android.cashier.model.bean;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;


/**
 * Created by <PERSON><PERSON><PERSON> on 16/9/12.
 */
@JsonBean
public class OrderInfoContent implements Serializable {
    private static final long serialVersionUID = 8956332120470945187L;
    //checked

    @SerializedName("item_name")
    private String itemName;

    @SerializedName("item_value")
    private String itemValue;

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemValue() {
        return itemValue;
    }

    public void setItemValue(String itemValue) {
        this.itemValue = itemValue;
    }
}
