package com.meituan.android.cashier.model.bean;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/6/15.
 */
@JsonBean
public class RouteInfo implements Serializable {
    private static final long serialVersionUID = 4973266716373621418L;
    // checked
    @SerializedName("cashier_type")
    private String cashierType;

    @SerializedName("cashier_info")
    private CashierInfo cashierInfo;

    @SerializedName("root_desc")
    private String rootDesc;

    @SerializedName("abtest_grp")
    private String abTestGroup;

    @SerializedName("retain_window")
    private RetainWindow retainWindow;

    @SerializedName("pop_window")
    private CashierPopWindowBean cashierPopWindowBean;

    /**
     * 降级url
     */
    @SerializedName("degrade_url")
    private String url;

    public String getCashierType() {
        return cashierType;
    }

    public void setCashierType(String cashierType) {
        this.cashierType = cashierType;
    }

    public CashierInfo getCashierInfo() {
        return cashierInfo;
    }

    public void setCashierInfo(CashierInfo cashierInfo) {
        this.cashierInfo = cashierInfo;
    }

    public String getRootDesc() {
        return rootDesc;
    }

    public void setRootDesc(String rootDesc) {
        this.rootDesc = rootDesc;
    }

    public String getAbTestGroup() {
        return abTestGroup;
    }

    public void setAbTestGroup(String abTestGroup) {
        this.abTestGroup = abTestGroup;
    }

    public RetainWindow getRetainWindow() {
        return retainWindow;
    }

    public void setRetainWindow(RetainWindow retainWindow) {
        this.retainWindow = retainWindow;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public CashierPopWindowBean getCashierPopWindowBean() {
        return cashierPopWindowBean;
    }

    public void setCashierPopWindowBean(CashierPopWindowBean cashierPopWindowBean) {
        this.cashierPopWindowBean = cashierPopWindowBean;
    }

    public Cashier getCashier() {
        if (cashierInfo == null) {
            return null;
        }
        if (TextUtils.equals("common", cashierType)) {
            return cashierInfo.getCommon();
        } else {
            return cashierInfo.getWallet();
        }
    }

    public String getGuideRequestNo() {
        CashierPopWindowBean cashierPopWindowBean = getCashierPopWindowBean();
        if (cashierPopWindowBean == null) {
            return "";
        }
        PayLaterPopDetailInfoBean payLaterPopDetailInfoBean = cashierPopWindowBean.getPayLaterPopDetailInfoBean();
        if (payLaterPopDetailInfoBean == null) {
            return "";
        }
        return payLaterPopDetailInfoBean.getGuideRequestNo();
    }

}
