package com.meituan.android.cashier.utils;

import android.view.View;
import android.view.ViewTreeObserver;

import com.meituan.android.cashier.R;


/**
 * <AUTHOR>
 * 监听Layout初始化完成状态，{@link ViewTreeObserver.OnGlobalLayoutListener#onGlobalLayout} 方法会在
 * ViewRootImpl.performTraversals中被执行。
 * 当执行完毕一次后，会取消对Layout的监听。
 * 务必要设置在初始化开始后，已经完成初始化的逻辑可能会达不到预期情况。
 */
public class ViewOnInitLayoutObserver implements ViewTreeObserver.OnGlobalLayoutListener {
    private final View parent;
    private final Handler handler;

    public ViewOnInitLayoutObserver(View parent, Handler handler) {
        this.parent = parent;
        this.handler = handler;
    }

    public static void subscribeOn(View parent, Handler handler) {
        if (parent != null && handler != null) {
            parent.getViewTreeObserver().addOnGlobalLayoutListener(new ViewOnInitLayoutObserver(parent, handler));
        }
    }

    @Override
    public void onGlobalLayout() {
        if (parent == null || handler == null) {
            return;
        }
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.JELLY_BEAN) {
            parent.getViewTreeObserver().removeOnGlobalLayoutListener(this);
        } else {
            parent.getViewTreeObserver().removeGlobalOnLayoutListener(this);
        }
        handler.onInitLayout(parent);
    }

    public interface Handler {
        void onInitLayout(View parent);
    }
}
