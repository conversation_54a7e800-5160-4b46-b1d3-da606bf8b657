package com.meituan.android.cashier.base.view.revision;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.Typeface;
import android.graphics.drawable.ColorDrawable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.StrikethroughSpan;
import android.util.AttributeSet;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.meituan.android.cashier.R;
import com.meituan.android.cashier.base.utils.OrderInfoBlockUtils;
import com.meituan.android.cashier.model.bean.Cashier;
import com.meituan.android.paybase.common.activity.PayBaseActivity;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.utils.CashAmountArithUtils;
import com.meituan.android.paybase.utils.FontUtils;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.MTPaySuppressFBWarnings;
import com.meituan.android.paybase.utils.Strings;
import com.meituan.android.paycommon.lib.utils.ViewUtils;
import com.meituan.android.paycommon.lib.widgets.AutoChangeNumberView;

/**
 * author: luo jing
 * date: 2018/8/30 19:35
 * description: order info view
 */
public class CashierOrderInfoView extends LinearLayout implements IOrderInfoView {
    private static final String TAG = "CashierOrderInfoView";
    private static final boolean DEBUG = false;
    //    private static final int ANIM_DURATION_LINE = 500;
    private static final int ANIM_DURATION_DISCOUNT_CASH = 450;
    private static final int ANIM_DURATION_ORIGIN_CASH = 300;
    private static final int ANIM_DELAY_AUTO_CASH = 0;
    private Cashier cashier;
    private AutoChangeNumberView businessInfoMoney;
    private TextView originAmountTv;
    private LinearLayout orderInfoLayout;
    private RelativeLayout originPriceLayout;
    private FrameLayout orderPriceAndInfoLayout;
    //辅助线
//    private View deleteLine;
    private float toDisplayAmount;
    private float lastAmount;
    private AnimatorSet unfoldSet, foldSet;

    public CashierOrderInfoView(Context context) {
        super(context);
    }

    public CashierOrderInfoView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public void init(Cashier cashier) {
        this.cashier = cashier;
        initView();
    }

    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.cashier__order_info_view, this);
        businessInfoMoney = findViewById(R.id.business_info_money);
        originAmountTv = findViewById(R.id.origin_price);
        originPriceLayout = findViewById(R.id.origin_price_layout);
        //订单点击区
        orderInfoLayout = findViewById(R.id.order_info_layout);
//        deleteLine = findViewById(R.id.delete_line);
        orderPriceAndInfoLayout = findViewById(R.id.price_and_order_info_layout);
        //金额字体
        Typeface boldTypeface = FontUtils.getBoldType(getContext());
        if (boldTypeface != null) {
            ((TextView) findViewById(R.id.business_money_symbol)).setTypeface(boldTypeface);
        }
        Typeface mediumTypeface = FontUtils.getMediumType(getContext());
        if (mediumTypeface != null) {
            businessInfoMoney.setTypeface(mediumTypeface);
        }
        //原价
        float originAmount = cashier != null ? cashier.getTotalFee() : 0;
        String originPrice = getContext().getString(R.string.mpay__money_prefix)
                + Strings.getFormattedDoubleValueWithZero(originAmount);
        setOriginAmount(originPrice);
        //订单信息
        TextView orderNameTv = findViewById(R.id.order_name);
        View orderNameDetailTv = findViewById(R.id.order_name_detail);
        if (cashier != null) {
            String orderName = cashier.getOrderName();
            if (!TextUtils.isEmpty(orderName)) {
                orderNameTv.setVisibility(View.VISIBLE);
                orderNameTv.setText(orderName);
            } else {
                orderNameTv.setVisibility(View.GONE);
            }
            if (cashier.getOrderInfo() != null) {
                orderNameDetailTv.setVisibility(View.VISIBLE);
                AnalyseUtils.logModelEvent("b_m32qv34l", "收银台首页展示", new AnalyseUtils.
                        MapBuilder().add("IS_TRUE", true).build(), AnalyseUtils.EventType.VIEW, -1);
                orderInfoLayout.setOnClickListener(v -> {
                    if (getContext() instanceof PayBaseActivity) {
                        showOrderInfoWindow((PayBaseActivity) getContext());
                    }
                });
            } else {
                orderNameDetailTv.setVisibility(View.GONE);
            }
        }
    }

    private void showOrderInfoWindow(Activity context) {
        if (context == null || context.isFinishing()
                || ((PayBaseActivity) context).isActivityDestroyed()) {
            return;
        }
        @SuppressLint("InflateParams")
        View v = LayoutInflater.from(context).inflate(R.layout.cashier__order_info, null);
        final PopupWindow window = new PopupWindow(v, ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT, true);
        window.setBackgroundDrawable(new ColorDrawable(0xb000000));
        //TODO 此处有内存泄漏的隐患
        window.showAtLocation(context.getWindow().getDecorView(), Gravity.CENTER, 0, 0);

        OrderInfoBlockUtils.showOrderInfoBlock(context, v, cashier.getOrderInfo());

        v.findViewById(R.id.dialog_close_icon).setOnClickListener(v1 -> window.dismiss());

        TextView titleTextView = v.findViewById(R.id.title_text_view);
        View.OnTouchListener touchListener = (view, motionEvent) -> true;
        titleTextView.setOnTouchListener(touchListener);

        AnalyseUtils.logModelEvent("b_9vkw8bm3", "收银台首页点击订单详情", null,
                AnalyseUtils.EventType.CLICK, -1);
    }

    //几个难点：ABA问题、重复点击、动画延时对展示的影响、系列动画互相之间的影响
    @Override
    public void refreshView(float refreshAmount) {
        this.toDisplayAmount = refreshAmount;
        final float originAmount = cashier != null ? cashier.getTotalFee() : 0;
        //首次进入不展示动画
        if (TextUtils.isEmpty(businessInfoMoney.getText())) {
            setBusinessInfoMoney(refreshAmount);
            if (CashAmountArithUtils.compare(originAmount, refreshAmount) > 0) {
                initOrderInfoLayoutPadding();
                //与动画的逻辑保持一致，此处用alpha控制原价标签的显示与隐藏。实际上，FrameLayout布局下的
                //originPriceLayout一直是View.VISIBLE的，只不过是透明度让其"不可见"
                originPriceLayout.setAlpha(1);
            } else {
                originPriceLayout.setAlpha(0);
            }
        } else {
            float yShift = getResources().getDimension(R.dimen.cashier__origin_amount_y_shift);
            float curAmount = getCurAmount(refreshAmount);
            if (CashAmountArithUtils.compare(lastAmount, refreshAmount) != 0) {
                businessInfoMoney.startPlay(curAmount, refreshAmount,
                        AutoChangeNumberView.FORMAT_TWO_DECIMAL, ANIM_DURATION_DISCOUNT_CASH,
                        ANIM_DELAY_AUTO_CASH, a -> setBusinessInfoMoney(toDisplayAmount));
                //刷新金额小于原价，展开动画
                if (CashAmountArithUtils.compare(refreshAmount, originAmount) < 0) {
                    unfoldAnim(yShift, null);
                } else {
                    foldAnim(yShift, null);
                }
            } else { //same不处理
                if (DEBUG) {
                    Log.d(TAG, "refreshView: same");
                }
            }
        }
        lastAmount = refreshAmount;
    }

    //safeAmount兜底值，出现异常，不影响支付
    private float getCurAmount(float safeAmount) {
        float curAmount = safeAmount;
        try {
            if (!TextUtils.isEmpty(businessInfoMoney.getText())) {
                curAmount = Float.parseFloat(String.valueOf(businessInfoMoney.getText()));
            }
        } catch (Exception e) {
            LoganUtils.logError("CashierOrderInfoView_getCurAmount", e.getMessage());
        }
        return curAmount;
    }

    //原价、减价
    private void setBusinessInfoMoney(float refreshAmount) {
        if (businessInfoMoney != null) {
            businessInfoMoney.setText(Strings.getFormattedDoubleValueWithZero(Double.valueOf(String.valueOf(refreshAmount))));
        }
    }

    private void setOriginAmount(String amount) {
        SpannableString spannableString = new SpannableString(amount);
        spannableString.setSpan(new StrikethroughSpan(), 0, amount.length(),
                Spanned.SPAN_EXCLUSIVE_INCLUSIVE);
        originAmountTv.setText(spannableString);
    }

    private void initOrderInfoLayoutPadding() {
        //调整订单信息的位置，与正常动画结束后保持一致,避免跳动
        orderInfoLayout.setPadding(0,
                getResources().getDimensionPixelSize(R.dimen.cashier__origin_amount_y_shift), 0,
                getResources().getDimensionPixelSize(R.dimen.cashier__order_info_padding_bottom));
    }

    private void clearOrderInfoLayoutPadding() {
        //动画开始前，重置上下padding，避免轨迹偏下
        orderInfoLayout.setPadding(0, 0, 0,
                getResources().getDimensionPixelSize(R.dimen.cashier__order_info_padding_bottom));
    }

    @MTPaySuppressFBWarnings("SIC_INNER_SHOULD_BE_STATIC_ANON")
    private void foldAnim(float yOrderInfoShift, OnAnimationEnd listener) {
        clearOrderInfoLayoutPadding();

        ObjectAnimator aOriginPrice = ObjectAnimator.ofFloat(originPriceLayout, "alpha", 1, 0);
        aOriginPrice.setDuration(ANIM_DURATION_ORIGIN_CASH);

        ObjectAnimator yOrderInfo = ObjectAnimator.ofFloat(orderInfoLayout, "translationY",
                yOrderInfoShift, 0);
        yOrderInfo.setDuration(ANIM_DURATION_ORIGIN_CASH);
        yOrderInfo.reverse();

        if (foldSet != null && foldSet.isRunning()) {
            foldSet.removeAllListeners();
            foldSet.end();
        }
        foldSet = new AnimatorSet();
        foldSet.playTogether(aOriginPrice, yOrderInfo);
        foldSet.start();
        foldSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (listener != null) {
                    listener.onAnimationEnd(animation);
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
    }

    @MTPaySuppressFBWarnings("SIC_INNER_SHOULD_BE_STATIC_ANON")
    private void unfoldAnim(float yOrderInfoMove, OnAnimationEnd listener) {
        clearOrderInfoLayoutPadding();

        ObjectAnimator aOriginPrice = ObjectAnimator.ofFloat(originPriceLayout, "alpha", 0, 1);
        aOriginPrice.setDuration(ANIM_DURATION_ORIGIN_CASH);

        ObjectAnimator yOrderInfo = ObjectAnimator.ofFloat(orderInfoLayout, "translationY", 0,
                yOrderInfoMove);
        yOrderInfo.setDuration(ANIM_DURATION_ORIGIN_CASH);

        if (unfoldSet != null && unfoldSet.isRunning()) {
            unfoldSet.removeAllListeners();
            unfoldSet.end();
        }
        unfoldSet = new AnimatorSet();
        unfoldSet.playTogether(aOriginPrice, yOrderInfo);
        unfoldSet.start();
        unfoldSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (listener != null) {
                    listener.onAnimationEnd(animation);
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
    }

    /**
     * 在DCEP界面中这个高度有点高了，需要重新设置高度
     *
     * @return
     */
    public FrameLayout getOrderPriceAndInfoLayout() {
        return orderPriceAndInfoLayout;
    }

    /* 注释的代码先留着，v4.9移除了删除线和晃动动画，预计后续版本会加回来
    private void unfoldAnim(float yOrderInfoMove, float deleteLineWidth) {
        ObjectAnimator aOriginPrice = ObjectAnimator.ofFloat(originPriceLayout, "alpha", 0, 1);
        aOriginPrice.setDuration(ANIM_DURATION_ORIGIN_CASH);

        ObjectAnimator yOrderInfo = ObjectAnimator.ofFloat(orderInfoLayout, "translationY", 0,
                yOrderInfoMove);
        yOrderInfo.setDuration(ANIM_DURATION_ORIGIN_CASH);

        ObjectAnimator xDeleteLine = ObjectAnimator.ofFloat(deleteLine, "translationX", 0,
                deleteLineWidth + getResources().
                        getDimension(R.dimen.cashier__delete_pre_shift));
        xDeleteLine.setDuration(ANIM_DURATION_LINE);

        ValueAnimator lineWidth = ValueAnimator.ofFloat(deleteLineWidth);
        lineWidth.setDuration(ANIM_DURATION_LINE);
        lineWidth.addUpdateListener(animation -> {
            if (deleteLine.getVisibility() != View.GONE) {
                if (animation.getAnimatedValue() instanceof Float) {
                    deleteLine.getLayoutParams().width = ((Float) animation.getAnimatedValue())
                            .intValue();
                    deleteLine.requestLayout();
                }
            }
        });

        ObjectAnimator moveLeft = ObjectAnimator.ofFloat(originAmountTv, "translationX", 0, 15);
        moveLeft.setDuration(ANIM_DURATION_DISCOUNT_CASH / 2);
        moveLeft.setRepeatMode(ObjectAnimator.REVERSE);
        moveLeft.setRepeatCount(1);

        AnimatorSet set = new AnimatorSet();
        set.playTogether(aOriginPrice, yOrderInfo, xDeleteLine, lineWidth, moveLeft);
        set.start();
    }

    private void showUnfoldAnimSet(String originAmount, float yOrderInfoShift) {
        originAmountTv.setText(originAmount);
        originAmountTv.setVisibility(View.VISIBLE);
        deleteLine.setVisibility(View.VISIBLE);
        //动画开始前，重置上下padding，避免轨迹偏下
        orderInfoLayout.setPadding(0, 0, 0,
                getResources().getDimensionPixelSize(R.dimen.cashier__order_info_padding_bottom));
        int[] size = getOriginAmountTvSize();
        //获取size失败时兜底，不展示动画
        if (size[0] == 0) {
            showOriginAmountNoAnim(originAmount);
        } else {
            unfoldAnim(yOrderInfoShift, size[0]);
        }
    }

    private int[] getOriginAmountTvSize() {
        int[] size = new int[]{0, 0};
        if (originAmountTv != null) {
            size[0] = originAmountTv.getWidth();
            size[1] = originAmountTv.getHeight();
            if (size[0] == 0 || size[1] == 0) {
                originAmountTv.measure(0, 0);
                size[0] = originAmountTv.getMeasuredWidth();
                size[1] = originAmountTv.getMeasuredHeight();
            }
        }
        return size;
    }

    private void showUnfoldAnimSet(String originAmount, float yOrderInfoShift) {
        originAmountTv.setText(originAmount);
        originAmountTv.setVisibility(View.VISIBLE);
        //动画开始前，重置上下padding，避免轨迹偏下
        orderInfoLayout.setPadding(0, 0, 0,
                getResources().getDimensionPixelSize(R.dimen.cashier__order_info_padding_bottom));
        int[] size = getOriginAmountTvSize();
        //获取size失败时兜底，不展示动画
        if (size[0] == 0) {
            showOriginAmountNoAnim(originAmount);
        } else {
            unfoldAnim(yOrderInfoShift, size[0]);
        }
    }

    //三个难点：ABA问题、重复点击、多种优惠下动画的控制
    @Override
    public void refreshView(float refreshAmount) {
        toDisplayAmount = refreshAmount;
        float originAmount = 0.00f;
        if (cashier != null) {
            originAmount = cashier.getTotalFee();
        }
        String originPrice = getContext().getString(R.string.mpay__money_prefix)
                + Strings.getFormattedDoubleValueWithZero(originAmount);
        float yShift = getResources().getDimension(R.dimen.cashier__origin_amount_y_shift);

        //首次进入不展示动画
        if (TextUtils.isEmpty(businessInfoMoney.getText())) {
            if (CashAmountArithUtils.compare(originAmount, refreshAmount) > 0) {
                showOriginAmountNoAnim(originPrice);
            } else {
                originAmountTv.setVisibility(View.GONE);
            }
//            deleteLine.setVisibility(View.GONE);
            setBusinessInfoMoney(refreshAmount);
        } else {
            float curAmount = getCurAmount(refreshAmount);
            if (CashAmountArithUtils.compare(curAmount, refreshAmount) != 0) {
                businessInfoMoney.startPlay(curAmount, refreshAmount,
                        AutoChangeNumberView.FORMAT_TWO_DECIMAL, ANIM_DURATION_DISCOUNT_CASH,
                        ANIM_DELAY_AUTO_CASH, a -> setBusinessInfoMoney(toDisplayAmount));
                //金额小于原价，展开动画
                if (CashAmountArithUtils.compare(refreshAmount, originAmount) < 0) {
                    showUnfoldAnimSet(originPrice, yShift);
                } else {
                    showFoldAnimSet(yShift);
                }
            } else {
                setBusinessInfoMoney(refreshAmount);
            }
        }
    }

    private void showOriginAmountNoAnim(String amount) {
        //调整订单信息的位置，与正常动画结束后保持一致,避免跳动
        orderInfoLayout.setPadding(0,
                getResources().getDimensionPixelSize(R.dimen.cashier__origin_amount_y_shift), 0,
                getResources().getDimensionPixelSize(R.dimen.cashier__order_info_padding_bottom));
        originAmountTv.setVisibility(View.VISIBLE);
        SpannableString spannableString = new SpannableString(amount);
        spannableString.setSpan(new StrikethroughSpan(), 0, amount.length(),
                Spanned.SPAN_EXCLUSIVE_INCLUSIVE);
        originAmountTv.setText(spannableString);
    }

    private void showUnfoldAnimSet(float yOrderInfoShift) {
        originAmountTv.setVisibility(View.VISIBLE);
        //动画开始前，重置上下padding，避免轨迹偏下
        orderInfoLayout.setPadding(0, 0, 0,
                getResources().getDimensionPixelSize(R.dimen.cashier__order_info_padding_bottom));
        unfoldAnim(yOrderInfoShift);
    }

    private void showFoldAnimSet(float yOrderInfoShift) {
        //动画开始前，重置上下padding，避免轨迹偏下
        orderInfoLayout.setPadding(0, 0, 0,
                getResources().getDimensionPixelSize(R.dimen.cashier__order_info_padding_bottom));
        foldAnim(yOrderInfoShift);
        if (originAmountTv.getVisibility() == View.VISIBLE) {
            foldAnim(yOrderInfoShift);
        }
        deleteLine.setVisibility(View.GONE);
    }*/
    private String getUniqueId() {
        Activity activity = ViewUtils.getActivityFromView(this);
        if (activity instanceof PayBaseActivity && !TextUtils.isEmpty(((PayBaseActivity) activity).getUniqueId())) {
            return ((PayBaseActivity) activity).getUniqueId();
        }
        return "";
    }
}

