package com.meituan.android.cashier.model.bean;

import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;

/**
 * 该类自定义 json 解析规则
 * 新增字段或修改字段需要修改 CashierPopWindowBeanDeserializer
 */
@JsonBean
@JsonAdapter(CashierPopWindowBeanDeserializer.class)
public class CashierPopWindowBean implements Serializable {

    private static final long serialVersionUID = 4421892210147535599L;

    public static final int STOP_PAYMENT_GUIDE = 1;
    public static final int POPWINDOW_PAYLATER_GUIDE = 2;
    public static final int BIND_CARD_PAY_GUIDE = 3;
    public static final int CREDIT_PAY_GUIDE = 4;
    public static final int PROMOTION_SIGNED_PAY_GUIDE = 5;
    public static final int PROMOTION_BINDED_CARD_PAY_GUIDE = 6;

    public static final String BEFORE_PAY_SCENE = "beforePay";
    public static final String INTERRUPT_PAY_SCENE = "interruptPay";

    @SerializedName("pop_detail_info")
    private PopDetailInfo popDetailInfo; // type = 1、3、4 时不为空
    private PayLaterPopDetailInfoBean payLaterPopDetailInfoBean; // type = 2 时不为空

    @SerializedName("type")
    private int type;

    //弹窗场景，beforePay-支付前弹  interruptPay-支付中断触发弹窗
    @SerializedName("pop_scene")
    private String popScene;

    public String getPopScene() {
        return popScene;
    }

    public void setPopScene(String popScene) {
        this.popScene = popScene;
    }

    public PopDetailInfo getPopDetailInfo() {
        return popDetailInfo;
    }

    public void setPopDetailInfo(PopDetailInfo popDetailInfo) {
        this.popDetailInfo = popDetailInfo;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public PayLaterPopDetailInfoBean getPayLaterPopDetailInfoBean() {
        return payLaterPopDetailInfoBean;
    }

    public void setPayLaterPopDetailInfoBean(PayLaterPopDetailInfoBean payLaterPopDetailInfoBean) {
        this.payLaterPopDetailInfoBean = payLaterPopDetailInfoBean;
    }
}
