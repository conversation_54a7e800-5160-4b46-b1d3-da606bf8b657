package com.meituan.android.cashier.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.ScrollView;

/**
 * MTCashierScrollView -> NSCScrollView
 * NSC: NativeStandardCashier的缩写
 */
public class NSCScrollView extends ScrollView {
    // 是否允许滑动
    private boolean scrollable;

    private MTCashierScrollChangeListener listener;

    public NSCScrollView(Context context) {
        super(context);
    }

    public NSCScrollView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public NSCScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void scrollTo(int x, int y) {
        // 判断是否允许自动滑动
        if (scrollable) {
            super.scrollTo(x, y);
        }
    }

    public void setScrollable(boolean scrollable) {
        this.scrollable = scrollable;
    }

    public boolean isScrollable() {
        return scrollable;
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);
        if (listener != null) {
            listener.onScrollChanged(this, l, t, oldl, oldt);
        }
    }

    public void setOnScrollChangeListener(MTCashierScrollChangeListener listener) {
        this.listener = listener;
    }
}
