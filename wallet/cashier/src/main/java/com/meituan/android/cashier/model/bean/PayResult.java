package com.meituan.android.cashier.model.bean;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.paybase.utils.JsonBean;
import com.meituan.android.paycommon.lib.coupon.model.Promotion;

import java.io.Serializable;

/**
 * 支付结果
 * Created by <PERSON><PERSON><PERSON><PERSON> on 14-8-21.
 * TODO 改下名字，这不是真正的PayResult，只是DirectPay接口的状态值，对应的是goHelloPay接口的MTPaymentURL
 */
@JsonBean
public class PayResult implements Serializable {
    private static final long serialVersionUID = -644764309558464844L;
    //checked
    private String url;
    @SerializedName("payed_total_by_credit")
    private boolean isPayedTotalByCredit;
    @SerializedName("pay_type")
    private String payType;
    @SerializedName("overload_info")
    private OverLoadInfo overLoadInfo;

    private Promotion promotion;

    private PopUp popup;

    private String action;

    @SerializedName("verify_url")
    private String verifyUrl;

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getVerifyUrl() {
        return verifyUrl;
    }

    public void setVerifyUrl(String verifyUrl) {
        this.verifyUrl = verifyUrl;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public boolean isPayedTotalByCredit() {
        return isPayedTotalByCredit;
    }

    public void setPayedTotalByCredit(boolean isPayedTotalByCredit) {
        this.isPayedTotalByCredit = isPayedTotalByCredit;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    @Override
    public String toString() {
        return "url=" + url + ",isPayedTotalByCredit=" + isPayedTotalByCredit + ",payType=" + payType;
    }
    public OverLoadInfo getOverLoadInfo() {
        return overLoadInfo;
    }

    public void setOverLoadInfo(OverLoadInfo overLoadInfo) {
        this.overLoadInfo = overLoadInfo;
    }

    public Promotion getPromotion() {
        return promotion;
    }

    public void setPromotion(Promotion promotion) {
        this.promotion = promotion;
    }

    public PopUp getPopUp() {
        return popup;
    }

    public void setPopUp(PopUp popup) {
        this.popup = popup;
    }
}
