package com.meituan.android.cashier.model.bean;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/6/15.
 */
@JsonBean
public class CashierInfo implements Serializable {
    private static final long serialVersionUID = 5641009440287621547L;

    @SerializedName("common")
    private Cashier common;
    @SerializedName("wallet")
    private Cashier wallet;

    public Cashier getCommon() {
        return common;
    }

    public void setCommon(Cashier common) {
        this.common = common;
    }

    public Cashier getWallet() {
        return wallet;
    }

    public void setWallet(Cashier wallet) {
        this.wallet = wallet;
    }
}
