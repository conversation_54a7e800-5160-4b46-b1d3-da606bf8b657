package com.meituan.android.cashier.utils;

import static com.meituan.android.cashier.base.utils.CashierAnalyseUtils.getCreditPayStatus;

import android.content.Context;
import android.text.TextUtils;

import com.meituan.android.cashier.R;
import com.meituan.android.cashier.base.utils.CashierAnalyseUtils;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.model.bean.Cashier;
import com.meituan.android.cashier.model.bean.CashierPayment;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.pay.common.payment.data.IBankcardData;
import com.meituan.android.pay.common.payment.data.IPaymentData;
import com.meituan.android.pay.common.payment.data.PayType;
import com.meituan.android.pay.common.payment.utils.PayTypeUtils;
import com.meituan.android.pay.utils.CreditOpenUtils;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.config.PayBaseConfig;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.MTPaySuppressFBWarnings;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Native 标准收银台支付流程埋点
 * 仅包括支付方式的曝光、点击、支付三种情况
 * https://km.sankuai.com/page/**********
 */
public final class NativeStandardCashierPayProcessStatistic {
    private String mMerchantNo;
    private Cashier mCashier;

    public void setMerchantNo(String merchantNo) {
        this.mMerchantNo = merchantNo;
    }

    public void setCashier(Cashier cashier) {
        this.mCashier = cashier;
    }

    /**
     * 曝光点，一般是页面的 PV 点
     *
     * @param checkedPaymentData 当前选中的支付方式
     */
    public HashMap<String, Object> getHomePVProperties(IPaymentData checkedPaymentData) {
        HashMap<String, Object> properties = new HashMap<>();
        properties.put("platform", "android");
        // 上报收银台版本：
        properties.put("nb_version", PayBaseConfig.getProvider().getPayVersion());
        // 上报订单号
        if (mCashier != null && !TextUtils.isEmpty(mCashier.getTradeNo())) {
            properties.put("tradeNo", mCashier.getTradeNo());
        }
        // 上报商户号
        if (!TextUtils.isEmpty(mMerchantNo)) {
            properties.put("merchant_no", mMerchantNo);
        }
        // 上报活动id
        if (checkedPaymentData instanceof MTPayment) {
            MTPayment mtPayment = (MTPayment) checkedPaymentData;
            properties.put("cardPayTitle", mtPayment.getName());
            if (!CollectionUtils.isEmpty(mtPayment.getBottomLabels())
                    || !CollectionUtils.isEmpty(mtPayment.getRightLabels())) {
                properties.put("cardPayLabels", true);
            } else {
                properties.put("cardPayLabels", false);
            }
            int cardNum = 0;
            CashierPayment wallet = getWalletPay();
            if (wallet != null && wallet.getWalletPaymentListPage() != null) {
                List<IBankcardData> paymentList = wallet.getWalletPaymentListPage().getMtPaymentList();
                if (!CollectionUtils.isEmpty(paymentList)) {
                    for (IBankcardData payment : paymentList) {
                        if (PayType.BIND_BANKCARD_LIST.contains(payment.getPayType())) {
                            cardNum++;
                        }
                    }
                }
                properties.put("binding_card_num", cardNum);
            }
        }
        if (mCashier != null) {
            properties.put("creditPay_status", getCreditPayStatus(mCashier));
        }
        if (mCashier != null) {
            properties.put("sub_pay_type", CashierAnalyseUtils.getAllPayType(mCashier));
        }
        if (mCashier != null) {
            int authType = CashierAnalyseUtils.getCreditPayAuthType(mCashier);
            if (authType != -1) {
                properties.put("real_name_auth_type", authType);
            }
        }
        if (checkedPaymentData != null) {
            properties.put("payType", checkedPaymentData.getPayType());
            properties.put("pay_type", checkedPaymentData.getPayType());
            properties.put("default_sub_pay_type", checkedPaymentData.getPayType());
            if (!CollectionUtils.isEmpty(checkedPaymentData.getRightLabels())
                    && checkedPaymentData.getRightLabels().get(0) != null) {
                properties.put("recommendStyle", checkedPaymentData.getRightLabels().get(0).getStyle());
            }
            if (!CollectionUtils.isEmpty(checkedPaymentData.getBottomLabels())) {
                properties.put("mtBottomLabel", true);
            } else {
                properties.put("mtBottomLabel", false);
            }
        } else {
            properties.put("payType", "");
            properties.put("pay_type", "");
        }
        properties.put("sub_type", "0");
        getBonusProperties(properties);
        putCreditPayInfo(properties, findCreditPayment());
        properties.put("open_source", CreditOpenUtils.STANDARD_HOMEBUTTON_SUFFIX);
        return properties;
    }

    private MTPayment findCreditPayment() {
        if (mCashier != null && !CollectionUtils.isEmpty(mCashier.getPaymentDataList())) {
            for (CashierPayment cashierPayment : mCashier.getPaymentDataList()) {
                if (PayTypeUtils.isWalletPay(cashierPayment.getPayType())) {
                    List<MTPayment> recommendPayments = cashierPayment.getRecommendPayment();
                    if (CollectionUtils.isEmpty(recommendPayments)) {
                        return null;
                    }
                    for (MTPayment recommendPayment : recommendPayments) {
                        if (CreditOpenUtils.isCreditPay(recommendPayment)) {
                            return recommendPayment;
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * @param properties
     * @param creditPayment
     */
    public static void putCreditPayInfo(Map<String, Object> properties, IPaymentData creditPayment) {
        // credit_style：0（老流程）/1（新流程）/ -999（不存在月付支付方式）
        // mtcreditpay_status：0（未开通）/ 1（已开通）/ -999（不存在月付支付方式）
        if (creditPayment instanceof MTPayment && CreditOpenUtils.isCreditPay((MTPayment) creditPayment)) {
            properties.put("credit_style", CreditOpenUtils.isHasNewOpenCreditInfo((MTPayment) creditPayment) ? "1" : "0");
            properties.put("mtcreditpay_status", CreditOpenUtils.isCreditOpen((MTPayment)creditPayment) ? "1" : "0");
        } else {
            properties.put("credit_style", "-999");
            properties.put("mtcreditpay_status", "-999");
        }
    }

    @MTPaySuppressFBWarnings("NP_NULL_ON_SOME_PATH")
    private CashierPayment getWalletPay() {
        if (mCashier != null && !CollectionUtils.isEmpty(mCashier.getPaymentDataList())) {
            for (CashierPayment cashierPayment : mCashier.getPaymentDataList()) {
                if (PayTypeUtils.isWalletPay(cashierPayment.getPayType())) {
                    return cashierPayment;
                }
            }
        }
        return null;
    }

    private void getBonusProperties(Map<String, Object> properties) {
        properties.put("reduction_switch", "null");
    }

    /**
     * @param checkedPaymentData 当前选中的支付方式
     * @param openSource         月付使用的场景标识
     */
    public void logOnHomeSwitchPayment(IPaymentData checkedPaymentData, String openSource, String uniqueId) {
        HashMap<String, Object> labels = new AnalyseUtils.InstantReportBuilder()
                .addTradeNo()
                .add("merchant_no", mMerchantNo)
                .add("pay_type", checkedPaymentData.getPayType())
                .add("status", String.valueOf(checkedPaymentData.getStatus()))
                .add("open_source", openSource)
                .build();
        putCreditPayInfo(labels, checkedPaymentData);
        CashierStaticsUtils.reportModelEventWithClickEvent("c_PJmoK", "b_pay_jvsgexor_mc",
                "切换支付方式", labels, uniqueId);
    }

    /**
     * @param checkedPaymentData 当前选中的支付方式
     * @param openSource         月付使用的场景标识
     */
    public void logOnHomePay(IPaymentData checkedPaymentData, String openSource, String uniqueId) {
        String payType = "-999";
        if (checkedPaymentData != null) {
            payType = checkedPaymentData.getPayType();
        }
        Context context = PayBaseConfig.getProvider().getApplicationContext();
        HashMap<String, Object> labels = new AnalyseUtils.MapBuilder().add("nb_version", PayBaseConfig.getProvider().getPayVersion()).
                add("pay_type", payType).
                add("tradeNo", mCashier.getTradeNo()).
                add("merchant_no", mMerchantNo).
                add("creditPay_status", getCreditPayStatus(mCashier)).
                add("open_source", openSource).
                add("sub_type", "0").
                build();
        putCreditPayInfo(labels, checkedPaymentData);
        CashierStaticsUtils.reportModelEventWithClickEvent("c_PJmoK", "b_xgald577", context.getString(R.string.cashier__mge_act_click_pay),
                labels, uniqueId);
    }
}