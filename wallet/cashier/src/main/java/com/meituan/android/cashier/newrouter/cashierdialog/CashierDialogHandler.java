package com.meituan.android.cashier.newrouter.cashierdialog;

import static com.meituan.android.pay.common.payment.data.WalletPayParams.KEY_LAUNCH_URL;

import android.os.Bundle;
import android.support.annotation.Keep;
import android.support.v4.app.FragmentActivity;
import android.text.TextUtils;

import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.dialog.AutomaticPayGuideDialog;
import com.meituan.android.cashier.dialogfragment.AutomaticPayGuideDialogFragment;
import com.meituan.android.cashier.model.bean.Cashier;
import com.meituan.android.cashier.model.bean.NoPswGuide;
import com.meituan.android.cashier.newrouter.CashierBusinessHandler;
import com.meituan.android.cashier.newrouter.NewCashierParams;
import com.meituan.android.cashier.util.CashierSLAMonitor;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.utils.ActivityStatusChecker;
import com.meituan.android.paybase.utils.Base64;
import com.meituan.android.paybase.utils.CatchException;
import com.meituan.android.paybase.utils.JsonString;
import com.meituan.android.paycommon.lib.utils.OuterBusinessParamUtils;
import com.meituan.android.payrouter.remake.router.manager.RouterManager;
import com.meituan.android.payrouter.utils.ProxyUtils;

import java.io.Serializable;
import java.util.HashMap;

public class CashierDialogHandler extends CashierBusinessHandler implements IRequestCallback, AutomaticPayGuideDialog.OnClickGuideButtonListener {
    private static final String GO_HELLO_PAY_DEFAULT_PATH = "/cashier/gohellopay";

    private Cashier cashierBean;

    private final CashierDialogResultHandler resultHandler;

    public CashierDialogHandler(FragmentActivity activity, NewCashierParams cashierParams, CashierDialogResultHandler resultHandler) {
        super(activity, cashierParams);
        this.resultHandler = resultHandler;
    }

    public CashierDialogHandler setCashierResponse(Cashier cashier) {
        this.cashierBean = cashier;
        return this;
    }

    public boolean show() {
        return showAutomaticPayGuide(getActivity());
    }

    /**
     * 打车自动扣款引导，比较旧，后续可以考虑下掉
     */
    private boolean showAutomaticPayGuide(FragmentActivity activity) {
        if (!ActivityStatusChecker.isValid(activity) || cashierBean == null || !NoPswGuide.isValid(cashierBean.getNoPswGuide())) {
            return false;
        }
        AutomaticPayGuideDialogFragment automaticPayGuideDialogFragment = AutomaticPayGuideDialogFragment.newInstance(cashierBean);
        automaticPayGuideDialogFragment.setOnClickGuideButton(this);
        Bundle arguments = automaticPayGuideDialogFragment.getArguments();
        if (arguments == null) {
            arguments = new Bundle();
            automaticPayGuideDialogFragment.setArguments(arguments);
        }
        arguments.putSerializable("CashierParams", getCashierParams());
        automaticPayGuideDialogFragment.show(activity.getSupportFragmentManager());
        CashierSLAMonitor.notifyRouterLoadEnd(getCashierParams().getCashierRouterTrace(), "打车代扣展示成功");
        return true;
    }

    @Override
    public void onClickGuideOpen(String url) {
        if (TextUtils.isEmpty(url)) { // 如果url为空或者执行过程中有Exception，则执行onCashierDialogCancel
            getResultHandler().onCashierDialogCancel();
            return;
        }
        CatchException.run(() -> {
            String requestInfo = new String(Base64.decode(url));
            HashMap<String, String> requestInfoMap = JsonString.parser(requestInfo).toStringMap();
            String launchUrl = requestInfoMap.get(KEY_LAUNCH_URL);
            launchUrl = !TextUtils.isEmpty(launchUrl) ? launchUrl : GO_HELLO_PAY_DEFAULT_PATH;
            FragmentActivity activity = getActivity();
            if (activity instanceof OuterBusinessParamUtils.OuterBusinessParamInterface) {
                OuterBusinessParamUtils.appendExtraParamsTogoHelloPay((OuterBusinessParamUtils.OuterBusinessParamInterface) activity, requestInfoMap);
            }
            getResultHandler().onCashierDialogSuccess(new Result(launchUrl, requestInfoMap));
        }).catchException(throwable -> getResultHandler().onCashierDialogCancel())
                .catchForReport("CashierDialogHandler_onClickGuideOpen");
    }

    @Override
    public void onClickGuideCancel(Cashier cashier) {
        getResultHandler().onCashierDialogCancel();
    }

    @Override
    public void onRequestSucc(int tag, Object obj) {

    }

    @Override
    public void onRequestException(int tag, Exception e) {

    }

    @Override
    public void onRequestFinal(int tag) {

    }

    @Override
    public void onRequestStart(int tag) {

    }

    private CashierDialogResultHandler getResultHandler() {
        return ProxyUtils.nonNullObject(CashierDialogResultHandler.class, resultHandler);
    }

    @Keep
    public static class Result implements Serializable {
        private String url;
        private HashMap<String, String> requestParams;

        public Result(String url, HashMap<String, String> requestParams) {
            this.url = url;
            this.requestParams = requestParams;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public HashMap<String, String> getRequestParams() {
            return requestParams;
        }

        public void setRequestParams(HashMap<String, String> requestParams) {
            this.requestParams = requestParams;
        }
    }
}
