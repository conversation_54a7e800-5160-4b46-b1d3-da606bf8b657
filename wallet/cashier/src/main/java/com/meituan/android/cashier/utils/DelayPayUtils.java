package com.meituan.android.cashier.utils;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.meituan.android.cashier.bean.CashierParams;
import com.meituan.android.cashier.bean.CashierRouterPreGuideHornConfig;
import com.meituan.android.paycommon.lib.fragment.HalfPageFragment;

public class DelayPayUtils {

    // 先用后付接口预请求逻辑
    // https://km.sankuai.com/collabpage/1886283333
    public static void prefetch(CashierRouterPreGuideHornConfig cashierRouterPreGuideHornConfig, HalfPageFragment.HalfPageFragmentConfig halfPageFragmentConfig, CashierParams mCashierParams) {
        if (!cashierRouterPreGuideHornConfig.getCashierType().equals("delaypay") || !cashierRouterPreGuideHornConfig.isNsf()) {
            return;
        }
        try {
            JsonObject requestData = new JsonObject();
            requestData.addProperty("tradeno", mCashierParams.getTradeNo());
            requestData.addProperty("pay_token", mCashierParams.getPayToken());
            JsonObject extraData = new JsonParser().parse(mCashierParams.getExtraData()).getAsJsonObject();
            requestData.addProperty("serialCode", extraData.get("serialCode").getAsString());
            requestData.addProperty("productScene", extraData.get("productScene").getAsString());
            requestData.addProperty("payType", extraData.get("payType").getAsString());
            halfPageFragmentConfig.setRequestData(requestData.toString());
            // 先进行参数解析，最后再setUrl，这样可以预防由于NSFParams错误导致发起错误NSF请求的问题
            halfPageFragmentConfig.setRequestUrl(cashierRouterPreGuideHornConfig.getNsfUrl());
        } catch (Exception e) {
            // ignore 若出现数据报错，则不进行NSF请求
        }
    }

}