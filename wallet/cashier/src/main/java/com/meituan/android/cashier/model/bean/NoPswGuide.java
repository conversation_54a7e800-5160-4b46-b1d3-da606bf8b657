package com.meituan.android.cashier.model.bean;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;

/**
 * Created by ljj
 * Date:17/3/15
 * Time:下午8:11
 */
@JsonBean
public class NoPswGuide implements Serializable {
    private static final long serialVersionUID = 2517619846875636369L;
    @SerializedName("guide_title")
    private String guideTitle;
    @SerializedName("description")
    private String description;
    @SerializedName("agreement_tip")
    private String agreeTip;
    @SerializedName("agreement_name")
    private String agreeName;
    @SerializedName("agreement_url")
    private String agreementUrl;
    @SerializedName("cancel_button")
    private String cancleButton;
    @SerializedName("open_button")
    private String openButton;
    @SerializedName("submit_url")
    private String submitUrl;

    public String getAgreementUrl() {
        return agreementUrl;
    }

    public void setAgreementUrl(String agreementUrl) {
        this.agreementUrl = agreementUrl;
    }

    public String getAgreeName() {
        return agreeName;
    }

    public void setAgreeName(String agreeName) {
        this.agreeName = agreeName;
    }

    public String getAgreeTip() {
        return agreeTip;
    }

    public void setAgreeTip(String agreeTip) {
        this.agreeTip = agreeTip;
    }

    public String getCancleButton() {
        return cancleButton;
    }

    public void setCancleButton(String cancleButton) {
        this.cancleButton = cancleButton;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getGuideTitle() {
        return guideTitle;
    }

    public void setGuideTitle(String guideTitle) {
        this.guideTitle = guideTitle;
    }

    public String getOpenButton() {
        return openButton;
    }

    public void setOpenButton(String openButton) {
        this.openButton = openButton;
    }

    public String getSubmitUrl() {
        return submitUrl;
    }

    public void setSubmitUrl(String submitUrl) {
        this.submitUrl = submitUrl;
    }

    public static boolean isValid(NoPswGuide noPswGuide) {
        return noPswGuide != null && !TextUtils.isEmpty(noPswGuide.getSubmitUrl());
    }
}
