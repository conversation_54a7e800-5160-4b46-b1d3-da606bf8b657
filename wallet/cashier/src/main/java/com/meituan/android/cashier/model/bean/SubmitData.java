package com.meituan.android.cashier.model.bean;

import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;

@JsonBean
public class SubmitData implements Serializable {

    private static final long serialVersionUID = -1296379160398835329L;

    private String submitUrl;
    private String payType;
    private String bankType;
    private String payScene;
    private String paySceneParams;

    public String getSubmitUrl() {
        return submitUrl;
    }

    public void setSubmitUrl(String submitUrl) {
        this.submitUrl = submitUrl;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getBankType() {
        return bankType;
    }

    public void setBankType(String bankType) {
        this.bankType = bankType;
    }

    public String getPayScene() {
        return payScene;
    }

    public void setPayScene(String payScene) {
        this.payScene = payScene;
    }

    public String getPaySceneParams() {
        return paySceneParams;
    }

    public void setPaySceneParams(String paySceneParams) {
        this.paySceneParams = paySceneParams;
    }
}
