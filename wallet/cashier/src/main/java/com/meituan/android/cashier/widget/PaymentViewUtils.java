package com.meituan.android.cashier.widget;

import android.view.View;
import android.view.ViewGroup;

import com.meituan.android.cashier.utils.ViewLayoutCalculation;
import com.meituan.android.pay.base.utils.compute.FloatComparator;
import com.meituan.android.pay.desk.payment.view.BasePaymentView;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paycommon.lib.widgets.ExtendableVerticalLinearLayout;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class PaymentViewUtils {
    private static final float MIN_SHOW_RATE_IN_CONTAINER = 1/2f; // 在container中的最小展示率为1/2。
    private static final float MAX_SHOW_RATE_IN_MASK = 1/2f; // 在mask中的最大展示率为1/2。

    public static List<PaymentViewStatus> calculatePaymentViewStatus(List<BasePaymentView> paymentViews, View container, View bottomMask) {
        List<PaymentViewStatus> statusList = new ArrayList<>();
        if (CollectionUtils.isEmpty(paymentViews)) {
            return statusList;
        }
        ViewLayoutCalculation containerAttr = ViewLayoutCalculation.locationAndVisible(container);
        ViewLayoutCalculation maskAttr = ViewLayoutCalculation.locationAndVisible(bottomMask);
        for (int i = 0; i < paymentViews.size(); i++) {
            PaymentViewStatus status = calculatePaymentViewStatus(paymentViews.get(i), containerAttr, maskAttr);
            if (status != null) {
                status.setItemIndex(i);
                statusList.add(status);
            }
        }
        return statusList;
    }

    public static PaymentViewStatus calculatePaymentViewStatus(BasePaymentView paymentView, ViewLayoutCalculation containerAttr, ViewLayoutCalculation maskAttr) {
        if (paymentView == null) {
            return new PaymentViewStatus();
        }
        PaymentViewStatus status = PaymentViewStatus.initFromPayment(paymentView);
        if (status != null) {
            status.setIsFirstScreenExposed(isPaymentViewOnScreen(paymentView, containerAttr, maskAttr));
        }
        return status;
    }

    /**
     * 前序遍历ViewGroup
     *
     * @param container 支付方式展示区域
     * @return 类型为BasePaymentView的View列表
     */
    public static List<BasePaymentView> findAllPaymentViews(ViewGroup container) {
        LinkedList<BasePaymentView> views = new LinkedList<>();
        LinkedList<View> stack = new LinkedList<>();
        stack.push(container);
        while (!stack.isEmpty()) {
            View cur = stack.pop();
            // 判断支付方式View
            if (cur instanceof BasePaymentView) {
                views.add((BasePaymentView) cur);
                continue;
            }
            // 遍历折叠区域
            if (cur instanceof ExtendableVerticalLinearLayout) {
                cur = ((ExtendableVerticalLinearLayout) cur).getExtendedView();
            }
            // 遍历非折叠区域
            if (cur instanceof ViewGroup) {
                for (int i = ((ViewGroup) cur).getChildCount() - 1; i >= 0; i--) {
                    stack.push(((ViewGroup) cur).getChildAt(i));
                }
            }
        }
        return views;
    }

    public static BasePaymentView findSelectedPaymentView(List<BasePaymentView> paymentViews) {
        if (CollectionUtils.isEmpty(paymentViews)) {
            return null;
        }
        for (BasePaymentView paymentView : paymentViews) {
            if (paymentView != null && paymentView.isChecked()) {
                return paymentView;
            }
        }
        return null;
    }

    /**
     * 判断支付方式是否显露的逻辑:
     * 1. 判断Visibility
     * 2. 判断是否在container的显示布局内：通过判断在container布局内的展示率> 1/2
     * 3. 判断是否被mask遮盖：通过判断在mask布局内的展示率 < 1/2
     *
     * @param paymentView   需要被判断的支付方式
     * @param containerAttr 所在的容器布局信息。通过{@link ViewLayoutCalculation#location(View)} 方式获取
     * @param maskAttr      底部营销信息遮罩布局信息。通过{@link ViewLayoutCalculation#location(View)} 方式获取
     * @return true：显示，false：不显示。
     */
    public static boolean isPaymentViewOnScreen(BasePaymentView paymentView, ViewLayoutCalculation containerAttr, ViewLayoutCalculation maskAttr) {
        // 如果当前view的状态不是 VISIBLE 或者没有 Attach，则判定为false
        if (paymentView == null
                || paymentView.getVisibility() != View.VISIBLE
                || !paymentView.isAttachedToWindow()
                || containerAttr == null) {
            return false;
        }
        ViewLayoutCalculation paymentViewAttr = ViewLayoutCalculation.locationAndVisible(paymentView);
        float visibleRateInContainer = ViewLayoutCalculation.visibleRateIn(paymentViewAttr, containerAttr);
        float visibleRateInMask = ViewLayoutCalculation.visibleRateIn(paymentViewAttr, maskAttr);
        // 如果当前view的布局不在container布局中的展示率超过 1/2 && 如果当前view在mask布局中的展示率小于 1/2，则表明展示成功
        return FloatComparator.isGreater(visibleRateInContainer, MIN_SHOW_RATE_IN_CONTAINER)
                && FloatComparator.isLesser(visibleRateInMask, MAX_SHOW_RATE_IN_MASK);
    }
}
