package com.meituan.android.cashier.business;

import static com.meituan.android.cashier.activity.MTCashierActivity.ERROR_CODE_DEFAULT;
import static com.meituan.android.cashier.activity.MTCashierActivity.ERROR_MSG_UNKNOWN_DISPATCHER_EXCEPTION;

import android.app.Activity;
import android.support.v4.app.FragmentActivity;

import com.meituan.android.cashier.R;
import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.model.PayErrorCode;
import com.meituan.android.cashier.newrouter.CashierBusinessHandler;
import com.meituan.android.cashier.newrouter.NewCashierParams;
import com.meituan.android.cashier.util.CashierSLAMonitor;
import com.meituan.android.paybase.common.dialog.PayDialog;
import com.meituan.android.paybase.retrofit.PayException;
import com.meituan.android.paybase.utils.ActivityStatusChecker;
import com.meituan.android.paycommon.lib.utils.ExceptionUtils;

import java.lang.ref.WeakReference;

public class DispatcherHandler extends CashierBusinessHandler {
    private final WeakReference<Activity> weakActivity;
    private final DispatcherResultHandler resultHandler;

    private boolean hasRetryRequestDispatcher;

    public DispatcherHandler(FragmentActivity activity, NewCashierParams cashierParams, DispatcherResultHandler resultHandler) {
        super(activity, cashierParams);
        this.weakActivity = new WeakReference<>(activity);
        this.resultHandler = resultHandler;
    }

    public void handleException(Exception exception) {
        Activity activity = weakActivity.get();
        if (!ActivityStatusChecker.isValid(activity)) {
            return;
        }
        if (exception instanceof PayException) {
            handlePayException((PayException) exception);
        } else {
            if (activity instanceof MTCashierActivity) {
                ((MTCashierActivity) activity).setResultStatus(MTCashierActivity.STATUS_FAIL);
                ((MTCashierActivity) activity).setErrorCodeAndErrorMessage(ERROR_CODE_DEFAULT, ERROR_MSG_UNKNOWN_DISPATCHER_EXCEPTION);
            }
            handleNonPayException(exception);
        }
    }

    private void handlePayException(PayException payException) {
        Activity activity = weakActivity.get();
        if (!ActivityStatusChecker.isValid(activity)) {
            return;
        }
        int errorCode = payException.getCode();
        String msg = payException.getMessage();
        if (activity instanceof MTCashierActivity) {
            ((MTCashierActivity) activity).setErrorCodeAndErrorMessage(String.valueOf(errorCode), msg);
        }
        if (errorCode == PayErrorCode.ALREADY_PAYED) {
            CashierSLAMonitor.reportStandardCashierFinished(getCashierParams().getCashierUniqueId(),
                    CashierSLAMonitor.CASHIER_FINISHED_STATUS_ALREADY_PAYED,
                    CashierSLAMonitor.NATIVE_STANDARD_CASHIER_SUCCESS_SCENE_NORMAL);
            CashierSLAMonitor.notifyRouterLoadEnd(getCashierParams().getCashierRouterTrace(), "standard-cashier already payed");
            new PayDialog.Builder(activity)
                    .msg(msg)
                    .subMsg(payException.getErrorCodeStr())
                    .rightBtn(PayDialog.DEFAULT_BUTTON_TEXT, (dialog) -> resultHandler.onAlreadyPayed())
                    .build().show();
        } else {
            CashierSLAMonitor.reportStandardCashierFinished(getCashierParams().getCashierUniqueId(),
                    CashierSLAMonitor.CASHIER_FINISHED_STATUS_FAILED,
                    CashierSLAMonitor.NATIVE_STANDARD_CASHIER_SUCCESS_SCENE_NORMAL);
            CashierSLAMonitor.notifyRouterLoadEnd(getCashierParams().getCashierRouterTrace(), "standard-cashier error");
            if (activity instanceof MTCashierActivity) {
                ((MTCashierActivity) activity).setResultStatus(MTCashierActivity.STATUS_FAIL);
            }
            ExceptionUtils.alertAndFinish(activity,
                    msg,
                    payException.getErrorCodeStr(),
                    MTCashierActivity.class);
        }
    }

    private void handleNonPayException(Exception exception) {
        // 非业务错误时，重新请求一次Dispatcher
        if (!hasRetryRequestDispatcher) {
            hasRetryRequestDispatcher = true;
            resultHandler.onNeedRetryRequest();
            return;
        }

        Activity activity = weakActivity.get();
        if (!ActivityStatusChecker.isValid(activity)) {
            return;
        }
        CashierSLAMonitor.reportStandardCashierFinished(getCashierParams().getCashierUniqueId(),
                CashierSLAMonitor.CASHIER_FINISHED_STATUS_NET_ERROR,
                CashierSLAMonitor.NATIVE_STANDARD_CASHIER_SUCCESS_SCENE_NORMAL);
        CashierSLAMonitor.notifyRouterLoadEnd(getCashierParams().getCashierRouterTrace(), "standard-cashier net error");
        String message = activity.getString(R.string.paycommon__error_msg_load_later);
        ExceptionUtils.alertAndFinish(activity, message, "", MTCashierActivity.class);
    }
}
