package com.meituan.android.cashier.base.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.meituan.android.cashier.R;
import com.meituan.android.cashier.model.bean.OrderInfo;
import com.meituan.android.cashier.model.bean.OrderInfoBlock;
import com.meituan.android.cashier.model.bean.OrderInfoContent;

/**
 * Created by <PERSON><PERSON><PERSON> on 16/9/12.
 */
public class OrderInfoBlockUtils {

    private static final String BLOCK_TYPE_1 = "type_1";
    private static final String BLOCK_TYPE_2 = "type_2";

    public static void showOrderInfoBlock(Context context, View rootView, @Nullable OrderInfo orderInfo) {
        LinearLayout blockContainer = (LinearLayout) rootView.findViewById(R.id.order_info_container);
        blockContainer.removeAllViews();

        if (orderInfo != null && orderInfo.getOrderInfoBlocks() != null) {
            int solidBlockCount = 0;
            for (int i = 0; i < orderInfo.getOrderInfoBlocks().size(); i++) {
                LinearLayout block = getOrderInfoBlock(context, orderInfo.getOrderInfoBlocks().get(i));
                if (block != null) {
                    blockContainer.addView(block);
                    solidBlockCount++;
                }
                if (block != null && i != orderInfo.getOrderInfoBlocks().size() - 1) {
                    blockContainer.addView(getDividerImage(context));
                    solidBlockCount++;
                } else if (block == null && i == orderInfo.getOrderInfoBlocks().size() - 1 && solidBlockCount > 0) {//最后一个block为空时，移除前面最后一个非空block的分割线
                    blockContainer.removeViewAt(solidBlockCount - 1);
                }
            }
        }
    }

    private static LinearLayout getOrderInfoBlock(Context context, OrderInfoBlock orderInfoBlock) {
        if (orderInfoBlock == null) {
            return null;
        }
        boolean hasChild = false;
        @SuppressLint("InflateParams")
        LinearLayout blockLinearLayout = (LinearLayout) LayoutInflater.from(context).inflate(R.layout.cashier__order_info_block, null);
        TextView blockTitle = (TextView) blockLinearLayout.findViewById(R.id.block_title);
        if (!TextUtils.isEmpty(orderInfoBlock.getBlockTitle())) {
            blockTitle.setText(orderInfoBlock.getBlockTitle());
            blockTitle.setVisibility(View.VISIBLE);
            hasChild = true;
        }
        if (TextUtils.equals(BLOCK_TYPE_1, orderInfoBlock.getBlockType())) {
            if (orderInfoBlock.getOrderInfoContents() != null) {
                for (int i = 0; i < orderInfoBlock.getOrderInfoContents().size(); i++) {
                    ViewGroup item1 = getBlockItem1(context, orderInfoBlock.getOrderInfoContents().get(i));
                    if (item1 != null) {
                        blockLinearLayout.addView(item1);
                        hasChild = true;
                    }
                }
            }
        } else if (TextUtils.equals(BLOCK_TYPE_2, orderInfoBlock.getBlockType())) {
            if (orderInfoBlock.getOrderInfoContents() != null) {
                for (int i = 0; i < orderInfoBlock.getOrderInfoContents().size(); i++) {
                    ViewGroup item2 = getBlockItem2(context, orderInfoBlock.getOrderInfoContents().get(i));
                    if (item2 != null) {
                        blockLinearLayout.addView(item2);
                        hasChild = true;
                    }
                }
            }
        }
        if (!hasChild) {
            return null;
        }
        return blockLinearLayout;
    }

    private static ViewGroup getBlockItem1(Context context, OrderInfoContent orderInfoContent) {
        if (orderInfoContent == null) {
            return null;
        }
        @SuppressLint("InflateParams")
        ViewGroup blockItem1 = (ViewGroup) LayoutInflater.from(context).inflate(R.layout.cashier__order_info_block_item_1, null);
        TextView itemContent = (TextView) blockItem1.findViewById(R.id.type_1_item_content);
        if (!TextUtils.isEmpty(orderInfoContent.getItemName())) {
            itemContent.setText(orderInfoContent.getItemName());
        } else {
            return null;
        }
        return blockItem1;
    }

    private static ViewGroup getBlockItem2(Context context, OrderInfoContent orderInfoContent) {
        if (orderInfoContent == null) {
            return null;
        }
        boolean bothNull = true;
        @SuppressLint("InflateParams")
        ViewGroup blockItem2 = (ViewGroup) LayoutInflater.from(context).inflate(R.layout.cashier__order_info_block_item_2, null);
        TextView itemContentKey = (TextView) blockItem2.findViewById(R.id.type_2_item_content_key);
        TextView itemContentValue = (TextView) blockItem2.findViewById(R.id.type_2_item_content_value);
        if (!TextUtils.isEmpty(orderInfoContent.getItemName())) {
            itemContentKey.setText(orderInfoContent.getItemName());
            bothNull = false;
        }
        if (!TextUtils.isEmpty(orderInfoContent.getItemValue())) {
            itemContentValue.setText(orderInfoContent.getItemValue());
            bothNull = false;
        }
        if (bothNull) {
            return null;
        }
        //增加无障碍朗读
        blockItem2.setContentDescription(orderInfoContent.getItemName() + orderInfoContent.getItemValue());
        return blockItem2;
    }

    private static View getDividerImage(Context context) {
        @SuppressLint("InflateParams")
        View view = LayoutInflater.from(context).inflate(R.layout.cashier__order_info_divider, null);
        return view;
    }
}
