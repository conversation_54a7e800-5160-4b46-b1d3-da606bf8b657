package com.meituan.android.cashier.model.bean;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;
import java.util.List;

@JsonBean
public class BankListPage implements Serializable {

    private static final long serialVersionUID = 6013851409488820364L;

    @SerializedName("page_title")
    private String pageTitle;

    @SerializedName("pay_button")
    private String payButton;

    @SerializedName("total_fee")
    private float totalFee;

    @SerializedName("banklist")
    private List<DCEPPayment> paymentList;

    public String getPageTitle() {
        return pageTitle;
    }

    public void setPageTitle(String pageTitle) {
        this.pageTitle = pageTitle;
    }

    public List<DCEPPayment> getPaymentList() {
        CollectionUtils.removeNullElement(paymentList);
        return paymentList;
    }

    public void setPaymentList(List<DCEPPayment> paymentList) {
        this.paymentList = paymentList;
    }

    public String getPayButton() {
        return payButton;
    }

    public void setPayButton(String payButton) {
        this.payButton = payButton;
    }

    public float getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(float totalFee) {
        this.totalFee = totalFee;
    }
}
