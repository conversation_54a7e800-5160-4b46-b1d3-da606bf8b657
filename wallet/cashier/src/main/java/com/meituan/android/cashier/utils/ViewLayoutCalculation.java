package com.meituan.android.cashier.utils;

import android.graphics.Rect;
import android.support.annotation.NonNull;
import android.view.View;

import com.meituan.android.neohybrid.util.gson.GsonProvider;

/**
 * <AUTHOR>
 * 用于计算View在布局上的各种属性。
 * 包括展示在屏幕上的绝对坐标：left,top,right,bottom
 * view自身的属性：width，height
 * view的可见属性：visibleRate：-1，完全不可见；>0，可见率；1，完全可见
 */
public final class ViewLayoutCalculation {
    private static final ViewLayoutCalculation ERROR = new ViewLayoutCalculation(-1, -1, -1, -1, -1, -1, -1f);
    public final int left;
    public final int top;
    public final int right;
    public final int bottom;
    public final int width;
    public final int height;
    public final float visibleRate;

    public ViewLayoutCalculation(int left, int top, int right, int bottom, int width, int height, float visibleRate) {
        this.left = left;
        this.top = top;
        this.right = right;
        this.bottom = bottom;
        this.width = width;
        this.height = height;
        this.visibleRate = visibleRate;
    }

    public static ViewLayoutCalculation location(View view) {
        if (view == null) {
            return ERROR;
        }
        int[] location = new int[2];
        view.getLocationOnScreen(location);
        int left = location[0];
        int top = location[1];
        int right = left + view.getWidth();
        int bottom = top + view.getHeight();
        int width = view.getWidth();
        int height = view.getHeight();
        return new ViewLayoutCalculation(left, top, right, bottom, width, height, -1f);
    }

    public static ViewLayoutCalculation visible(View view) {
        if (view == null) {
            return ERROR;
        }
        Rect rect = new Rect();
        view.getLocalVisibleRect(rect);
        float visibleRate = rect.top == 0 ? (float) rect.bottom / view.getHeight() : -1f;
        return new ViewLayoutCalculation(-1, -1, -1, -1, -1, -1, visibleRate);
    }

    public static ViewLayoutCalculation locationAndVisible(View view) {
        if (view == null) {
            return ERROR;
        }
        ViewLayoutCalculation location = location(view);
        ViewLayoutCalculation visible = visible(view);

        return new ViewLayoutCalculation(
                location.left,
                location.top,
                location.right,
                location.bottom,
                location.width,
                location.height,
                visible.visibleRate);
    }

    /**
     * targetAttr在comparedAttr中的展示率
     *
     * @param targetAttr          目标view属性
     * @param comparedAttr        被比较的view属性
     * @return -1：没有关系；>=0: 展示率，最大值为 1f
     */
    public static float visibleRateIn(ViewLayoutCalculation targetAttr, ViewLayoutCalculation comparedAttr) {
        // 二者布局没有相交，二者之间没有关系
        if (targetAttr == null || comparedAttr == null
                || targetAttr.top >= comparedAttr.bottom || targetAttr.bottom <= comparedAttr.top) {
            return -1;
        }
        // 完全在被比较的view中显示。
        float visibleRate = 1f;
        if (targetAttr.top <= comparedAttr.top && targetAttr.bottom <= comparedAttr.bottom) {
            // 两布局相交，且目标view在被比较view上方
            visibleRate = (float) (targetAttr.bottom - comparedAttr.top) / targetAttr.height;
        } else if (targetAttr.top > comparedAttr.top && targetAttr.bottom > comparedAttr.bottom) {
            // 两布局相交，且目标view在被比较view下方
            visibleRate = (float) (comparedAttr.bottom - targetAttr.top) / targetAttr.height;
        }
        return visibleRate;
    }

    @NonNull
    @Override
    public String toString() {
        return GsonProvider.getInstance().toJson(this);
    }
}
