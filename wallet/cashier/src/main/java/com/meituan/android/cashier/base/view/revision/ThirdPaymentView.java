package com.meituan.android.cashier.base.view.revision;

import android.content.Context;
import android.support.v4.content.ContextCompat;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;

import com.meituan.android.cashier.R;
import com.meituan.android.pay.desk.payment.view.BasePaymentView;
import com.meituan.android.paycommon.lib.abtest.CommonABTestManager;

/**
 * author: luo jing
 * date: 2019-09-04 16:31
 * description: 第三方支付支付方式视图
 */
public class ThirdPaymentView extends BasePaymentView {

    private boolean isShowDivider = true;
    private String noPromoInfo;


    public ThirdPaymentView(Context context) {
        super(context);
    }

    public ThirdPaymentView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    protected boolean isShowDividerLine() {
        return isShowDivider();
    }

    public void setNoPromoInfo(String noPromoInfo) {
        this.noPromoInfo = noPromoInfo;
    }

    @Override
    protected void initAttachContent() {
        super.initAttachContent();
        if (CommonABTestManager.showPromoInfo()) {
            if (TextUtils.isEmpty(noPromoInfo)) {
                attachContent.setVisibility(View.GONE);
            } else {
                attachContent.setVisibility(View.VISIBLE);
                attachContent.setTextColor(ContextCompat.getColor(getContext(), R.color.cashier__third_payment_discount_tag_color));
                attachContent.setText(noPromoInfo);
            }
        } else {
            attachContent.setVisibility(View.GONE);
        }
    }

    public boolean isShowDivider() {
        return isShowDivider;
    }

    public void setShowDivider(boolean showDivider) {
        isShowDivider = showDivider;
    }
}