package com.meituan.android.cashier.widget;

import static com.meituan.android.cashier.retrofit.CashierRequestUtils.getWalletPayment;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Typeface;
import android.os.Bundle;
import android.os.Parcelable;
import android.support.annotation.Nullable;
import android.support.v4.app.Fragment;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.meituan.android.cashier.R;
import com.meituan.android.cashier.base.utils.CashierAnalyseUtils;
import com.meituan.android.cashier.base.view.revision.ThirdPaymentView;
import com.meituan.android.cashier.common.CashierStaticsUtils;
import com.meituan.android.cashier.fragment.MTCashierRevisionFragment;
import com.meituan.android.cashier.model.bean.Cashier;
import com.meituan.android.cashier.model.bean.CashierPayment;
import com.meituan.android.pay.common.payment.bean.DeductSwitchDiscount;
import com.meituan.android.pay.common.payment.bean.FinanceServiceBean;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.pay.common.payment.bean.installment.Installment;
import com.meituan.android.pay.common.payment.bean.installment.Period;
import com.meituan.android.pay.common.payment.data.IPaymentData;
import com.meituan.android.pay.common.payment.utils.PayTypeUtils;
import com.meituan.android.pay.desk.pack.IPaymentInnerClick;
import com.meituan.android.pay.desk.pack.WalletPayArea;
import com.meituan.android.pay.desk.payment.IDeductSwitchView;
import com.meituan.android.pay.desk.payment.IPaymentView;
import com.meituan.android.pay.desk.payment.discount.DiscountCashierUtils;
import com.meituan.android.pay.desk.payment.view.CreditPaymentView;
import com.meituan.android.pay.desk.payment.view.MTPaymentBackgroundView;
import com.meituan.android.paybase.common.analyse.AnalyseUtils;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.MTPayNeedToPersist;
import com.meituan.android.paycommon.lib.widgets.ExtendableVerticalLinearLayout;
import com.meituan.android.paycommon.lib.widgets.NoDuplicateClickListener;

import java.util.HashMap;
import java.util.List;

/**
 * 最多渲染四个区域，分别是标准收银台的【支付方式标题区】、【支付方式区】、【金融服务标题区】和【金融服务区】
 * 【支付方式区】包含【美团支付区】、【第三方支付区】和【支付方式折叠区】
 * 【金融服务区】包含【美团月付区】和【金融服务折叠区】
 */
public class NativeStandardCashierAreaView extends LinearLayout {
    private static final String PARENT_STATE = "NativeStandardCashierAreaView_state";
    private static final String TAG_PAYMENT_AREA_FOLDED = "tag_payment_area_folded";
    private static final String TAG_FINANCE_AREA_FOLDED = "tag_finance_area_folded";
    private static final String TAG_AREA_MT = "tag_area_mt";
    private static final String TAG_AREA_THIRD_PAY = "tag_area_third_pay";
    private OnThirdPaymentClickListener mOnThirdPaymentClickListener;
    private OnCreditClickListener mOnCreditClickListener;
    private IPaymentInnerClick mMTPaymentInnerClick;
    private IPaymentInnerClick creditInnerClick;
    private WalletPayArea.OnPaymentClickListener mOnMTPaymentClickListener;

    @MTPayNeedToPersist
    private LinearLayout financeAreaView;

    public NativeStandardCashierAreaView(Context context) {
        super(context);
        init();
    }

    public NativeStandardCashierAreaView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public NativeStandardCashierAreaView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        setOrientation(LinearLayout.VERTICAL);
    }

    public void refreshPaymentView(IPaymentData checkedPaymentData, Cashier cashier) {
        for (int i = 0; i < getChildCount(); i++) {
            ViewGroup area = (ViewGroup) getChildAt(i);
            refreshInnerPayment(checkedPaymentData, area, cashier);
        }
    }

    private void refreshInnerPayment(IPaymentData checkedPaymentData, ViewGroup viewGroup, Cashier cashier) {
        int childCount = viewGroup.getChildCount();
        for (int i = 0; i < childCount; i++) {
            View view = viewGroup.getChildAt(i);
            if (view instanceof IPaymentView && checkedPaymentData != null) {
                ((IPaymentView) view).refreshView(checkedPaymentData);
            } else if (view instanceof IDeductSwitchView && checkedPaymentData != null) {
                DeductSwitchDiscount discount = DiscountCashierUtils.getCashierDeductSwitchDiscount(cashier.getTotalFee(),
                        getWalletPayment(cashier), checkedPaymentData);
                ((IDeductSwitchView) view).refreshView(discount);
            } else if (view instanceof LinearLayout) {
                refreshInnerPayment(checkedPaymentData, (LinearLayout) view, cashier);
            }
        }
    }

    public void init(Cashier cashier, MTCashierRevisionFragment mtCashierRevisionFragment) {
        if (cashier == null) {
            return;
        }
        removeAllViews();
        // 初始化【支付方式标题区】和【支付方式区】
        if (!CollectionUtils.isEmpty(cashier.getPaymentDataList())) {
            String payTitle = getResources().getString(R.string.cashier__payment_title);
            if (!TextUtils.isEmpty(cashier.getPayTitle())) {
                payTitle = cashier.getPayTitle();
            }
            initTitleView(payTitle, mtCashierRevisionFragment);
            initPaymentAreaView(cashier.getPaymentDataList(), mtCashierRevisionFragment, payTitle);
        }
        // 初始化【金融服务标题区】和【金融服务区】
        if (!CollectionUtils.isEmpty(cashier.getFinanceDataList())) {
            String financeTitle = getResources().getString(R.string.cashier__finance_title);
            if (!TextUtils.isEmpty(cashier.getFinanceTitle())) {
                financeTitle = cashier.getFinanceTitle();
            }
            String subTitle = null;
            if (cashier.getFinanceDataList().get(0) != null && cashier.getFinanceDataList().get(0).getCreditProductInfo() != null) {
                subTitle = cashier.getFinanceDataList().get(0).getCreditProductInfo().getServiceProviderDesc();
            }
            initFinanceTitleView(financeTitle, subTitle, mtCashierRevisionFragment);
            initFinanceAreaView(cashier.getFinanceDataList(), mtCashierRevisionFragment, financeTitle);
        }
    }

    /**
     * 标题区，【支付方式标题区】
     *
     * @param title                     标题
     * @param mtCashierRevisionFragment 页面
     */
    private void initTitleView(String title, MTCashierRevisionFragment mtCashierRevisionFragment) {
        LinearLayout linearLayout = new LinearLayout(getContext());
        TextView titleView = new TextView(mtCashierRevisionFragment.getContext());
        titleView.setText(title);
        titleView.setTextColor(getResources().getColor(R.color.cashier__title));
        titleView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
        titleView.setTypeface(Typeface.DEFAULT_BOLD);
        LayoutParams layoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.leftMargin = getResources().getDimensionPixelOffset(R.dimen.cashier___pay_type_card_margin_12);
        linearLayout.addView(titleView, layoutParams);
        addView(linearLayout);
    }

    /**
     * 【金融服务副标题区】
     *
     * @param title
     * @param subTitle
     * @param mtCashierRevisionFragment
     */

    private void initFinanceTitleView(String title, String subTitle, MTCashierRevisionFragment mtCashierRevisionFragment) {
        LinearLayout linearLayout = new LinearLayout(getContext());
        LayoutParams layoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        linearLayout.setOrientation(HORIZONTAL);
        linearLayout.setLayoutParams(layoutParams);
        TextView titleView = new TextView(mtCashierRevisionFragment.getContext());
        titleView.setText(title);
        titleView.setTextColor(getResources().getColor(R.color.cashier__title));
        titleView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
        titleView.setTypeface(Typeface.DEFAULT_BOLD);
        TextView subTitleView = new TextView(mtCashierRevisionFragment.getContext());
        subTitleView.setText(subTitle);
        subTitleView.setTextColor(getResources().getColor(R.color.cashier__sub_title));
        subTitleView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);
        subTitleView.setEllipsize(TextUtils.TruncateAt.END);
        subTitleView.setSingleLine(true);
        subTitleView.setMaxLines(1);
        LayoutParams titleLayoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        titleLayoutParams.leftMargin = getResources().getDimensionPixelOffset(R.dimen.cashier___pay_type_card_margin_12);
        LayoutParams subTitleLayoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        subTitleLayoutParams.leftMargin = getResources().getDimensionPixelOffset(R.dimen.cashier___finance_subtitle_margin_12);
        linearLayout.addView(titleView, titleLayoutParams);
        if (!TextUtils.isEmpty(subTitle)) {
            linearLayout.addView(subTitleView, subTitleLayoutParams);
        }
        addView(linearLayout);
    }

    /**
     * @param cashierPayments           所有支付方式
     * @param mtCashierRevisionFragment 页面
     * @param payTitle                  区域标题，用于拼接折叠区的名称
     */
    private void initPaymentAreaView(List<CashierPayment> cashierPayments, MTCashierRevisionFragment mtCashierRevisionFragment, String payTitle) {
        if (CollectionUtils.isEmpty(cashierPayments)) {
            return;
        }
        LinearLayout paymentAreaView = new LinearLayout(getContext());
        ViewGroup prePaymentArea = null;
        for (CashierPayment cashierPayment : cashierPayments) {
            ViewGroup currentAreaView;
            if (cashierPayment.isFolded()) {
                currentAreaView = inflateFoldedPayment(mtCashierRevisionFragment, cashierPayment, payTitle, prePaymentArea, paymentAreaView);
            } else {
                if (PayTypeUtils.isWalletPay(cashierPayment.getPayType())) {
                    currentAreaView = inflateMTPayment(mtCashierRevisionFragment, cashierPayment, prePaymentArea, paymentAreaView);
                } else {
                    currentAreaView = inflateThirdPayment(cashierPayment, prePaymentArea, paymentAreaView);
                }
            }
            prePaymentArea = currentAreaView;
            // 支付方式曝光的旧埋点
            CashierStaticsUtils.reportModelEventWithViewEvent(mtCashierRevisionFragment.getPageName(),
                    "b_3p4zs2ds", getContext().getString(R.string.mpay__mge_act_show_pay_type),
                    CashierAnalyseUtils.reportCashierPayment(cashierPayment), -1, mtCashierRevisionFragment.getUniqueId());
        }
        paymentAreaView.setOrientation(LinearLayout.VERTICAL);
        LayoutParams layoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.topMargin = getResources().getDimensionPixelOffset(R.dimen.cashier___pay_type_card_margin_9);
        layoutParams.bottomMargin = getResources().getDimensionPixelOffset(R.dimen.cashier___pay_type_card_margin_22);
        addView(paymentAreaView, layoutParams);
    }

    /**
     * @param mtCashierRevisionFragment 页面
     * @param prePaymentArea            上个区域
     * @param cashierPayment            支付方式数据结构
     * @param payTitle                  折叠区标题使用"展开更多" + payTitle的字段
     * @return 折叠区
     */
    private ViewGroup inflateFoldedPayment(MTCashierRevisionFragment mtCashierRevisionFragment, CashierPayment cashierPayment, String payTitle,
                                           ViewGroup prePaymentArea, LinearLayout paymentAreaView) {
        // 获取、创建折叠区域
        ExtendableVerticalLinearLayout extendableVerticalLinearLayout;
        if (prePaymentArea == null || !TextUtils.equals((CharSequence) prePaymentArea.getTag(), TAG_PAYMENT_AREA_FOLDED)) {
            extendableVerticalLinearLayout = createFoldedPaymentArea(mtCashierRevisionFragment, payTitle, prePaymentArea, paymentAreaView);
        } else {
            extendableVerticalLinearLayout = (ExtendableVerticalLinearLayout) prePaymentArea;
        }
        // 获取、创建折叠区域中展开后的布局
        LinearLayout extendedView;
        if (extendableVerticalLinearLayout.getExtendedView() == null) {
            extendedView = new LinearLayout(getContext());
            extendedView.setOrientation(LinearLayout.VERTICAL);
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            extendedView.setLayoutParams(params);
            extendableVerticalLinearLayout.setExtendedView(extendedView);
        } else {
            extendedView = (LinearLayout) extendableVerticalLinearLayout.getExtendedView();
        }
        extendedView.addView(inflateThirdPayment(cashierPayment));
        return extendableVerticalLinearLayout;
    }

    private ExtendableVerticalLinearLayout createFoldedPaymentArea(MTCashierRevisionFragment mtCashierRevisionFragment, String payTitle,
                                                                   ViewGroup prePaymentArea, LinearLayout paymentAreaView) {
        ExtendableVerticalLinearLayout extendableVerticalLinearLayout = new ExtendableVerticalLinearLayout(getContext());
        LayoutParams layoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        extendableVerticalLinearLayout.setTag(TAG_PAYMENT_AREA_FOLDED);
        if (prePaymentArea == null || !TextUtils.equals((CharSequence) prePaymentArea.getTag(), TAG_AREA_THIRD_PAY)) {
            extendableVerticalLinearLayout.setFoldedView(createFoldedView(mtCashierRevisionFragment, extendableVerticalLinearLayout, payTitle, true, true));
            layoutParams.topMargin = getResources().getDimensionPixelOffset(R.dimen.cashier___pay_type_card_margin_9);
            layoutParams.leftMargin = getResources().getDimensionPixelOffset(R.dimen.cashier___pay_type_card_margin_12);
            layoutParams.rightMargin = getResources().getDimensionPixelOffset(R.dimen.cashier___pay_type_card_margin_12);
            extendableVerticalLinearLayout.setBackgroundResource(R.drawable.cashier__bg_paytype);
            paymentAreaView.addView(extendableVerticalLinearLayout, layoutParams);
        } else {
            extendableVerticalLinearLayout.setFoldedView(createFoldedView(mtCashierRevisionFragment, extendableVerticalLinearLayout, payTitle, true, false));
            prePaymentArea.addView(extendableVerticalLinearLayout, layoutParams);
        }
        extendableVerticalLinearLayout.foldWithoutAnim();
        return extendableVerticalLinearLayout;
    }

    private View createFoldedView(MTCashierRevisionFragment mtCashierRevisionFragment, ExtendableVerticalLinearLayout extendableVerticalLinearLayout, String title, boolean isPaymentArea, boolean isFullFolded) {
        @SuppressLint("InflateParams")
        View view = LayoutInflater.from(getContext()).inflate(R.layout.cashier__payment_more_view,
                null);
        TextView morePaymentView = view.findViewById(R.id.cashier_more);
        String newTitle = getResources().getString(R.string.cashier__unfold_mt_more_payment2) + title;
        morePaymentView.setText(newTitle);
        if (isFullFolded) {
            ViewGroup.LayoutParams layoutParams = morePaymentView.getLayoutParams();
            layoutParams.height = 135;
            morePaymentView.setLayoutParams(layoutParams);
            if (!isPaymentArea) {
                morePaymentView.setTextSize(16);
                morePaymentView.setTextColor(getResources().getColor(R.color.cashier__black6));
            }
        }
        ImageView moreArrowView = view.findViewById(R.id.cashier_more_arrow);
        // 月付断直连需求新埋点上报参数，page_style = 1代表新样式，native只有新样式
        HashMap<String, Object> reportParams = new AnalyseUtils.MapBuilder().add("page_style", "1").add("utm_source", "-999").build();
        if (isPaymentArea) {
            // 月付断直连需求新埋点-展开三方支付组件按钮曝光事件
            CashierStaticsUtils.reportModelEventWithViewEvent("c_PJmoK", "b_pay_n96iqp1l_mv",
                    getResources().getString(R.string.cashier__unfold_mt_more_payment_mv), reportParams, mtCashierRevisionFragment.getUniqueId());
        }
        view.setOnClickListener(new NoDuplicateClickListener() {
            @Override
            public void onSingleClick(View v) {
                // 【展开更多支付方式】埋点
                if (isPaymentArea) {
                    // 旧埋点
                    AnalyseUtils.logModelEvent("b_zP3hQ", getResources().getString(R.string.cashier__unfold_mt_more_payment_mc),
                            new AnalyseUtils.MapBuilder().add("IS_BOTTOM", "TRUE").build(),
                            AnalyseUtils.EventType.CLICK, -1);
                    CashierStaticsUtils.reportSystemCheck(mtCashierRevisionFragment.getPageName(), "b_v6xIt",
                            new AnalyseUtils.InstantReportBuilder().addTradeNo().build(), mtCashierRevisionFragment.getUniqueId());
                    // 月付断直连需求新埋点-展开三方支付组件按钮点击事件
                    CashierStaticsUtils.reportModelEventWithClickEvent("c_PJmoK", "b_pay_n96iqp1l_mc",
                            getResources().getString(R.string.cashier__unfold_mt_more_payment_mc), reportParams, mtCashierRevisionFragment.getUniqueId());
                }

                extendableVerticalLinearLayout.extend(200, new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationStart(Animator animation) {
                        super.onAnimationStart(animation);
                        if (mtCashierRevisionFragment.getView() != null) {
                            NSCScrollView scrollView = mtCashierRevisionFragment.getView().findViewById(R.id.cashier_scroll_layout);
                            if (scrollView != null) {
                                scrollView.setScrollable(false);
                            }
                        }
                    }

                    @Override
                    public void onAnimationEnd(Animator animation) {
                        super.onAnimationEnd(animation);
                        if (mtCashierRevisionFragment.getView() != null) {
                            NSCScrollView scrollView = mtCashierRevisionFragment.getView().findViewById(R.id.cashier_scroll_layout);
                            if (scrollView != null) {
                                scrollView.setScrollable(true);
                            }
                        }
                    }
                });
            }
        });
        RelativeLayout.LayoutParams moreArrowLayoutParams = (RelativeLayout.LayoutParams) moreArrowView.getLayoutParams();
        moreArrowLayoutParams.addRule(RelativeLayout.RIGHT_OF, R.id.cashier_more);
        return view;
    }

    // 初始化【美团支付区】
    private MTPaymentBackgroundView inflateMTPayment(MTCashierRevisionFragment mtCashierRevisionFragment, CashierPayment cashierPayment,
                                                     ViewGroup prePaymentArea, LinearLayout paymentAreaView) {
        MTPaymentBackgroundView mtPaymentArea = createMTPaymentArea(prePaymentArea, paymentAreaView);
        mtPaymentArea.init(cashierPayment);
        WalletPayArea payAreaView = new WalletPayArea(cashierPayment);
        LinearLayout walletView = payAreaView.initPayment(mtCashierRevisionFragment, mtCashierRevisionFragment.getUniqueId());
        walletView.setTag("walletView");
        mtPaymentArea.addView(walletView);
        payAreaView.setPaymentClick(selectedPayment -> {
            if (mOnMTPaymentClickListener != null) {
                mOnMTPaymentClickListener.onPaymentClick(selectedPayment);
            }
        });
        payAreaView.setPaymentInnerClick(new IPaymentInnerClick() {
            @Override
            public void onClickAllPayment(View view) {
                if (mMTPaymentInnerClick != null) {
                    mMTPaymentInnerClick.onClickAllPayment(view);
                }
            }

            @Override
            public void onClickPointSwitch(IPaymentData iPaymentData, CompoundButton buttonView, boolean isChecked) {
                if (mMTPaymentInnerClick != null) {
                    mMTPaymentInnerClick.onClickPointSwitch(iPaymentData, buttonView, isChecked);
                }
            }

            @Override
            public void onUpdateAgreement(View view, CompoundButton buttonView, boolean isChecked) {
                if (mMTPaymentInnerClick != null) {
                    mMTPaymentInnerClick.onUpdateAgreement(view, buttonView, isChecked);
                }
            }

            @Override
            public void onChangeCombineBank(View view) {
                if (mMTPaymentInnerClick != null) {
                    mMTPaymentInnerClick.onChangeCombineBank(view);
                }
            }

            @Override
            public void onClickNewCardAd(View view) {
                if (mMTPaymentInnerClick != null) {
                    mMTPaymentInnerClick.onClickNewCardAd(view);
                }
            }

            @Override
            public void onClickDeductSwitch(View view, CompoundButton buttonView, boolean isChecked) {
                if (mMTPaymentInnerClick != null) {
                    mMTPaymentInnerClick.onClickDeductSwitch(view, buttonView, isChecked);
                }
            }

            @Override
            public void onClickPeriodItem(MTPayment mtPayment) {
                if (mMTPaymentInnerClick != null) {
                    mMTPaymentInnerClick.onClickPeriodItem(mtPayment);
                }
            }
        });
        return mtPaymentArea;
    }

    // 创建美团支付卡片
    private MTPaymentBackgroundView createMTPaymentArea(ViewGroup prePaymentArea, LinearLayout paymentAreaView) {
        MTPaymentBackgroundView mtPaymentBackgroundView = new MTPaymentBackgroundView(getContext());
        mtPaymentBackgroundView.setOrientation(LinearLayout.VERTICAL);
        LayoutParams layoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        if (prePaymentArea != null) {
            layoutParams.topMargin = getResources().getDimensionPixelOffset(R.dimen.cashier___pay_type_card_margin_9);
        }
        mtPaymentBackgroundView.setTag(TAG_AREA_MT);
        paymentAreaView.addView(mtPaymentBackgroundView, layoutParams);
        return mtPaymentBackgroundView;
    }

    // 初始化【三方支付】的View
    private ThirdPaymentView inflateThirdPayment(CashierPayment cashierPayment) {
        ThirdPaymentView paymentView = new ThirdPaymentView(getContext());
        paymentView.setTag("ThirdPaymentView");
        paymentView.setNoPromoInfo(cashierPayment.getNoPromoInfo());
        paymentView.setShowDivider(true);
        paymentView.init(cashierPayment);
        paymentView.setOnClickListener(view -> {
            if (mOnThirdPaymentClickListener != null) {
                mOnThirdPaymentClickListener.onPaymentClick(cashierPayment);
            }
        });
        return paymentView;
    }

    // 初始化【三方支付】的View 2.0版本
    private ViewGroup inflateThirdPayment(CashierPayment cashierPayment, ViewGroup prePaymentArea, LinearLayout paymentAreaView) {
        LinearLayout linearLayout;
        if (prePaymentArea == null || !TextUtils.equals((CharSequence) prePaymentArea.getTag(), TAG_AREA_THIRD_PAY)) {
            linearLayout = createThirdPaymentArea(prePaymentArea, paymentAreaView);
        } else {
            linearLayout = (LinearLayout) prePaymentArea;
        }
        linearLayout.addView(inflateThirdPayment(cashierPayment));
        return linearLayout;
    }

    // 创建第三方支付方式卡片
    private LinearLayout createThirdPaymentArea(ViewGroup prePaymentArea, LinearLayout paymentAreaView) {
        LinearLayout linearLayout = new LinearLayout(getContext());
        linearLayout.setOrientation(LinearLayout.VERTICAL);
        linearLayout.setBackgroundResource(R.drawable.cashier__bg_paytype);
        linearLayout.setTag(TAG_AREA_THIRD_PAY);
        LayoutParams layoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        int margin = getResources().getDimensionPixelOffset(R.dimen.cashier___pay_type_card_margin_12);
        layoutParams.leftMargin = margin;
        layoutParams.rightMargin = margin;
        if (prePaymentArea != null) {
            layoutParams.topMargin = getResources().getDimensionPixelOffset(R.dimen.cashier___pay_type_card_margin_9);
        }
        paymentAreaView.addView(linearLayout, layoutParams);
        return linearLayout;
    }

    // 初始化金融服务区
    private void initFinanceAreaView(List<FinanceServiceBean> financeServiceBeans, MTCashierRevisionFragment mtCashierRevisionFragment, String financeTitle) {
        if (CollectionUtils.isEmpty(financeServiceBeans)) {
            return;
        }
        financeAreaView = new LinearLayout(getContext());
        ViewGroup preFinanceArea = null;
        for (FinanceServiceBean financeServiceBean : financeServiceBeans) {
            ViewGroup currentFinanceArea;
            if (financeServiceBean.isFolded()) {
                currentFinanceArea = inflateFoldedFinance(mtCashierRevisionFragment, preFinanceArea, financeServiceBean, financeTitle);
            } else {
                currentFinanceArea = inflateCreditPayView(mtCashierRevisionFragment, financeServiceBean, null);
            }
            if (currentFinanceArea.getParent() != null) {
                ((ViewGroup) currentFinanceArea.getParent()).removeView(currentFinanceArea);
            }
            financeAreaView.addView(currentFinanceArea);
            preFinanceArea = currentFinanceArea;
        }

        financeAreaView.setOrientation(LinearLayout.VERTICAL);
        financeAreaView.setBackgroundResource(R.drawable.cashier__bg_paytype);
        int margin = getResources().getDimensionPixelOffset(R.dimen.cashier___pay_type_card_margin_12);
        LayoutParams layoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.leftMargin = margin;
        layoutParams.rightMargin = margin;
        layoutParams.topMargin = getResources().getDimensionPixelOffset(R.dimen.cashier___pay_type_card_margin_9);
        layoutParams.bottomMargin = getResources().getDimensionPixelOffset(R.dimen.cashier___pay_type_card_margin_22);
        addView(financeAreaView, layoutParams);
    }

    public LinearLayout getFinanceAreaView() {
        return financeAreaView;
    }

    private ViewGroup inflateFoldedFinance(MTCashierRevisionFragment mtCashierRevisionFragment, ViewGroup preFinanceArea, FinanceServiceBean financeServiceBean, String financeTitle) {
        // 获取、创建折叠区域
        ExtendableVerticalLinearLayout extendableVerticalLinearLayout;
        if (preFinanceArea == null || !TextUtils.equals((CharSequence) preFinanceArea.getTag(), TAG_FINANCE_AREA_FOLDED)) {
            extendableVerticalLinearLayout = createFoldedFinanceArea(mtCashierRevisionFragment, preFinanceArea, financeTitle);
        } else {
            extendableVerticalLinearLayout = (ExtendableVerticalLinearLayout) preFinanceArea;
        }
        // 获取、创建折叠区域中展开后的布局
        LinearLayout extendedView;
        if (extendableVerticalLinearLayout.getExtendedView() == null) {
            extendedView = new LinearLayout(getContext());
            extendedView.setOrientation(LinearLayout.VERTICAL);
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            extendedView.setLayoutParams(params);
            extendableVerticalLinearLayout.setExtendedView(extendedView);
        } else {
            extendedView = (LinearLayout) extendableVerticalLinearLayout.getExtendedView();
        }
        extendedView.addView(inflateCreditPayView(mtCashierRevisionFragment, financeServiceBean, extendableVerticalLinearLayout));
        return extendableVerticalLinearLayout;
    }

    private ExtendableVerticalLinearLayout createFoldedFinanceArea(MTCashierRevisionFragment mtCashierRevisionFragment, ViewGroup preFinanceArea, String financeTitle) {
        ExtendableVerticalLinearLayout extendableVerticalLinearLayout = new ExtendableVerticalLinearLayout(getContext());
        LayoutParams layoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        extendableVerticalLinearLayout.setTag(TAG_FINANCE_AREA_FOLDED);
        if (preFinanceArea == null) {
            extendableVerticalLinearLayout.setFoldedView(createFoldedView(mtCashierRevisionFragment, extendableVerticalLinearLayout, financeTitle, false, true));
        } else {
            extendableVerticalLinearLayout.setFoldedView(createFoldedView(mtCashierRevisionFragment, extendableVerticalLinearLayout, financeTitle, false, false));
            preFinanceArea.addView(extendableVerticalLinearLayout, layoutParams);
        }
        extendableVerticalLinearLayout.foldWithoutAnim();
        return extendableVerticalLinearLayout;
    }

    /**
     * 月付组件
     */
    private CreditPaymentView inflateCreditPayView(Fragment fragment, FinanceServiceBean financeServiceBean, ExtendableVerticalLinearLayout extendableVerticalLinearLayout) {
        AnalyseUtils.techMis("b_pay_cdj00em9_mv", new AnalyseUtils.MapBuilder()
                .add("is_support_period", financeServiceBean.getIsSupportInstallment()).build());
        CreditPaymentView creditPaymentView = new CreditPaymentView(fragment.getContext());
        creditPaymentView.setActivity(fragment.getActivity());
        creditPaymentView.init(financeServiceBean);
        creditPaymentView.setOnUpdateChangeListener((buttonView, isChecked) -> {
            if (financeServiceBean.getUpdateAgreement() != null) {
                financeServiceBean.getUpdateAgreement().setIsChecked(isChecked);
            }
            if (creditInnerClick != null) {
                creditInnerClick.onUpdateAgreement(creditPaymentView, buttonView, isChecked);
            }
        });
        creditPaymentView.setOnClickPeriodItemListener((parent, view, position, id) -> {
            // 埋点字段
            int period = -1;
            Installment installment = financeServiceBean.getInstallment();
            if (installment != null && !CollectionUtils.isEmpty(installment.getPeriodList())) {
                Period clickPeriod = installment.getPeriodList().get(position);
                if (clickPeriod != null) {
                    savePeriodSelectedStatus(clickPeriod, installment.getPeriodList());
                    creditPaymentView.refreshView(financeServiceBean);
                    period = clickPeriod.getPeriod();
                }
                if (creditInnerClick != null) {
                    creditInnerClick.onClickPeriodItem(financeServiceBean);
                }
                AnalyseUtils.techMis("b_pay_u5r394f2_mc", new AnalyseUtils.MapBuilder()
                        .add("choose_period", String.valueOf(period))
                        .add("pay_type", financeServiceBean.getPayType())
                        .build());
            }
        });
        creditPaymentView.setOnClickListener(view -> {
            if (mOnCreditClickListener != null) {
                mOnCreditClickListener.onCreditClick(financeServiceBean);
                if (extendableVerticalLinearLayout != null) {
                    LayoutParams layoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                            ViewGroup.LayoutParams.WRAP_CONTENT);
                    extendableVerticalLinearLayout.setLayoutParams(layoutParams);
                }
            }
        });
        return creditPaymentView;
    }

    private void savePeriodSelectedStatus(Period checkedPeriod, List<Period> periods) {
        if (CollectionUtils.isEmpty(periods)) {
            return;
        }
        for (Period period : periods) {
            period.setSelected(period == checkedPeriod);
        }
    }

    @Override
    protected Parcelable onSaveInstanceState() {
        // 保存ParentState
        Bundle state = new Bundle();
        Parcelable superState = super.onSaveInstanceState();
        state.putParcelable(PARENT_STATE, superState);
        return state;
    }

    @Override
    protected void onRestoreInstanceState(Parcelable state) {
        Bundle bundle = (Bundle) state;
        // 恢复Parent state
        Parcelable parentState = bundle.getParcelable(PARENT_STATE);
        super.onRestoreInstanceState(parentState);
    }

    public void setOnThirdPaymentClickListener(@Nullable OnThirdPaymentClickListener clickListener) {
        this.mOnThirdPaymentClickListener = clickListener;
    }

    public void setOnCreditClickListener(@Nullable OnCreditClickListener creditService) {
        this.mOnCreditClickListener = creditService;
    }


    public void setOnMTPaymentClick(WalletPayArea.OnPaymentClickListener onPaymentClickListener) {
        this.mOnMTPaymentClickListener = onPaymentClickListener;
    }

    public void setMTPaymentInnerClick(IPaymentInnerClick innerClick) {
        this.mMTPaymentInnerClick = innerClick;
    }

    public void setCreditInnerClick(IPaymentInnerClick creditInnerClick) {
        this.creditInnerClick = creditInnerClick;
    }

    public interface OnThirdPaymentClickListener {
        void onPaymentClick(CashierPayment cashierPayment);
    }

    public interface OnCreditClickListener {
        void onCreditClick(FinanceServiceBean creditService);
    }
}
