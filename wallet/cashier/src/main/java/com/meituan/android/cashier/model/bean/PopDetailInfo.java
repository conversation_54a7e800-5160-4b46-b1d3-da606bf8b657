package com.meituan.android.cashier.model.bean;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.pay.common.payment.bean.MTPayment;
import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;

@JsonBean
public class PopDetailInfo implements Serializable {

    private static final long serialVersionUID = 6505079290827443567L;

    // 11.6.1版本已下线
    public static final String RED_ENVELOPE_STYLE = "red_envelope_style";
    public static final String FUNCTION_STYLE = "function_style";

    // 11.6.1版本已下线
    public static final String CREDITPAY_STYLE = "creditpay_style";
    public static final String FUNCTION_SINGED_STYLE= "function_signed_style";
    public static final String COMMON_STYLE= "common_style";

    private String style;

    @SerializedName("detail")
    private String detail;

    @SerializedName("lbtn")
    private String leftBtn;

    @SerializedName("rbtn")
    private String rightBtn;

    @SerializedName("redirect_url")
    private String redirectUrl; // 点击右侧按钮后的跳转链接

    private String title;

    @SerializedName("guide_button")
    private String guideButton;

    @SerializedName("sub_title")
    private String subtitle;

    @SerializedName("total_promo_money")
    private float promotionMoney;

    @SerializedName("background_image")
    private String backgroundImage;

    @SerializedName("guide_pay_type_info")
    private MTPayment guidePayTypeInfo;

    @SerializedName("popupType")
    private String popupType;
    //弹窗场景，beforePay-支付前弹  interruptPay-支付中断触发弹窗-埋点用，可为空
    private String popScene;

    //引导弹窗5期通用样式弹窗新增字段-营销优惠区域底图
    @SerializedName("marketing_background_image")
    private String marketingBackgroundImage;

    //引导弹窗5期通用样式弹窗新增字段-营销优惠区域主标题
    @SerializedName("marketing_main_title")
    private String marketingMainTitle;

    //引导弹窗5期通用样式弹窗新增字段-营销优惠区域副标题
    @SerializedName("marketing_sub_title")
    private String marketingSubTitle;

    //引导弹窗5期通用样式弹窗新增字段-弹窗Button底图
    @SerializedName("guide_button_background_image")
    private String guideButtonBackgroundImage;

    //引导弹窗5期通用样式弹窗新增字段-广告id，仅埋点使用
    @SerializedName("ad_id")
    private String adId;

    //引导弹窗5期通用样式弹窗新增字段-支付方式后缀
    @SerializedName("payment_suffix")
    private String paymentSuffix;

    public String getPopScene() {
        return popScene;
    }

    public void setPopScene(String popScene) {
        this.popScene = popScene;
    }
    public String getPopupType() {
        return popupType;
    }

    public void setPopupType(String popupType) {
        this.popupType = popupType;
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getLeftBtn() {
        return leftBtn;
    }

    public void setLeftBtn(String leftBtn) {
        this.leftBtn = leftBtn;
    }

    public String getRightBtn() {
        return rightBtn;
    }

    public void setRightBtn(String rightBtn) {
        this.rightBtn = rightBtn;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getGuideButton() {
        return guideButton;
    }

    public void setGuideButton(String guideButton) {
        this.guideButton = guideButton;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public float getPromotionMoney() {
        return promotionMoney;
    }

    public void setPromotionMoney(float promotionMoney) {
        this.promotionMoney = promotionMoney;
    }

    public String getBackgroundImage() {
        return backgroundImage;
    }

    public void setBackgroundImage(String backgroundImage) {
        this.backgroundImage = backgroundImage;
    }

    public MTPayment getGuidePayTypeInfo() {
        return guidePayTypeInfo;
    }

    public void setGuidePayTypeInfo(MTPayment guidePayTypeInfo) {
        this.guidePayTypeInfo = guidePayTypeInfo;
    }

    public String getMarketingBackgroundImage() {
        return marketingBackgroundImage;
    }

    public void setMarketingBackgroundImage(String marketingBackgroundImage) {
        this.marketingBackgroundImage = marketingBackgroundImage;
    }

    public String getMarketingMainTitle() {
        return marketingMainTitle;
    }

    public void setMarketingMainTitle(String marketingMainTitle) {
        this.marketingMainTitle = marketingMainTitle;
    }

    public String getMarketingSubTitle() {
        return marketingSubTitle;
    }

    public void setMarketingSubTitle(String marketingSubTitle) {
        this.marketingSubTitle = marketingSubTitle;
    }

    public String getGuideButtonBackgroundImage() {
        return guideButtonBackgroundImage;
    }

    public void setGuideButtonBackgroundImage(String guideButtonBackgroundImage) {
        this.guideButtonBackgroundImage = guideButtonBackgroundImage;
    }

    public String getAdId(){
        return adId;
    }
    public void setAdId(String adId){
        this.adId = adId;
    }

    public String getPaymentSuffix(){
        return paymentSuffix;
    }
    public void setPaymentSuffix(String paymentSuffix){
        this.paymentSuffix = paymentSuffix;
    }

    /**
     * 校验旧拉新弹窗"sub_title"的完整性，因为旧拉新弹窗除了完整性校验以外，还必传"sub_title"
     * @return 是否显示旧拉新弹窗
     */
    public static boolean allowShowOldDialog(PopDetailInfo popDetailInfo) {
        return popDetailInfo != null && !TextUtils.isEmpty(popDetailInfo.getSubtitle());
    }

    /**
     * 弹窗的展示功能是否可用
     * @return
     */
    public static boolean isDialogValid(PopDetailInfo popDetailInfo) {
        return popDetailInfo != null
                && !TextUtils.isEmpty(popDetailInfo.getDetail())
                && !TextUtils.isEmpty(popDetailInfo.getLeftBtn())
                && !TextUtils.isEmpty(popDetailInfo.getRightBtn())
                && !TextUtils.isEmpty(popDetailInfo.getRedirectUrl());
    }

}
