package com.meituan.android.cashier.mtpay;

import static com.meituan.android.cashier.activity.MTCashierActivity.KEY_INSTALLED_APPS;
import static com.meituan.android.cashier.utils.GoHelloPaySceneUtils.reportMtPaySLAStart;
import static com.meituan.android.pay.common.payment.data.WalletPayParams.KEY_CASHIER_PAY_TOKEN;
import static com.meituan.android.pay.common.payment.data.WalletPayParams.KEY_HYBRID_INFO;
import static com.meituan.android.pay.common.payment.data.WalletPayParams.KEY_MTP_CASHIER_URL;
import static com.meituan.android.pay.common.payment.data.WalletPayParams.KEY_TRADE_NO;
import static com.meituan.android.pay.desk.component.analyse.DeskAnalyseUtils.analysePaySucess;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.support.v4.content.LocalBroadcastManager;
import android.text.TextUtils;
import android.view.View;

import com.meituan.android.cashier.activity.MTCashierActivity;
import com.meituan.android.cashier.bean.CashierParams;
import com.meituan.android.cashier.bean.CashierProductInfo;
import com.meituan.android.cashier.bean.ClientRouterInfoBean;
import com.meituan.android.cashier.bean.ClientRouterParamBean;
import com.meituan.android.cashier.common.CashierUtil;
import com.meituan.android.cashier.model.bean.MTPaymentURL;
import com.meituan.android.cashier.newrouter.remake.CashierRouterAdapter;
import com.meituan.android.cashier.newrouter.remake.CashierRouterConstants;
import com.meituan.android.cashier.newrouter.remake.CashierRouterHelper;
import com.meituan.android.cashier.newrouter.remake.DowngradeInfoHandler;
import com.meituan.android.cashier.retrofit.CashierRequestService;
import com.meituan.android.cashier.retrofit.CashierRequestUtils;
import com.meituan.android.neohybrid.cache.HybridParamsCache;
import com.meituan.android.pay.common.payment.data.WalletPayParams;
import com.meituan.android.pay.model.PayErrorCode;
import com.meituan.android.pay.utils.HybridHalfPageCashierStatics;
import com.meituan.android.paybase.config.PayBaseConfig;
import com.meituan.android.paybase.dialog.ProgressController;
import com.meituan.android.paybase.dialog.ProgressType;
import com.meituan.android.paybase.downgrading.PayHornConfigBean;
import com.meituan.android.paybase.moduleinterface.payment.PayActionListener;
import com.meituan.android.paybase.moduleinterface.payment.PayFailInfo;
import com.meituan.android.paybase.payrouter.constants.RouterAdapterConstants;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.retrofit.PayException;
import com.meituan.android.paybase.utils.Base64;
import com.meituan.android.paybase.utils.CatchException;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.GsonProvider;
import com.meituan.android.paybase.utils.JsonString;
import com.meituan.android.paybase.utils.LocalBroadCastUtil;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paybase.utils.SaveInstanceUtil;
import com.meituan.android.paybase.utils.SdkDataStorageUtils;
import com.meituan.android.paycommon.lib.coupon.model.Promotion;
import com.meituan.android.paycommon.lib.retrofit.PayRetrofit;
import com.meituan.android.paycommon.lib.utils.OuterBusinessParamUtils;
import com.meituan.android.paymentchannel.PayerMediator;
import com.meituan.android.paymentchannel.PayersID;
import com.meituan.android.payrouter.remake.router.adapter.AbstractRouterAdapter;
import com.meituan.android.payrouter.remake.router.data.CheckResult;
import com.meituan.android.payrouter.remake.router.data.InvokeInfo;
import com.meituan.android.payrouter.remake.router.manager.RouterManager;
import com.sankuai.meituan.retrofit2.Call;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 降级场景：
 * 1.当前后台负载过重，无法完成支付降级，overload
 * 2.gohellopay 接口请求失败降级
 * 3.独立收银台渲染失败降级
 * 4.业务决策降级
 * 改动内容：
 * 2023-12-17：
 * 1. 独立收银台加载成功不再由广播实现，是通过Router发送消息，进而触发回调。
 * 2. 固化fingerprint通过async的开关。
 * 3. 加载成功后的背景色，重建场景后的背景色设置
 * 4. enable_degrade & enable_offline_degrade 开关值获取方式，避免了npe。
 * 5. 请求参数的获取方式
 */
@ServiceLoaderInterface(key = RouterAdapterConstants.ROUTER_ADAPTER_MT_HYBRID_HALFPAGE_CASHIER, interfaceClass = AbstractRouterAdapter.class)
public class HybridPrePosedMTCashierRouterAdapter extends CashierRouterAdapter implements PayActionListener, IRequestCallback {
    public static final int CODE_FAIL_REASON_BUSINESS_DOWNGRADE = -111;
    public static final int CODE_FAIL_REASON_LOADING_CANCEL = -112;
    public static final int CODE_FAIL_REASON_RENDER_FAIL = -113;
    public static final int CODE_FAIL_REASON_DEFAULT = 0;

    private static final String TAG = "MTHybridHalfPageCashier";
    private static final String VAL_DOWNGRADE_TO_BUSINESS = "downgrade_to_business";
    private static final String KEY_ACTION = "action";
    private final HybridPrePosedMTCashierConfigManager mHybridPrePosedMTCashierConfigManager = new HybridPrePosedMTCashierConfigManager();
    private MTCashierActivity mCashierActivity;
    private Call<MTPaymentURL> mGoHelloPayCall;
    private ClientRouterInfoBean mClientRouterInfoBean;
    private BroadcastReceiver mBroadcastReceiver;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mCashierActivity = (MTCashierActivity) getActivity();
        mClientRouterInfoBean = mHybridPrePosedMTCashierConfigManager.getHybridPrePosedMTCashierConfig(cashierParams());
    }

    @Override
    public CheckResult check() {
        if (!CashierParams.checkValid(cashierParams())) {
            return CheckResult.fail("001", "cashierParams is null");
        }
        if (mClientRouterInfoBean == null) { // 1.配置为空
            return CheckResult.fail("002", "config is empty");
        }
        String cashierUrl = mClientRouterInfoBean.getCashierUrl();
        if (TextUtils.isEmpty(cashierUrl)) { // 2.页面 url 为空
            return CheckResult.fail("003", "cashier url is empty");
        }
        if (cashierParams().getCashierRouterInfo() == null) { // 跳转参数相关为空
            return CheckResult.fail("004", "cashierRouterInfo is null");
        }
        CashierProductInfo cashierProductInfo = cashierParams().getCashierRouterInfo().getProductInfo();
        if (cashierProductInfo == null || TextUtils.isEmpty(cashierProductInfo.getPath())) { // 跳转参数相关为空
            return CheckResult.fail("005", "cashierRouterInfo.path is null");
        }
        HashMap<String, Object> nestConfigurations = mClientRouterInfoBean.getNestConfigurations();
        if (nestConfigurations == null) { // 3.不存在 nestConfigurations 时，当作不降级处理
            return CheckResult.success();
        }
        boolean enableDegrade = Boolean.parseBoolean(String.valueOf(nestConfigurations.get("enable_degrade")));
        if (enableDegrade) {
            return CheckResult.fail("006", "enable_degrade");
        }
        boolean enableOfflineDegrade = Boolean.parseBoolean(String.valueOf(nestConfigurations.get("enable_offline_degrade")));
        if (enableOfflineDegrade) { // 4.离线包降级开启，不存在匹配的离线包
            if (existOffline(cashierUrl)) {
                return CheckResult.success();
            }
            return CheckResult.fail("007", "offline degrade");
        }
        return CheckResult.success();
    }

    @Override
    public void invoke(InvokeInfo info) {
        super.invoke(info);
        // report
        HybridHalfPageCashierStatics.registerCommonBusinessParams(commonBusinessParams(false), cashierUniqueId());
        HybridHalfPageCashierStatics.onSLAStart(cashierUniqueId());

        CashierProductInfo cashierProductInfo = cashierParams().getCashierRouterInfo().getProductInfo();
        HashMap<String, String> alreadyParams = JsonString.parser(cashierProductInfo.getNextReqParams()).toStringMap();
        // appendGuidePlans
        CashierRequestUtils.appendGuidePlans(alreadyParams, cashierParams().getGuidePlanInfos());
        // appendTransmissionParam 后端不处理此数据，主要是用于传给美团支付侧
        CashierRequestUtils.appendTransmissionParams(alreadyParams, generateMtpHybridInfo());
        // appendExtraParams
        OuterBusinessParamUtils.appendExtraParamsTogoHelloPay(mCashierActivity, alreadyParams);

        // 开关固化，线上适用开关：hybrid_halfcashier_asyncloading_fingerprint_switch，默认为true。
        String fingerprint = HybridParamsCache.getFingerprintFromCache();
        mGoHelloPayCall = PayRetrofit.getInstance().create(CashierRequestService.class, this, MTHalfPageCashierReqTagConstant.REQ_TAG_GO_HELLO_PAY)
                .goHelloPay(cashierProductInfo.getPath(), appendParams(alreadyParams, cashierProductInfo), fingerprint);
        initBroadcastReceiver();

        // report
        HybridHalfPageCashierStatics.reportRequestStart("cashier/gohellopay", "b_pay_cashier_gohellopay_start_sc", null, cashierUniqueId());
        HybridHalfPageCashierStatics.logCustomRequestStart("cashier_gohellopay_start", cashierUniqueId());
    }

    private String getUrlPath(String url) {
        if (url.startsWith("http:") || url.startsWith("https:")) {
            return Uri.parse(url).getPath();
        }
        if (url.contains("?")) {
            return url.substring(0, url.indexOf('?'));
        }
        return url;
    }

    private boolean existOffline(String cashierUrl) {
        ClientRouterParamBean clientRouterParamBean = ClientRouterParamBean.createClientRouterParamBean();
        List<String> offlines = clientRouterParamBean.getGlobalOfflineHybridMtp();
        if (CollectionUtils.isEmpty(offlines)) {
            return false;
        }
        for (String offline : offlines) {
            if (TextUtils.equals(getUrlPath(offline), getUrlPath(cashierUrl))) {
                return true;
            }
        }
        return false;
    }


    /**
     * 独立收银台加载成功后需要设置下背景色
     */
    @Override
    public void onLoadFinished(boolean success) {
        super.onLoadFinished(success);
        CatchException.run(success, () -> {
            View decorView = mCashierActivity.getWindow().getDecorView();
            decorView.setBackgroundColor(Color.parseColor("#********"));
        }).catchForReport("HybridPrePosedMTCashierRouterAdapter_onLoadFinished");
    }

    /**
     * hybrid 独立收银台标准业务参数
     */
    private HashMap<String, Object> commonBusinessParams(boolean isSavedState) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("nb_container", "hybrid");
        final String vRoot = SdkDataStorageUtils.getDataStorageCenter(mCashierActivity).getString(MTCashierActivity.KEY_IS_ROOT, "0");
        hashMap.put("device_rooted", vRoot);
        hashMap.put("is_saved_state", isSavedState);
        if (cashierParams() != null) {
            hashMap.put("trade_no", cashierParams().getTradeNo());
            hashMap.put("merchant_no", cashierParams().getMerchantNo());
        }
        hashMap.put("hybrid_mtpay_verison", getNbHybridVersion());
        String url = getCashierUrl();
        hashMap.put("hybrid_current_url", url);
        hashMap.put("nb_hybrid_version", getNbHybridVersion());
        return hashMap;
    }

    private String generateMtpHybridInfo() {
        JSONObject result = new JSONObject();
        JSONObject mtHybridInfo = new JSONObject();
        try {
            mtHybridInfo.put(KEY_CASHIER_PAY_TOKEN, cashierParams().getPayToken());
            mtHybridInfo.put(KEY_TRADE_NO, cashierParams().getTradeNo());
            mtHybridInfo.put(KEY_MTP_CASHIER_URL, getCashierUrl());
            mtHybridInfo.put("force_enter", false);
            mtHybridInfo.put("app_id", cashierParams().getAppId());
            mtHybridInfo.put("nb_hybrid_version", getNbHybridVersion());
            mtHybridInfo.put("install_app", CashierUtil.getInstalledApps(mCashierActivity));
            final String vRoot = SdkDataStorageUtils.getDataStorageCenter(mCashierActivity).getString(MTCashierActivity.KEY_IS_ROOT, "0");
            mtHybridInfo.put("rooted", vRoot);
            result.put(KEY_HYBRID_INFO, mtHybridInfo);
        } catch (Exception e) {
            LoganUtils.logError("HybridPrePosedMTCashierRouterAdapter_generateMtpHybridInfo", e.getMessage());
        }
        return result.toString();
    }

    private HashMap<String, String> appendParams(HashMap<String, String> alreadyParams, CashierProductInfo cashierProductInfo) {
        if (alreadyParams == null) {
            alreadyParams = new HashMap<>();
        }
        alreadyParams.put("tradeno", cashierParams().getTradeNo());
        alreadyParams.put("pay_token", cashierParams().getPayToken());
        alreadyParams.put(WalletPayParams.KEY_GUIDE_PLAN_INFOS, cashierParams().getGuidePlanInfos());
        alreadyParams.put("nb_hybrid_version", getNbHybridVersion());
        alreadyParams.put("submit_path", cashierProductInfo.getPath());
        alreadyParams.put("nb_container", "hybrid");
        alreadyParams.put(KEY_INSTALLED_APPS, String.valueOf(CashierUtil.getInstalledApps(mCashierActivity)));
        return alreadyParams;
    }

    private String getCashierUrl() {
        if (mClientRouterInfoBean == null) {
            return "";
        }
        String cashierUrl = mClientRouterInfoBean.getCashierUrl();
        if (TextUtils.isEmpty(cashierUrl)) {
            return cashierUrl;
        }
        Uri.Builder uriBuilder = Uri.parse(cashierUrl).buildUpon();
        uriBuilder.appendQueryParameter("app_pay_sdk_version", PayBaseConfig.getProvider().getPayVersion());
        uriBuilder.appendQueryParameter("device_platform", PayBaseConfig.getProvider().getPlatform());
        return uriBuilder.build().toString();
    }

    private String getNbHybridVersion() {
        String cashierUrl = getCashierUrl();
        if (TextUtils.isEmpty(cashierUrl)) {
            return null;
        }
        // 此处的正则表达式和 iOS 端一致
        String patternStr = "v([0-9]+.){2,3}[0-9]+";
        Pattern pattern = Pattern.compile(patternStr);
        Matcher m = pattern.matcher(cashierUrl);
        if (m.find()) {
            String version = m.group(0);
            if (!TextUtils.isEmpty(version)) {
                return version.replace("v", "");
            }
            return version;
        }
        return null;
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        SaveInstanceUtil.saveInstanceOfClass(this, getClass(), outState);
    }

    @Override
    public void onRestoreInstanceState(Bundle outState) {
        HybridHalfPageCashierStatics.registerCommonBusinessParams(commonBusinessParams(true), cashierUniqueId());
    }

    @Override
    public void onDestroy() {
        if (mGoHelloPayCall != null && !mGoHelloPayCall.isCanceled()) {
            mGoHelloPayCall.cancel();
        }
        if (mBroadcastReceiver != null) {
            LocalBroadcastManager.getInstance(mCashierActivity).unregisterReceiver(mBroadcastReceiver);
        }
        HybridHalfPageCashierStatics.unRegisterCommonBusinessParams(cashierUniqueId());
    }

    @Override
    public boolean onActivityResult(int requestCode, int resultCode, Intent data) {
        PayerMediator.getInstance().setPayActionListener(mCashierActivity, this);
        if (PayerMediator.getInstance().consumeActivityResult(mCashierActivity, requestCode, resultCode, data)) {
            LoganUtils.log("HybridPrePosedMTCashierRouterAdapter_onActivityResult_requestCode: " + requestCode);
        }
        return true;
    }

    @Override
    public void onRequestSucc(int tag, Object obj) {
        if (tag == MTHalfPageCashierReqTagConstant.REQ_TAG_GO_HELLO_PAY) {
            HybridHalfPageCashierStatics.reportRequestSuccess("cashier/gohellopay", "b_pay_cashier_gohellopay_succ_sc", null, cashierUniqueId());
            HybridHalfPageCashierStatics.logCustomRequestSuccess("cashier_gohellopay_succ", null, cashierUniqueId());
            MTPaymentURL mtPaymentURL = (MTPaymentURL) obj;
            if (mtPaymentURL != null) {
                PayHornConfigBean.setGmDegradeFlag(mtPaymentURL.getUrl());
            }
            dealGoHelloPayResponse(mtPaymentURL);
        }
    }

    private void dealGoHelloPayResponse(MTPaymentURL mtPaymentURL) {
        if (mtPaymentURL == null) {
            return;
        }
        if (mtPaymentURL.getOverLoadInfo() != null && mtPaymentURL.getOverLoadInfo().isStatus()) { //当前后台负载过重，无法完成支付，弹出提示窗口
            CashierRouterHelper.from(this).downgrade();
        } else {
            try {
                String orderInfo = new String(Base64.decode(mtPaymentURL.getUrl()));
                JSONObject jsonObject = new JSONObject(orderInfo);
                String qdbNo = jsonObject.optString("trans_id");
                HybridHalfPageCashierStatics.appendCommonBusinessParams("qdb_no", TextUtils.isEmpty(qdbNo) ? "-999" : qdbNo, cashierUniqueId());
            } catch (Exception e) {
                LoganUtils.logError("HybridPrePosedMTCashierRouterAdapter_dealGoHelloPayResponse", e.getMessage());
            }
            PayerMediator.getInstance().startPay(mCashierActivity, PayersID.ID_MEITUANPAY, mtPaymentURL.getUrl(), cashierParams().getTradeNo(), this);
            reportMtPaySLAStart(mtPaymentURL, "hybrid", cashierUniqueId());
        }
    }

    @Override
    public void onRequestException(int tag, Exception e) {
        // 直接降级到标准收银台
        if (tag == MTHalfPageCashierReqTagConstant.REQ_TAG_GO_HELLO_PAY) {
            HybridHalfPageCashierStatics.logCustomRequestFailed("cashier_gohellopay_fail", e, cashierUniqueId());
            HybridHalfPageCashierStatics.reportRequestFailed("cashier/gohellopay", "b_pay_cashier_gohellopay_fail_sc", e, cashierUniqueId());
            if (e instanceof PayException) {
                CashierRouterHelper.from(this).info(DowngradeInfoHandler.parseException("preposed-mtcashier", (PayException) e)).downgrade();
            } else {
                CashierRouterHelper.from(this).downgrade();
            }
        }
    }

    @Override
    public void onRequestStart(int tag) {
        ProgressController.of(getActivity()).setProgressType(ProgressType.CASHIER).show();
    }

    @Override
    public void onRequestFinal(int tag) {
        ProgressController.of(getActivity()).hide();
    }

    @Override
    public void onPayPreExecute(String payType) {

    }

    @Override
    public void onGotPayResult(String payType, int payResult, PayFailInfo payFailInfo) {
        if (TextUtils.equals(PayersID.ID_MEITUANPAY, payType)) {
            onGotMTPayResult(payType, payResult, payFailInfo);
        }
    }

    /**
     * 获取美团支付结果
     *
     * @param payType     支付方式
     * @param payResult   支付结果标识
     * @param payFailInfo 失败数据
     */
    private void onGotMTPayResult(String payType, int payResult, PayFailInfo payFailInfo) {
        if (payResult == PayActionListener.SUCCESS) {
            analysePaySucess(cashierUniqueId());
            Map<String, Object> reportMap = new HashMap<>();
            reportMap.put("pay_type", payType);
            reportMap.put("class", "HybridPrePosedMTCashierRouterAdapter");
            LoganUtils.log("收银台支付成功后埋点", reportMap);
            mCashierActivity.setHalfPageMarketingBackgroundColor("#********");
            onMTPaySuccess(payFailInfo);
        } else if (payResult == PayActionListener.CANCEL) {
            CashierRouterHelper.from(this).cancel().finish();
        } else {
            int failReason = failReason(payFailInfo);
            switch (failReason) {
                case CODE_FAIL_REASON_RENDER_FAIL: // 渲染失败，降级
                case CODE_FAIL_REASON_BUSINESS_DOWNGRADE: // 业务决策，降级
                    CashierRouterHelper.from(this).info(DowngradeInfoHandler.parsePayFailInfo("preposed-mtcashier", payFailInfo))
                            .destProductType(CashierRouterConstants.PRODUCT_TYPE_STANDARD_CASHIER).downgrade();
                    break;
                case CODE_FAIL_REASON_LOADING_CANCEL: // 容器加载过程中点击 back 键，取消加载
                default:
                    CashierRouterHelper.from(this).cancel().finish();
                    break;
            }
        }
    }

    /**
     * 美团支付成功处理
     *
     * @param failInfo 历史逻辑：美团支付的业务数据没有地方方，放在failInfo中
     */
    private void onMTPaySuccess(PayFailInfo failInfo) {
        if (failInfo == null) {
            CashierRouterHelper.from(this).success().finish();
            return;
        }
        Promotion promotion = CatchException.run(() -> GsonProvider.getInstance()
                .fromJson(JsonString.parser(failInfo.getExtra()).get("pay_promotion"), Promotion.class)).value();
        CashierRouterHelper.from(this).success().promotion(promotion).finish();
    }

    /**
     * 获取失败信息，通过失败信息决定是取消支付，还是降级
     *
     * @param failInfo 失败信息
     * @return 从failInfo中获取的失败类型
     */
    private int failReason(PayFailInfo failInfo) {
        if (failInfo == null) {
            return CODE_FAIL_REASON_DEFAULT;
        }
        if (failInfo.getErrorCode() == PayErrorCode.BACK_CANCEL) { // 判断是否是加载过程中用户点击返回键
            return CODE_FAIL_REASON_LOADING_CANCEL;
        } else if (failInfo.getErrorCode() == PayErrorCode.HYBRID_PREPOSED_MTCASHIER_LOADING_ERROR) { // 判断独立收银台是否渲染失败
            return CODE_FAIL_REASON_RENDER_FAIL;
        }
        if (!TextUtils.equals(JsonString.parser(failInfo.getExtra()).get(KEY_ACTION), VAL_DOWNGRADE_TO_BUSINESS)) { // 业务决策降级是否是降级到标准收银台
            return CODE_FAIL_REASON_BUSINESS_DOWNGRADE;
        }
        return CODE_FAIL_REASON_DEFAULT;
    }

    private void initBroadcastReceiver() {
        final String successAction = "com.meituan.android.cashier.mtpay.loadState.success";
        final String failAction = "com.meituan.android.cashier.mtpay.loadState.fail";
        if (mBroadcastReceiver == null) {
            mBroadcastReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    if (LocalBroadCastUtil.actionEquals(mCashierActivity, successAction, intent)) {
                        String trace = adapterContext().trace();
                        RouterManager.notifier(trace).notifyLoadSuccess("HybridPrePosedCashier load success");
                    }
                }
            };
        }
        LocalBroadCastUtil.registerBroadCast(mCashierActivity, new String[]{successAction, failAction}, mBroadcastReceiver);
    }
}
