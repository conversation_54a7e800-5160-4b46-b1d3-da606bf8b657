<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cashier_remaining_time"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"

    android:gravity="center"
    android:orientation="vertical">

    <!--订单取消的时候显示-->
    <TextView
        android:id="@+id/cashier_no_remaining_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/cashier__pay_timeout_message"
        android:textColor="@color/paybase__text_color_3"
        android:textSize="14sp"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/cashier_remaining_time_value"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <!--以下在倒计时的时候显示-->
        <TextView
            android:id="@+id/cashier_remaining_time_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingRight="4dp"
            android:text="@string/cashier__count_down"
            android:textColor="@color/cashier__gray"
            android:textSize="12dp"
            tools:ignore="SpUsage" />

        <TextView
            android:id="@+id/remain_time_hour1"
            style="@style/cashier__remaining_time_display_num_revision"
            tools:text="1" />

        <TextView
            android:id="@+id/remain_time_hour2"
            style="@style/cashier__remaining_time_display_num_revision"
            tools:text="1" />

        <TextView
            android:id="@+id/colon_between_hour_and_min"
            style="@style/cashier__remaining_time_display_num_revision"
            android:paddingBottom="1dp"
            android:text="@string/cashier__remaining_time_colon" />

        <TextView
            android:id="@+id/remain_time_min1"
            style="@style/cashier__remaining_time_display_num_revision"
            tools:text="1" />

        <TextView
            android:id="@+id/remain_time_min2"
            style="@style/cashier__remaining_time_display_num_revision"
            tools:text="1" />

        <TextView
            style="@style/cashier__remaining_time_display_num_revision"
            android:paddingBottom="1dp"
            android:text="@string/cashier__remaining_time_colon" />

        <TextView
            android:id="@+id/remain_time_sec1"
            style="@style/cashier__remaining_time_display_num_revision"
            tools:text="1" />

        <TextView
            android:id="@+id/remain_time_sec2"
            style="@style/cashier__remaining_time_display_num_revision"
            tools:text="1" />
    </LinearLayout>
</LinearLayout>