<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="65dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="20dp"
        android:layout_toLeftOf="@+id/dcep_selected"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/dcep_icon"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_marginRight="8dp" />

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="left|center_vertical">

                <TextView
                    android:id="@+id/dcep_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toLeftOf="@+id/dcep_name_ext"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:textColor="@color/paybase__half_page_content_text_color"
                    android:textSize="16sp"
                    tools:text="中国工商银行储蓄卡测试银行储蓄卡测试测试测试测试" />

                <TextView
                    android:id="@+id/dcep_name_ext"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="0dp"
                    android:singleLine="true"
                    android:textColor="@color/paybase__half_page_content_text_color"
                    android:textSize="16sp"
                    tools:text="(8723)" />
            </RelativeLayout>

        </LinearLayout>

        <com.meituan.android.paycommon.lib.widgets.PayLabelContainer
            android:id="@+id/dcep_label_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="28dp"
            android:layout_marginTop="4dp"
            android:layout_marginRight="6dp"
            android:orientation="horizontal" />
    </LinearLayout>

    <ImageView
        android:id="@+id/dcep_selected"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="24dp"
        android:src="@drawable/mtpaysdk__payment_checkbox_unselected" />

    <View
        android:id="@+id/quickbind_gray_line"
        style="@style/paybase__horizonal_divider"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="51dp"
        android:layout_marginRight="24dp"
        android:background="#f0f0f0" />
</RelativeLayout>