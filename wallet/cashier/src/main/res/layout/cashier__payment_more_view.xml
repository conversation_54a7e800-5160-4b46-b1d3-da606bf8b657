<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/transparent"
    android:paddingLeft="46dp">

    <TextView
        android:id="@+id/cashier_more"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/cashier___more_payment_height"
        android:layout_marginRight="4dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:singleLine="true"
        android:text="@string/cashier__unfold_mt_more_payment2"
        android:textColor="@color/cashier__black3"
        android:textSize="12dp"
        tools:ignore="SpUsage" />

    <ImageView
        android:id="@+id/cashier_more_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginRight="27dp"
        android:src="@drawable/cashier__more_view_arrow" />
</RelativeLayout>