<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/business_money_symbol"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="bottom"
            android:text="&#165; "
            android:textColor="@color/cashier__black5"
            android:textSize="22sp"
            tools:ignore="SpUsage" />

        <com.meituan.android.paycommon.lib.widgets.AutoChangeNumberView
            android:id="@+id/business_info_money"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:textColor="@color/cashier__black5"
            android:textSize="37sp"
            tools:ignore="SpUsage"
            tools:text="200" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/price_and_order_info_layout"
        android:layout_width="match_parent"
        android:layout_height="53dp"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/origin_price_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/origin_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/cashier__gray"
                android:textSize="12sp"
                android:visibility="visible"
                tools:ignore="SpUsage"
                tools:text="190" />

            <!--<View-->
            <!--android:id="@+id/delete_line"-->
            <!--android:layout_width="0dp"-->
            <!--android:layout_height="1dp"-->
            <!--android:layout_centerVertical="true"-->
            <!--android:layout_marginRight="20dp"-->
            <!--android:layout_toLeftOf="@id/origin_price"-->
            <!--android:background="@color/cashier__black5"-->
            <!--android:visibility="gone" />-->
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/order_info_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="40dp"
            android:layout_marginRight="40dp"
            android:background="@color/transparent"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/order_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:paddingLeft="20dp"
                android:paddingRight="6dp"
                android:singleLine="true"
                android:textColor="@color/cashier__gray"
                android:textSize="12sp"
                tools:ignore="SpUsage"
                tools:text="你试时设计师测试时设计师测试时设计师测试试时设计师测试试时设计师测试" />

            <View
                android:id="@+id/order_name_detail"
                android:layout_width="6dp"
                android:layout_height="10dp"
                android:layout_marginTop="1px"
                android:layout_marginRight="22dp"
                android:background="@drawable/paycommon__change_bank_arrow_dark"
                android:visibility="visible" />
        </LinearLayout>
    </FrameLayout>
</LinearLayout>