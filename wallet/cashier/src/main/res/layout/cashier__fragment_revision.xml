<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cashier_main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/cashier__bg_gray"
    android:orientation="vertical"
    tools:ignore="SpUsage">

    <LinearLayout
        android:id="@+id/cashier_content_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!--通知-->
        <com.meituan.android.paybase.widgets.notice.NoticeView
            android:id="@+id/notice_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:visibility="gone" />

        <RelativeLayout
            android:id="@+id/layout_cashier_head"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="17.5dp"
            android:layout_marginBottom="3dp"
            android:orientation="vertical">

            <!--剩余时间倒计时-->
            <LinearLayout
                android:id="@+id/layout_cashier_remaining_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical" />

            <LinearLayout
                android:id="@+id/layout_business_info"
                android:layout_below="@+id/layout_cashier_remaining_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2.5dp"
                android:baselineAligned="false"
                android:gravity="center"
                android:orientation="vertical" />
        </RelativeLayout>

        <com.meituan.android.cashier.widget.NSCScrollView
            android:id="@+id/cashier_scroll_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:descendantFocusability="blocksDescendants">
            <com.meituan.android.cashier.widget.NativeStandardCashierAreaView
                android:id="@+id/cashier__pay_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingBottom="150dp" />
        </com.meituan.android.cashier.widget.NSCScrollView>
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/cashier_bottom_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:clickable="true"
        android:orientation="vertical"
        android:focusable="true">

        <View
            android:id="@+id/view_bottom_shadow"
            android:layout_width="match_parent"
            android:layout_height="63dp"
            android:layout_alignBottom="@+id/view_bottom_blank"
            android:background="@drawable/cashier__bottom_shadow" />

        <FrameLayout
            android:id="@+id/cashier_discount_and_button_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginRight="12dp">

            <LinearLayout
                android:id="@+id/cashier__discount_view"
                android:layout_width="match_parent"
                android:layout_height="96dp"
                android:background="@drawable/cashier__bg_circle_corner"
                android:clickable="true"
                android:focusable="true"
                android:orientation="vertical"
                android:paddingTop="3dp" />

            <!-- 营销引导按钮 -->
            <com.meituan.android.cashier.widget.CashierMarketingGuideFloatView
                android:id="@+id/cashier_discount_guide"
                android:layout_width="match_parent"
                android:layout_height="96dp"
                android:background="@drawable/cashier__bg_circle_corner"
                android:focusable="true"
                android:orientation="vertical"
                android:paddingTop="10dp"
                android:visibility="gone"
                tools:visibility="visible" />

            <!--底部按钮上面的提示布局，例如买单协议, 优先显示买单协议所以该协议放下面 -->
            <LinearLayout
                android:id="@+id/remind_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="27dp"
                android:background="@drawable/cashier__bg_circle_corner"
                android:focusable="true"
                android:orientation="vertical" />


            <com.meituan.android.paybase.widgets.ProgressButton
                android:id="@+id/btn_cashier_pay_confirm"
                style="@style/cashier__button"
                android:layout_height="48dp"
                android:layout_gravity="bottom"
                android:layout_marginTop="0dp"
                android:layout_marginBottom="0dp"
                android:text="@string/cashier__pay_confirm"
                android:textColor="#1E1E1E"
                android:textSize="16dp"
                android:textStyle="bold"
                tools:ignore="SpUsage" />

        </FrameLayout>

        <!--该白块了为了点击该区域后触发按钮事件-->
        <View
            android:id="@+id/view_bottom_blank"
            android:layout_width="match_parent"
            android:layout_height="15dp"
            android:layout_below="@id/cashier_discount_and_button_layout"
            android:layout_marginLeft="12dp"
            android:layout_marginRight="12dp"
            android:importantForAccessibility="no"
            tools:ignore="UnusedAttribute" />

    </RelativeLayout>

</FrameLayout>