<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="310dp"
    android:layout_height="219dp"
    android:background="@drawable/paybase__bg_pay_dialog"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/card_pay_guide_dialog_close"
        android:layout_width="46dp"
        android:layout_height="46dp"
        android:layout_alignParentRight="true"
        android:padding="15dp"
        android:src="@drawable/cashier__card_pay_function_guide_dialog_close" />

    <TextView
        android:id="@+id/card_pay_guide_dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="31dp"
        android:layout_marginRight="15dp"
        android:ellipsize="end"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/cashier__color"
        android:textSize="20sp"
        android:textStyle="bold"
        tools:ignore="RelativeOverlap"
        tools:text="银行卡支付立减" />

    <LinearLayout
        android:id="@+id/card_pay_guide_dialog_money_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/card_pay_guide_dialog_title"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="2dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/card_pay_guide_dialog_money_symbol"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="bottom"
            android:text="&#165; "
            android:textColor="@color/cashier__card_pay_guide_dialog_money"
            android:textSize="20dp"
            tools:ignore="SpUsage" />

        <TextView
            android:id="@+id/card_pay_guide_dialog_money"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:textColor="@color/cashier__card_pay_guide_dialog_money"
            android:textSize="36dp"
            tools:ignore="SpUsage"
            tools:text="200" />
    </LinearLayout>

    <TextView
        android:id="@+id/card_pay_guide_dialog_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/card_pay_guide_dialog_money_layout"
        android:layout_centerHorizontal="true"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:ellipsize="end"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/cashier__color"
        android:textSize="14sp"
        tools:text="限时专享优惠" />

    <LinearLayout
        android:id="@+id/button_linear_layout"
        android:layout_width="match_parent"
        android:layout_height="75dp"
        android:layout_alignParentBottom="true"
        android:gravity="center"
        android:orientation="horizontal">

        <com.meituan.android.paybase.widgets.ProgressButton
            android:id="@+id/card_pay_guide_dialog_button"
            style="@style/cashier__button"
            android:layout_height="45dp"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:ellipsize="end"
            android:gravity="center"
            android:singleLine="true"
            android:textColor="@color/cashier__card_pay_guide_dialog_btn_text"
            android:textSize="14dp"
            android:textStyle="bold"
            tools:ignore="SpUsage"
            tools:text="立即省钱" />
    </LinearLayout>
</RelativeLayout>