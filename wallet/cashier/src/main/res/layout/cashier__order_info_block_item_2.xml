<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingBottom="1dp"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:paddingTop="1dp">

    <TextView
        android:id="@+id/type_2_item_content_key"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/cashier__order_info_block_item_margin"
        android:textColor="@color/cashier__black2"
        android:singleLine="false"
        android:textSize="@dimen/cashier__order_info_block_item_content"
        android:layout_weight="2"
        android:importantForAccessibility="no"
        style="@style/cashier__order_info_single_line"
        tools:targetApi="jelly_bean" />

    <TextView
        android:id="@+id/type_2_item_content_value"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_marginRight="@dimen/cashier__order_info_block_item_margin"
        android:textColor="@color/cashier__black2"
        android:textSize="@dimen/cashier__order_info_block_item_content"
        android:gravity="right"
        android:layout_weight="1"
        android:importantForAccessibility="no"
        style="@style/cashier__order_info_single_line"
        tools:targetApi="jelly_bean" />

</LinearLayout>