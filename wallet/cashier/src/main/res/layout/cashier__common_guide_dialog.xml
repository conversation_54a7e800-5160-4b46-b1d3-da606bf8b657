<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cashier_common_guide_dialog_content"
    android:layout_width="300dp"
    android:layout_height="wrap_content"
    android:background="@drawable/paybase__bg_pay_dialog"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/common_dialog_close"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_alignParentRight="true"
        android:paddingTop="15dp"
        android:paddingRight="15dp"
        android:src="@drawable/cashier__card_pay_function_guide_dialog_close" />

    <TextView
        android:id="@+id/common_dialog_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/common_dialog_close"
        android:layout_centerHorizontal="true"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="24dp"
        android:gravity="center"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/cashier__common_dialog_title"
        android:textSize="18dp"
        android:textStyle="bold"
        tools:ignore="SpUsage"
        tools:text="美团支付限时优惠" />

    <FrameLayout
        android:id="@+id/common_dialog_marketing_area"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/common_dialog_title"
        android:layout_marginTop="21dp">

        <com.meituan.android.paybase.widgets.bankcard.RoundImageView
            android:id="@+id/marketing_background_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="18dp"
            android:layout_marginRight="18dp"
            android:scaleType="fitXY"
            android:src="@drawable/cashier__common_dialog_marketing_background" />

        <LinearLayout
            android:id="@+id/marketing_background_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="18dp"
            android:layout_marginRight="18dp"
            android:background="#00FFFFFF"
            android:orientation="vertical"
            tools:ignore="DuplicateIds">

            <TextView
                android:id="@+id/common_dialog_marketing_main_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="21dp"
                android:gravity="center"
                android:maxLines="2"
                android:textColor="@color/cashier__common_dialog_marketing_main_title"
                android:textSize="21dp"
                android:textStyle="bold"
                tools:ignore="SpUsage"
                tools:text="情人节情人节下单享优惠5元" />

            <TextView
                android:id="@+id/common_dialog_marketing_sub_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="2dp"
                android:gravity="center"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="@color/cashier__common_dialog_marketing_sub_title"
                android:textSize="14dp"
                tools:ignore="SpUsage"
                tools:text="返5元优惠券" />


            <LinearLayout
                android:id="@+id/common_dialog_guide_pay_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="9dp"
                android:layout_marginBottom="21dp"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/common_dialog_guide_pay_type_pay_icon"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center_vertical" />

                <TextView
                    android:id="@+id/common_dialog_guide_pay_type_payment_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="6dp"
                    android:gravity="center"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/cashier__common_dialog_guide_pay_type_payment_name"
                    android:textSize="14dp"
                    tools:ignore="SpUsage"
                    tools:text="中信银行储蓄卡(8417) 支付" />
            </LinearLayout>
        </LinearLayout>
    </FrameLayout>

    <FrameLayout
        android:id="@+id/common_dialog_guide_button"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_below="@id/common_dialog_marketing_area"
        android:layout_marginLeft="18dp"
        android:layout_marginTop="15dp"
        android:layout_marginRight="18dp"
        android:layout_marginBottom="18dp">

        <com.meituan.android.paybase.widgets.bankcard.RoundImageView
            android:id="@+id/common_dialog_guide_button_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            android:src="@drawable/cashier__common_dialog_guide_button_background" />

        <com.meituan.android.paybase.widgets.ProgressButton
            android:id="@+id/common_dialog_guide_button_text"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="#00FFFFFF"
            android:ellipsize="end"
            android:gravity="center"
            android:singleLine="true"
            android:textColor="@color/cashier__common_dialog_guide_button_text"
            android:textSize="14dp"
            android:textStyle="bold"
            tools:ignore="SpUsage"
            tools:text="立享优惠" />
    </FrameLayout>

</RelativeLayout>