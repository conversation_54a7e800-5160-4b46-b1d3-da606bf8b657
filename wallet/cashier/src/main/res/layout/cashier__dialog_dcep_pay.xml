<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/cashier__dcep_dialog_background"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="66dp">

        <TextView
            android:id="@+id/dcep_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:layout_marginLeft="50dp"
            android:layout_marginRight="50dp"
            android:ellipsize="middle"
            android:singleLine="true"
            android:textColor="@color/paybase__half_page_content_text_color"
            android:textSize="18sp"
            android:textStyle="bold"
            tools:ignore="RelativeOverlap"
            tools:text="选择数字人民币钱包" />

        <ImageView
            android:id="@+id/dcep_close"
            android:layout_width="47dp"
            android:layout_height="55dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:paddingLeft="10dp"
            android:paddingTop="14dp"
            android:paddingRight="22dp"
            android:paddingBottom="14dp"
            android:src="@drawable/cashier_dcep_dialog_close" />
    </RelativeLayout>

    <com.meituan.android.cashier.base.view.revision.CashierOrderInfoView
        android:id="@+id/dcep_money"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp" />

    <ListView
        android:id="@+id/dcep_bank_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="22dp"
        android:layout_weight="1"
        android:divider="@null"
        android:listSelector="@android:color/transparent" />

    <LinearLayout
        android:id="@+id/button_linear_layout"
        android:layout_width="match_parent"
        android:layout_height="84dp"
        android:gravity="center"
        android:orientation="horizontal">

        <com.meituan.android.paybase.widgets.ProgressButton
            android:id="@+id/dcep_confirm_btn"
            style="@style/cashier__button"
            android:layout_height="48dp"
            android:layout_marginLeft="18dp"
            android:layout_marginRight="18dp"
            android:text="@string/cashier__pay"
            android:textSize="16dp"
            android:textStyle="bold"
            tools:ignore="SpUsage" />
    </LinearLayout>
</LinearLayout>
