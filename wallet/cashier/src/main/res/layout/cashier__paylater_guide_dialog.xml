<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/cashier__bg_paylater_guide_dialog"
    android:orientation="vertical"
    android:paddingLeft="@dimen/cashier__paylater_guide_dialog_horizontal_padding"
    android:paddingRight="@dimen/cashier__paylater_guide_dialog_horizontal_padding"
    android:paddingBottom="6dp">

    <!-- UI地址 https://mtdui.sankuai.com/design/#/group/837/project/6144 -->

    <LinearLayout
        android:id="@+id/dialog_root"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        tools:ignore="UselessParent">

        <ImageView
            android:id="@+id/business_logo"
            android:layout_width="@dimen/cashier__paylater_guide_dialog_business_logo_size"
            android:layout_height="@dimen/cashier__paylater_guide_dialog_business_logo_size"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/cashier__paylater_guide_dialog_business_logo_marginTop"
            android:layout_marginBottom="@dimen/cashier__paylater_guide_dialog_business_logo_marginBottom"
            android:scaleType="fitXY" />


        <RelativeLayout
            android:id="@+id/score_container"
            android:layout_width="match_parent"
            android:layout_height="@dimen/cashier__paylater_guide_dialog_score_container_height">

            <View
                android:id="@+id/score_bg"
                android:layout_width="@dimen/cashier__paylater_guide_dialog_score_bg_width"
                android:layout_height="@dimen/cashier__paylater_guide_dialog_score_bg_height"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/cashier__paylater_guide_dialog_score_bg_marginTop"
                android:background="@drawable/cashier__bg_paylater_guide_score" />

            <TextView
                android:id="@+id/score"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/cashier__paylater_guide_dialog_score_marginTop"
                android:textColor="@color/cashier__color"
                android:textSize="@dimen/cashier__paylater_guide_dialog_score_textsize"
                android:textStyle="bold"
                tools:text="575" />

            <TextView
                android:id="@+id/score_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/score"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/cashier__paylater_guide_dialog_score_name_marginTop"
                android:ellipsize="end"
                android:textColor="@color/cashier__color"
                android:textSize="@dimen/cashier__paylater_guide_dialog_score_desc_textsize"
                tools:text="美团信任分" />
        </RelativeLayout>

        <!-- 头部区域：可领取美团单车先骑后付权益 -->
        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/cashier__paylater_guide_dialog_title_marginBottom"
            android:gravity="center"
            android:textColor="@color/cashier__color"
            android:textSize="@dimen/cashier__paylater_guide_dialog_title_textsize"
            android:textStyle="bold"
            tools:ignore="SpUsage"
            tools:text="美团自动扣款" />

        <!-- 引导说明文案 -->
        <TextView
            android:id="@+id/desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/cashier__paylater_guide_dialog_detail_marginTop"
            android:gravity="center"
            android:textColor="@color/cashier__color"
            android:textSize="@dimen/cashier__paylater_guide_dialog_detail_textsize"
            tools:ignore="SpUsage"
            tools:text="服务结束后使用美团支付自动扣款，方便快捷" />

        <!-- 引导图片 -->
        <ImageView
            android:id="@+id/guide_picture"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/cashier__paylater_guide_dialog_image_marginTop"
            android:layout_marginBottom="@dimen/cashier__paylater_guide_dialog_image_marginBottom"
            android:scaleType="fitXY" />

        <android.support.v4.widget.Space
            android:id="@+id/guide_picture_placeholder"
            android:layout_width="match_parent"
            android:layout_height="@dimen/cashier__paylater_guide_dialog_image_placeholder1_height" />
    </LinearLayout>


</FrameLayout>