<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#b0000000">


    <RelativeLayout
        android:id="@+id/popup_window"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        tools:ignore="UselessParent">

        <LinearLayout
            android:id="@+id/popup_window_solid"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginLeft="32dp"
            android:layout_marginTop="132dp"
            android:layout_marginRight="32dp"
            android:layout_marginBottom="132dp"
            android:background="@drawable/cashier__bg_order_info"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/dialog_close_icon"
                android:layout_width="41dp"
                android:layout_height="26dp"
                android:layout_gravity="end"
                android:paddingRight="10dp"
                android:paddingLeft="15dp"
                android:paddingTop="10dp"
                android:src="@drawable/cashier_dcep_dialog_close" />

            <TextView
                android:id="@+id/title_text_view"
                style="@style/cashier__order_info_single_line"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="@string/cashier__order_detail"
                android:textStyle="bold"
                android:textColor="@color/cashier__black1"
                android:textSize="@dimen/cashier__order_info_window_title" />

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/cashier__order_info_scroll_view_min_height"
                android:paddingTop="8dp"
                android:paddingBottom="24dp">

                <LinearLayout
                    android:id="@+id/order_info_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                </LinearLayout>
            </ScrollView>
        </LinearLayout>

    </RelativeLayout>
</RelativeLayout>