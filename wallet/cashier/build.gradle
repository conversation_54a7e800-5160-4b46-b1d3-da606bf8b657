apply from: '../base_build.gradle'

dependencies {
    api project(':meituanpay')
    api project(':cashier-web')
    api project(':cashier-common')
    api project(':cashier-oneclick')
//    api project(':cashier-elderly')
    api project(':cashier-hybridwrapper')
    api project(':payment-channel')

    api 'com.meituan.android.sniffer:sniffer-annotation:0.0.9'
    androidTestImplementation 'commons-io:commons-io:1.3.2'
    androidTestImplementation 'org.mockito:mockito-all:1.9.5'

    annotationProcessor 'com.sankuai.meituan.serviceloader:processor:2.1.0.11'
}