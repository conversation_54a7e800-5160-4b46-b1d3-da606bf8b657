# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Settings specified in this file will override any Gradle settings
# configured through the IDE.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx10248m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
android.injected.testOnly=false
org.gradle.jvmargs=-Xmx6g -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -XX:+UseParallelGC --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.lang.invoke=ALL-UNNAMED --add-opens java.prefs/java.util.prefs=ALL-UNNAMED --add-opens java.base/java.nio.charset=ALL-UNNAMED --add-opens java.base/java.net=ALL-UNNAMED --add-opens java.base/java.util.concurrent.atomic=ALL-UNNAMED
org.gradle.parallel=true
org.gradle.daemon=true
android.enableJetifier=false
#org.gradle.unsafe.configuration-cache=true
# Use this flag carefully, in case some of the plugins are not fully compatible.
#org.gradle.unsafe.configuration-cache-problems=warn
#android.useDeprecatedNdk=true

VERSION_NAME=1335.0.0
VERSION_CODE=133500
GROUP=com.sankuai.meituan.pay

RELEASE_REPOSITORY_URL=http://nexus:8081/nexus/content/repositories/thirdparty
SNAPSHOT_REPOSITORY_URL=http://nexus:8081/nexus/content/repositories/3rdPartySnapshot
POM_DESCRIPTION=meituan cashier
POM_URL=
POM_SCM_URL=
POM_SCM_CONNECTION=
POM_SCM_DEV_CONNECTION=
POM_LICENCE_NAME=
POM_LICENCE_URL=
POM_LICENCE_DIST=
POM_DEVELOPER_ID=
POM_DEVELOPER_NAME=
#
#ANDROID_BUILD_TARGET_SDK_VERSION=25
#ANDROID_BUILD_MIN_SDK_VERSION=15
#ANDROID_BUILD_TOOLS_VERSION=25.0.2
#ANDROID_BUILD_SDK_VERSION=25
ANDROID_BUILD_TARGET_SDK_VERSION=30
ANDROID_BUILD_MIN_SDK_VERSION=21
ANDROID_BUILD_TOOLS_VERSION=28.0.3
ANDROID_BUILD_SDK_VERSION=30

PACKAGE_NAME=com.sankuai.meituan.paydemo
APP_NAME=pay-demo
HPX_JDK_VERSION=11

IN_PAY_DEMO=

PROJECT=meituan
#PROJECT=waimai
#PROJECT=beam

DISABLE_MTJACOCO=

#LeakCanary
#DISABLE_LEAKCANARY=

#pay-base
#ENABLE_PROJECT_PAY_BASE=

#fin-third-pay
#ENABLE_FIN_THIRDPAY_ADAPTER=

#identifycard
#ENABLE_PROJECT_IDENTIFYCRAD=true

#bankcard-recognize
#ENABLE_PROJECT_BANKCRAD=true

#pay-router
#ENABLE_PROJECT_PAY_ROUTER=

#pay-keqing
#ENABLE_PROJECT_PAY_KEQING=

#pay-debugkit
#ENABLE_PROJECT_PAY_DEBUGKIT=