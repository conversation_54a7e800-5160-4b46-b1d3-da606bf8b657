//
//  PPSwitchEnvOptimize.m
//  ipaymentapp
//
//  Created by 曾泽昱 on 2025/5/27.
//  Copyright © 2025 Meituan.com. All rights reserved.
//
#if DEBUG || TEST

#import <CIPFoundation/NSURL+CIPAdditions.h>
#import <SAKDebugKit/SAKOneClickDataManager.h>
#import <SAKDebugKit/SDKURLSwitcherHandler.h>
#import <SAKHorn/SAKHorn.h>
#import <SAKHybridCashier/SHCDebugConfigurationsManager.h>
#import <SAKPaymentKit/SAKHornManager.h>
#import <SAKPaymentKit/SPKAlertView.h>
#import <SAKPaymentKit/SPKJSONObject.h>
#import <SAKUIKit/TKAlertCenter+NSError.h>
#import "PPSwitchEnvOptimize.h"
#import <NVDebugNetworkAgent/NVDebugNetworkAgent.h>

const NSInteger GlobalEnvironmentId = 2270; //支付QA对应环境Id
NSString *const OriginalHybridCashierVersion = @"8.8.0";

@interface PPSwitchEnvOptimize ()

@property (nonatomic, strong) NSMutableArray<SAKOneClickEvnModule *> *cacheArray; //泳道配置

@end

@implementation PPSwitchEnvOptimize

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    @weakify(self);
    self.codeScanSucceed = ^(NSURL *resultURL) {
        @strongify(self);
        NSString *scheme = [[resultURL scheme] lowercaseString];
        NSString *host = [[resultURL host] lowercaseString];

        if ([scheme isEqualToString:@"paydemo"] && [host isEqualToString:@"www.environment.com"]) {
            [self switchEnvironmentWithUrl:resultURL];
        } else {
        }

        [self.navigationController popViewControllerAnimated:YES];
    };
    self.codeScanFailed = ^{
        [[TKAlertCenter defaultCenter] postAlertWithMessage:@"Operation failed"];
    };
    self.codeScanCancel = ^{
        [[TKAlertCenter defaultCenter] postAlertWithMessage:@"Operation canceled"];
    };
}

- (void)switchEnvironmentWithUrl:(NSURL *)resultURL
{

    NSDictionary *dic = [resultURL cipf_parseQueryWithDecoding];
    // 泳道标识
    NSString *lanePath = dic[@"lanePath"];

    if (lanePath.length > 0) {
        [self switchLaneEnv:lanePath];
    }

    // 标准收银台切换
    [self switchStandardCashier];

    // 标准收银台版本号
    NSString *hybridCashierVersion = dic[@"hybridCashierVersion"];

    if (hybridCashierVersion.length > 0) {
        [self switchHybridCashierVersion:hybridCashierVersion];
    }

    // Horn环境切换
    NSString *hornEnv = dic[@"hornEnv"];
    [self switchHornEnv:hornEnv];
    
    // SharkDebug环境切换为Mock环境
    [self switchsharkDebugEnv];

    [SPKAlertView showAlertViewWithMessage:@"环境切换成功"
                     completionButtonTitle:@"我知道了"
                                completion:^{
        [self dismissViewControllerAnimated:YES
                                 completion:nil];
    }];
}

// 标准收银台切换
- (void)switchStandardCashier
{
    // 开启路由 mock 总开关🔥
    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"NeoDebug_EntryMockSwitch"];
    // 开启Hybrid 收银台 mock 开关
    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"NeoDebug_HybridMockSwitch"];

    [[NSUserDefaults standardUserDefaults] stringForKey:SHCConfigKeyFromDefault];
}

// 泳道环境切换
- (void)switchLaneEnv:(NSString *)lanePath
{
    self.cacheArray = [[[SAKOneClickDataManager sharedManager] getModuleArrayEnvId:GlobalEnvironmentId] mutableCopy];

    if (self.cacheArray.count == 0) {
        @weakify(self);
        [[SAKOneClickDataManager sharedManager] pullEnvironmentDetail:GlobalEnvironmentId
                                                             callBack:^(NSArray<SAKOneClickEvnModule *> *modelArray) {
            @strongify(self);
            self.cacheArray = [modelArray mutableCopy];
            [self updateLaneEnv:lanePath];
            
        }];
    } else {
        [self updateLaneEnv:lanePath];
    }
}

- (void)updateLaneEnv:(NSString *)lanePath
{
    if ([lanePath isEqualToString:@"default"]) {
        self.cacheArray[0].effectUrl = @"http://stable.pay.test.sankuai.com";
        self.cacheArray[1].effectUrl = @"http://stable.pay.test.sankuai.com";
    } else {
        self.cacheArray[0].effectUrl = [NSString stringWithFormat:@"http://%@-sl-stable.pay.test.sankuai.com", lanePath];
        self.cacheArray[1].effectUrl = [NSString stringWithFormat:@"http://%@-sl-stable.pay.test.sankuai.com", lanePath];
    }
    

    [[SAKOneClickDataManager sharedManager] setModuleArray:self.cacheArray
                                                     envId:GlobalEnvironmentId];
    [[SAKOneClickDataManager sharedManager] setDefaultEnvironmentId:GlobalEnvironmentId];
}

// 收银台版本切换
- (void)switchHybridCashierVersion:(NSString *)version
{
    //修改hybrid_cashier_path
    NSString *testHornStorage = [SHCDebugConfigurationsManager defaultManager].testHornStorage;

    testHornStorage = testHornStorage ? : [[SHCDebugConfigurationsManager defaultManager] getHornJson:HybridCashierHornTypeTest];
    NSMutableDictionary *hornConfig = [testHornStorage.spk_JSONDictionary mutableCopy];
    hornConfig[@"hybrid_cashier_path"] = [hornConfig[@"hybrid_cashier_path"] stringByReplacingOccurrencesOfString:OriginalHybridCashierVersion withString:version];
    [[NSUserDefaults standardUserDefaults] setObject:hornConfig.spk_JSONString forKey:SHCConfigKeyFromDefault];
}

// Horn环境切换
- (void)switchHornEnv:(NSString *)hornEnv
{
    NSCharacterSet *separators = [NSCharacterSet characterSetWithCharactersInString:@",，"];  // 同时包含中英文逗号
    NSArray *array = [hornEnv componentsSeparatedByCharactersInSet:separators];

    [array enumerateObjectsUsingBlock:^(id _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
        [SAKHorn setEnableDebug:YES
                        forType:obj];
    }];
//    [SAKHornManager sharedManager].usePayRouterForCashier = NO;
//    NSArray *routerHornConfigs = [SAKHornManager sharedManager].routerHornConfigs;
//
//    NSMutableArray *newRouterHornConfigs = [routerHornConfigs mutableCopy];
//    for (SAKCashierHornModel *config in newRouterHornConfigs) {
//        if (config.url && [@"preorder_cashier" isEqualToString: config.cashierType]) {
//            config.url = @"http://1944-wunqz-sl-stable.pay.test.sankuai.com/preorder-cashier/index.html";
//        }
//    }
//    [SAKHornManager sharedManager].routerHornConfigs = [newRouterHornConfigs copy];
}

- (void)switchsharkDebugEnv
{
    [[NVDebugNetworkAgent instance] switch2Domain:SKNetworkDebugMock flag:NO];
}

@end

#endif /* if DEBUG || TEST */
