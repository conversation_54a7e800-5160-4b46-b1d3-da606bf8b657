apply plugin: 'com.android.library'

buildscript {
    def gradle_version = '7.4.2'
    repositories {
        mavenLocal() // 本地资源
        maven { // 内网 nexus仓库 （代理了大多数外网资源）
            url 'http://depot.sankuai.com/nexus/content/groups/public/'
            allowInsecureProtocol = true
        }
        maven { // 内网 pixel仓库 （代理了更多外网资源）
            url "http://pixel.sankuai.com/repository/mtdp"
            allowInsecureProtocol = true
        }
    }

    dependencies {
        classpath "com.android.tools.build:gradle:${gradle_version}"
    }
}

repositories {
    mavenLocal()
    maven {
        url 'http://depot.sankuai.com/nexus/content/groups/public/'
        allowInsecureProtocol = true
    }
    maven {
        url "http://pixel.sankuai.com/repository/mtdp"
        allowInsecureProtocol = true
    }
}

android {
    viewBinding { enabled = true }
    useLibrary 'org.apache.http.legacy'
    compileSdkVersion Integer.parseInt(project.ANDROID_BUILD_SDK_VERSION)

    defaultConfig {
        minSdkVersion Integer.parseInt(project.ANDROID_BUILD_MIN_SDK_VERSION)
        targetSdkVersion Integer.parseInt(project.ANDROID_BUILD_TARGET_SDK_VERSION)
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            consumerProguardFiles 'consumer-rules.pro'
        }
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt')
            consumerProguardFiles 'consumer-rules.pro'
        }
    }

    lintOptions {
        lintConfig file("lint.xml")
        warningsAsErrors false
        abortOnError false
        htmlReport true
        htmlOutput file("lint-report/lint-report.html")
        xmlReport true
        xmlOutput file("lint-report/lint-report.xml")
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

def isProperty(String name, String value) {
    return rootProject.hasProperty(name) && rootProject.property(name) == value;
}

def haveProperty(String name) {
    return rootProject.hasProperty(name);
}
if (hasProperty("hpxDebugkit")) {
    dependencies {
        api 'com.meituan.android.wallet:library:1335.0.4'
//        implementation 'com.squareup.leakcanary:leakcanary-android:2.9.1'
    }
} else {
    dependencies {
        api project(':wallet')
    }
}

if (isProperty("PROJECT", "meituan")) {
    apply from: file('../dependency_meituan.gradle')
} else if (isProperty("PROJECT", "waimai")) {
    apply from: file('./dependency.gradle')
} else if (isProperty("PROJECT", "beam")) {
    apply from: file('./dependency_beam.gradle');
}

apply from: file('../configurations.gradle')
apply from: file('../dependency_pay.gradle')