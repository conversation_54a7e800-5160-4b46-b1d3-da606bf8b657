[{"title": "工具", "type": "list", "list": [{"type": "navigate", "title": "扫一扫", "text": "跳转、AppMock", "method": "pay://debugkit/capture", "methods": {"group": "imeituan://www.meituan.com/scanQRCode?openAR=1"}}]}, {"title": "支付设置", "type": "list", "list": [{"key": "network_setting", "type": "navigate", "title": "网络设置", "method": "pay://debugkit/setting?page=network"}, {"type": "navigate", "title": "Horn设置", "text": "支付", "method": "method://debugHorn/showPayHorn"}, {"type": "navigate", "title": "Recce设置", "method": "imeituan://www.meituan.com/recce/dev-tools"}, {"type": "navigate", "title": "收银台设置", "text": "Hybrid", "method": "pay://debugkit/setting?page=cashier"}]}, {"title": "基础设置", "type": "list", "list": [{"type": "navigate", "title": "外卖设置", "method": "pay://debugkit/setting?page=waimai"}, {"type": "navigate", "title": "其他设置", "text": "Horn、KNB、Yoda", "method": "pay://debugkit/setting?page=other"}]}]