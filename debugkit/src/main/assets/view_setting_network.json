[{"key": "network_pay_host", "type": "selectable", "title": "支付网络切换", "addition": {"title": "添加新环境", "selectionTitle": "环境名称", "selectionText": "环境地址"}, "selections": [{"divider": true, "title": "Prod"}, {"title": "生产环境", "value": "https://npay.meituan.com", "text": "https://npay.meituan.com"}, {"title": "生产环境", "value": "https://pay.meituan.com", "text": "https://pay.meituan.com"}, {"title": "预发环境", "value": "https://stable.pay.st.sankuai.com", "text": "https://stable.pay.st.sankuai.com"}, {"divider": true, "title": "Test"}, {"title": "测试环境", "value": "http://stable.pay.test.sankuai.com", "text": "http://stable.pay.test.sankuai.com"}, {"title": "泳道环境", "value": "http://pay01-sl-cashier.qa.pay.test.sankuai.com", "text": "http://pay01-sl-cashier.qa.pay.test.sankuai.com"}, {"title": "联调泳道", "value": "http://1989-kwuzl-sl-stable.pay.test.sankuai.com", "text": "http://1989-kwuzl-sl-stable.pay.test.sankuai.com"}]}, {"key": "network_other", "type": "list", "title": "其他设置", "list": [{"type": "navigate", "title": "一键切换环境", "text": "OneClick", "method": "imeituan://www.meituan.com/dev/switchtestenv"}]}]