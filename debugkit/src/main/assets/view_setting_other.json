[{"title": "Horn", "type": "list", "list": [{"type": "navigate", "title": "Horn 开发者面板", "method": "imeituan://www.meituan.com/dev/hornconfig"}, {"type": "navigate", "title": "Horn 设置", "method": "method://debugHorn/showAllHorn", "text": "全部"}]}, {"title": "KNB", "type": "list", "list": [{"key": "enable_knb_debug", "type": "check", "title": "Debug 工具"}]}, {"title": "Debugkit", "type": "list", "list": [{"key": "enable_three_point_debug", "type": "check", "title": "三指点击辅助启动"}, {"key": "enable_debug_toast", "type": "check", "title": "Toast 提示"}, {"key": "enable_pay_log", "type": "check", "title": "Log 日志"}]}, {"type": "list", "title": "Yoda 环境设置", "list": [{"key": "yoda_env", "type": "selectable", "selections": [{"title": "prod", "value": "1", "text": "ONLINE"}, {"title": "stage", "value": "2", "text": "ONLINE_TEST"}, {"title": "dev", "value": "3", "text": "OFFLINE_TEST"}, {"title": "ppe", "value": "4", "text": "OFFLINE_PPE"}, {"title": "test", "value": "5", "text": "OFFLINE_VERIFY_INF_TEST"}]}]}]