package com.sankuai.meituan.retrofit2;

import com.google.gson.reflect.TypeToken;
import com.meituan.android.pay.base.utils.reflect.Reflection;
import com.meituan.android.pay.debugkit.utils.network.PayResponse;
import com.meituan.android.pay.debugkit.utils.network.retrofit.call.PayCall;
import com.meituan.android.pay.debugkit.utils.network.retrofit.call.PayResponseCall;

import java.lang.annotation.Annotation;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 */
public class PayCallAdapterFactory extends CallAdapter.Factory {

    public static PayCallAdapterFactory create() {
        return new PayCallAdapterFactory();
    }

    @Override
    public CallAdapter<?> get(Type returnType, Annotation[] annotations, Retrofit retrofit) {
        if (Reflection.notInstance(returnType, ParameterizedType.class)) {
            return null;
        }
        Class<?> rawType = getRawType(returnType);
        if (rawType != PayCall.class && rawType != PayResponseCall.class) {
            return null;
        }
        Type responseType = getParameterUpperBound(0, (ParameterizedType) returnType);
        if (rawType == PayCall.class) {
            return new PayCallAdapter(responseType);
        } else {
            return new PayResponseCallAdapter(responseType);
        }
    }

    public static class PayCallAdapter implements CallAdapter<PayCall<?>> {
        private final Type responseType;

        public PayCallAdapter(Type responseType) {
            this.responseType = responseType;
        }

        @Override
        public Type responseType() {
            return responseType;
        }

        @Override
        public <T> PayCall<?> adapt(Call<T> call) {
            Call<T> delegate = new ExecutorCallAdapterFactory.ExecutorCallbackCall<>(
                    Platform.get().defaultCallbackExecutor(), call);
            return new PayCall<>(delegate);
        }
    }

    public static class PayResponseCallAdapter implements CallAdapter<PayResponseCall<?>> {
        private final Type responseType;

        public PayResponseCallAdapter(Type responseType) {
            this.responseType = responseType;
        }

        @Override
        public Type responseType() {
            return TypeToken.getParameterized(PayResponse.class, responseType).getType();
        }

        @Override
        public <T> PayResponseCall<?> adapt(Call<T> call) {
            Call<?> delegate = new ExecutorCallAdapterFactory.ExecutorCallbackCall<>(
                    Platform.get().defaultCallbackExecutor(), call);
            //noinspection unchecked
            return new PayResponseCall<>((Call<PayResponse<T>>) delegate);
        }
    }
}
