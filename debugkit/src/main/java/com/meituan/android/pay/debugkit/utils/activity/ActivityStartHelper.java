package com.meituan.android.pay.debugkit.utils.activity;

import android.content.Intent;

import com.meituan.android.pay.base.container.PayBaseActivity;
import com.meituan.android.pay.base.container.PayBaseFragment;
import com.meituan.android.pay.base.utils.observable.ObservableProvider;
import com.meituan.android.pay.base.utils.observable.inf.ActivityStarter;
import com.meituan.android.pay.base.utils.observable.inf.OnActivityResult;

import java.lang.ref.WeakReference;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
public class ActivityStartHelper<T extends ActivityStarter> {
    public static ActivityStartHelper<?> with(PayBaseActivity activity) {
        return bind(activity);
    }

    public static ActivityStartHelper<?> with(PayBaseFragment fragment) {
        return bind(fragment);
    }

    public static ActivityStartHelper<?> with(ActivityStarter starter) {
        return bind(starter);
    }

    static <T extends ActivityStarter> ActivityStartHelper<?> bind(T starter) {
        ActivityStartHelper<?> activityStartHelper = ObservableProvider.from(starter)
                .dispatch(ActivityStartHelper.class)
                .getSpecificObserver();
        if (activityStartHelper == null) {
            activityStartHelper = ObservableProvider.from(starter)
                    .dispatch(ActivityStartHelper.class)
                    .subscribe(new ActivityStartHelper<>(starter))
                    .getSpecificObserver();
        }
        return activityStartHelper;
    }

    private final WeakReference<T> reference;

    private final AtomicInteger requestCode = new AtomicInteger(0xFF);

    private ActivityStartHelper(T starter) {
        this.reference = new WeakReference<>(starter);
    }

    public void register(int requestCode, OnActivityResult callback) {
        register(requestCode, false, callback);
    }

    public void register(int requestCode, boolean callbackOnce, OnActivityResult callback) {
        ObservableProvider.from(reference.get())
                .dispatch(OnActivityResult.class)
                .subscribe(String.valueOf(requestCode), callbackOnce, callback);
    }

    public void startActivityForResult(int requestCode, Intent intent) {
        T starter = reference.get();
        if (starter != null) {
            starter.startActivityForResult(intent, requestCode);
        }
    }

    public void launchForResult(int requestCode, Intent intent, OnActivityResult callback) {
        register(requestCode, true, callback);
        startActivityForResult(requestCode, intent);
    }

    public void launchForResult(Intent intent, OnActivityResult callback) {
        launchForResult(requestCode.getAndIncrement(), intent, callback);
    }

    public void launch(Intent intent) {
        T starter = reference.get();
        if (starter != null && starter.getActivity() != null) {
            starter.getActivity().startActivity(intent);
        }
    }
}
