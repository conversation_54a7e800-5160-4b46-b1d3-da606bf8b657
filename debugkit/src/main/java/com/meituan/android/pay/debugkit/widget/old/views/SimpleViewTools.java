package com.meituan.android.pay.debugkit.widget.old.views;

import android.text.TextUtils;
import android.widget.LinearLayout;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SimpleViewTools {
    private List<SimpleViewHolder> dataRepository = new ArrayList<>();

    private LinearLayout parent;

    private void buildViews() {
        if (parent == null || dataRepository.isEmpty()) {
            return;
        }
        for (int i = 0; i < dataRepository.size(); i++) {
            SimpleViewHolder holder = dataRepository.get(i);
            parent.addView(holder.getView(parent.getContext()).getView());
            if (holder instanceof SimpleDividerView.Holder) {
                final int index = i;
                ((SimpleDividerView.Holder) holder).setListener(v -> flipViews(index));
            }
        }
        for (int i = 0; i < dataRepository.size(); i++) {
            SimpleViewHolder holder = dataRepository.get(i);
            if (holder instanceof SimpleDividerView.Holder) {
                ((SimpleDividerView.Holder) holder).initCollapsedStatus();
            }
        }
    }

    private void flipViews(int position) {
        for (int j = position + 1; j < dataRepository.size(); j++) {
            SimpleViewHolder expandedHolder = dataRepository.get(j);
            SimpleView<? extends SimpleViewBean> flipView = expandedHolder.getView(parent.getContext());
            if (!flipView.flip()) {
                return;
            }
        }
    }

    public void destroy() {
        dataRepository.clear();
        parent = null;
    }

    public String value(String key) {
        for (SimpleViewHolder holder : dataRepository) {
            if (TextUtils.equals(key, holder.key())) {
                return holder.value();
            }
        }
        return "";
    }

    public static Builder builder(LinearLayout parent) {
        return new Builder(parent);
    }

    public static class Builder {
        private final List<SimpleViewHolder> dataRepository = new ArrayList<>();

        private final LinearLayout parent;

        public Builder(LinearLayout parent) {
            this.parent = parent;
        }

        public Builder addSimpleView(SimpleViewBean bean) {
            dataRepository.add(bean.holder());
            return this;
        }

        public SimpleViewTools build() {
            SimpleViewTools simpleViewTools = new SimpleViewTools();
            simpleViewTools.dataRepository = dataRepository;
            simpleViewTools.parent = parent;
            simpleViewTools.buildViews();
            return simpleViewTools;
        }
    }

}
