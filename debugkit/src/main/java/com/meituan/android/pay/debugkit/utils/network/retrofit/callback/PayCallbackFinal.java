package com.meituan.android.pay.debugkit.utils.network.retrofit.callback;

import android.support.annotation.NonNull;

import rx.functions.Action0;

/**
 * <AUTHOR>
 */
public interface PayCallbackFinal {
    void onFinal(@NonNull CallInfo info);

    static PayCallbackFinal simple(Action0 onFinal) {
        return info -> {
            if (onFinal != null) {
                onFinal.call();
            }
        };
    }
}
