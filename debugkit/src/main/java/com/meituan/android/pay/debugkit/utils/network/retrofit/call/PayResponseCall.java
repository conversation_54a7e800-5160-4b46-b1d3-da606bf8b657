package com.meituan.android.pay.debugkit.utils.network.retrofit.call;

import com.meituan.android.pay.debugkit.utils.network.PayResponse;
import com.sankuai.meituan.retrofit2.Call;

import java.io.IOException;

/**
 * <AUTHOR>
 */
public class PayResponseCall<T> extends BaseCall<PayResponse<T>, T> {

    public PayResponseCall(Call<PayResponse<T>> delegate) {
        super(delegate);
    }

    @Override
    protected T transfer(PayResponse<T> response) throws Exception {
        if (PayResponse.isPaySuccess(response)) {
            return response.data();
        } else if (PayResponse.isPayError(response)) {
            throw response.error();
        } else {
            throw new IOException("empty result");
        }
    }
}
