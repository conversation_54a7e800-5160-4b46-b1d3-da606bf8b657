package com.meituan.android.pay.debugkit.utils.network.retrofit.interceptor;

import android.net.Uri;
import android.support.annotation.NonNull;

import com.meituan.android.pay.base.utils.exception.Throw;
import com.meituan.android.pay.debugkit.utils.struct.Stream;
import com.meituan.android.pay.base.utils.scheme.QueryParser;
import com.sankuai.meituan.retrofit2.FormBody;
import com.sankuai.meituan.retrofit2.Request;
import com.sankuai.meituan.retrofit2.RequestBody;
import com.sankuai.meituan.retrofit2.ResponseBody;
import com.sankuai.meituan.retrofit2.raw.RawResponse;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import okio.Buffer;

/**
 * <AUTHOR>
 */
final class RequestUtils {
    private static final String CONTENT_TYPE_RESPONSE = "application/json;charset=UTF-8";

    private static final String CONTENT_TYPE_JSON = "application/json";

    public static boolean isJsonBody(Request request) {
        return request != null && isJsonBody(request.body());
    }

    public static boolean isJsonBody(RequestBody requestBody) {
        if (requestBody == null) {
            return false;
        }
        String contentType = requestBody.contentType();
        return contentType != null && contentType.contains(CONTENT_TYPE_JSON);
    }

    public static String getRequestPath(Request request) {
        if (request == null) {
            return "";
        }
        return Uri.parse(request.url()).getPath();
    }

    public static Map<String, String> readForm(final RequestBody request) throws IOException {
        try (Buffer buffer = new Buffer()) {
            buffer.clear();
            request.writeTo(buffer.outputStream());
            String formString = buffer.readUtf8();
            return QueryParser.decode(formString);
        } catch (Exception e) {
            return new HashMap<>();
        }
    }

    public static RequestBody writeForm(@NonNull Map<String, String> form) {
        FormBody.Builder builder = new FormBody.Builder();
        Stream.on(form).foreach(entry ->
                builder.add(entry.getKey(), entry.getValue()));
        return builder.build();
    }

    public static RawResponse writeBody(RawResponse rawResponse, String bodyString) throws RuntimeException {
        if (rawResponse == null || bodyString == null) {
            throw Throw.origin()
                    .isNull("rawResponse", rawResponse)
                    .isNull("bodyString", bodyString).make();
        }
        try (ResponseBody responseBody = rawResponse.body()) {
            return new RawResponse.Builder(rawResponse)
                    .body(responseBody.newBuilder()
                            .contentType(CONTENT_TYPE_RESPONSE)
                            .contentLength(bodyString.length())
                            .soure(new ByteArrayInputStream(bodyString.getBytes()))
                            .build())
                    .build();
        }
    }

}
