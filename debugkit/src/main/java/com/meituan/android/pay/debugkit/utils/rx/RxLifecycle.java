package com.meituan.android.pay.debugkit.utils.rx;

import android.arch.lifecycle.LifecycleOwner;

import com.meituan.android.pay.base.utils.lifecycle.LifecycleObservation;
import com.meituan.android.pay.base.utils.lifecycle.LifecycleObserver;

import java.lang.ref.WeakReference;

import rx.Subscriber;
import rx.functions.Action1;
import rx.functions.Actions;
import rx.internal.util.ActionSubscriber;
import rx.internal.util.InternalObservableUtils;

/**
 * <AUTHOR>
 */
public class RxLifecycle {
    public static <T> Subscriber<T> bind(LifecycleOwner lifecycleOwner, Subscriber<T> subscriber) {
        if (lifecycleOwner == null) {
            return subscriber;
        }
        RxLifecycleObserver<T> observer = new RxLifecycleObserver<>(subscriber);
        if (!LifecycleObservation.addObserver(lifecycleOwner, observer)) {
            subscriber.unsubscribe();
        }
        return subscriber;
    }

    public static <T> Subscriber<T> bind(LifecycleOwner lifecycleOwner, Action1<? super T> onNext) {
        return bind(lifecycleOwner, new ActionSubscriber<>(onNext,
                InternalObservableUtils.ERROR_NOT_IMPLEMENTED,
                Actions.empty()));
    }

    private static class RxLifecycleObserver<T> implements LifecycleObserver {
        private final WeakReference<Subscriber<T>> subscriberWeakReference;

        public RxLifecycleObserver(Subscriber<T> subscriber) {
            this.subscriberWeakReference = new WeakReference<>(subscriber);
        }

        @Override
        public void onDestroy() {
            Subscriber<T> subscriber = subscriberWeakReference.get();
            if (subscriber != null) {
                subscriber.unsubscribe();
            }
        }
    }
}
