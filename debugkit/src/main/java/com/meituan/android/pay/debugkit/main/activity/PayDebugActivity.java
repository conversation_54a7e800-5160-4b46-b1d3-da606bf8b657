package com.meituan.android.pay.debugkit.main.activity;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.support.v7.app.AppCompatActivity;

import com.meituan.android.pay.base.compat.ActivityCompat;
import com.meituan.android.pay.base.staticis.TechMonitor;
import com.meituan.android.pay.base.utils.lifecycle.LifecycleObservable;
import com.meituan.android.pay.base.utils.observable.ObservableProvider;
import com.meituan.android.pay.base.utils.observable.inf.ActivityStarter;
import com.meituan.android.pay.base.utils.observable.inf.OnActivityResult;
import com.meituan.android.pay.base.utils.observable.inf.OnBackPressed;
import com.meituan.android.pay.base.utils.observable.inf.OnWindowFocusChanged;
import com.meituan.android.paybase.dialog.ProgressController;
import com.meituan.android.paybase.utils.SaveInstanceUtil;

public class PayDebugActivity extends AppCompatActivity implements LifecycleObservable,
        ActivityStarter, OnBackPressed, OnWindowFocusChanged {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        onLifecycleCreated();
        ObservableProvider.bind(this);
        ActivityCompat.UI.hideActionBar(this);
        ActivityCompat.UI.setBasicBackgroundColor(this);

        // 兼容性修复
        ActivityCompat.Adapter.fixOrientation(this);
        ActivityCompat.Adapter.fixActionBar(this);
        // 重建数据
        SaveInstanceUtil.restore(savedInstanceState, this);
    }

    @Override
    protected void onStart() {
        super.onStart();
        onLifecycleStarted();
    }

    @Override
    protected void onResume() {
        super.onResume();
        onLifecycleResumed();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        onLifecycleSaveInstanceState();
        SaveInstanceUtil.restore(outState, this);
    }

    @Override
    protected void onPause() {
        super.onPause();
        onLifecyclePaused();
    }

    @Override
    protected void onStop() {
        super.onStop();
        onLifecycleStopped();
    }

    @Override
    protected void onDestroy() {
        ObservableProvider.unObserve(this);
        onLifecycleDestroyed();
        super.onDestroy();
    }

    public void showLoading() {
        ProgressController.of(this).show();
    }

    public void hideLoading() {
        ProgressController.of(this).hide();
    }

    @Override
    public Activity getActivity() {
        return this;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        // 分发 Activity 结果
        ObservableProvider.from(this)
                .dispatch(OnActivityResult.class)
                .call(it -> {
                    it.onActivityResult(requestCode, resultCode, data);
                });
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        ObservableProvider.from(this)
                .dispatch(OnWindowFocusChanged.class)
                .call(it -> {
                    it.onWindowFocusChanged(hasFocus);
                });
    }

    @Override
    public void onBackPressed() {
        if (onBackPressing()) {
            return;
        }
        try {
            super.onBackPressed();
        } catch (Exception e) {
            TechMonitor.forException(e);
            finish();
        }
    }

    @Override
    public boolean onBackPressing() {
        return ObservableProvider.from(this)
            .dispatch(OnBackPressed.class)
            .call(OnBackPressed::onBackPressing);
    }
}

