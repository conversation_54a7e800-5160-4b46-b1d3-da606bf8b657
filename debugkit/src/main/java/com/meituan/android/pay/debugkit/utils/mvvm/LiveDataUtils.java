package com.meituan.android.pay.debugkit.utils.mvvm;

import android.arch.lifecycle.LifecycleOwner;
import android.arch.lifecycle.LiveData;
import android.arch.lifecycle.MutableLiveData;
import android.arch.lifecycle.Observer;
import android.support.annotation.NonNull;

import com.meituan.android.pay.base.utils.function.Consumer;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class LiveDataUtils {

    public static <T> boolean compareAndSet(MutableLiveData<T> liveData, T value) {
        if (liveData == null || Objects.equals(liveData.getValue(), value)) {
            return false;
        }
        liveData.setValue(value);
        return true;
    }

    public static <T> MutableLiveData<T> from(T t) {
        MutableLiveData<T> liveData = new MutableLiveData<>();
        liveData.setValue(t);
        return liveData;
    }

    @NonNull
    public static <T> Observer<T> observer(Consumer<T> observer) {
        return t -> {
            if (t != null && observer != null) {
                observer.accept(t);
            }
        };
    }

    public static <T> void observe(LifecycleOwner owner,
                                   LiveData<T> liveData,
                                   Consumer<T> observer) {
        if (owner != null && liveData != null && observer != null) {
            liveData.observe(owner, observer(observer));
        }
    }

    public static <T> void observeForever(LiveData<T> liveData, Consumer<T> observer) {
        if (liveData != null && observer != null) {
            liveData.observeForever(observer(observer));
        }
    }

    public static <T> void twoSideBind(LifecycleOwner owner,
                                       MutableLiveData<T> xLiveData,
                                       MutableLiveData<T> yLiveData) {
        if (owner != null && xLiveData != null && yLiveData != null) {
            xLiveData.observe(owner, t -> LiveDataUtils.compareAndSet(yLiveData, t));
            yLiveData.observe(owner, t -> LiveDataUtils.compareAndSet(xLiveData, t));
        }
    }

    public static <T> void twoSideBindForever(MutableLiveData<T> xLiveData,
                                              MutableLiveData<T> yLiveData) {
        if (xLiveData != null && yLiveData != null) {
            xLiveData.observeForever(t -> LiveDataUtils.compareAndSet(yLiveData, t));
            yLiveData.observeForever(t -> LiveDataUtils.compareAndSet(xLiveData, t));
        }
    }

    public static <T> void bind(LifecycleOwner owner,
                                LiveData<T> xLiveData,
                                MutableLiveData<T> yLiveData) {
        if (owner != null && xLiveData != null && yLiveData != null) {
            xLiveData.observe(owner, yLiveData::setValue);
        }
    }

    public static <T> void bindForever(LiveData<T> xLiveData,
                                       MutableLiveData<T> yLiveData) {
        if (xLiveData != null && yLiveData != null) {
            xLiveData.observeForever(yLiveData::setValue);
        }
    }

    public static <T> void remove(LifecycleOwner owner, LiveData<T> liveData) {
        if (owner != null && liveData != null) {
            liveData.removeObservers(owner);
        }
    }

}
