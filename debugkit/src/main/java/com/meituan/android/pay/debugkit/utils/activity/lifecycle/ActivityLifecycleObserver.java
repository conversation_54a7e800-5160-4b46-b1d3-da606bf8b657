package com.meituan.android.pay.debugkit.utils.activity.lifecycle;

import android.app.Activity;
import android.app.Application;
import android.arch.lifecycle.Lifecycle;
import android.arch.lifecycle.LifecycleOwner;
import android.os.Bundle;
import android.support.annotation.NonNull;

import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.pay.base.utils.cache.WeakMapCache;

import java.util.concurrent.atomic.AtomicBoolean;

import rx.functions.Action1;

/**
 * <AUTHOR>
 */
public class ActivityLifecycleObserver implements Application.ActivityLifecycleCallbacks {
    private static final WeakMapCache<Activity, ActivityLifecycleCallback> LIFECYCLE_CACHE =
            WeakMapCache.init(key -> new ActivityLifecycleCallback());

    private static final AtomicBoolean INIT = new AtomicBoolean(false);

    private static void init() {
        if (!INIT.getAndSet(true)) {
            Application application = PayProvider.getApplication();
            application.registerActivityLifecycleCallbacks(new ActivityLifecycleObserver());
        }
    }

    private static ActivityLifecycleCallback createLifecycle(Activity activity) {
        if (activity != null) {
            init();
            return LIFECYCLE_CACHE.cache(activity);
        }
        return null;
    }

    public static LifecycleOwner owner(Activity activity) {
        if (activity instanceof LifecycleOwner) {
            return (LifecycleOwner) activity;
        }
        return ActivityLifecycleObserver.createLifecycle(activity);
    }

    public static Lifecycle lifecycle(Activity activity) {
        return owner(activity).getLifecycle();
    }

    @Override
    public void onActivityCreated(@NonNull Activity activity, Bundle savedInstanceState) {
        dispatch(activity, callback -> callback.onActivityCreated(activity, savedInstanceState));
    }

    @Override
    public void onActivityStarted(@NonNull Activity activity) {
        dispatch(activity, callback -> callback.onActivityStarted(activity));
    }

    @Override
    public void onActivityResumed(@NonNull Activity activity) {
        dispatch(activity, callback -> callback.onActivityResumed(activity));
    }

    @Override
    public void onActivityPaused(@NonNull Activity activity) {
        dispatch(activity, callback -> callback.onActivityPaused(activity));
    }

    @Override
    public void onActivityStopped(@NonNull Activity activity) {
        dispatch(activity, callback -> callback.onActivityStopped(activity));
    }

    @Override
    public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {
        dispatch(activity, callback -> callback.onActivitySaveInstanceState(activity, outState));
    }

    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {
        dispatch(activity, callback -> callback.onActivityDestroyed(activity));
        LIFECYCLE_CACHE.remove(activity);
    }

    private void dispatch(Activity activity, Action1<ActivityLifecycleCallback> action) {
        ActivityLifecycleCallback lifecycle = LIFECYCLE_CACHE.get(activity);
        if (lifecycle != null && action != null) {
            action.call(lifecycle);
        }
    }
}
