package com.meituan.android.pay.debugkit.widget.old.views;

import android.content.Context;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.meituan.android.pay.debugkit.R;

/**
 * <AUTHOR>
 */
public class SimpleContentView extends LinearLayout implements SimpleView<SimpleContentView.Bean> {
    private TextView title;
    private TextView content;

    public SimpleContentView(Context context) {
        super(context);
        init();
    }

    private void init() {
        inflate(getContext(), R.layout.simple_view_content_view, this);
        this.title = findViewById(R.id.title);
        this.content = findViewById(R.id.content);
    }

    public void refresh(Bean bean) {
        if (bean == null) {
            return;
        }
        title.setText(bean.getTitle());
        content.setText(bean.getContent());
    }

    @Override
    public boolean flip() {
        setVisibility(getVisibility() == VISIBLE ? GONE : VISIBLE);
        return true;
    }

    @Override
    public View getView() {
        return this;
    }

    public static class Holder implements SimpleViewHolder {
        private SimpleContentView view;

        private final Bean bean;

        public Holder(Bean bean) {
            this.bean = bean;
        }

        @Override
        public SimpleView<Bean> getView(Context context) {
            if (view != null) {
                return view;
            }
            this.view = new SimpleContentView(context);
            view.refresh(bean);
            return view;
        }

        @Override
        public String key() {
            return bean.key;
        }

        @Override
        public String value() {
            return bean.content;
        }
    }

    public static class Bean implements SimpleViewBean {
        private final String key;

        private String title;

        private String content;

        public Bean(String key, String title, String content) {
            this.key = key;
            this.title = title;
            this.content = content;
        }

        public String getTitle() {
            return title != null ? title : "";
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getContent() {
            return content != null ? content : "";
        }

        public void setContent(String content) {
            this.content = content;
        }

        @Override
        public SimpleViewHolder holder() {
            return new Holder(this);
        }
    }
}
