package com.meituan.android.pay.debugkit.main.data.model;

import android.support.annotation.Keep;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.pay.debugkit.utils.system.DateUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Keep
public class OrderInfo implements Serializable {
    @SerializedName("status")
    private OrderStatus status; // 0未付款, 支付成功16

    @SerializedName(value = "sellerId", alternate = {"seller", "seller_id"})
    private String sellerId;

    @SerializedName(value = "tradeNo", alternate = "transtradeno")
    private String tradeNo; // 订单编号

    @SerializedName(value = "refundNo", alternate = "objid")
    private String refundNo; // 退款单编号

    @SerializedName("subject")
    private String subject; // 订单标题

    @SerializedName("money")
    private String money; // 订单金额

    @SerializedName(value = "payType", alternate = "paytype")
    private PayType payType; // 付款类型

    @SerializedName(value = "orderTime", alternate = "ordertime")
    private long orderTime; // 下单时间

    @SerializedName(value = "payTime", alternate = "paytime")
    private long payTime; // 支付时间

    public OrderStatus getStatus() {
        return status != null ? status : OrderStatus.UNKNOWN;
    }

    public void setStatus(OrderStatus status) {
        this.status = status;
    }

    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getRefundNo() {
        return refundNo;
    }

    public void setRefundNo(String refundNo) {
        this.refundNo = refundNo;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getMoney() {
        return money;
    }

    public String getMoneyContent() {
        return money + "元";
    }

    public void setMoney(String money) {
        this.money = money;
    }

    public PayType getPayType() {
        return payType;
    }

    public void setPayType(PayType payType) {
        this.payType = payType;
    }

    public long getOrderTime() {
        return orderTime;
    }

    public String getOrderTimeFormatted() {
        return DateUtils.format(orderTime * 1000L);
    }

    public void setOrderTime(long orderTime) {
        this.orderTime = orderTime;
    }

    public long getPayTime() {
        return payTime;
    }

    public String getPayTimeFormatted() {
        return DateUtils.format(payTime * 1000L);
    }

    public void setPayTime(long payTime) {
        this.payTime = payTime;
    }
}
