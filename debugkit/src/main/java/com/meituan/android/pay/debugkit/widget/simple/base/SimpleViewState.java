package com.meituan.android.pay.debugkit.widget.simple.base;

import android.arch.lifecycle.MutableLiveData;
import android.text.TextUtils;

import com.google.gson.annotations.JsonAdapter;
import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.pay.base.utils.function.CollectionUtils;
import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.pay.debugkit.utils.mvvm.LiveDataUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@JsonAdapter(SimpleStateTypeAdapter.class)
public abstract class SimpleViewState implements Serializable, SimpleViewInflater {
    public static final String SERIALIZE_KEY = "type";

    protected String key = "simple-" + hashCode();

    protected String type;

    protected String method;

    protected Map<String, String> methods;

    protected String[] args;

    protected final MutableLiveData<String> value = new MutableLiveData<>();

    // ********************
    public List<SimpleViewState> children() {
        return new ArrayList<>();
    }

    public String getKey() {
        return key;
    }

    public String getVal() {
        return getValue().getValue();
    }

    public String getMethod() {
        if (DebugManager.info().isInPayDemo()) {
            return method;
        }
        if (methods != null) {
            String appMethod = CollectionUtils.get(methods, PayProvider.app().getAppName());
            if (appMethod != null) {
                return appMethod;
            }
        }
        return method;
    }

    public String[] getArgs() {
        return args;
    }

    public MutableLiveData<String> getValue() {
        return value;
    }

    public MutableLiveData<SimpleMethod> getInvokedMethod() {
        return invokedMethod;
    }

    public void setValue(String value) {
        LiveDataUtils.compareAndSet(this.value, value);
    }

    // ******************** invoke ********************
    protected final transient MutableLiveData<SimpleMethod> invokedMethod = new MutableLiveData<>();

    public void invoke() {
        invoke(new SimpleMethod(getMethod(), args));
    }

    public void invoke(String method, String... args) {
        invoke(new SimpleMethod(method, args));
    }

    protected void invoke(SimpleMethod simpleMethod) {
        invokedMethod.setValue(simpleMethod);
    }
}
