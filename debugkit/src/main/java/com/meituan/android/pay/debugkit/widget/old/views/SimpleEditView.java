package com.meituan.android.pay.debugkit.widget.old.views;

import android.content.Context;
import android.support.annotation.Nullable;
import android.support.v4.content.ContextCompat;
import android.util.AttributeSet;
import android.view.View;

import com.meituan.android.pay.debugkit.widget.old.CellView;
import com.meituan.android.pay.debugkit.R;

/**
 * <AUTHOR>
 */
public class SimpleEditView extends CellView implements SimpleView<SimpleEditView.Bean> {

    public SimpleEditView(Context context) {
        super(context);
    }

    public SimpleEditView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public SimpleEditView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void refresh(Bean bean) {
        if (bean == null) {
            return;
        }
        setTitleColor(ContextCompat.getColor(getContext(), R.color.cashier__gray));
        setTitle(bean.title);
        setHeight(45);
        setUseEditText(true);
        setEditText(bean.content);
        setHintText(bean.hint);
    }

    @Override
    public boolean flip() {
        setVisibility(getVisibility() == VISIBLE ? GONE : VISIBLE);
        return true;
    }

    @Override
    public View getView() {
        return this;
    }

    public static class Holder implements SimpleViewHolder {
        private SimpleEditView view;

        private final Bean bean;

        public Holder(Bean bean) {
            this.bean = bean;
        }

        @Override
        public SimpleView<Bean> getView(Context context) {
            if (view != null) {
                return view;
            }
            this.view = new SimpleEditView(context);
            view.refresh(bean);
            return view;
        }

        @Override
        public String key() {
            return bean.key;
        }

        @Override
        public String value() {
            return view.getContent();
        }
    }

    public static class Bean implements SimpleViewBean {
        private final String key;

        private String title;

        private String content;

        private String hint;

        public Bean(String key, String title, String content) {
            this.key = key;
            this.title = title;
            this.content = content;
        }

        public Bean(String key, String title, String content, String hint) {
            this.key = key;
            this.title = title;
            this.content = content;
            this.hint = hint;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getHint() {
            return hint;
        }

        public void setHint(String hint) {
            this.hint = hint;
        }

        @Override
        public SimpleViewHolder holder() {
            return new Holder(this);
        }
    }
}
