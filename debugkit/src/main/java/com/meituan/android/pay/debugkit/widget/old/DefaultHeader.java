package com.meituan.android.pay.debugkit.widget.old;

import android.content.Context;
import android.support.v4.view.ViewCompat;
import android.util.AttributeSet;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.meituan.android.pay.debugkit.widget.old.listener.IHeaderView;
import com.meituan.android.pay.debugkit.R;


/**
 * 默认下拉刷新样式
 */
public class DefaultHeader extends RelativeLayout implements IHeaderView {
    private CommonSpinLoadingView loadingView;
    private TextView textView;

    public DefaultHeader(Context context) {
        this(context, null);
    }

    public DefaultHeader(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DefaultHeader(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        inflate(getContext(), R.layout.pull_to_refresh_default_header, this);


        loadingView = findViewById(R.id.refreshing_icon);
        textView = findViewById(R.id.text);


    }

    @Override
    public void pullToRefresh() {
        loadingView.hideSpinning();

        textView.setText(getContext().getString(R.string.paybase__pull_to_refresh));
    }

    @Override
    public void releaseToRefresh() {
        loadingView.hideSpinning();

        textView.setText(getContext().getString(R.string.paybase__release_to_refresh));

    }


    @Override
    public void onRefresh() {
        loadingView.showSpinning();
        textView.setVisibility(View.INVISIBLE);//开始刷新的时候消失
    }


    @Override
    public void onReset(float distance, float fraction) {
        loadingView.hideSpinning();

    }



    @Override
    public void onPull(float distance, float fraction) {
        textView.setVisibility(View.VISIBLE);
        loadingView.hideSpinning();

        fraction = fraction * 10;
        if (fraction > 1) {
            fraction = 1f;
        }
        ViewCompat.setAlpha(textView, fraction);

    }
}
