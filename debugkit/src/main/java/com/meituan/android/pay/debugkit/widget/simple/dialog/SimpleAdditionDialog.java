package com.meituan.android.pay.debugkit.widget.simple.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import com.meituan.android.pay.debugkit.utils.system.InputUtils;
import com.meituan.android.pay.debugkit.utils.ui.UIUtils;
import com.meituan.android.pay.debugkit.R;

/**
 * <AUTHOR>
 */
public class SimpleAdditionDialog extends Dialog {
    private final String title;
    private final String selectionTitle;
    private final String selectionText;
    private final String positiveText;
    private final String negativeText;
    private final OnClickListener positiveListener;

    private SimpleAdditionDialog(Builder builder) {
        super(builder.activity);
        this.title = builder.title;
        this.selectionTitle = builder.selectionTitle;
        this.selectionText = builder.selectionText;
        this.positiveText = builder.positiveText;
        this.negativeText = builder.negativeText;
        this.positiveListener = builder.positiveListener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        int maxWidth = (int) (UIUtils.Screen.getWidowWidth() * 0.8233);
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(maxWidth, ViewGroup.LayoutParams.WRAP_CONTENT);
        View view = getLayoutInflater()
                .inflate(R.layout.pay_debug_simple_dialog, (ViewGroup) null, false);
        setContentView(view, params);

        // 初始化视图
        TextView titleView = findViewById(R.id.title);
        EditText selectionTitleView = findViewById(R.id.selection_title);
        EditText selectionTextView = findViewById(R.id.selection_text);
        Button negativeView = findViewById(R.id.negative);
        Button positiveView = findViewById(R.id.positive);

        // 设置内容
        titleView.setText(title);
        selectionTitleView.setHint(selectionTitle);
        selectionTextView.setHint(selectionText);
        negativeView.setText(negativeText);
        positiveView.setText(positiveText);

        // 设置点击监听
        positiveView.setOnClickListener(v -> {
            if (positiveListener != null) {
                positiveListener.onClick(selectionTitleView.getText().toString(),
                        selectionTextView.getText().toString());
            }
            dismiss();
        });

        negativeView.setOnClickListener(v -> dismiss());
        setCancelable(false);
        setCanceledOnTouchOutside(false);
    }

    @Override
    public void dismiss() {
        InputUtils.hideKeyBoard(getCurrentFocus());
        super.dismiss();
    }

    public static class Builder {
        private final Activity activity;
        private String title = "Title";
        private String selectionTitle = "";
        private String selectionText = "";
        private String positiveText = "确认";
        private String negativeText = "取消";
        private OnClickListener positiveListener;

        public Builder(Activity activity) {
            this.activity = activity;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setSelectionTitle(String selectionTitle) {
            this.selectionTitle = selectionTitle;
            return this;
        }

        public Builder setSelectionText(String selectionText) {
            this.selectionText = selectionText;
            return this;
        }

        public Builder setPositiveButton(String text, OnClickListener listener) {
            this.positiveText = text;
            this.positiveListener = listener;
            return this;
        }

        public Builder setNegativeButton(String text) {
            this.negativeText = text;
            return this;
        }

        public SimpleAdditionDialog create() {
            return new SimpleAdditionDialog(this);
        }
    }

    public interface OnClickListener {
        void onClick(String selectionTitle, String selectionText);
    }
}
