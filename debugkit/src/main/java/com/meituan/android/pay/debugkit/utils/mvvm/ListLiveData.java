package com.meituan.android.pay.debugkit.utils.mvvm;

import android.arch.lifecycle.LiveData;
import android.arch.lifecycle.MutableLiveData;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

@JsonAdapter(ListLiveData.ListLiveDataTypeAdapter.class)
public class ListLiveData<T> extends MutableLiveData<List<T>> {

    private final MutableLiveData<List<T>> additionalList = new MutableLiveData<>();

    public LiveData<List<T>> additional() {
        return additionalList;
    }

    public void addValue(List<T> list) {
        List<T> value = getValue();
        if (value == null) {
            value = new ArrayList<>();
            setValue(value);
        }
        if (list != null) {
            additionalList.setValue(list);
            value.addAll(list);
        }
    }

    public static class ListLiveDataTypeAdapter implements JsonSerializer<ListLiveData<?>>,
            JsonDeserializer<ListLiveData<?>> {

        @Override
        public ListLiveData<?> deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
            List<Object> value = context.deserialize(json, TypeToken.getParameterized(List.class,
                    ((ParameterizedType) typeOfT).getActualTypeArguments()[0]).getType());
            ListLiveData<Object> listLiveData = new ListLiveData<>();
            listLiveData.setValue(value);
            return listLiveData;
        }

        @Override
        public JsonElement serialize(ListLiveData<?> src, Type typeOfSrc, JsonSerializationContext context) {
            return context.serialize(src.getValue());
        }
    }
}