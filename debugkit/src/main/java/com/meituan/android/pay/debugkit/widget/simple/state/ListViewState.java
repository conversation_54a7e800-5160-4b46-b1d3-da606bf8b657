package com.meituan.android.pay.debugkit.widget.simple.state;

import android.arch.lifecycle.LiveData;
import android.support.annotation.Keep;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import com.meituan.android.pay.base.container.PayBaseFragment;
import com.meituan.android.pay.base.utils.function.CollectionUtils;
import com.meituan.android.pay.debugkit.utils.mvvm.BindingActions;
import com.meituan.android.pay.debugkit.utils.mvvm.ListLiveData;
import com.meituan.android.pay.debugkit.utils.mvvm.LiveDataUtils;
import com.meituan.android.pay.debugkit.utils.ui.UIUtils;
import com.meituan.android.pay.debugkit.widget.simple.base.SimpleViewState;
import com.meituan.android.pay.debugkit.databinding.PayDebugSimpleListBinding;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import java.util.List;

/**
 * <AUTHOR>
 */
@Keep
@ServiceLoaderInterface(key = "list", interfaceClass = SimpleViewState.class)
public class ListViewState extends TitleViewState {

    private String color;

    private final ListLiveData<SimpleViewState> list = new ListLiveData<>();

    public String getColor() {
        return TextUtils.isEmpty(color) ? "#FFFFD100" : color;
    }

    public LiveData<List<SimpleViewState>> getList() {
        return this.list;
    }

    public void setList(List<SimpleViewState> list) {
        this.list.setValue(list);
    }

    @Override
    public List<SimpleViewState> children() {
        return CollectionUtils.removeNull(this.list.getValue());
    }

    @Override
    public View inflate(PayBaseFragment fragment) {
        PayDebugSimpleListBinding binding = PayDebugSimpleListBinding
                .inflate(fragment.getLayoutInflater(), (ViewGroup) fragment.getView(), false);
        BindingActions.setValueVisibility(binding.titleLayout, getTitle());
        binding.titleLayout.setOnClickListener(v -> UIUtils.Visibility.flipVisible(binding.list));
        binding.title.setText(getTitle());
        LiveDataUtils.observe(fragment, getList(), simpleViewStates -> {
            binding.list.removeAllViews();
            for (SimpleViewState viewState : simpleViewStates) {
                binding.list.addView(viewState.inflate(fragment));
            }
        });
        return binding.getRoot();
    }
}
