package com.meituan.android.pay.debugkit.main.data.local;

import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.pay.base.utils.function.MapBuilder;
import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.pay.debugkit.main.data.model.OrderInfo;
import com.meituan.android.pay.debugkit.main.data.model.OrderRequest;
import com.meituan.android.pay.debugkit.main.data.model.RefundResult;
import com.meituan.android.pay.debugkit.main.data.remote.OrderApiCall;
import com.meituan.android.pay.debugkit.main.data.remote.RefundApiCall;
import com.meituan.android.pay.debugkit.utils.network.retrofit.PayRetrofit;
import com.meituan.android.pay.debugkit.utils.network.retrofit.call.PayResponseCall;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import rx.Observable;

/**
 * <AUTHOR>
 */
public class OrderRepository {
    public static final String SELLER_ID = "sellerId";
    public static final String SELLER_ID_TEST = "11000002029894";
    public static final String SELLER_ID_PROD = "11000019125438";

    public static final String PARTNER_ID = "partner_key";
    public static final String PARTNER_ID_TEST = "oCr7Sd9FJ2rMCZMWulbhW6dg1M70aYvu";
    public static final String PARTNER_ID_PROD = "3Z3oY8BSObNfqNXzDAjgiBQuRm21LM8c";

    public static final String NO_LOGIN = "noLogin";
    public static final String ORDER_INFO = "orderInfo";

    public final Map<String, String> ORDER_PARAMS_TEST = new HashMap<>();
    public final Map<String, String> ORDER_PARAMS_PROD = new HashMap<>();

    {
        ORDER_PARAMS_TEST.putAll(MapBuilder.string()
                .add(PARTNER_ID, PARTNER_ID_TEST)
                .add(SELLER_ID, SELLER_ID_TEST)
                .add(NO_LOGIN, "0")
                .add(ORDER_INFO, "")
                .build());
        ORDER_PARAMS_PROD.putAll(MapBuilder.string()
                .add(PARTNER_ID, PARTNER_ID_PROD)
                .add(SELLER_ID, SELLER_ID_PROD)
                .add(NO_LOGIN, "0")
                .add(ORDER_INFO, "")
                .build());
    }

    public PayResponseCall<OrderRequest> submitOrder(Map<String, String> form) {
        return PayRetrofit.get()
                .create(OrderApiCall.class)
                .submitOrder(form);
    }

    public PayResponseCall<List<OrderInfo>> queryOrderRefundList() {
        return PayRetrofit.get()
                .create(RefundApiCall.class)
                .queryRefundOrder(PayProvider.app().getUserId(), 10, 0);
    }

    public PayResponseCall<RefundResult> submitRefund() {
        return PayRetrofit.get()
                .create(RefundApiCall.class)
                .processRefund(null);
    }

    public Observable<Map<String, String>> getOrderParams(String cashierType) {
        return DebugManager.info().getOfflineStatus()
                .take(1)
                .map(isOffline -> {
                    Map<String, String> orderParams;
                    if (isOffline) {
                        orderParams = new HashMap<>(ORDER_PARAMS_TEST);
                    } else {
                        orderParams = new HashMap<>(ORDER_PARAMS_PROD);
                    }
                    if ("oneclickpay".equals(cashierType)) {
                        orderParams.put("tradeScene", "7");
                    }
                    return orderParams;
                });
    }

    public Observable<String> getOrderParam(String key) {
        return DebugManager.info().getOfflineStatus()
                .take(1)
                .map(isOffline -> {
                    Map<String, String> orderParams;
                    if (isOffline) {
                        orderParams = ORDER_PARAMS_TEST;
                    } else {
                        orderParams = ORDER_PARAMS_PROD;
                    }
                    return orderParams.get(key);
                });
    }
}
