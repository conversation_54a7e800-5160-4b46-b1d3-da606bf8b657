package com.meituan.android.pay.debugkit.capacity;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;

import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.paybase.fingerprint.manager.IPayFingerprint;
import com.meituan.android.paybase.fingerprint.manager.PayFingerprintFactory;
import com.meituan.android.paybase.fingerprint.soter.sotercore.external.SoterCore;
import com.meituan.android.paybase.fingerprint.util.FingerprintProcessController;
import com.meituan.android.paybase.fingerprint.util.GoogleFingerprintKeyUtil;

/**
 * <AUTHOR>
 */
public class DebugFingerprint {

    public static class CheckGoogleKey {
        public void genKey() {
            GoogleFingerprintKeyUtil.createKey(PayProvider.app().getUserId());
        }

        public void removeKey() {
            GoogleFingerprintKeyUtil.removeKey(PayProvider.app().getUserId());
        }

        public void hasKey() {
            GoogleFingerprintKeyUtil.hasKey(PayProvider.app().getUserId());
        }
    }

    public static class CheckSoterKey {

        public void genKey() {
            SoterCore.generateAppGlobalSecureKey();
            SoterCore.generateAuthKey(SoterCore.getLoginAuthKeyName(""));
        }

        public void removeKey() {
            SoterCore.removeAppGlobalSecureKey();
        }

        public void removeAuth() {
            SoterCore.removeAuthKey(SoterCore.getLoginAuthKeyName(""), false);
        }

        public void startCheckFinger(String pageType, String fingerType) {
            Uri.Builder builder = Uri.parse("meituanpayment://auth/verifyfingerprint").buildUpon();
            builder.appendQueryParameter("partner_id", "115");
            builder.appendQueryParameter("merchant_no", "11000006520594");
            builder.appendQueryParameter("order_no", "123");
            builder.appendQueryParameter("pagetip", "tttt");
            builder.appendQueryParameter("scene", "12");
            builder.appendQueryParameter("title", "titleeee");
            builder.appendQueryParameter("tip", "tip");
            builder.appendQueryParameter("subtip", "subtipppp");
            builder.appendQueryParameter("finger_type", fingerType); // 1,2
            builder.appendQueryParameter("pagetype", pageType); // 1,2
            if (!TextUtils.isEmpty("finger_type")) {
                builder.appendQueryParameter("challenge", "1");
            }

            Uri uri = builder.build();
            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
            intent.setPackage(PayProvider.getPackageName());
            Activity activity = DebugManager.getActivity();

//            ActivityObserlaunchvation.start(activity)
//                    .(intent, 2, (requestCode, resultCode, data) -> {
//                        if (resultCode == Activity.RESULT_OK) {
//                            ToastUtils.showToast(activity, "token:" + data.getStringExtra("fingerprint_token"));
//                        } else {
//                            String errorMessage = "指纹验证取消";
//                            if (data != null) {
//                                errorMessage += ":" + data.getStringExtra("error_message");
//                            }
//                            ToastUtils.showToast(activity, errorMessage);
//                        }
//                    });
        }

        // 标明当前设备是否支持指纹识别
        public void detectHardware() {
            IPayFingerprint googleImpl = PayFingerprintFactory.getInstance(FingerprintProcessController.GOOGLE_PROCESS);
            boolean googleIsHardwareDetected = googleImpl != null && googleImpl.isHardwareDetected();
        }

        // 标明当前设备是否有已录入指纹支持指纹识别
        public void hasEnrolledFingerprints() {
            IPayFingerprint googleImpl = PayFingerprintFactory.getInstance(FingerprintProcessController.GOOGLE_PROCESS);
            boolean googleHasEnrolledFingerprints = googleImpl != null && googleImpl.hasEnrolledFingerprints();
        }
    }
}
