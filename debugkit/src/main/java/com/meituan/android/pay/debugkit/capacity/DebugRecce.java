package com.meituan.android.pay.debugkit.capacity;

import android.content.Context;
import android.support.annotation.Keep;

import com.meituan.android.pay.base.utils.debug.PayDebugCenter;
import com.meituan.android.pay.common.recce.RecceDebug;
import com.meituan.android.pay.debugkit.DebugInfo;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * <AUTHOR>
 */
@Keep
@ServiceLoaderInterface(key = "debug_recce", interfaceClass = DebugInit.class)
public class DebugRecce implements DebugInit, RecceDebug {

    @Override
    public void onInit(Context context, DebugInfo debugInfo) {
        PayDebugCenter.register(RecceDebug.class, this);
    }

    @Override
    public boolean isBundleVersionAvailable(String businessScene, String bundleVersion) {
        return true;
    }

    @Override
    public String getBundleVersion(String bundleName) {
        return "2.0.0";
    }

    @Override
    public boolean isAvailable(String key) {
        return false;
    }
}
