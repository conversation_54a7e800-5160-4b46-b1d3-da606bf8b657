package com.meituan.android.pay.debugkit.capacity;

import static android.content.Intent.FLAG_ACTIVITY_CLEAR_TOP;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.meituan.android.common.horn.Horn;
import com.meituan.android.common.horn2.InnerHorn;
import com.meituan.android.common.horn2.storage.FileLocalStorage;
import com.meituan.android.common.horn2.storage.ILocalStorage;
import com.meituan.android.common.horn2.storage.StorageBean;
import com.meituan.android.pay.base.utils.exception.Catch;
import com.meituan.android.pay.base.utils.function.ArrayUtils;
import com.meituan.android.pay.base.utils.observable.inf.OnDestroy;
import com.meituan.android.pay.base.utils.serialize.GsonUtils;
import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.pay.debugkit.utils.activity.lifecycle.ActivityLifecycleObserver;
import com.meituan.android.pay.debugkit.utils.router.OpenMethod;
import com.meituan.android.pay.debugkit.utils.struct.Stream;
import com.meituan.android.pay.debugkit.utils.system.AssetsUtils;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import java.io.File;
import java.lang.reflect.Field;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@ServiceLoaderInterface(key = "debugHorn", interfaceClass = OpenMethod.class)
public class DebugHorn implements OpenMethod {
    public static DebugFileLocalStorage debugFileLocalStorage;

    public void debug(boolean debug, String hornName) {
        Horn.debug(DebugManager.getApplication(), hornName, debug);
    }

    public void showPayHorn() {
        Activity activity = DebugManager.getActivity();
        if (activity == null) {
            return;
        }
        hookHorn();
        setOnlyPayHorn(true);
        String scheme = "imeituan://www.meituan.com/dev/hornconfiglist";
        Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(scheme));
        intent.setFlags(FLAG_ACTIVITY_CLEAR_TOP);
        intent.setPackage(activity.getPackageName());
        activity.startActivity(intent);

        ActivityLifecycleObserver.lifecycle(activity).addObserver((OnDestroy) () ->
                DebugHorn.this.setOnlyPayHorn(false));
    }

    public void showAllHorn() {
        Activity activity = DebugManager.getActivity();
        if (activity == null) {
            return;
        }
        hookHorn();
        setOnlyPayHorn(false);
        String scheme = "imeituan://www.meituan.com/dev/hornconfiglist";
        Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(scheme));
        intent.setFlags(FLAG_ACTIVITY_CLEAR_TOP);
        intent.setPackage(activity.getPackageName());
        activity.startActivity(intent);
    }

    private void hookHorn() {
        if (debugFileLocalStorage != null) {
            return;
        }
        try {
            Field InnerHornStorage = Stream.on(ArrayUtils.asList(InnerHorn.class.getDeclaredFields()))
                    .filter(field -> ILocalStorage.class.isAssignableFrom(field.getType()))
                    .first();
            if (InnerHornStorage != null) {
                InnerHornStorage.setAccessible(true);
                FileLocalStorage fileLocalStorage = (FileLocalStorage) InnerHornStorage.get(null);
                debugFileLocalStorage = new DebugFileLocalStorage(fileLocalStorage);
                debugFileLocalStorage.initContext(DebugManager.getApplication());
                InnerHornStorage.set(null, debugFileLocalStorage);
            }
        } catch (Exception e) {
            Catch.with(e).log();
        }
    }

    private void setOnlyPayHorn(boolean onlyPayHorn) {
        if (debugFileLocalStorage != null) {
            debugFileLocalStorage.setOnlyPayHorn(onlyPayHorn);
        }
    }

    public static final class DebugFileLocalStorage extends FileLocalStorage {
        private static final List<String> PAY_HORN_LIST = GsonUtils.toStringList(AssetsUtils.get("pay_horn_list.json"));
        private static final Set<String> PAY_HORN_LIST_CACHE = new HashSet<>();
        private final FileLocalStorage delegate;
        private boolean onlyPayHorn;

        public DebugFileLocalStorage(FileLocalStorage fileLocalStorage) {
            this.delegate = fileLocalStorage;
        }

        public void setOnlyPayHorn(boolean onlyPayHorn) {
            this.onlyPayHorn = onlyPayHorn;
        }

        @Override
        public void initContext(@NonNull Context context) {
            delegate.initContext(context);
        }

        @Override
        public void initStorage() {
            delegate.initStorage();
        }

        @NonNull
        @Override
        public Set<String> localConfigs() {
            Set<String> set = delegate.localConfigs();
            Set<String> configs = new HashSet<>();
            if (onlyPayHorn) {
                for (String hornName : set) {
                    if (PAY_HORN_LIST_CACHE.contains(hornName)) {
                        configs.add(hornName);
                    } else if (Stream.on(PAY_HORN_LIST).filter(hornName::contains).first() != null) {
                        PAY_HORN_LIST_CACHE.add(hornName);
                        configs.add(hornName);
                    }
                }
                return configs;
            } else {
                return set;
            }
        }

        @NonNull
        @Override
        public Set<String> debugTypes() {
            return delegate.debugTypes();
        }

        @Override
        public void setDebug(@NonNull String type, boolean isDebug) {
            delegate.setDebug(type, isDebug);
        }

        @Override
        public boolean isMock() {
            return delegate.isMock();
        }

        @Override
        public void setMock(boolean mock) {
            delegate.setMock(mock);
        }

        @NonNull
        @Override
        public StorageBean loadConfig(@NonNull String type, int flags) {
            return delegate.loadConfig(type, flags);
        }

        @Override
        public void storeConfig(@NonNull StorageBean bean) {
            delegate.storeConfig(bean);
        }

        @Override
        public void clearConfig(@NonNull String type) {
            delegate.clearConfig(type);
        }

        @Override
        public void storeDebugContent(@NonNull String type, String content) {
            delegate.storeDebugContent(type, content);
        }

        @Nullable
        @Override
        public File fetchOrDownloadBlob(@NonNull String url, @NonNull String type, boolean canDownload) {
            return delegate.fetchOrDownloadBlob(url, type, canDownload);
        }

        @Override
        public boolean isLaunchException() {
            return delegate.isLaunchException();
        }

        @Override
        public long lastRequestMs(@NonNull String type) {
            return delegate.lastRequestMs(type);
        }

        @Override
        public void setLastRequestMs(@NonNull String type, long value) {
            delegate.setLastRequestMs(type, value);
        }
    }
}