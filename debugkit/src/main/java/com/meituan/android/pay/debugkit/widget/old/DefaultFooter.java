package com.meituan.android.pay.debugkit.widget.old;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.FrameLayout;

import com.meituan.android.pay.debugkit.widget.old.listener.IFooterView;
import com.meituan.android.pay.debugkit.R;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/12/27.
 * 默认上拉加载样式
 */

public class DefaultFooter extends FrameLayout implements IFooterView {
    private CommonSpinLoadingView loadingView;

    public DefaultFooter(Context context) {
        this(context, null);
    }

    public DefaultFooter(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DefaultFooter(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        inflate(getContext(), R.layout.pull_to_refresh_default_footer, this);
        loadingView = findViewById(R.id.refreshing_icon);
    }
    @Override
    public void onStopLoadMore() {

    }

    @Override
    public void onLoadMore() {
        loadingView.startSpinning();

    }

}
