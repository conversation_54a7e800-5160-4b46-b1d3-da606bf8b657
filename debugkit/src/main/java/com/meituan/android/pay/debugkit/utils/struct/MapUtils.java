package com.meituan.android.pay.debugkit.utils.struct;

import com.meituan.android.pay.base.utils.function.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class MapUtils {

    public static List<String> selectValues(Map<String, String> map, List<String> keys) {
        List<String> values = new ArrayList<>();
        if (CollectionUtils.isEmpty(map) || CollectionUtils.isEmpty(keys)) {
            return values;
        }
        for (String key : keys) {
            values.add(map.get(key));
        }
        return values;
    }
}
