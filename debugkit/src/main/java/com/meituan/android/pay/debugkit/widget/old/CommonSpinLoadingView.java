package com.meituan.android.pay.debugkit.widget.old;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.View;

import com.meituan.android.pay.debugkit.R;

/**
 * Created by mengyang on 2017/6/30.
 */

public class CommonSpinLoadingView extends View {

    //Sizes (with defaults)
    private float mSpinWidth = 2f;
    private float mSpinLength = 160f;
    private float mBgWidth = 2f;

    //Colors (with defaults)
    private int mSpinColor;
    private int mBgColor;

    //Animation
    private float mSpinSpeed = 10f;
    private float mProgress = 0f;
    private int mDelayMillis = 20;//每次绘制间隔多少毫秒
    private boolean isSpinning = false;

    //Paint
    private Paint mSpinPaint = new Paint();
    private Paint mBgPaint = new Paint();

    //RectF
    private RectF mSpinCircleBounds;
    private RectF mBgCircleBounds;

    //View
    private int mLayoutWidth;
    private int mLayoutHeight;
    private int mPaddingTop;
    private int mPaddingBottom;
    private int mPaddingLeft;
    private int mPaddingRight;


    public CommonSpinLoadingView(Context context, AttributeSet attrs) {
        super(context, attrs);
        parseAttributes(context.obtainStyledAttributes(attrs, R.styleable.pay_debug_common_spin_loading_view));
    }

    private void parseAttributes(TypedArray a) {
        mSpinWidth = a.getDimension(R.styleable.pay_debug_common_spin_loading_view_spinWidth, mSpinWidth);
        mBgWidth = a.getDimension(R.styleable.pay_debug_common_spin_loading_view_bgWidth, mBgWidth);
        mSpinLength = a.getFloat(R.styleable.pay_debug_common_spin_loading_view_spinLength, mSpinLength);
        mSpinSpeed = a.getFloat(R.styleable.pay_debug_common_spin_loading_view_spinSpeed, mSpinSpeed);

        mDelayMillis = a.getInteger(R.styleable.pay_debug_common_spin_loading_view_delayMillis, mDelayMillis);
        if (mDelayMillis < 0) {
            mDelayMillis = 10;
        }

        mSpinColor = a.getColor(R.styleable.pay_debug_common_spin_loading_view_spinColor, getResources().getColor(R.color.paybase__base_green));
        mBgColor = a.getColor(R.styleable.pay_debug_common_spin_loading_view_bgColor, getResources().getColor(R.color.paybase__background_color));

        a.recycle();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);

        int size = 0;
        int width = getMeasuredWidth();
        int height = getMeasuredHeight();
        int widthWithoutPadding = width - getPaddingLeft() - getPaddingRight();
        int heightWithoutPadding = height - getPaddingTop() - getPaddingBottom();

        int heightMode = MeasureSpec.getMode(heightMeasureSpec);
        int widthMode = MeasureSpec.getMode(widthMeasureSpec);
        if (heightMode != MeasureSpec.UNSPECIFIED && widthMode != MeasureSpec.UNSPECIFIED) {
            if (widthWithoutPadding > heightWithoutPadding) {
                size = heightWithoutPadding;
            } else {
                size = widthWithoutPadding;
            }
        } else {
            size = Math.max(heightWithoutPadding, widthWithoutPadding);
        }

        setMeasuredDimension(
                size + getPaddingLeft() + getPaddingRight(),
                size + getPaddingTop() + getPaddingBottom());
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mLayoutWidth = w;
        mLayoutHeight = h;
        setBounds();
        setPaints();
    }

    private void setBounds() {
        int minValue = Math.min(mLayoutWidth, mLayoutHeight);
        int xOffset = mLayoutWidth - minValue;
        int yOffset = mLayoutHeight - minValue;

        mPaddingTop = getPaddingTop() + (yOffset / 2);
        mPaddingBottom = getPaddingBottom() + (yOffset / 2);
        mPaddingLeft = getPaddingLeft() + (xOffset / 2);
        mPaddingRight = getPaddingRight() + (xOffset / 2);

        int width = getWidth();
        int height = getHeight();

        mSpinCircleBounds = new RectF(
                mPaddingLeft + mSpinWidth,
                mPaddingTop + mSpinWidth,
                width - mPaddingRight - mSpinWidth,
                height - mPaddingBottom - mSpinWidth);
        mBgCircleBounds = new RectF(
                mPaddingLeft + mBgWidth,
                mPaddingTop + mBgWidth,
                width - mPaddingRight - mBgWidth,
                height - mPaddingBottom - mBgWidth);
    }

    private void setPaints() {
        mSpinPaint.setColor(mSpinColor);
        mSpinPaint.setAntiAlias(true);
        mSpinPaint.setStyle(Paint.Style.STROKE);
        mSpinPaint.setStrokeWidth(mSpinWidth);

        mBgPaint.setColor(mBgColor);
        mBgPaint.setAntiAlias(true);
        mBgPaint.setStyle(Paint.Style.STROKE);
        mBgPaint.setStrokeWidth(mBgWidth);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        //绘制背景圆环
        canvas.drawArc(mBgCircleBounds, 0, 360, false, mBgPaint);
        //绘制转动圆环
        if (isSpinning) {
            canvas.drawArc(mSpinCircleBounds, mProgress - 90, mSpinLength, false, mSpinPaint);
            scheduleRedraw();
        }
    }

    private void scheduleRedraw() {
        mProgress += mSpinSpeed;
        if (mProgress > 360) {
            mProgress = 0;
        }
        postInvalidate();
//        postInvalidateDelayed(mDelayMillis);
    }

    /**
     * 显示并开始转动
     */
    public void showSpinning() {
        setVisibility(VISIBLE);
        startSpinning();
    }

    /**
     * 开始转动
     */
    public void startSpinning() {
        isSpinning = true;
        postInvalidate();
    }

    /**
     * 隐藏控件并停止转动
     */
    public void hideSpinning() {
        setVisibility(GONE);
        stopSpinning();
    }

    /**
     * 停止转动
     */
    public void stopSpinning() {
        isSpinning = false;
        mProgress = 0;
        postInvalidate();
    }
}
