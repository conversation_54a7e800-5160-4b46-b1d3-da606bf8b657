package com.meituan.android.pay.debugkit.capacity;

import com.dianping.nvnetwork.NVAppMockManager;
import com.dianping.nvnetwork.NVGlobal;
import com.meituan.android.cipstorage.CIPStorageCenter;
import com.meituan.android.pay.base.context.PayProvider;
import com.sankuai.meituan.kernel.net.utils.StorageUtil;

import rx.Observable;

/**
 * <AUTHOR>
 */
public class DebugMock {
    public static final String MOCK_URL_DEFAULT = "https://appmock.sankuai.com";
    public static final String MOCK_URL_FOR_MIS = "https://appmock.sankuai.com/mw/register?_=0__0&uid=";

    public static Observable<String> bindMockUrl(String url) {
        return Observable.create(subscriber -> NVAppMockManager.instance()
                .registerMock(url, new NVAppMockManager.RegisterCallback() {
                    @Override
                    public void success() {
                        stopMock();
                        mock(true);
                        subscriber.onNext(url);
                        subscriber.onCompleted();
                    }

                    @Override
                    public void failed(String message) {
                        subscriber.onError(new IllegalStateException(message));
                    }
                }));
    }

    public static Observable<String> bindMockMis(String misId) {
        return bindMockUrl(MOCK_URL_FOR_MIS + misId);
    }

    private static void mock(boolean mock) {
        NVGlobal.setDebug(mock);
        NVAppMockManager.instance().mock(mock);
        // 必须要用StorageUtil，不能换成其他的
        CIPStorageCenter cipStorageCenter = StorageUtil.instance(PayProvider.getContext());
        cipStorageCenter.setBoolean("dianping_mock_enable", mock);
        cipStorageCenter.setBoolean("enable_dianping_mock", mock);
    }

    public static void stopMock() {
        mock(false);
    }

}
