package com.meituan.android.pay.debugkit.main.paylater.model;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;

/**
 * Created by lvshu<PERSON> on 2018/4/18.
 */

@JsonBean
public class OrderInfo implements Serializable {

    private static final long serialVersionUID = -7242507102867383173L;
    // 未支付
    public static final int PAY_STATUS_NO = 0;
    // 用户已确认
    public static final int PAY_STATUS_CONFIRM = 4;
    // 商户已完结
    public static final int PAY_STATUS_COMPLETED = 6;
    // 支付关闭
    public static final int PAY_STATUS_CLOSE = 13;
    // 支付失败
    public static final int PAY_STATUS_FAIL = 14;
    // 记账成功
    public static final int PAY_STATUS_SUCC = 16;
    // 通知业务线成功
    public static final int PAY_STATUS_NOTIFY_SUCC = 64;
    // 部分退款
    public static final int PAY_STATUS_REFUND_PART = 96;
    // 全部退款
    public static final int PAY_STATUS_REFUND_ALL = 97;

    private long id;
    @SerializedName("partnerid")
    private String partnerId;
    @SerializedName("tradeno")
    private String tradeNo;
    private String money;
    private String subject;
    @SerializedName("objid")
    private String objId;              // 订单编号
    @SerializedName("paytype")         // 付款类型
    private String payType;
    @SerializedName("ordertime")
    private long orderTime;          // 下单时间
    @SerializedName("paytime")
    private long paytime;            // 支付时间
    @SerializedName("transtradeno")
    private String transTradeNo;

    // 0未付款， 支付成功16，
    private int status;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getMoney() {
        return money;
    }

    public void setMoney(String money) {
        this.money = money;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public long getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(long orderTime) {
        this.orderTime = orderTime;
    }

    public long getPaytime() {
        return paytime;
    }

    public void setPaytime(long paytime) {
        this.paytime = paytime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getObjId() {
        return objId;
    }

    public void setObjId(String objId) {
        this.objId = objId;
    }

    public String getTransTradeNo() {
        return transTradeNo;
    }

    public void setTransTradeNo(String transTradeNo) {
        this.transTradeNo = transTradeNo;
    }

    public static String getPayResultByStatus(int status) {
        String result = "";
        switch (status) {
            case PAY_STATUS_NO:
                result = "未支付";
                break;
            case PAY_STATUS_CONFIRM:
                result = "用户已确认";
                break;
            case PAY_STATUS_COMPLETED:
                result = "商户已完结";
                break;
            case PAY_STATUS_CLOSE:
                result = "支付关闭";
                break;
            case PAY_STATUS_FAIL:
                result = "支付失败";
                break;
            case PAY_STATUS_SUCC:
                result = "记账成功";
                break;
            case PAY_STATUS_NOTIFY_SUCC:
                result = "通知业务线成功";
                break;
            case PAY_STATUS_REFUND_PART:
                result = "部分退款";
                break;
            case PAY_STATUS_REFUND_ALL:
                result = "全部退款";
                break;
            default:
                result = "未知状态";
                break;
        }
        return result + "-" + +status;
    }
}
