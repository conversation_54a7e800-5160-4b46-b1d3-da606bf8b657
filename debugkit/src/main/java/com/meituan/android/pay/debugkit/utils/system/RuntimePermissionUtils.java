package com.meituan.android.pay.debugkit.utils.system;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.hardware.Camera;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.support.v4.app.Fragment;

import com.meituan.android.paybase.common.dialog.PayDialog;
import com.meituan.android.paybase.utils.RomUtils;

import java.lang.reflect.Field;


/**
 * Created by <PERSON><PERSON>xian on 15/12/31.
 * 权限相关工具类
 */
public final class RuntimePermissionUtils {

    /**
     * 检查特定权限
     *
     * @param context
     * @param permission 权限名
     * @return
     */
    public static boolean checkSelfPermission(Context context, String permission) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            return true;
        }
        return context != null && isPermissionGrant(context.checkSelfPermission(permission));
    }

    /**
     * 请求特定权限
     *
     * @param activity
     * @param permissions 权限名
     * @param requestCode 回调requestCode
     */
    public static void requestPermissions(Activity activity, String[] permissions,
                                          int requestCode) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            return;
        }
        if (activity == null) {
            return;
        }
        activity.requestPermissions(permissions, requestCode);
    }

    public static void requestPermissions(Fragment fragment, String[] permissions,
                                          int requestCode) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            return;
        }
        if (fragment == null) {
            return;
        }
        fragment.requestPermissions(permissions, requestCode);
    }

    public static boolean isPermissionGrant(int result) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            return true;
        }
        if (PackageManager.PERMISSION_GRANTED == result) {
            return true;
        } else if (PackageManager.PERMISSION_DENIED == result) {
            return false;
        }
        return false;
    }

    /**
     * 该方法用来在checkSelfPermission返回有相机权限之后，对权限进行进一步确认。
     * Android 手机有谷歌和厂商两套权限系统。早期的机型可能两套权限不同步，即谷歌返回有权限，但厂商可能并没有给，
     * 所以通过真正调用硬件的办法来验证是否有权限。
     * 理论上该方法可以与但如果合并会干扰checkSelfPermission的判断合并，但如果合并会干扰checkSelfPermission的
     * 判断，进而影响权限的申请流程，所以拆开，在checkSelfPermission之后，对获取到的mCamera再进行一次检验。
     *
     * @param mCamera 通过Camera.open拿到的相机实例。当成参数传进来而没有在方法内部调用Camera.open获取，是考虑
     *                到连续的Camera.open可能会造成体验上卡顿
     * @return 是否真的有相机权限
     * @see #checkSelfPermission(Context context, String permission)
     * @see #isPermissionGrant(int result)
     */
    public static boolean isReallyHasCameraPermission(Camera mCamera) {
        boolean result = true;
        try {
            if (mCamera != null) {
                //vivo 低系统的手机没有权限也能获取mCamera，且不为空。通过反射去检验是否有权限
                if (RomUtils.isVivo()) {
                    Field hasPermission = mCamera.getClass().getDeclaredField("mHasPermission");
                    hasPermission.setAccessible(true);
                    result = (Boolean) hasPermission.get(mCamera);
                }
            } else {
                //如果获取的mCamera会为空，认为没有权限
                result = false;
            }
        } catch (Exception e) {
            result = false;
        }
        return result;
    }

    /**
     * 是否需要弹出权限申请框
     *
     * @param activity
     * @param permission 权限名
     * @return
     */
    public static boolean shouldShowRequestPermissionRationale(Activity activity,
                                                               String permission) {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.M
                && activity.shouldShowRequestPermissionRationale(permission);
    }

    public static boolean shouldShowRequestPermissionRationale(Fragment fragment,
                                                               String permission) {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.M
                && fragment.shouldShowRequestPermissionRationale(permission);
    }

    /**
     * 权限被禁止后，弹出"去设置"的引导对话框
     *
     * @param activity    弹窗的activity
     * @param msg         对话框内容
     * @param requestCode "去设置"跳转的requestCode
     */
    public static void popTipDialog(final Activity activity, String msg, final int requestCode,
                                    final TipDialogClickListener tipDialogClickListener) {
        if (activity == null) {
            return;
        }
        new PayDialog.Builder(activity).msg(msg).leftBtn("取消", dialog -> {
            if (tipDialogClickListener != null) {
                tipDialogClickListener.onClick();
            }
        }).rightBtn("去设置", (v) -> {
            Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            Uri uri = Uri.fromParts("package", activity.getPackageName(), null);
            intent.setData(uri);
            activity.startActivityForResult(intent, requestCode);
        }).build().show();
    }

    public interface TipDialogClickListener {
        void onClick();
    }
}
