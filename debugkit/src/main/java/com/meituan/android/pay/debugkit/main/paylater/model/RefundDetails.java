package com.meituan.android.pay.debugkit.main.paylater.model;

import com.meituan.android.paybase.utils.JsonBean;

import java.io.Serializable;

/**
 * 参考文档：https://km.sankuai.com/page/478462030
 */
@JsonBean
public class RefundDetails implements Serializable {

    /**
     * 商户号
     */
    private String sellerId;
    /**
     * 业务线识别号
     */
    private String partnerId;
    /**
     * 退款受理时间
     */
    private long acceptTime;
    /**
     * 退款金额，单位：分
     */
    private long moneyCent;
    /**
     * 退款类型，退款类型 1表示支付平台自动退款: 0是业务线申请
     */
    private String source;
    /**
     * 业务线退款流水号
     */
    private String refundNo;
    /**
     * 支付单id
     */
    private String payOrderId;
    /**
     * 聚合退款流水号
     */
    private String payRefundFlow;
    /**
     * 退款状态
     */
    private String refundStatus;


    private String tradeNo;

    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    public long getAcceptTime() {
        return acceptTime;
    }

    public void setAcceptTime(long acceptTime) {
        this.acceptTime = acceptTime;
    }

    public long getMoneyCent() {
        return moneyCent;
    }

    public void setMoneyCent(long moneyCent) {
        this.moneyCent = moneyCent;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getRefundNo() {
        return refundNo;
    }

    public void setRefundNo(String refundNo) {
        this.refundNo = refundNo;
    }

    public String getPayOrderId() {
        return payOrderId;
    }

    public void setPayOrderId(String payOrderId) {
        this.payOrderId = payOrderId;
    }

    public String getPayRefundFlow() {
        return payRefundFlow;
    }

    public void setPayRefundFlow(String payRefundFlow) {
        this.payRefundFlow = payRefundFlow;
    }

    public String getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }
}
