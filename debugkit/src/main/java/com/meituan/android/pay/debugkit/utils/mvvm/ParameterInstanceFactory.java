package com.meituan.android.pay.debugkit.utils.mvvm;

import android.arch.lifecycle.ViewModel;
import android.arch.lifecycle.ViewModelProvider;
import android.support.annotation.NonNull;

import com.meituan.android.pay.debugkit.utils.struct.Stream;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;

/**
 * <AUTHOR>
 */
public class ParameterInstanceFactory extends ViewModelProvider.NewInstanceFactory {

    private final Object[] parameters;

    public ParameterInstanceFactory() {
        this.parameters = null;
    }

    public ParameterInstanceFactory(Object... parameters) {
        this.parameters = parameters;
    }

    @NonNull
    @Override
    public <T extends ViewModel> T create(@NonNull Class<T> modelClass) {
        try {
            if (parameters == null) {
                return modelClass.newInstance();
            }
            Constructor<?>[] constructors = modelClass.getConstructors();
            Constructor<?> selected = Stream.on(constructors)
                    .filter(constructor -> constructor.getParameterTypes().length == parameters.length)
                    .filter(constructor -> Stream.index(constructor.getParameterTypes())
                            .filter(item -> !item.value.isInstance(parameters[item.index]))
                            .first() == null)
                    .first();
            if (selected != null) {
                //noinspection unchecked
                return (T) selected.newInstance(parameters);
            } else {
                return modelClass.newInstance();
            }
        } catch (InvocationTargetException | IllegalAccessException
                 | java.lang.InstantiationException ex) {
            throw new RuntimeException(ex);
        }
    }
}
