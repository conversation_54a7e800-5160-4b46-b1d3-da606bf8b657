package com.meituan.android.pay.debugkit.utils.reflect;

import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.android.pay.base.utils.exception.Throw;
import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.pay.debugkit.utils.struct.Stream;
import com.meituan.android.pay.base.utils.log.PayLogger;

import java.lang.reflect.Method;
import java.util.Arrays;

/**
 * <AUTHOR>
 */
public class Reflection {
    public static void invokeMethod(Object proxy, String methodName, Object... args) {
        if (TextUtils.isEmpty(methodName)) {
            return;
        }
        try {
            Method method = getPublicMethod(proxy.getClass(), methodName, args);
            if (method == null) {
                method = getDeclaredMethod(proxy.getClass(), methodName, args);
            }
            if (method == null) {
                throw Throw.origin().msg("method not found").make();
            }
            method.setAccessible(true);
            method.invoke(proxy, args);
            PayLogger.debug("Reflection|invokeMethod", methodName, DebugManager.getActivity());
        } catch (Exception e) {
            PayLogger.debug("PayDebugReflection", "invokeNonparametric", e);
        }
    }

    public static Method getPublicMethod(@NonNull Class<?> clz, String methodName, Object... args) {
        try {
            try {
                return clz.getMethod(methodName, getArgumentsClass(args));
            } catch (Exception e) {
                return clz.getMethod(methodName);
            }
        } catch (Exception e) {
            return null;
        }
    }

    public static Method getDeclaredMethod(@NonNull Class<?> clz, String methodName, Object... args) {
        try {
            try {
                return clz.getDeclaredMethod(methodName, getArgumentsClass(args));
            } catch (Exception e) {
                return clz.getDeclaredMethod(methodName);
            }
        } catch (Exception e) {
            return null;
        }
    }

    public static Class<?>[] getArgumentsClass(Object... args) {
        if (args == null || args.length == 0) {
            return null;
        }
        Class<?>[] parameterClz = new Class[args.length];
        Stream.index(Arrays.asList(args)).foreach(item ->
                parameterClz[item.index] = item.value.getClass());
        return parameterClz;
    }
}
