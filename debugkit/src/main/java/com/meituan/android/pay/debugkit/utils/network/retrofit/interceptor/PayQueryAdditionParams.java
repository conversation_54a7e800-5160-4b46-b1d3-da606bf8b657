package com.meituan.android.pay.debugkit.utils.network.retrofit.interceptor;

import android.support.annotation.Keep;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.pay.base.utils.compute.NumberParser;
import com.meituan.android.pay.base.utils.serialize.GsonUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Keep
public class PayQueryAdditionParams implements Serializable {
    private static final List<String> BLACKLIST_PATH =
            Collections.singletonList("/api/mpm/member/getorcreatemember");

    private static final List<String> BLACKLIST_KEYS =
            Collections.singletonList("member_id");

    @SerializedName("ci")
    private String ci;

    @SerializedName("uuid")
    private String uuid;

    @SerializedName("version_name")
    private String versionName;

    @SerializedName("utm_term")
    private String utmTerm;

    @SerializedName("utm_campaign")
    private String utmCampaign;

    @SerializedName("utm_medium")
    private String utmMedium;

    @SerializedName("utm_content")
    private String utmContent;

    @SerializedName("utm_source")
    private String utmSource;

    @SerializedName("member_id")
    private String memberId;

    @SerializedName("zone_user_id")
    private String zoneUserId;

    public String getCi() {
        return ci;
    }

    public String getUuid() {
        return uuid;
    }

    public String getVersionName() {
        return versionName;
    }

    public String getUtmTerm() {
        return utmTerm;
    }

    public String getUtmCampaign() {
        return utmCampaign;
    }

    public String getUtmMedium() {
        return utmMedium;
    }

    public String getUtmContent() {
        return utmContent;
    }

    public String getUtmSource() {
        return utmSource;
    }

    public String getMemberId() {
        return memberId;
    }

    public String getZoneUserId() {
        return zoneUserId;
    }

    private PayQueryAdditionParams() {
    }

    public static PayQueryAdditionParams newInstance() {
        PayQueryAdditionParams params = new PayQueryAdditionParams();
        long cityId = NumberParser.parseLong(PayProvider.app().getCityId());
        params.ci = cityId > 0 ? String.valueOf(cityId) : "";
        params.uuid = PayProvider.app().getUUID();
        params.versionName = PayProvider.app().getAppVersionName();
        params.utmTerm = PayProvider.app().getAppVersionCode();
        params.utmCampaign = PayProvider.app().getCampaign();
        params.utmMedium = PayProvider.app().getPlatform();
        params.utmContent = PayProvider.app().getUUID();
        params.utmSource = PayProvider.app().getChannel();
        params.memberId = PayProvider.pay().getMemberId();
        params.zoneUserId = PayProvider.pay().getPayUserId();
        return params;
    }

    public Map<String, String> toQuery() {
        return GsonUtils.toStringMap(this);
    }

    public static List<String> keyBlackList(String path) {
        if (!BLACKLIST_PATH.contains(path)) {
            return new ArrayList<>();
        } else {
            return BLACKLIST_KEYS;
        }
    }
}
