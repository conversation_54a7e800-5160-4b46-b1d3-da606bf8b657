package com.meituan.android.pay.debugkit.widget.simple.state;

import android.arch.lifecycle.MutableLiveData;
import android.support.annotation.Keep;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import com.meituan.android.pay.base.container.PayBaseFragment;
import com.meituan.android.pay.debugkit.databinding.PayDebugSimpleNavigateBinding;
import com.meituan.android.pay.debugkit.utils.mvvm.BindingActions;
import com.meituan.android.pay.debugkit.utils.mvvm.LiveDataUtils;
import com.meituan.android.pay.debugkit.widget.simple.base.SimpleViewState;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * <AUTHOR>
 */
@Keep
@ServiceLoaderInterface(key = "navigate", interfaceClass = SimpleViewState.class)
public class NavigateViewState extends TitleViewState {

    private final MutableLiveData<String> text = new MutableLiveData<>();

    @Override
    public View inflate(PayBaseFragment fragment) {
        LiveDataUtils.twoSideBind(fragment, text, value);
        PayDebugSimpleNavigateBinding binding = PayDebugSimpleNavigateBinding
                .inflate(fragment.getLayoutInflater(), (ViewGroup) fragment.getView(), false);
        binding.main.setOnClickListener(v -> invoke());
        binding.title.setText(getTitle());
        LiveDataUtils.observe(fragment, text, value -> {
            binding.tail.setText(value);
            BindingActions.setLayoutWeight(binding.title, TextUtils.isEmpty(value) ? 1 : 0);
            BindingActions.setValueVisibility(binding.tail, value);
        });
        return binding.getRoot();
    }
}
