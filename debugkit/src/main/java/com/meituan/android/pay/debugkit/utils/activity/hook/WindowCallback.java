package com.meituan.android.pay.debugkit.utils.activity.hook;

import static android.view.MotionEvent.ACTION_POINTER_DOWN;

import android.app.Activity;
import android.os.Build;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.view.ActionMode;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.SearchEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.accessibility.AccessibilityEvent;

/**
 * author:  kkli
 * date:    2021-08-07
 * description:
 */
public class WindowCallback implements Window.Callback {
    private final Activity activity;
    private final Window.Callback delegate;
    private final EventListener listener;
    private boolean throttle;

    public static void bindWindow(Activity activity, EventListener listener) {
        if (activity == null) {
            return;
        }
        Window window = activity.getWindow();
        Window.Callback callback = window.getCallback();
        if (!(callback instanceof WindowCallback)) {
            window.setCallback(new WindowCallback(activity, listener));
        }
    }

    private WindowCallback(Activity activity, EventListener listener) {
        this.activity = activity;
        this.delegate = activity.getWindow().getCallback();
        this.listener = listener;
    }

    private boolean isThreePointDown(MotionEvent event) {
        return event.getActionMasked() == ACTION_POINTER_DOWN && event.getPointerCount() == 3;
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        if (throttle) {
            return true;
        }
        if (isThreePointDown(event) && listener != null) {
            throttle = true;
            activity.getWindow().getDecorView().postDelayed(() -> throttle = false, 500);
            listener.onGestureEvent(EventListener.THREE_POINT);
            return true;
        }
        return delegate != null && delegate.dispatchTouchEvent(event);
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (listener != null && listener.onKeyEvent(event)) {
            return true;
        }
        return delegate != null && delegate.dispatchKeyEvent(event);
    }

    @Override
    public boolean dispatchKeyShortcutEvent(KeyEvent event) {
        return delegate != null && delegate.dispatchKeyShortcutEvent(event);
    }

    @Override
    public boolean dispatchTrackballEvent(MotionEvent event) {
        return delegate != null && delegate.dispatchTrackballEvent(event);
    }

    @Override
    public boolean dispatchGenericMotionEvent(MotionEvent event) {
        return delegate != null && delegate.dispatchGenericMotionEvent(event);
    }

    @Override
    public boolean dispatchPopulateAccessibilityEvent(AccessibilityEvent event) {
        return delegate != null && delegate.dispatchPopulateAccessibilityEvent(event);
    }

    @Nullable
    @Override
    public View onCreatePanelView(int featureId) {
        return delegate != null ? delegate.onCreatePanelView(featureId) : null;
    }

    @Override
    public boolean onCreatePanelMenu(int featureId, @NonNull Menu menu) {
        return delegate != null && delegate.onCreatePanelMenu(featureId, menu);
    }

    @Override
    public boolean onPreparePanel(int featureId, View view, @NonNull Menu menu) {
        return delegate != null && delegate.onPreparePanel(featureId, view, menu);
    }

    @Override
    public boolean onMenuOpened(int featureId, @NonNull Menu menu) {
        return delegate != null && delegate.onMenuOpened(featureId, menu);
    }

    @Override
    public boolean onMenuItemSelected(int featureId, @NonNull MenuItem item) {
        return delegate != null && delegate.onMenuItemSelected(featureId, item);
    }

    @Override
    public void onWindowAttributesChanged(WindowManager.LayoutParams attrs) {
        if (delegate != null) {
            delegate.onWindowAttributesChanged(attrs);
        }
    }

    @Override
    public void onContentChanged() {
        if (delegate != null) {
            delegate.onContentChanged();
        }
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        if (delegate != null) {
            delegate.onWindowFocusChanged(hasFocus);
        }
    }

    @Override
    public void onAttachedToWindow() {
        if (delegate != null) {
            delegate.onAttachedToWindow();
        }
    }

    @Override
    public void onDetachedFromWindow() {
        if (delegate != null) {
            delegate.onDetachedFromWindow();
        }
    }

    @Override
    public void onPanelClosed(int featureId, @NonNull Menu menu) {
        if (delegate != null) {
            delegate.onPanelClosed(featureId, menu);
        }
    }

    @Override
    public boolean onSearchRequested() {
        return delegate != null && delegate.onSearchRequested();
    }

    @Override
    public boolean onSearchRequested(SearchEvent searchEvent) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            return delegate != null && delegate.onSearchRequested(searchEvent);
        }
        return delegate != null && delegate.onSearchRequested();
    }

    @Nullable
    @Override
    public ActionMode onWindowStartingActionMode(ActionMode.Callback callback) {
        return delegate != null ? delegate.onWindowStartingActionMode(callback) : null;
    }

    @Nullable
    @Override
    public ActionMode onWindowStartingActionMode(ActionMode.Callback callback, int type) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            return delegate != null ? delegate.onWindowStartingActionMode(callback, type) : null;
        }
        return delegate != null ? delegate.onWindowStartingActionMode(callback) : null;
    }

    @Override
    public void onActionModeStarted(ActionMode mode) {
        if (delegate != null) {
            delegate.onActionModeStarted(mode);
        }
    }

    @Override
    public void onActionModeFinished(ActionMode mode) {
        if (delegate != null) {
            delegate.onActionModeFinished(mode);
        }
    }
}
