package com.meituan.android.pay.debugkit.main.order;

import android.arch.lifecycle.LiveData;
import android.arch.lifecycle.MutableLiveData;
import android.arch.lifecycle.Transformations;
import android.content.Intent;
import android.net.Uri;
import android.support.annotation.Keep;
import android.support.annotation.NonNull;

import com.meituan.android.cashier.oneclick.constant.OneClickConstants;
import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.pay.base.utils.compute.BoolParser;
import com.meituan.android.pay.base.utils.compute.CashComputation;
import com.meituan.android.pay.base.utils.function.ArrayUtils;
import com.meituan.android.pay.base.utils.function.MapBuilder;
import com.meituan.android.pay.base.utils.function.Predicate;
import com.meituan.android.pay.base.utils.log.PayLogger;
import com.meituan.android.pay.base.utils.scheme.QueryParser;
import com.meituan.android.pay.base.utils.serialize.GsonUtils;
import com.meituan.android.pay.base.utils.serialize.JsonBuilder;
import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.pay.debugkit.capacity.pay.DebugOneClickCashier;
import com.meituan.android.pay.debugkit.main.base.PayDemoViewModel;
import com.meituan.android.pay.debugkit.main.data.DataProvider;
import com.meituan.android.pay.debugkit.utils.mvvm.LiveDataUtils;
import com.meituan.android.pay.debugkit.utils.network.retrofit.callback.CallInfo;
import com.meituan.android.pay.debugkit.utils.network.retrofit.callback.PayCallbackFinal;
import com.meituan.android.pay.debugkit.utils.struct.MapUtils;
import com.meituan.android.pay.debugkit.utils.struct.Stream;
import com.meituan.android.pay.debugkit.widget.simple.base.SimpleValueProvider;
import com.meituan.android.paybase.dialog.ToastUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import rx.functions.Action0;

/**
 * <AUTHOR>
 * 【劣势】用 Java 实现 MVVM 无法节省各种 binding 的开发工作量，会增加额外复杂度。
 */
@Keep
public class OrderViewModel extends PayDemoViewModel {
    public static final String PRODUCT_NAME = "productName";
    public static final String PRODUCT_DESC = "productDesc";
    public static final String PAY_FEE_CENT = "payFeeCent";
    public static final String EXPIRE_TIME = "expireTime";
    public static final String EXTRA_INFO = "extraInfo";
    public static final String SELLER_ID = "sellerId";
    public static final String ORDER_INFO = "orderInfo";
    public static final String NO_LOGIN = "noLogin";

    public static final String TRADE_NO = "tradeno";
    public static final String PAY_TOKEN = "pay_token";

    public static final String CASHIER_TRADE_NO = "trade_number";
    public static final String CASHIER_PAY_TOKEN = PAY_TOKEN;
    public static final String CASHIER_CIF = "cif";
    public static final String CASHIER_CALLBACK_URL = "callback_url";
    public static final String CASHIER_MERCHANT_NO = "merchant_no";
    public static final String CASHIER_EXTRA_DATA = "extra_data";
    public static final String CASHIER_EXTRA_STATICS = "extra_statics";
    public static final String CASHIER_TYPE = "cashier_type";
    public static final String CASHIER_URI = "meituanpayment://cashier/launch";

    public static final List<String> ORDER_FORM_KEYS = Arrays.asList(
            PRODUCT_NAME, PRODUCT_DESC, PAY_FEE_CENT, EXPIRE_TIME, EXTRA_INFO,
            ORDER_INFO, SELLER_ID, NO_LOGIN);

    public static final List<String> CASHIER_LAUNCH_KEYS = Arrays.asList(
            CASHIER_TRADE_NO, CASHIER_PAY_TOKEN, CASHIER_CIF,
            CASHIER_CALLBACK_URL, CASHIER_MERCHANT_NO,
            CASHIER_EXTRA_DATA, CASHIER_EXTRA_STATICS, CASHIER_TYPE
    );

    private final MutableLiveData<Boolean> loading = new MutableLiveData<>();

    private final SimpleValueProvider provider;

    private final OrderNavigator navigator;

    public OrderViewModel(OrderNavigator navigator, SimpleValueProvider provider) {
        this.provider = provider;
        this.navigator = navigator;
    }

    public LiveData<Boolean> loading() {
        return Transformations.switchMap(provider.liveDataOf("isLoading"), isLoading ->
                LiveDataUtils.from(BoolParser.bool(isLoading)));
    }

    public LiveData<String> money() {
        return Transformations.switchMap(provider.liveDataOf("payFeeCent"), cent ->
                LiveDataUtils.from(CashComputation.cent2Yuan(cent)));
    }

    public void onSubmitOrder(boolean cashier) {
        DataProvider.order()
                .getOrderParams(provider.valueOf(CASHIER_TYPE))
                .doOnNext(requestForm -> requestForm.putAll(provider.valuesOf(ORDER_FORM_KEYS)))
                .subscribe(requestForm -> DataProvider.order()
                        .submitOrder(requestForm)
                        .onStart(() -> loading.setValue(true))
                        .onFinal(() -> loading.setValue(false))
                        .onError(e -> ToastUtils.showSnackToast(DebugManager.getActivity(), "网络有点问题"))
                        .onSuccess(response -> {
                            Map<String, String> updated = GsonUtils.toStringMap(response);
                            provider.update(MapBuilder.builder(updated)
                                    .add(CASHIER_TRADE_NO, response.getTradeNo())
                                    .add(CASHIER_PAY_TOKEN, response.getPayToken())
                                    .build());
                            if (cashier) {
                                onLaunchCashier();
                            }
                        }).observeCall(OrderViewModel.this));
    }

    public void onLaunchCashier() {
        Map<String, String> params = provider.valuesOf(CASHIER_LAUNCH_KEYS);
        List<String> vipValues = MapUtils.selectValues(params,
                ArrayUtils.asList(CASHIER_TRADE_NO, CASHIER_PAY_TOKEN));
        List<String> illegalValues = Stream.on(vipValues)
                .filter(Predicate.passEmpty()).collect();
        if (!illegalValues.isEmpty()) {
            return;
        }
        handleOneClickCashier(params);
        Uri.Builder builder = Uri.parse(CASHIER_URI).buildUpon();
        Stream.on(params).foreach(entry -> {
            String key = entry.getKey();
            String val = entry.getValue();
            builder.appendQueryParameter(key, val);
        });
        Uri uri = builder.build();
        PayLogger.debug("启动收银台", QueryParser.decode(uri.getEncodedQuery()));
        Intent intent = new Intent(Intent.ACTION_VIEW).setData(uri);
        intent.setPackage(PayProvider.getPackageName());
        navigator.onCashier(intent);
    }

    private void handleOneClickCashier(Map<String, String> params) {
        String cashierType = params.get("cashier_type");
        if (!"oneclickpay".equals(cashierType)) {
            return;
        }
        String extraData = params.get(CASHIER_EXTRA_DATA);
        String openStatus = BoolParser.toIntString(DebugOneClickCashier.OPEN_STATUS.getFormattedValue());
        String serialCode = UUID.randomUUID().toString();
        extraData = JsonBuilder.builder(extraData)
                .add(OneClickConstants.ARG_EXTRA_ONE_CLICK_PAY_OPEN, openStatus)
                .add(OneClickConstants.ARG_EXTRA_ONE_CLICK_PAY_SERIAL_CODE, serialCode)
                .buildString();
        params.put(CASHIER_EXTRA_DATA, extraData);
    }
}
