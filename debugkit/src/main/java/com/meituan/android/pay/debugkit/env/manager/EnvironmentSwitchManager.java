package com.meituan.android.pay.debugkit.env.manager;

import android.content.Context;
import android.content.SharedPreferences;

import com.meituan.android.pay.base.utils.exception.Catch;
import com.meituan.android.pay.debugkit.DebugManager;

/**
 * 环境切换管理器
 * 对应iOS中的环境切换相关功能
 * 
 * <AUTHOR>
 * @since 2025/1/9
 */
public class EnvironmentSwitchManager {
    
    private static final String TAG = "EnvironmentSwitchManager";
    
    // SharedPreferences相关常量
    private static final String PREF_NAME = "environment_switch_config";
    private static final String KEY_SHARK_DEBUG_ENV = "shark_debug_env";
    private static final String KEY_MOCK_SWITCH = "mock_switch_enabled";
    
    private final Context context;
    private final SharedPreferences preferences;

    public EnvironmentSwitchManager(Context context) {
        this.context = context;
        this.preferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
    }

    /**
     * 切换SharkDebug环境为Mock环境
     * 对应iOS的switchsharkDebugEnv方法
     */
    public void switchSharkDebugEnv() {
        try {
            // 启用Mock环境
            enableMockEnvironment();
            
            // 保存状态
            preferences.edit()
                    .putBoolean(KEY_SHARK_DEBUG_ENV, true)
                    .putLong("shark_debug_switch_time", System.currentTimeMillis())
                    .apply();
                    
            DebugManager.getStorage().setBoolean("shark_debug_mock_enabled", true);
            
        } catch (Exception e) {
            Catch.with(e).tag(TAG).log();
        }
    }

    /**
     * 启用Mock环境
     */
    private void enableMockEnvironment() {
        try {
            // 设置Mock开关
            preferences.edit()
                    .putBoolean(KEY_MOCK_SWITCH, true)
                    .apply();
            
            // 通知相关组件Mock环境已启用
            notifyMockEnvironmentEnabled();
            
        } catch (Exception e) {
            Catch.with(e).tag(TAG).log();
        }
    }

    /**
     * 通知Mock环境已启用
     */
    private void notifyMockEnvironmentEnabled() {
        // 这里可以添加通知其他组件的逻辑
        // 例如：发送广播、调用回调等
    }

    /**
     * 检查是否启用了Mock环境
     */
    public boolean isMockEnvironmentEnabled() {
        return preferences.getBoolean(KEY_MOCK_SWITCH, false);
    }

    /**
     * 检查是否启用了SharkDebug环境
     */
    public boolean isSharkDebugEnvironmentEnabled() {
        return preferences.getBoolean(KEY_SHARK_DEBUG_ENV, false);
    }

    /**
     * 重置环境设置
     */
    public void resetEnvironmentSettings() {
        preferences.edit().clear().apply();
        DebugManager.getStorage().setBoolean("shark_debug_mock_enabled", false);
    }

    /**
     * 获取环境切换时间
     */
    public long getLastSwitchTime() {
        return preferences.getLong("shark_debug_switch_time", 0);
    }
}
