package com.meituan.android.pay.debugkit.utils.network.retrofit.callback;

import android.support.annotation.NonNull;

import rx.functions.Action1;

/**
 * <AUTHOR>
 */
public interface PayCallbackError {
    void onError(@NonNull CallInfo info, @NonNull Exception exception);

    static PayCallbackError simple(Action1<Exception> onError) {
        return (info, exception) -> {
            if (onError != null) {
                onError.call(exception);
            }
        };
    }
}
