package com.meituan.android.pay.debugkit.main.paylater.model;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

public class RefundInfo implements Serializable {
    @SerializedName("payRefundTime")
    private String payRefundTime;

    @SerializedName("payRefundFlow")
    private String payRefundFlow;

    public String getPayRefundTime() {
        return payRefundTime;
    }

    public void setPayRefundTime(String payRefundTime) {
        this.payRefundTime = payRefundTime;
    }

    public String getPayRefundFlow() {
        return payRefundFlow;
    }

    public void setPayRefundFlow(String payRefundFlow) {
        this.payRefundFlow = payRefundFlow;
    }
}
