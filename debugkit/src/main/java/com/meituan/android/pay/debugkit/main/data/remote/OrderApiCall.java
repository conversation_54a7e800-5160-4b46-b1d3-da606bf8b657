package com.meituan.android.pay.debugkit.main.data.remote;

import com.meituan.android.pay.debugkit.main.data.model.OrderRequest;
import com.meituan.android.pay.debugkit.utils.network.retrofit.call.PayResponseCall;
import com.sankuai.meituan.retrofit2.http.FieldMap;
import com.sankuai.meituan.retrofit2.http.FormUrlEncoded;
import com.sankuai.meituan.retrofit2.http.POST;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface OrderApiCall {

    // 标准下单
    @FormUrlEncoded
    @POST("/demo/genorder")
    PayResponseCall<OrderRequest> submitOrder(@FieldMap Map<String, String> form);

    // 合单支付的下单
    @FormUrlEncoded
    @POST("/demo/gencombineorder")
    PayResponseCall<OrderRequest> genCombTransOrder(@FieldMap HashMap<String, String> form);

    // 打车充值下单
    @FormUrlEncoded
    @POST("/demo/mtcashier/genspecialorder")
    PayResponseCall<OrderRequest> genTaxiOrder(@FieldMap HashMap<String, String> form);

    // 钱袋宝充值下单
    @FormUrlEncoded
    @POST("/nocashier/getthirdcashierurl")
    PayResponseCall<OrderRequest> genQDBOrder(@FieldMap HashMap<String, String> form);

}
