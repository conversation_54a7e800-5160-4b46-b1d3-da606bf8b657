package com.meituan.android.pay.debugkit.main.data.remote;

import com.meituan.android.pay.debugkit.main.paylater.model.OrderInfo;
import com.meituan.android.pay.debugkit.main.paylater.model.RefundDetails;
import com.meituan.android.pay.debugkit.main.paylater.model.RefundInfo;
import com.sankuai.meituan.retrofit2.Call;
import com.sankuai.meituan.retrofit2.http.Field;
import com.sankuai.meituan.retrofit2.http.FieldMap;
import com.sankuai.meituan.retrofit2.http.FormUrlEncoded;
import com.sankuai.meituan.retrofit2.http.POST;

import java.util.HashMap;
import java.util.List;

/**
 * Created by zhuzijian on 2017/7/19.
 */

public interface PayDemoRequestService {
    @FormUrlEncoded
    @POST("/demo/payorder/querypayorderbyuid")
    Call<List<OrderInfo>> queryOrderList(@Field("userid") String userid, @Field("limit") String limit, @Field("offset") String offset);

    @FormUrlEncoded
    @POST("/demo/refund/apply")
    Call<RefundInfo> tradeRefundDemo(@FieldMap HashMap<String, Object> args);

    @FormUrlEncoded
    @POST("/demo/refund/list")
    Call<List<RefundDetails>> refundList(@Field("userId") String userid, @Field("limit") String limit, @Field("offset") String offset);
}
