package com.meituan.android.pay.debugkit.utils.mvvm;

import android.arch.lifecycle.ViewModel;
import android.arch.lifecycle.ViewModelProviders;
import android.support.v4.app.Fragment;

/**
 * <AUTHOR>
 */
public class ParameterViewModelProvider<V extends ViewModel> {
    private Fragment fragment;

    private Class<V> viewModelClz;

    public static <V extends ViewModel> ParameterViewModelProvider<V> of(Fragment fragment,
                                                                         Class<V> viewModelClz) {
        ParameterViewModelProvider<V> provider = new ParameterViewModelProvider<>();
        provider.setFragment(fragment);
        provider.setViewModelClz(viewModelClz);
        return provider;
    }

    public V init(Object... parameters) {
        ParameterInstanceFactory factory = new ParameterInstanceFactory(parameters);
        return ViewModelProviders.of(fragment, factory)
                .get(viewModelClz.getName(), viewModelClz);
    }

    public void setViewModelClz(Class<V> viewModelClz) {
        this.viewModelClz = viewModelClz;
    }

    public void setFragment(Fragment fragment) {
        this.fragment = fragment;
    }
}
