package com.meituan.android.pay.debugkit.widget.simple.state;

import android.support.annotation.Keep;
import android.view.View;
import android.view.ViewGroup;

import com.meituan.android.pay.base.container.PayBaseFragment;
import com.meituan.android.pay.debugkit.databinding.PayDebugSimpleAdditionBinding;
import com.meituan.android.pay.debugkit.widget.simple.base.SimpleViewState;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * <AUTHOR>
 */
@Keep
@ServiceLoaderInterface(key = "addition", interfaceClass = SimpleViewState.class)
public class AdditionViewState extends TitleViewState {

    protected String selectionTitle = "";

    protected String selectionText = "";

    @Override
    public View inflate(PayBaseFragment fragment) {
        PayDebugSimpleAdditionBinding binding = PayDebugSimpleAdditionBinding
                .inflate(fragment.getLayoutInflater(), (ViewGroup) fragment.getView(), false);
        binding.main.setOnClickListener(v -> invoke("add", getVal()));
        return binding.getRoot();
    }

    public String getSelectionTitle() {
        return selectionTitle;
    }

    public void setSelectionTitle(String selectionTitle) {
        this.selectionTitle = selectionTitle;
    }

    public String getSelectionText() {
        return selectionText;
    }

    public void setSelectionText(String selectionText) {
        this.selectionText = selectionText;
    }
}
