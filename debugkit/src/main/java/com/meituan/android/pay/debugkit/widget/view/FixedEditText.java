package com.meituan.android.pay.debugkit.widget.view;

import android.arch.lifecycle.LifecycleOwner;
import android.arch.lifecycle.MutableLiveData;
import android.content.Context;
import android.support.v7.widget.AppCompatEditText;
import android.text.Editable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.Gravity;

import com.meituan.android.pay.debugkit.utils.mvvm.LiveDataUtils;
import com.meituan.android.pay.debugkit.widget.view.listener.TextWatcherImpl;

/**
 * <AUTHOR>
 */
public class FixedEditText extends AppCompatEditText {
    public FixedEditText(Context context) {
        super(context);
        init();
    }

    public FixedEditText(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public FixedEditText(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        setBackground(null);
    }

    public void setText(String text) {
        String curText = getEditableText().toString();
        if (!TextUtils.equals(curText, text)) {
            setText((CharSequence) text);
        }
    }

    public void setOrder(boolean right) {
        if (right) {
            setGravity(Gravity.END);
        } else {
            setGravity(Gravity.START);
        }
    }

    public void bind(LifecycleOwner lifecycleOwner, MutableLiveData<String> liveData) {
        if (lifecycleOwner != null && liveData != null) {
            LiveDataUtils.observe(lifecycleOwner, liveData, this::setText);
            addTextChangedListener(new TextWatcherImpl() {
                @Override
                public void afterTextChanged(Editable s) {
                    liveData.setValue(s.toString());
                }
            });
        }
    }

}
