package com.meituan.android.pay.debugkit.main.paylater;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.meituan.android.neohybrid.init.HybridSDK;
import com.meituan.android.paybase.common.fragment.PayBaseFragment;
import com.meituan.android.paybase.utils.UriUtils;
import com.meituan.android.paycommon.lib.config.MTPayConfig;
import com.meituan.android.pay.debugkit.R;
import com.meituan.android.pay.debugkit.widget.old.views.SimpleClickView;
import com.meituan.android.pay.debugkit.widget.old.views.SimpleViewTools;

/**
 * <AUTHOR>
 */
public class PayLaterDemoFragment extends PayBaseFragment {
    private static final String OPEN_PAY_LATER_ORDER = "openPayLaterOrder";

    private static final String OPEN_PAY_LATER_SIGN = "openPayLaterSign";

    private static final String OPEN_PAY_LATER_ORDER_LIST = "openPayLaterOrderList";

    private SimpleViewTools simpleViewTools;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.old_fragment_paylater_demo_setting, container, false);
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        LinearLayout settings = view.findViewById(R.id.demo_pay_later_settings);
        simpleViewTools = SimpleViewTools.builder(settings)
                .addSimpleView(new SimpleClickView.Bean(OPEN_PAY_LATER_ORDER_LIST, "打开交易列表页", "点击开启", v -> {
                    Intent intent = new Intent(getActivity(), PayLaterOrderListActivity.class);
                    startActivity(intent);
                }))
                .addSimpleView(new SimpleClickView.Bean(OPEN_PAY_LATER_ORDER_LIST, "打开提交订单页", "点击开启", v -> {
                    String h5UrlString = HybridSDK.getHost() + "/resource/submit-order/index.html#/";
                    Uri.Builder builder = Uri.parse(h5UrlString).buildUpon();
                    builder.appendQueryParameter("userId", MTPayConfig.getProvider().getUserId());
                    builder.appendQueryParameter("token", MTPayConfig.getProvider().getUserToken());
                    UriUtils.open(getContext(), builder.toString());
                }))
                .build();

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (simpleViewTools != null) {
            simpleViewTools.destroy();
        }
    }
}
