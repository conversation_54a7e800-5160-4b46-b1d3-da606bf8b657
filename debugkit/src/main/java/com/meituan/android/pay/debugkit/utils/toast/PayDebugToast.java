package com.meituan.android.pay.debugkit.utils.toast;

import android.app.Activity;
import android.text.TextUtils;

import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.pay.debugkit.capacity.DebugSetting;
import com.meituan.android.paybase.dialog.ToastUtils;

/**
 * <AUTHOR>
 */
public final class PayDebugToast {

    public static void toast(String content) {
        Activity activity = DebugManager.getActivity();

        if (activity != null
                && !TextUtils.isEmpty(content)
                && DebugSetting.enableDebugToast()) {
            ToastUtils.showSnackToast(activity, content);
        }
    }

}
