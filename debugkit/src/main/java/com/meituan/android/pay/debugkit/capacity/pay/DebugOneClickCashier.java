package com.meituan.android.pay.debugkit.capacity.pay;

import com.meituan.android.pay.debugkit.capacity.DebugInit;
import com.meituan.android.pay.debugkit.utils.struct.SavedData;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * <AUTHOR>
 */
@ServiceLoaderInterface(key = "debug_one_click_cashier", interfaceClass = DebugInit.class)
public class DebugOneClickCashier implements DebugInit {

    public static final SavedData<Boolean> OPEN_STATUS = SavedData.just("one_click_open_status", false);
}
