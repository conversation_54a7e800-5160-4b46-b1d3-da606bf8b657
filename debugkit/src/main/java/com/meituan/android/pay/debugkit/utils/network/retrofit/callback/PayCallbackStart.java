package com.meituan.android.pay.debugkit.utils.network.retrofit.callback;

import android.support.annotation.NonNull;

import rx.functions.Action0;

/**
 * <AUTHOR>
 */
public interface PayCallbackStart {
    void onStart(@NonNull CallInfo info);

    static PayCallbackStart simple(Action0 onStart) {
        return info -> {
            if (onStart != null) {
                onStart.call();
            }
        };
    }
}
