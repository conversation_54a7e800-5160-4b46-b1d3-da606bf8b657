package com.meituan.android.pay.debugkit.widget.old.views;

import android.content.Context;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;

import com.meituan.android.pay.debugkit.widget.old.TextDivider;

/**
 * <AUTHOR>
 */
public class SimpleDividerView extends TextDivider implements SimpleView<SimpleDividerView.Bean> {

    public SimpleDividerView(Context context) {
        super(context);
    }

    public SimpleDividerView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public SimpleDividerView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void refresh(Bean bean) {
        if (bean == null) {
            return;
        }
        String content = bean.content + (bean.collapsed ? "(点击展开)" : "(点击收起)");
        setDividerText(content);
    }

    @Override
    public boolean flip() {
        return false;
    }

    @Override
    public View getView() {
        return this;
    }

    public static class Holder implements SimpleViewHolder {
        private SimpleDividerView view;

        private OnClickListener listener;

        private final Bean bean;

        public Holder(Bean bean) {
            this.bean = bean;
        }

        @Override
        public SimpleView<Bean> getView(Context context) {
            if (view != null) {
               return view;
            }
            view = new SimpleDividerView(context);
            view.refresh(bean);
            return view;
        }

        @Override
        public String key() {
            return bean.content;
        }

        @Override
        public String value() {
            return String.valueOf(bean.canCollapsed);
        }

        public void setListener(OnClickListener listener) {
            this.listener = listener;
            this.view.setOnClickListener(v -> performClick());
        }

        public void initCollapsedStatus() {
            if (this.bean.isCollapsed()) {
                performClick();
            }
        }

        public void performClick() {
            if (this.bean.isCanCollapsed() && this.listener != null) {
                this.bean.setCollapsed(!this.bean.collapsed);
                this.listener.onClick(view);
            }
        }
    }

    public static class Bean implements SimpleViewBean {
        private String content;

        private boolean collapsed;

        private boolean canCollapsed;

        public Bean(String content, boolean collapsed, boolean canCollapsed) {
            this.content = content;
            this.collapsed = collapsed;
            this.canCollapsed = canCollapsed;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public boolean isCollapsed() {
            return collapsed;
        }

        public void setCollapsed(boolean collapsed) {
            this.collapsed = collapsed;
        }

        public boolean isCanCollapsed() {
            return canCollapsed;
        }

        public void setCanCollapsed(boolean canCollapsed) {
            this.canCollapsed = canCollapsed;
        }

        @Override
        public SimpleViewHolder holder() {
            return new Holder(this);
        }
    }
}
