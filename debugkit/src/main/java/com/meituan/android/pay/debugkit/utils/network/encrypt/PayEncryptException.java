package com.meituan.android.pay.debugkit.utils.network.encrypt;

/**
 * <AUTHOR>
 */
public class PayEncryptException extends IllegalStateException {
    private final int errCode;
    private final String errMsg;

    public PayEncryptException(int errCode, String errMsg) {
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    public PayEncryptException(int errCode, String errMsg, Throwable t) {
        super(t);
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    public int getErrCode() {
        return errCode;
    }

    public String getErrMsg() {
        return errMsg;
    }
}
