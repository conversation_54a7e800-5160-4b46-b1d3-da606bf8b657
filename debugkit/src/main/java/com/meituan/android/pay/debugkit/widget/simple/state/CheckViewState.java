package com.meituan.android.pay.debugkit.widget.simple.state;

import android.arch.lifecycle.MutableLiveData;
import android.arch.lifecycle.Transformations;
import android.support.annotation.Keep;
import android.view.View;
import android.view.ViewGroup;

import com.meituan.android.pay.base.container.PayBaseFragment;
import com.meituan.android.pay.debugkit.databinding.PayDebugSimpleCheckBinding;
import com.meituan.android.pay.debugkit.utils.mvvm.LiveDataUtils;
import com.meituan.android.pay.debugkit.widget.simple.base.SimpleViewState;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * <AUTHOR>
 */
@Keep
@ServiceLoaderInterface(key = "check", interfaceClass = SimpleViewState.class)
public class CheckViewState extends TitleViewState {

    public MutableLiveData<Boolean> isChecked() {
        return (MutableLiveData<Boolean>) Transformations.switchMap(getValue(), input -> {
            MutableLiveData<Boolean> liveData = new MutableLiveData<>();
            liveData.setValue(Boolean.parseBoolean(input));
            return liveData;
        });
    }

    public void setChecked(boolean checked) {
        setValue(String.valueOf(checked));
    }

    @Override
    public View inflate(PayBaseFragment fragment) {
        PayDebugSimpleCheckBinding binding = PayDebugSimpleCheckBinding.inflate(
                fragment.getLayoutInflater(), (ViewGroup) fragment.getView(), false);
        binding.title.setText(getTitle());
        binding.checkbox.setOnCheckedChangeListener((buttonView, isChecked) ->
                setChecked(isChecked));
        LiveDataUtils.observe(fragment, isChecked(), isChecked ->
                binding.checkbox.setChecked(Boolean.TRUE.equals(isChecked)));
        return binding.getRoot();
    }
}
