package com.meituan.android.pay.debugkit;

import android.support.annotation.Keep;
import android.text.TextUtils;

import com.meituan.android.pay.base.context.PayProvider;

import java.io.Serializable;

import rx.Observable;
import rx.subjects.BehaviorSubject;

/**
 * <AUTHOR>
 */
@Keep
public class DebugInfo implements Serializable {
    private final BehaviorSubject<Boolean> offlineStatus = BehaviorSubject.create();

    private boolean isFirstLaunch;

    private boolean isInPayDemo;

    public Observable<Boolean> getOfflineStatus() {
        return offlineStatus;
    }

    public void setOfflineStatus(boolean offline) {
        offlineStatus.onNext(offline);
    }

    public boolean isFirstLaunch() {
        return isFirstLaunch;
    }

    public void setFirstLaunch(boolean firstLaunch) {
        isFirstLaunch = firstLaunch;
    }

    public boolean isInPayDemo() {
        return isInPayDemo;
    }

    public void setInPayDemo(boolean inPayDemo) {
        isInPayDemo = inPayDemo;
    }

    public static boolean isInMT() {
        return TextUtils.equals(PayProvider.app().getAppName(), "group");
    }

    public static boolean isInDP() {
        return TextUtils.equals(PayProvider.app().getAppName(), "dianping-nova");
    }

    public static boolean isInWM() {
        return TextUtils.equals(PayProvider.app().getAppName(), "waimai");
    }

}
