package com.meituan.android.pay.debugkit.utils.router;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;

import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.pay.base.utils.function.Cond;
import com.meituan.android.pay.base.utils.reflect.Reflection;
import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.paybase.webview.WebViewActivity;

/**
 * <AUTHOR>
 */
public class OpenUtils {

    public static void openPayWebView(Context context, String url) {
        if (context != null && !TextUtils.isEmpty(url)) {
            WebViewActivity.openWithLinkedUrl(context, url);
        }
    }

    public static void openScheme(Context context, String scheme) {
        if (context != null && !TextUtils.isEmpty(scheme)) {
            Intent intent = new Intent();
            intent.setAction(Intent.ACTION_VIEW);
            intent.setData(Uri.parse(scheme));
            intent.setPackage(context.getPackageName());
            if (Reflection.notInstance(context, Activity.class)) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            }
            context.startActivity(intent);
        }
    }

    public static void openActivity(Context context, Class<? extends Activity> activityClass) {
        if (context != null && activityClass != null) {
            context.startActivity(new Intent(context, activityClass));
        }
    }

    public static boolean open(String url) {
        if (TextUtils.isEmpty(url)) {
            return false;
        } else if (Cond.isStartWith(url, "method://")) {
            OpenMethod.open(url);
            return true;
        } else if (Cond.isContains(url, "://")) {
            openScheme(DebugManager.getActivity(), url);
            return true;
        } else if (Cond.isStartWith(url, "/")) {
            String newUrl = PayProvider.pay().getHost() + url;
            openPayWebView(DebugManager.getActivity(), newUrl);
            return true;
        }
        return false;
    }

}
