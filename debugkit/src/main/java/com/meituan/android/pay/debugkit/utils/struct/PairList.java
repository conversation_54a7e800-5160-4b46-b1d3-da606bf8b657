package com.meituan.android.pay.debugkit.utils.struct;

import com.meituan.android.pay.base.utils.function.ArrayUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * 性能较差
 */
public class PairList<K, V> {
    private final ArrayList<K> keyList = new ArrayList<>();
    private final ArrayList<V> valList = new ArrayList<>();

    public PairList() {
    }

    public PairList(Map<K, V> map) {
        Stream.on(map).foreach(entry -> add(entry.getKey(), entry.getValue()));
    }

    public PairList<K, V> add(K key, V val) {
        if (keyList.contains(key) || valList.contains(val)) {
            return this;
        }
        keyList.add(key);
        valList.add(val);
        return this;
    }

    public K getKey(V val) {
        return ArrayUtils.get(keyList, valList.indexOf(val));
    }

    public V getVal(K key) {
        return ArrayUtils.get(valList, keyList.indexOf(key));
    }

    public void removeKey(K key) {
        V val = getVal(key);
        keyList.remove(key);
        valList.remove(val);
    }

    public void removeVal(V val) {
        K key = getKey(val);
        keyList.remove(key);
        valList.remove(val);
    }

    public int size() {
        return keyList.size();
    }

    public Collection<K> keys() {
        return keyList;
    }

    public Collection<V> values() {
        return valList;
    }

}
