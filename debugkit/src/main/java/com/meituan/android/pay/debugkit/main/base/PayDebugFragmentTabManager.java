package com.meituan.android.pay.debugkit.main.base;

import android.support.annotation.DrawableRes;
import android.support.v4.app.Fragment;
import android.support.v4.app.FragmentActivity;
import android.support.v4.app.FragmentTransaction;
import android.support.v4.content.ContextCompat;
import android.view.View;

import com.meituan.android.pay.base.utils.function.ArrayUtils;
import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.pay.debugkit.utils.struct.Stream;
import com.meituan.android.pay.debugkit.R;
import com.meituan.android.pay.debugkit.databinding.PayDebugTabLayoutBinding;
import com.meituan.android.pay.debugkit.databinding.PayDebugTabLayoutIconBinding;
import com.meituan.android.pay.debugkit.utils.ui.UIUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class PayDebugFragmentTabManager {
    private final FragmentActivity activity;

    private final FragmentTabAdapter fragmentTabAdapter;

    public PayDebugFragmentTabManager(FragmentActivity activity) {
        this.activity = activity;
        this.fragmentTabAdapter = new FragmentTabAdapter();
    }

    public PayDebugFragmentTabManager add(String title, int icon, Fragment fragment) {
        fragmentTabAdapter.add(title, icon, fragment);
        return this;
    }

    public PayDebugFragmentTabManager startOnIndex(int index) {
        int selectIndex = DebugManager.getStorage().getInteger("selectIndex", -1);
        if (selectIndex > -1) {
            index = selectIndex;
        }
        if (index >= fragmentTabAdapter.holders.size() || index < 0) {
            index = 0;
        }
        PayDebugTabLayoutBinding binding = PayDebugTabLayoutBinding.inflate(activity.getLayoutInflater());
        for (FragmentTabHolder state : fragmentTabAdapter.holders) {
            binding.icons.addView(state.onCreateView(), UIUtils.LayoutParams.LLP(1));
        }
        activity.addContentView(binding.getRoot(), UIUtils.LayoutParams.LLP());
        fragmentTabAdapter.show(index);
        return this;
    }

    public static PayDebugFragmentTabManager with(FragmentActivity activity) {
        return new PayDebugFragmentTabManager(activity);
    }

    private class FragmentTabAdapter {
        private final List<FragmentTabHolder> holders = new ArrayList<>();
        private int selectIndex = -1;

        public FragmentTabAdapter add(String title, int icon, Fragment fragment) {
            holders.add(new FragmentTabHolder(holders.size(), title, icon, fragment));
            return this;
        }

        public PayDebugFragmentTabManager back() {
            return PayDebugFragmentTabManager.this;
        }

        public void show(int index) {
            if (index != selectIndex && ArrayUtils.isIndex(holders, index)) {
                if (ArrayUtils.isIndex(holders, selectIndex)) {
                    switchToFragment(selectIndex, index);
                } else {
                    switchToFragment(index);
                }
                Stream.on(holders).foreach(tab -> tab.onSelected(index));
                selectIndex = index;
                DebugManager.getStorage().setInteger("selectIndex", selectIndex);
            }
        }

        public void switchToFragment(int index) {
            FragmentTransaction transaction = activity.getSupportFragmentManager()
                    .beginTransaction();
            holders.get(index).show(transaction);
            transaction.commitAllowingStateLoss();
        }

        public void switchToFragment(int hideIndex, int showIndex) {
            boolean left = hideIndex < showIndex;
            int animEnter = left ? R.anim.fragment_enter_left : R.anim.fragment_enter_right;
            int animExit = left ? R.anim.fragment_exit_left : R.anim.fragment_exit_right;
            FragmentTransaction transaction = activity.getSupportFragmentManager()
                    .beginTransaction()
                    .setCustomAnimations(animEnter, animExit);
            holders.get(hideIndex).hide(transaction);
            holders.get(showIndex).show(transaction);
            transaction.commitAllowingStateLoss();
        }
    }

    private class FragmentTabHolder {
        private final int id;
        private final String title;
        private final @DrawableRes int iconRes;
        private final Fragment fragment;
        private PayDebugTabLayoutIconBinding binding;

        public FragmentTabHolder(int id, String title, int iconRes, Fragment fragment) {
            this.id = id;
            this.title = title;
            this.iconRes = iconRes;
            this.fragment = fragment;
        }

        public View onCreateView() {
            binding = PayDebugTabLayoutIconBinding.inflate(activity.getLayoutInflater());
            binding.icon.setImageDrawable(ContextCompat.getDrawable(activity, iconRes));
            binding.title.setText(title);

            View rootView = binding.getRoot();
            rootView.setOnClickListener(v -> fragmentTabAdapter.show(id));
            return rootView;
        }

        public void onSelected(boolean selected) {
            if (binding != null) {
                binding.getRoot().setSelected(selected);
            }
        }

        public void onSelected(int id) {
            if (binding != null) {
                binding.getRoot().setSelected(this.id == id);
            }
        }

        public void hide(FragmentTransaction transaction) {
            if (fragment.isAdded()) {
                transaction.hide(fragment);
            }
        }

        public void show(FragmentTransaction transaction) {
            if (!fragment.isAdded()) {
                transaction.add(R.id.content, fragment);
            }
            transaction.show(fragment);
        }

    }
}
