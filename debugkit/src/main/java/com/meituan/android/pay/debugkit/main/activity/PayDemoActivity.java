package com.meituan.android.pay.debugkit.main.activity;

import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.app.Fragment;
import android.text.TextUtils;

import com.meituan.android.pay.base.compat.ActivityCompat;
import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.pay.base.utils.reflect.Reflection;
import com.meituan.android.pay.base.utils.scheme.UriParser;
import com.meituan.android.pay.base.utils.serviceloader.ServiceLoaderUtils;
import com.meituan.android.pay.debugkit.R;
import com.meituan.android.pay.debugkit.databinding.PayDebugFrameLayoutBinding;
import com.meituan.android.pay.debugkit.utils.ui.StatusBarManager;
import com.meituan.android.pay.debugkit.utils.ui.UIUtils;

/**
 * <AUTHOR>
 */
public class PayDemoActivity extends PayDebugActivity {

    public static Intent fragment(@NonNull Class<? extends Fragment> clz) {
        return fragment(clz, null);
    }

    public static Intent fragment(@NonNull Class<? extends Fragment> clz, String businessData) {
        Intent intent = new Intent(PayProvider.getContext(), PayDemoActivity.class);
        intent.setAction(Intent.ACTION_VIEW);
        intent.setPackage(PayProvider.getPackageName());
        intent.putExtra("fragment", clz.getName());
        if (!TextUtils.isEmpty(businessData)) {
            intent.putExtra("businessData", businessData);
        }
        return intent;
    }

    public static String getBusinessData(Intent intent) {
        if (intent != null) {
            return intent.getStringExtra("businessData");
        }
        return null;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        StatusBarManager.with(this)
                .setColorResource(R.color.pay_debug_background_primary)
                .setTextAdjustLuminance()
                .set();
        if (getIntent() == null) {
            finish();
            return;
        }
        Fragment fragment = getFragment(getIntent());
        if (fragment == null) {
            finish();
            return;
        }
        PayDebugFrameLayoutBinding binding = PayDebugFrameLayoutBinding.inflate(getLayoutInflater());
        addContentView(binding.getRoot(), UIUtils.LayoutParams.LP());
        ActivityCompat.UI.setPayBackgroundColor(this);
        getSupportFragmentManager().beginTransaction()
                .add(R.id.frame, fragment)
                .commitAllowingStateLoss();
    }

    private Fragment getFragment(Intent intent) {
        if (intent == null) {
            return null;
        }
        String fragmentClz = intent.getStringExtra("fragment");
        if (!TextUtils.isEmpty(fragmentClz)) {
            return (Fragment) Reflection.Constructor.newInstance(fragmentClz);
        } else {
            String fragmentName = UriParser.with(intent.getData()).getQuery("page");
            return ServiceLoaderUtils.loadSelected(Fragment.class, fragmentName);
        }
    }
}
