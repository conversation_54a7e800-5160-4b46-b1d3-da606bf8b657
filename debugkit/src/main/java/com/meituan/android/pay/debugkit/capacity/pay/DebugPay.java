package com.meituan.android.pay.debugkit.capacity.pay;

import android.content.Context;
import android.text.TextUtils;

import com.meituan.android.neohybrid.Neo;
import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.pay.base.utils.log.PayLogger;
import com.meituan.android.pay.debugkit.DebugInfo;
import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.pay.debugkit.capacity.DebugInit;
import com.meituan.android.pay.debugkit.utils.struct.SavedData;
import com.meituan.android.pay.debugkit.utils.toast.PayDebugToast;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;
import com.sankuai.meituan.switchtestenv.DevOnekeySwitchTestEnv;
import com.sankuai.meituan.switchtestenv.SwitchTestEnvListener;

/**
 * <AUTHOR>
 * 环境控制逻辑：
 * 1. 修改OneClick -> 触发监听 -> 修改支付Host -> 触发重新登录（可能）
 * 2. 修改支付Host -> 触发监听 -> 修改OneClick -> 触发重新登录（可能）
 */
@ServiceLoaderInterface(key = "debug_pay", interfaceClass = DebugInit.class, singleton = true)
public class DebugPay implements DebugInit, SwitchTestEnvListener {
    public static final String DEFAULT_HOST = "https://npay.meituan.com";
    public static final String DEFAULT_PAY_HOST = "https://pay.meituan.com";
    public static final String DEBUG_PAY_HOST = "debug_pay_host";
    public static final String DEBUG_HYBRID_HOST = "debug_hybrid_host";

    public static final SavedData<String> PAY_HOST = SavedData.create("network_pay_host",
            DebugPay::getHost, DebugPay::setHost);

    public static String getHost() {
        return Neo.debugger().selectStr(DEBUG_PAY_HOST, DEFAULT_HOST);
    }

    public static void setHost(String payHost) {
        if (TextUtils.isEmpty(payHost)) {
            return;
        }
        boolean isOffline = payHost.contains(".test.");
        DebugManager.info().setOfflineStatus(isOffline);
        if (DEFAULT_HOST.equals(payHost)) {
            Neo.debugger().set(DEBUG_PAY_HOST, DEFAULT_PAY_HOST);
            Neo.debugger().set(DEBUG_HYBRID_HOST, DEFAULT_HOST);
        } else {
            Neo.debugger().set(DEBUG_PAY_HOST, payHost);
            Neo.debugger().set(DEBUG_HYBRID_HOST, payHost);
        }
        // notification
        String offlineString = DebugManager.getOfflineString();
        PayDebugToast.toast(offlineString + "\n" + payHost);
        PayLogger.debug("[支付Host变化]", offlineString, payHost);
    }

    @Override
    public void switchTestEnvFinish(boolean isOnlineUrl) {
        String payHost = DevOnekeySwitchTestEnv.getRuleUrl(PayProvider.getContext(), DEFAULT_HOST);
        PAY_HOST.setFormattedValue(payHost);
    }

    @Override
    public void onInit(Context context, DebugInfo debugInfo) {
        DevOnekeySwitchTestEnv.setSwitchTestEnvListener(this);
        if (debugInfo.isFirstLaunch() && debugInfo.isInPayDemo()) {
            PAY_HOST.setFormattedValue("http://stable.pay.test.sankuai.com");
        }
    }
}
