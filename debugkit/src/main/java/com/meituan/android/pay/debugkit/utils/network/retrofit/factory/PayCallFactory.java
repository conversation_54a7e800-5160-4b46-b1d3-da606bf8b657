package com.meituan.android.pay.debugkit.utils.network.retrofit.factory;

import android.content.Context;
import android.support.annotation.NonNull;

import com.dianping.nvnetwork.NVGlobal;
import com.dianping.nvnetwork.RxInterceptor;
import com.meituan.android.common.mtguard.wtscore.plugin.sign.interceptors.NVCandyInterceptor;
import com.meituan.android.common.mtguard.wtscore.plugin.sign.interceptors.Ok3CandyInterceptor;
import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.paybase.net.DefaultRetrofitFactory;
import com.meituan.android.paybase.net.PayBaseCallFactory;
import com.meituan.android.paybase.net.cat.Ok3CatMonitorInterceptor;
import com.meituan.android.paybase.net.cat.Ok3NetExceptionCatMonitorInterceptor;
import com.meituan.android.paybase.utils.LoganUtils;
import com.meituan.android.paycommon.lib.retrofit.channel.ChannelUtils;
import com.meituan.android.singleton.RetrofitCallFactorySingleton;
import com.sankuai.meituan.kernel.net.INetInjector;
import com.sankuai.meituan.retrofit2.Request;
import com.sankuai.meituan.retrofit2.raw.RawCall;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class PayCallFactory implements RawCall.Factory {
    public static final int HTTP_CALL_TIMEOUT_MILLS = 30;

    private final RawCall.Factory httpCallFactory;

    private final RawCall.Factory sharkCallFactory;

    private PayCallFactory() {
        this.httpCallFactory = createHttpCallFactory(PayProvider.getContext(), HTTP_CALL_TIMEOUT_MILLS);
        this.sharkCallFactory = createSharkCallFactory(PayProvider.getContext());
    }

    @Override
    public RawCall get(Request request) {
        // 新增开关，验证通过后需要把 useShark 相关的开关移除。
        if (ChannelUtils.useSharkForBlackList(request.url()) && sharkCallFactory != null) {
            return sharkCallFactory.get(request);
        }
        PayBaseCallFactory.createMtNvCallFactory(PayProvider.getContext());
        return httpCallFactory.get(request);
    }

    public static RawCall.Factory createHttpCallFactory(Context context, int timeoutSeconds) {
        return RetrofitCallFactorySingleton.getInstance(new INetInjector() {
            @Override
            public void onOkHttpBuild(@NonNull okhttp3.OkHttpClient.Builder builder) {
                builder.connectTimeout(timeoutSeconds, TimeUnit.SECONDS)
                        .writeTimeout(timeoutSeconds, TimeUnit.SECONDS)
                        .readTimeout(timeoutSeconds, TimeUnit.SECONDS)
                        .addInterceptor(new Ok3NetExceptionCatMonitorInterceptor());
                if (PayProvider.isDebuggable()) {
                    builder.addInterceptor(new Ok3CandyInterceptor(context)); // mtguard验签
                    // 注意,签名一定要使用原始域名进行签名,也就是在替换mock域名之前,进行签名.否则会报验签失败
                    builder.addInterceptor(new DefaultRetrofitFactory.Ok3MockInterceptor());
                } else {
                    builder.addNetworkInterceptor(new Ok3CandyInterceptor(context)); // mtguard验签
                }
                builder.addInterceptor(new Ok3CatMonitorInterceptor()); // cat端到端监控
            }
        });
    }

    public static RawCall.Factory createSharkCallFactory(Context context) {
        if (!NVGlobal.isInit()) {
            LoganUtils.log("NVGlobal is not initialized");
            return createHttpCallFactory(context, HTTP_CALL_TIMEOUT_MILLS); // Shark创建失败时使用Http
        }
        return RetrofitCallFactorySingleton.getInstance(new INetInjector() {
            @Override
            public Object[] getRxInterceptors() {
                return new RxInterceptor[]{new NVCandyInterceptor()};
            }

            @Override
            public boolean enableMock() {
                return PayProvider.isDebuggable();
            }

            @Override
            public boolean enableShark() {
                return true;
            }
        });

    }

    public static PayCallFactory create() {
        return new PayCallFactory();
    }

}
