package com.meituan.android.pay.debugkit.utils.network.retrofit;

import android.support.annotation.NonNull;

import com.google.gson.JsonElement;
import com.meituan.android.pay.debugkit.utils.network.retrofit.call.PayCall;
import com.sankuai.meituan.retrofit2.http.Body;
import com.sankuai.meituan.retrofit2.http.FieldMap;
import com.sankuai.meituan.retrofit2.http.FormUrlEncoded;
import com.sankuai.meituan.retrofit2.http.GET;
import com.sankuai.meituan.retrofit2.http.HeaderMap;
import com.sankuai.meituan.retrofit2.http.POST;
import com.sankuai.meituan.retrofit2.http.Path;
import com.sankuai.meituan.retrofit2.http.QueryMap;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class PayRequest {
    private final Builder request;

    PayRequest(@NonNull Builder request) {
        this.request = request;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static Builder Get(String url) {
        return new Builder().get().path(url);
    }

    public static Builder Form(String url) {
        return new Builder().form().path(url);
    }

    public static Builder Json(String url) {
        return new Builder().json().path(url);
    }

    PayCall<JsonElement> request() {
        RequestCall requestCall = PayRetrofit.get().create(RequestCall.class);
        PayCall<JsonElement> payCall;
        if (request.method == Builder.GET) {
            payCall = requestCall.get(request.path, request.header, request.query);
        } else if (request.body != null) {
            payCall = requestCall.postJson(request.path,
                    request.header, request.query, request.body);
        } else {
            payCall = requestCall.postForm(request.path,
                    request.header, request.query, request.form);
        }
        return payCall;
    }

    public static class Builder {
        private static final int POST = 0;
        private static final int GET = 1;
        private static final int FORM = 0;
        private static final int JSON = 1;
        int method = POST;
        int contentType = FORM;
        String path;
        Map<String, String> header;
        Map<String, String> query;
        Map<String, String> form;
        JsonElement body;

        Builder() {
        }

        Builder(Builder builder) {
            this.method = builder.method;
            this.contentType = builder.contentType;
            this.path = builder.path;
            this.header = builder.header;
            this.query = builder.query;
            this.form = builder.form;
            this.body = builder.body;
        }

        public PayCall<JsonElement> buildCall() {
            return new PayRequest(this).request();
        }

        public Builder get() {
            this.method = GET;
            return this;
        }

        public Builder form() {
            this.method = POST;
            this.contentType = FORM;
            return this;
        }

        public Builder json() {
            this.method = POST;
            this.contentType = JSON;
            return this;
        }

        public Builder set(String path, Map<String, String> form) {
            return path(path).form(form);
        }

        public Builder path(String path) {
            this.path = path;
            return this;
        }

        public Builder header(Map<String, String> header) {
            this.header = header;
            return this;
        }

        public Builder header(String key, String value) {
            if (header == null) {
                header = new HashMap<>();
            }
            header.put(key, value);
            return this;
        }

        public Builder query(Map<String, String> query) {
            this.query = query;
            return this;
        }

        public Builder query(String key, String value) {
            if (query == null) {
                query = new HashMap<>();
            }
            query.put(key, value);
            return this;
        }

        public Builder form(Map<String, String> form) {
            this.form = form;
            this.contentType = FORM;
            return this;
        }

        public Builder body(JsonElement body) {
            this.body = body;
            this.contentType = JSON;
            return this;
        }

    }

    public interface RequestCall {

        @FormUrlEncoded
        @POST("{path}")
        PayCall<JsonElement> postForm(@Path(value = "path", encoded = true) String path,
                                      @HeaderMap Map<String, String> headers,
                                      @QueryMap Map<String, String> query,
                                      @FieldMap Map<String, String> body);

        @POST("{path}")
        PayCall<JsonElement> postJson(@Path(value = "path", encoded = true) String path,
                                      @HeaderMap Map<String, String> header,
                                      @QueryMap Map<String, String> query,
                                      @Body JsonElement body);

        @GET("{path}")
        PayCall<JsonElement> get(@Path(value = "path", encoded = true) String path,
                                 @HeaderMap Map<String, String> header,
                                 @QueryMap Map<String, String> query);
    }
}
