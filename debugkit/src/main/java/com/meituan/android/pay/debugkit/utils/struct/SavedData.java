package com.meituan.android.pay.debugkit.utils.struct;

import android.arch.lifecycle.MutableLiveData;
import android.support.annotation.Keep;
import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.android.pay.base.utils.compute.BoolParser;
import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.pay.debugkit.utils.mvvm.LiveDataUtils;
import com.meituan.android.pay.debugkit.utils.rx.RxFunction;
import com.meituan.android.pay.debugkit.utils.serialize.DebugGsonUtils;

import java.util.HashMap;
import java.util.Map;

import rx.Observable;
import rx.functions.Action1;
import rx.functions.Func0;
import rx.schedulers.Schedulers;

/**
 * <AUTHOR>
 */
@Keep
public class SavedData<T> extends MutableLiveData<String> {
    private static final Map<String, SavedData<?>> SAVED_DATA_MAP = new HashMap<>();

    public static MutableLiveData<String> getLivedata(String key) {
        return SAVED_DATA_MAP.get(key);
    }

    public static boolean isData(String key) {
        MutableLiveData<String> liveData = getLivedata(key);
        return BoolParser.bool(liveData != null ? liveData.getValue() : null);
    }

    public static String getData(String key) {
        MutableLiveData<String> liveData = getLivedata(key);
        return liveData != null ? liveData.getValue() : null;
    }

    private final String key;
    private final Func0<T> valInit;
    private final Action1<T> valObserver;
    private Class<T> savedType;

    public SavedData(String key, @NonNull Func0<T> valInit, Action1<T> valObserver) {
        this.key = key;
        this.valInit = valInit;
        this.valObserver = valObserver;
        init();
    }

    private void init() {
        T initVal = initFromSaved();
        setFormattedValue(initVal);
        Observable.just(savedType.getName())
                .subscribeOn(Schedulers.io())
                .doOnNext(type -> DebugManager.getStorage().setString(key + "-class", type))
                .subscribe();
        LiveDataUtils.observeForever(this, val -> Observable.just(val)
                .doOnNext(s -> RxFunction.call(valObserver, getFormattedValue()))
                .observeOn(Schedulers.io())
                .doOnNext(value -> DebugManager.getStorage().setString(key, value))
                .subscribe());
        SAVED_DATA_MAP.put(key, this);
    }

    /**
     * @noinspection unchecked
     */
    private T initFromSaved() {
        try {
            String savedValueString = DebugManager.getStorage().getString(key, null);
            String savedValueClass = DebugManager.getStorage().getString(key + "-class", "");
            if (TextUtils.isEmpty(savedValueString) || TextUtils.isEmpty(savedValueClass)) {
                return initValue();
            }
            savedType = (Class<T>) Class.forName(savedValueClass);
            T initValue = DebugGsonUtils.fromJsonSafety(savedValueString, savedType);
            if (initValue == null) {
                return initValue();
            }
            return initValue;
        } catch (Exception e) {
            return initValue();
        }
    }

    /**
     * @noinspection unchecked
     */
    private T initValue() {
        T value = valInit.call();
        savedType = (Class<T>) value.getClass();
        setFormattedValue(value);
        return value;
    }

    public void setFormattedValue(T value) {
        setValue(DebugGsonUtils.toString(value));
    }

    public T getFormattedValue() {
        return DebugGsonUtils.fromJsonSafety(getValue(), savedType);
    }

    private T getFormattedValue(String val) {
        return DebugGsonUtils.fromJsonSafety(val, savedType);
    }

    public static <T> SavedData<T> create(String key, @NonNull Func0<T> valInit, @NonNull Action1<T> valObserver) {
        return new SavedData<>(key, valInit, valObserver);
    }

    public static <T> SavedData<T> create(String key, @NonNull Func0<T> valInit) {
        return new SavedData<>(key, valInit, null);
    }

    public static <T> SavedData<T> just(String key, @NonNull T valInit) {
        return new SavedData<>(key, () -> valInit, null);
    }

    public static <T> SavedData<T> just(String key, @NonNull T valInit, @NonNull Action1<T> valObserver) {
        return new SavedData<>(key, () -> valInit, valObserver);

    }
}
