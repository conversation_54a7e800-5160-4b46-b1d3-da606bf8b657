package com.meituan.android.pay.debugkit.main.order.refund;

import android.view.LayoutInflater;

import com.meituan.android.pay.debugkit.main.base.PayDemoRecyclerFragment;
import com.meituan.android.pay.debugkit.main.data.DataProvider;
import com.meituan.android.pay.debugkit.main.data.model.OrderInfo;
import com.meituan.android.pay.debugkit.widget.view.recycler.BindingAdapter;
import com.meituan.android.paybase.dialog.ProgressController;
import com.meituan.android.pay.debugkit.databinding.PayDebugFragmentRecyclerLayoutBinding;
import com.meituan.android.pay.debugkit.databinding.PayDebugOrderListItemBinding;

/**
 * <AUTHOR>
 */
public class OrderRefundListFragment extends PayDemoRecyclerFragment<PayDebugOrderListItemBinding, OrderInfo> {

    @Override
    public void onCreateView(LayoutInflater inflater, PayDebugFragmentRecyclerLayoutBinding binding) {
        super.onCreateView(inflater, binding);
        binding.title.setText("订单详情");

        BindingAdapter<PayDebugOrderListItemBinding, OrderInfo> adapter = adapter()
                .setInflater((itemInflater, parent) ->
                        PayDebugOrderListItemBinding.inflate(itemInflater, parent, false))
                .setBinder((itemBinding, response) -> {
                    itemBinding.title.setText(response.getSubject());
                    itemBinding.status.setText(response.getStatus().content());
                    itemBinding.content.setText(response.getSellerId());
                    itemBinding.money.setText(response.getMoneyContent());
                    itemBinding.date.setText(response.getOrderTimeFormatted());
                });

        // 请求数据
        DataProvider.order()
                .queryOrderRefundList()
                .onStart(() -> ProgressController.of(getActivity()).show())
                .onFinal(() -> ProgressController.of(getActivity()).hide())
                .onSuccess(responseList -> adapter.clear().add(responseList).notifyRender())
                .observeCall(this);
    }
}
