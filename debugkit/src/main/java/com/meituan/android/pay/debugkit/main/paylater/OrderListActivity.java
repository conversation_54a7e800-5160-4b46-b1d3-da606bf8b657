package com.meituan.android.pay.debugkit.main.paylater;

import android.content.Intent;
import android.os.Bundle;
import android.support.v7.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.pay.debugkit.main.paylater.model.OrderInfo;
import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paycommon.lib.retrofit.PayRetrofit;
import com.meituan.android.pay.debugkit.R;
import com.meituan.android.pay.debugkit.main.activity.PayDebugActivity;
import com.meituan.android.pay.debugkit.main.data.remote.PayDemoRequestService;
import com.meituan.android.pay.debugkit.widget.old.PowerfulRecyclerView;
import com.meituan.android.pay.debugkit.widget.old.adapter.SimpleSwipeAndDragAdapter;
import com.meituan.android.pay.debugkit.widget.old.listener.OnItemTouchListener;
import com.meituan.android.pay.debugkit.widget.old.listener.OnLoadMoreListener;
import com.meituan.android.pay.debugkit.widget.old.listener.OnRefreshListener;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;


public class OrderListActivity extends PayDebugActivity implements IRequestCallback,
        OnLoadMoreListener, OnRefreshListener {

    private static final int REQUEST_TAG_QUERY_ORDER = 1;

    private PowerfulRecyclerView mPowerfulRecyclerView;
    private RecyclerView.Adapter mAdapter;
    private List<OrderInfo> mData = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.old_order_list);
        mPowerfulRecyclerView = findViewById(R.id.pull_refresh_order_list);
        init();
        queryOrderList(0);
    }

    @SuppressWarnings("unchecked")
    private void init() {
        mAdapter = new SimpleSwipeAndDragAdapter(mData) {
            @Override
            public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
                View view = LayoutInflater.from(OrderListActivity.this).inflate(R.layout.old_order_list_item, parent, false);
                return new RecyclerView.ViewHolder(view) {
                };
            }

            @Override
            public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
                OrderInfo orderInfo = (OrderInfo) datas.get(position);
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                ((TextView) holder.itemView.findViewById(R.id.order_name)).setText((orderInfo.getSubject()));
                ((TextView) holder.itemView.findViewById(R.id.order_price)).setText((orderInfo.getMoney()) + "元");
                ((TextView) holder.itemView.findViewById(R.id.order_date)).setText(format.format(orderInfo.getOrderTime() * 1000L));

                TextView payState = holder.itemView.findViewById(R.id.order_state);
                payState.setText((OrderInfo.getPayResultByStatus(orderInfo.getStatus())));
                if (OrderInfo.PAY_STATUS_SUCC == orderInfo.getStatus()) {
                    payState.setTextColor(getResources().getColor(R.color.mpay__dark_blue));
                } else {
                    payState.setTextColor(getResources().getColor(R.color.pay_debug_black));
                }
            }
        };
        mPowerfulRecyclerView.setAdapter(mAdapter);
        mPowerfulRecyclerView.setRefreshEnable(true);
        mPowerfulRecyclerView.setLoadMoreEnable(true);
        mPowerfulRecyclerView.setOnRefreshListener(this);
        mPowerfulRecyclerView.setOnLoadMoreListener(this);
        mPowerfulRecyclerView.setOnItemTouchListener(new OnItemTouchListener() {

            @Override
            public void onClick(RecyclerView.ViewHolder viewHolder, int i) {
                OrderInfo orderInfo = mData.get(i);
                openRefundActivity(orderInfo);
            }

            @Override
            public boolean onLongClick(RecyclerView.ViewHolder viewHolder, int i, float v, float v1) {
                return false;
            }

            @Override
            public void onPress(RecyclerView.ViewHolder viewHolder, int i) {

            }

            @Override
            public void onUp(RecyclerView.ViewHolder viewHolder, int i) {

            }
        });
    }

    // 参考文档：https://km.sankuai.com/page/15542531
    private void queryOrderList(int offset) {
        PayRetrofit.getInstance().create(
                PayDemoRequestService.class, this, REQUEST_TAG_QUERY_ORDER)
                .queryOrderList(PayProvider.app().getUserId(), "10", String.valueOf(offset));
    }

    @Override
    public void onStartRefresh() {
        queryOrderList(0);
    }

    @Override
    public void onLoadMore() {
        queryOrderList(mData.size());
    }

    @SuppressWarnings("unchecked")
    @Override
    public void onRequestSucc(int i, Object o) {
        if (i == REQUEST_TAG_QUERY_ORDER) {
            if (mPowerfulRecyclerView.isRefreshing()) {
                mData.clear();
            }
            mData.addAll((List<OrderInfo>) o);
            mAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void onRequestException(int i, Exception e) {
        ToastUtils.showSnackToast(this, "数据异常：" + e.getMessage());
        finish();
    }

    @Override
    public void onRequestFinal(int i) {
        mPowerfulRecyclerView.stopRefresh();
        mPowerfulRecyclerView.stopLoadMore();
    }

    @Override
    public void onRequestStart(int i) {
    }

    @Override
    public void onDestroy() {
        mData.clear();
        super.onDestroy();
    }

    private void openRefundActivity(OrderInfo orderInfo) {
        Intent intent = new Intent(OrderListActivity.this, OrderDetailsActivity.class);
        intent.putExtra(OrderDetailsActivity.PARAM_REFUND_ORDER_INFO, orderInfo);
        startActivity(intent);
    }
}
