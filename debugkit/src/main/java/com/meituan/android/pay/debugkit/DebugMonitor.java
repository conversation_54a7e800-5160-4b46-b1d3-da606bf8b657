package com.meituan.android.pay.debugkit;

import android.app.Activity;
import android.app.Application;
import android.content.ContentProvider;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.view.KeyEvent;

import com.meituan.android.pay.debugkit.utils.activity.hook.ActivityLifecycleCallbacksImpl;
import com.meituan.android.pay.debugkit.utils.activity.hook.EventListener;
import com.meituan.android.pay.debugkit.utils.activity.hook.WindowCallback;
import com.meituan.android.pay.debugkit.utils.router.OpenUtils;
import com.meituan.android.pay.debugkit.utils.rx.RxFunction;
import com.meituan.android.pay.debugkit.utils.system.FileUtils;

import java.lang.ref.WeakReference;
import java.util.concurrent.atomic.AtomicBoolean;

import rx.schedulers.Schedulers;

/**
 * <AUTHOR>
 */
public class DebugMonitor extends ContentProvider {
    private static final String FILE_NAME = "**pay**debug**";
    private static final Monitor MONITOR = new Monitor();
    private static final AtomicBoolean INIT_FLAG = new AtomicBoolean(false);
    private static boolean enable3PointDebug;

    @Override
    public boolean onCreate() {
        if (INIT_FLAG.compareAndSet(false, true)) {
            // 只会执行一次的初始化
            Context context = getContext();
            if (context instanceof Application) {
                Application application = (Application) context;
                application.registerActivityLifecycleCallbacks(MONITOR);
            }
            try {
                check3PointDebug(context);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return true;
    }

    public static void check3PointDebug(Context context) {
        if (context != null) {
            FileUtils.openInternalFile(context, FILE_NAME)
                    .onErrorReturn(RxFunction.onErrorReturnNull())
                    .filter(FileUtils.isFileExist())
                    .subscribe(file -> set3PointDebug(true));
        }
    }

    public static void set3PointDebug(Context context, boolean enable) {
        if (context instanceof Application) {
            set3PointDebug(enable);
            if (enable3PointDebug) {
                FileUtils.openInternalFile(context, FILE_NAME)
                        .filter(FileUtils.isFileNotExist())
                        .doOnNext(FileUtils.createNewFile())
                        .onErrorReturn(RxFunction.onErrorReturnNull())
                        .subscribeOn(Schedulers.io())
                        .subscribe();
            } else {
                FileUtils.openInternalFile(context, FILE_NAME)
                        .filter(FileUtils.isFileExist())
                        .doOnNext(FileUtils.deleteFile())
                        .onErrorReturn(RxFunction.onErrorReturnNull())
                        .subscribeOn(Schedulers.io())
                        .subscribe();
            }
        }
    }

    public static Activity getActivity() {
        return MONITOR.getActivity();
    }

    public static void set3PointDebug(boolean enable) {
        enable3PointDebug = enable;
    }

    @Nullable
    @Override
    public Cursor query(@NonNull Uri uri, @Nullable String[] projection, @Nullable String selection, @Nullable String[] selectionArgs, @Nullable String sortOrder) {
        return null;
    }

    @Nullable
    @Override
    public String getType(@NonNull Uri uri) {
        return null;
    }

    @Nullable
    @Override
    public Uri insert(@NonNull Uri uri, @Nullable ContentValues values) {
        return null;
    }

    @Override
    public int delete(@NonNull Uri uri, @Nullable String selection, @Nullable String[] selectionArgs) {
        return 0;
    }

    @Override
    public int update(@NonNull Uri uri, @Nullable ContentValues values, @Nullable String selection, @Nullable String[] selectionArgs) {
        return 0;
    }

    public static class Monitor extends ActivityLifecycleCallbacksImpl {
        private WeakReference<Activity> weakActivity = new WeakReference<>(null);

        public Activity getActivity() {
            return weakActivity != null ? weakActivity.get() : null;
        }

        @Override
        public void onActivityResumed(@NonNull Activity activity) {
            weakActivity = new WeakReference<>(activity);
            if (enable3PointDebug) {
                WindowCallback.bindWindow(activity, new EventListener() {
                    @Override
                    public void onGestureEvent(String event) {
                        OpenUtils.openScheme(activity, "pay://debugkit/main");
                    }

                    @Override
                    public boolean onKeyEvent(KeyEvent event) {
                        return false;
                    }
                });
            }
        }
    }
}
