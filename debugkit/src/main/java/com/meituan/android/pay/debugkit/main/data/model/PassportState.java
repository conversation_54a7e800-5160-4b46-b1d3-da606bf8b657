package com.meituan.android.pay.debugkit.main.data.model;

import android.support.annotation.Keep;

import com.meituan.passport.pojo.User;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Keep
public class PassportState implements Serializable {
    private final String id;
    private final String name;
    private final String image;
    private final String button;

    public PassportState(String id, String name, String image, String button) {
        this.id = id;
        this.name = name;
        this.image = image;
        this.button = button;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getImage() {
        return image;
    }

    public String getButton() {
        return button;
    }

    public static PassportState noLogin() {
        return new PassportState("", "未登录", "", "登录");
    }

    public static PassportState from(User user) {
        if (user != null) {
            return new PassportState(String.valueOf(user.id),
                    user.username,
                    user.avatarurl,
                    "退出");
        }
        return noLogin();
    }
}