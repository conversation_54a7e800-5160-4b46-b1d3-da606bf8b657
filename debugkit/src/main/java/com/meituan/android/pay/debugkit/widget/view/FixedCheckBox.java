package com.meituan.android.pay.debugkit.widget.view;

import android.content.Context;
import android.support.v4.content.ContextCompat;
import android.support.v7.widget.AppCompatCheckBox;
import android.util.AttributeSet;

import com.meituan.android.pay.debugkit.R;

/**
 * <AUTHOR>
 */
public class FixedCheckBox extends AppCompatCheckBox {

    public FixedCheckBox(Context context) {
        super(context);
        setButtonDrawable(ContextCompat.getDrawable(context, android.R.color.transparent));
        setBackgroundResource(R.drawable.pay_debug_fixed_checkbox_background);
    }

    public FixedCheckBox(Context context, AttributeSet attrs) {
        super(context, attrs);
        setButtonDrawable(ContextCompat.getDrawable(context, android.R.color.transparent));
        setBackgroundResource(R.drawable.pay_debug_fixed_checkbox_background);
    }

    public FixedCheckBox(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setButtonDrawable(ContextCompat.getDrawable(context, android.R.color.transparent));
        setBackgroundResource(R.drawable.pay_debug_fixed_checkbox_background);
    }
}
