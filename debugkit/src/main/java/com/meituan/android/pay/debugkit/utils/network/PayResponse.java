package com.meituan.android.pay.debugkit.utils.network;

import android.support.annotation.Keep;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Keep
public class PayResponse<T> implements Serializable {
    private final String status;

    private final T data;

    private final PayException error;

    public PayResponse(String status, T data, PayException error) {
        this.status = status;
        this.data = data;
        this.error = error;
    }

    public String getStatus() {
        return status;
    }

    public T data() {
        return data;
    }

    public PayException error() {
        return error;
    }

    public static <T> boolean isPaySuccess(PayResponse<T> response) {
        return response != null && response.data != null;
    }

    public static <T> boolean isPayError(PayResponse<T> response) {
        return response != null && response.error != null;
    }
}
