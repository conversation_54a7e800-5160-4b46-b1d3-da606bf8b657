package com.meituan.android.pay.debugkit.widget.simple;

import android.arch.lifecycle.MutableLiveData;
import android.view.View;
import android.widget.LinearLayout;

import com.meituan.android.pay.base.container.PayBaseFragment;
import com.meituan.android.pay.base.utils.cache.MapCache;
import com.meituan.android.pay.debugkit.utils.mvvm.LiveDataUtils;
import com.meituan.android.pay.debugkit.utils.serialize.DebugGsonUtils;
import com.meituan.android.pay.debugkit.utils.struct.Stream;
import com.meituan.android.pay.debugkit.utils.ui.UIUtils;
import com.meituan.android.pay.debugkit.widget.simple.base.SimpleMethod;
import com.meituan.android.pay.debugkit.widget.simple.base.SimpleValueProvider;
import com.meituan.android.pay.debugkit.widget.simple.base.SimpleViewState;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import rx.Observable;
import rx.functions.Action1;

/**
 * <AUTHOR>
 */
public class SimpleSharedViewState implements SimpleValueProvider {
    private final MapCache<String, MutableLiveData<String>> sharedData =
            MapCache.init(key -> new MutableLiveData<>());

    private final MutableLiveData<SimpleMethod> invokedMethod = new MutableLiveData<>();

    public MutableLiveData<SimpleMethod> getInvokedMethod() {
        return invokedMethod;
    }

    private List<SimpleViewState> viewStateList = new ArrayList<>();

    @Override
    public void update(String key, String value) {
        LiveDataUtils.compareAndSet(liveDataOf(key), value);
    }

    @Override
    public void update(Map<String, String> data) {
        Stream.on(data).foreach(entry -> update(entry.getKey(), entry.getValue()));
    }

    @Override
    public Map<String, String> valuesOf(List<String> keys) {
        Map<String, String> result = new HashMap<>();
        Stream.on(keys).foreach(key -> {
            String value = liveDataOf(key).getValue();
            if (value != null) {
                result.put(key, value);
            }
        });
        return result;
    }

    @Override
    public String valueOf(String key) {
        return liveDataOf(key).getValue();
    }

    @Override
    public MutableLiveData<String> liveDataOf(String key) {
        return sharedData.cache(key);
    }

    public Map<String, MutableLiveData<String>> boundLiveData() {
        return Stream.on(sharedData.map())
                .filter(entry -> entry.getKey() != null && !entry.getKey().startsWith("simple-"))
                .collect();
    }

    public void bindInvoker(PayBaseFragment fragment) {
        LiveDataUtils.observe(fragment, invokedMethod, simpleMethod ->
                simpleMethod.invoke(fragment));
    }

    public Observable<View> getView(PayBaseFragment fragment, String viewData) {
        this.viewStateList = DebugGsonUtils.fromJsonToList(viewData, SimpleViewState.class);

        Action1<SimpleViewState> bindAction = simpleViewState -> {
            LiveDataUtils.bind(fragment, simpleViewState.getInvokedMethod(), getInvokedMethod());
            LiveDataUtils.twoSideBind(fragment, simpleViewState.getValue(), liveDataOf(simpleViewState.getKey()));
        };
        Action1<SimpleViewState> bindChildrenAction = simpleViewState -> Observable.from(simpleViewState.children())
                .doOnNext(bindAction).subscribe();

        return Observable.from(viewStateList)
                .doOnNext(bindAction)
                .doOnNext(bindChildrenAction)
                .toList()
                .map(simpleViewStates -> {
                    LinearLayout linearLayout = new LinearLayout(fragment.getContext());
                    linearLayout.setOrientation(LinearLayout.VERTICAL);
                    linearLayout.setLayoutParams(UIUtils.LayoutParams.LLP());
                    for (SimpleViewState simpleViewState : simpleViewStates) {
                        linearLayout.addView(simpleViewState.inflate(fragment));
                    }
                    return linearLayout;
                });
    }

    public Observable<String> getViewData() {
        return Observable.just(DebugGsonUtils.toString(viewStateList));
    }
}
