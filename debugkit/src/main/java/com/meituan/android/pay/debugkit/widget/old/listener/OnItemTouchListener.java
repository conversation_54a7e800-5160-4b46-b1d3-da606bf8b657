package com.meituan.android.pay.debugkit.widget.old.listener;

import android.support.v7.widget.RecyclerView;

/**
 * holder点击和长按监听器
 */
public interface OnItemTouchListener {

    /**
     * item被点击
     * @param holder item holder
     * @param position position
     */
    void onClick(RecyclerView.ViewHolder holder, int position);

    /**
     * item被长按
     * @param holder item holder
     * @param position position
     * @param mRawX  被点击的坐标X
     * @param mRawY 被点击的坐标XY
     * @return
     */
    boolean onLongClick(RecyclerView.ViewHolder holder, int position, float mRawX,
                        float mRawY);

    /**
     * 触摸item 对应ACTION_DOWN
     * @param holder item holder
     * @param position position
     */
    void onPress(RecyclerView.ViewHolder holder, int position);

    /**
     * 手指离开item 对应ACTION_UP
     * @param holder item holder
     * @param position position
     */
    void onUp(RecyclerView.ViewHolder holder, int position);

}
