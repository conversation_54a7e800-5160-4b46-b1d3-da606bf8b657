package com.meituan.android.pay.debugkit.main.data.model;

import android.support.annotation.Keep;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import com.google.gson.annotations.JsonAdapter;

import java.io.Serializable;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 */
@Keep
@JsonAdapter(OrderStatus.Adapter.class)
public enum OrderStatus implements Serializable {
    UNKNOWN(999, "未知状态"),
    NO(0, "未支付"),
    CONFIRM(4, "用户已确认"),
    COMPLETED(6, "商户已完结"),
    CLOSE(13, "支付关闭"),
    FAIL(14, "支付失败"),
    SUCCESS(16, "记账成功"),
    SUCCESS_NOTIFIED(64, "通知业务线成功"),
    REFUND_PART(96, "部分退款"),
    REFUND_ALL(97, "全部退款");

    private final int code;
    private final String message;

    OrderStatus(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int code() {
        return code;
    }

    public String message() {
        return message;
    }

    public String content() {
        return message + "-" + code;
    }

    public static OrderStatus of(int code) {
        for (OrderStatus status : OrderStatus.values()) {
            if (status.code == code) {
                return status;
            }
        }
        return UNKNOWN;
    }

    public static class Adapter implements JsonDeserializer<OrderStatus>, JsonSerializer<OrderStatus> {

        @Override
        public OrderStatus deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
            return OrderStatus.of(json.getAsNumber().intValue());
        }

        @Override
        public JsonElement serialize(OrderStatus src, Type typeOfSrc, JsonSerializationContext context) {
            return new JsonPrimitive(src.code);
        }
    }
}
