package com.meituan.android.pay.debugkit.utils.ui;

import android.app.Activity;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.os.Build;
import android.support.annotation.ColorRes;
import android.support.v4.content.ContextCompat;
import android.support.v4.graphics.ColorUtils;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.meituan.android.pay.base.utils.exception.Catch;

/**
 * <AUTHOR>
 */
public class StatusBarManager {
    private static final double STATUS_BAR_LUMINANCE_DARK = 0.5;

    private final Activity activity;

    private int flag;

    private int color;

    public StatusBarManager(Activity activity) {
        this.activity = activity;
        this.flag = activity.getWindow().getDecorView().getSystemUiVisibility();
        this.color = activity.getWindow().getStatusBarColor();
    }

    public StatusBarManager setTextAdjustLuminance() {
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.M) {
            return this;
        }
        if (ColorUtils.calculateLuminance(color) > STATUS_BAR_LUMINANCE_DARK) {
            this.flag |= View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;
        } else {
            this.flag = removeFlag(flag, View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        }
        return this;
    }

    public StatusBarManager setTextDark(boolean dark) {
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.M) {
            return this;
        }
        if (dark) {
            this.flag = flag | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;
        } else {
            this.flag = removeFlag(flag, View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        }
        return this;
    }

    public StatusBarManager setFullScreen(boolean fullScreen) {
        if (fullScreen) {
            this.flag = flag | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN;
        } else {
            this.flag = removeFlag(flag, View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
        }
        return this;
    }

    public StatusBarManager setImmersion(boolean immersion) {
        if (immersion) {
            this.flag = flag | View.SYSTEM_UI_FLAG_FULLSCREEN;
        } else {
            this.flag = removeFlag(flag, View.SYSTEM_UI_FLAG_FULLSCREEN);
        }
        return this;
    }

    public StatusBarManager setSystemColor() {
        try {
            final TypedArray typedArray = activity.obtainStyledAttributes(
                    new int[]{android.R.attr.statusBarColor});
            this.color = typedArray.getColor(0, 0);
            typedArray.recycle();
        } catch (Exception e) {
            Catch.with(e).log();
        }
        return this;
    }

    public StatusBarManager setColor(int color) {
        this.color = color;
        return this;
    }

    public StatusBarManager setColor(String color) {
        try {
            this.color = Color.parseColor(color);
        } catch (Exception e) {
            Catch.with(e).log();
        }
        return this;
    }

    public StatusBarManager setColorResource(@ColorRes int colorRes) {
        this.color = ContextCompat.getColor(activity, colorRes);
        return this;
    }

    private boolean isFlagMasked(int flag, int mask) {
        return (flag & mask) != 0;
    }

    private int removeFlag(int flag, int removed) {
        return (flag | removed) ^ removed;
    }

    public State set() {
        Window window = activity.getWindow();
        window.getDecorView().setSystemUiVisibility(flag);
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        window.setStatusBarColor(color);
        return new State(flag, color);
    }

    public State set(State state) {
        Window window = activity.getWindow();
        window.getDecorView().setSystemUiVisibility(state.flag);
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        window.setStatusBarColor(state.color);
        return state;
    }

    public static StatusBarManager with(Activity activity) {
        return new StatusBarManager(activity);
    }

    public static class State {
        public final int flag;
        public final int color;

        public State(int flag, int color) {
            this.flag = flag;
            this.color = color;
        }
    }
}
