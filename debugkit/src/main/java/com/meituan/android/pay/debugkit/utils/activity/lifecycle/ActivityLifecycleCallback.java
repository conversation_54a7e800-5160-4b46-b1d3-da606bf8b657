package com.meituan.android.pay.debugkit.utils.activity.lifecycle;

import android.app.Activity;
import android.app.Application;
import android.arch.lifecycle.Lifecycle;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.meituan.android.pay.base.utils.lifecycle.LifecycleObservable;
import com.meituan.android.pay.base.utils.log.PayLogger;

/**
 * <AUTHOR>
 */
public class ActivityLifecycleCallback implements Application.ActivityLifecycleCallbacks, LifecycleObservable {

    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
        onLifecycleCreated();
    }

    @Override
    public void onActivityStarted(@NonNull Activity activity) {
        onLifecycleStarted();
    }

    @Override
    public void onActivityResumed(@NonNull Activity activity) {
        PayLogger.debug("ActivityLifecycleCallback|onActivityResumed", activity);
        onLifecycleResumed();
    }

    @Override
    public void onActivityPaused(@NonNull Activity activity) {
        onLifecyclePaused();
    }

    @Override
    public void onActivityStopped(@NonNull Activity activity) {
        onLifecycleStopped();
    }

    @Override
    public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {
        getLifecycleRegistry().markState(Lifecycle.State.CREATED);
    }

    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {
        onLifecycleDestroyed();
    }
}
