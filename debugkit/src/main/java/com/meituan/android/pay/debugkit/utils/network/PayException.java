package com.meituan.android.pay.debugkit.utils.network;

import android.support.annotation.Keep;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.pay.base.utils.serialize.GsonProvider;

import java.io.IOException;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Keep
public class PayException extends IOException implements Serializable {
    @SerializedName("level")
    private final int level;

    @SerializedName("code")
    private final String code;

    @SerializedName("message")
    private final String message;

    @SerializedName("type")
    private final String type;

    @SerializedName("extra")
    private final String extra;

    public PayException(int level, String code, String msg, String type) {
        this(level, code, msg, type, null);
    }

    public PayException(int level, String code, String msg, String type, String extra) {
        this.level = level;
        this.code = code;
        this.message = msg;
        this.type = type;
        this.extra = extra;
    }

    public int getLevel() {
        return level;
    }

    public String getCode() {
        return code;
    }

    @Nullable
    @Override
    public String getMessage() {
        return message;
    }

    public String getType() {
        return type;
    }

    public String getExtra() {
        return extra;
    }

    public String getShowedCode() {
        return "(" + code + ")";
    }

    @NonNull
    @Override
    public String toString() {
        return GsonProvider.get().toJson(this);
    }
}
