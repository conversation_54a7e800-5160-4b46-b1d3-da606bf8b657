package com.meituan.android.pay.debugkit.utils.serialize;

import android.arch.lifecycle.MutableLiveData;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.meituan.android.pay.base.utils.serialize.FixedNumberParseAdapter;
import com.meituan.android.pay.base.utils.serialize.GsonUtils;

/**
 * <AUTHOR>
 */
public class DebugGsonProvider {
    private DebugGsonProvider() {
    }

    public static Gson delegate() {
        return Holder.DELEGATE;
    }

    public static Gson get() {
        return Holder.INSTANCE;
    }

    public static Gson format() {
        return Holder.FORMAT;
    }

    private static class Holder {
        private static final Gson DELEGATE = new GsonBuilder().create();

        private static final Gson INSTANCE = new GsonBuilder()
                .registerTypeAdapter(MutableLiveData.class, new MutableLiveDataTypeAdapter())
                .registerTypeAdapter(GsonUtils.TypeTokens.OBJECT_MAP, new FixedNumberParseAdapter())
                .create();

        private static final Gson FORMAT = new GsonBuilder()
                .registerTypeAdapter(MutableLiveData.class, new MutableLiveDataTypeAdapter())
                .registerTypeAdapter(GsonUtils.TypeTokens.OBJECT_MAP, new FixedNumberParseAdapter())
                .disableHtmlEscaping().setPrettyPrinting().create();
    }
}
