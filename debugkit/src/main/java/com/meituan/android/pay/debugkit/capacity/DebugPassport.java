package com.meituan.android.pay.debugkit.capacity;

import android.app.Activity;
import android.content.Intent;

import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.pay.debugkit.R;
import com.meituan.android.singleton.UserCenterSingleton;
import com.meituan.passport.LoginActivity;

/**
 * <AUTHOR>
 */
public class DebugPassport {

    public static void login() {
        Activity activity = DebugManager.getActivity();
        if (activity != null) {
            activity.startActivity(new Intent(activity, LoginActivity.class));
            activity.overridePendingTransition(R.anim.paycommon_fragment_slide_right_in,
                    R.anim.paycommon_fragment_slide_left_out);
        }
    }

    public static void logout() {
        UserCenterSingleton.getInstance().logOut();
    }

    public static void flip() {
        if (UserCenterSingleton.getInstance().isLogin()) {
            logout();
        } else {
            login();
        }
    }
}
