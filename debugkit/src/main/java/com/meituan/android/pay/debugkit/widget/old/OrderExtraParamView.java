package com.meituan.android.pay.debugkit.widget.old;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.EditText;
import android.widget.FrameLayout;

import com.meituan.android.pay.debugkit.R;

public class OrderExtraParamView extends FrameLayout {

    private EditText mKeyEdit;
    private EditText mValueEdit;

    private OnRemoveListener mOnRemoveListener;

    public OrderExtraParamView(Context context) {
        super(context);
        init();
    }

    public OrderExtraParamView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        inflate(getContext(), R.layout.view_append_params, this);
        mKeyEdit = findViewById(R.id.edit_key);
        mValueEdit = findViewById(R.id.edit_value);
        findViewById(R.id.btn_remove_param).setOnClickListener(v -> {
            if (mOnRemoveListener != null) {
                mOnRemoveListener.onRemove(OrderExtraParamView.this);
            }
        });
    }

    public String getParamKey() {
        return mKeyEdit.getText().toString().trim();
    }

    public String getParamValue() {
        return mValueEdit.getText().toString().trim();
    }

    public void setOnRemoveListener(OnRemoveListener listener) {
        mOnRemoveListener = listener;
    }

    public interface OnRemoveListener {
        void onRemove(OrderExtraParamView view);
    }
}
