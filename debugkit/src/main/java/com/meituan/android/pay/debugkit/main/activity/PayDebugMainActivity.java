package com.meituan.android.pay.debugkit.main.activity;

import android.os.Bundle;
import android.support.v4.app.ActivityCompat;
import android.view.KeyEvent;

import com.meituan.android.pay.debugkit.R;
import com.meituan.android.pay.debugkit.main.base.PayDebugFragmentTabManager;
import com.meituan.android.pay.debugkit.main.jump.JumpFragment;
import com.meituan.android.pay.debugkit.main.order.OrderFragment;
import com.meituan.android.pay.debugkit.main.setting.SettingFragment;
import com.meituan.android.pay.debugkit.utils.system.RuntimePermissionUtils;
import com.meituan.android.pay.debugkit.utils.ui.StatusBarManager;

/**
 * <AUTHOR>
 */
public class PayDebugMainActivity extends PayDebugActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        StatusBarManager.with(this)
                .setColorResource(R.color.pay_debug_background_primary)
                .setTextAdjustLuminance()
                .set();
        PayDebugFragmentTabManager.with(this)
                .add("跳转", R.drawable.pay_debug_main_tab_entrance, new JumpFragment())
                .add("设置", R.drawable.pay_debug_main_tab_setting, new SettingFragment())
                .add("下单", R.drawable.pay_debug_main_tab_order, new OrderFragment())
                .startOnIndex(1);

        if (!RuntimePermissionUtils.checkSelfPermission(this, android.Manifest.permission.ACCESS_COARSE_LOCATION)
                || !RuntimePermissionUtils.checkSelfPermission(this, android.Manifest.permission.ACCESS_FINE_LOCATION)
                || !RuntimePermissionUtils.checkSelfPermission(this, android.Manifest.permission.READ_PHONE_STATE)
                || !RuntimePermissionUtils.checkSelfPermission(this, android.Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
            ActivityCompat.requestPermissions(this,
                    new String[]{
                            android.Manifest.permission.ACCESS_COARSE_LOCATION,
                            android.Manifest.permission.ACCESS_FINE_LOCATION,
                            android.Manifest.permission.READ_PHONE_STATE,
                            android.Manifest.permission.WRITE_EXTERNAL_STORAGE
                    }, 0xAA // PERMISSION_REQUEST_CODE_LOCATION
            );
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}