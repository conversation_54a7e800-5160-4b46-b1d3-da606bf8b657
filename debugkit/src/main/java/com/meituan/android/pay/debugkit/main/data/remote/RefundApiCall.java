package com.meituan.android.pay.debugkit.main.data.remote;

import com.meituan.android.pay.debugkit.main.data.model.OrderInfo;
import com.meituan.android.pay.debugkit.main.data.model.RefundResult;
import com.meituan.android.pay.debugkit.main.data.model.RefundInfo;
import com.meituan.android.pay.debugkit.utils.network.retrofit.call.PayResponseCall;
import com.sankuai.meituan.retrofit2.http.Field;
import com.sankuai.meituan.retrofit2.http.FieldMap;
import com.sankuai.meituan.retrofit2.http.FormUrlEncoded;
import com.sankuai.meituan.retrofit2.http.POST;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface RefundApiCall {

    @FormUrlEncoded
    @POST("/demo/payorder/querypayorderbyuid")
    PayResponseCall<List<OrderInfo>> queryRefundOrder(@Field("userid") String userid,
                                                      @Field("limit") int limit,
                                                      @Field("offset") int offset);


    @FormUrlEncoded
    @POST("/demo/refund/apply")
    PayResponseCall<RefundResult> processRefund(@FieldMap HashMap<String, Object> form);


    @FormUrlEncoded
    @POST("/demo/refund/list")
    PayResponseCall<List<RefundInfo>> queryRefundProcess(@Field("userId") String userid,
                                                         @Field("limit") String limit,
                                                         @Field("offset") String offset);
}
