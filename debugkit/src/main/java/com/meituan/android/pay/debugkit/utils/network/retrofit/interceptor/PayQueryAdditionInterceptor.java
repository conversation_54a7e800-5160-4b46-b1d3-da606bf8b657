package com.meituan.android.pay.debugkit.utils.network.retrofit.interceptor;

import android.net.Uri;
import android.text.TextUtils;

import com.meituan.android.pay.debugkit.utils.struct.Stream;
import com.meituan.android.pay.base.utils.scheme.QueryParser;
import com.sankuai.meituan.retrofit2.Interceptor;
import com.sankuai.meituan.retrofit2.Request;
import com.sankuai.meituan.retrofit2.raw.RawResponse;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class PayQueryAdditionInterceptor implements Interceptor {

    @Override
    public RawResponse intercept(Chain chain) throws IOException {
        Request request = chain.request();
        Uri uri = Uri.parse(request.url());

        Map<String, String> originQuery = QueryParser.origin(uri.getEncodedQuery());
        Map<String, String> additionalQuery = PayQueryAdditionParams.newInstance().toQuery();

        // 去除黑名单中的key
        Stream.on(PayQueryAdditionParams.keyBlackList(uri.getPath()))
                .foreach(additionalQuery::remove);

        // 构建新url
        Uri.Builder newUri = uri.buildUpon();
        Stream.on(additionalQuery).foreach(entry -> {
            String additionalKey = entry.getKey();
            String additionalVal = entry.getValue();
            String originValue = originQuery.get(additionalKey);

            if (TextUtils.isEmpty(originValue) && !TextUtils.isEmpty(additionalVal)) {
                newUri.appendQueryParameter(additionalKey, additionalVal);
            }
        });

        return chain.proceed(request.newBuilder()
                .url(newUri.toString())
                .build());
    }
}
