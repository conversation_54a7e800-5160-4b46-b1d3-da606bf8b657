package com.meituan.android.pay.debugkit.main.paylater.model;

import com.google.gson.annotations.SerializedName;
import com.sankuai.model.JsonBean;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@JsonBean
public class TradeInfoVO implements Serializable {

    @SerializedName("tradeOrderVO")
    private TradeOrderVO tradeOrderVO;

    @SerializedName("payOrderVOs")
    private List<PayOrderVO> payOrderVOs;

    public TradeOrderVO getTradeOrderVO() {
        return tradeOrderVO;
    }

    public void setTradeOrderVO(TradeOrderVO tradeOrderVO) {
        this.tradeOrderVO = tradeOrderVO;
    }

    public List<PayOrderVO> getPayOrderVOs() {
        return payOrderVOs != null ? payOrderVOs : Collections.emptyList();
    }

    public void setPayOrderVOs(List<PayOrderVO> payOrderVOs) {
        this.payOrderVOs = payOrderVOs;
    }

    public static boolean isLegal(TradeInfoVO tradeInfoVO) {
        return tradeInfoVO != null && tradeInfoVO.getTradeOrderVO() != null;
    }
}
