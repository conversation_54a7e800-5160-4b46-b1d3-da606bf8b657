package com.meituan.android.pay.debugkit.main.jump;

import android.view.LayoutInflater;

import com.meituan.android.pay.debugkit.databinding.PayDebugFragmentCommonLayoutBinding;
import com.meituan.android.pay.debugkit.capacity.Jumper;
import com.meituan.android.pay.debugkit.main.base.PayDemoFragment;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * <AUTHOR>
 */
@ServiceLoaderInterface(key = "jump", interfaceClass = PayDemoFragment.class)
public class JumpFragment extends PayDemoFragment implements Jumper {

    @Override
    public void onCreateView(LayoutInflater inflater, PayDebugFragmentCommonLayoutBinding binding) {
        super.onCreateView(inflater, binding);
        binding.title.setText("跳转");
    }

    @Override
    public String viewFileName() {
        return "view_jump.json";
    }
}
