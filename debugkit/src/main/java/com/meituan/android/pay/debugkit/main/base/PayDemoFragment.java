package com.meituan.android.pay.debugkit.main.base;

import android.arch.lifecycle.MutableLiveData;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.meituan.android.pay.base.container.PayBaseFragment;
import com.meituan.android.pay.base.utils.compute.BoolParser;
import com.meituan.android.pay.base.utils.function.Consumer;
import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.pay.debugkit.databinding.PayDebugFragmentCommonLayoutBinding;
import com.meituan.android.pay.debugkit.capacity.Jumper;
import com.meituan.android.pay.debugkit.utils.mvvm.LiveDataUtils;
import com.meituan.android.pay.debugkit.utils.rx.RxLifeSubscriber;
import com.meituan.android.pay.debugkit.utils.struct.SavedData;
import com.meituan.android.pay.debugkit.utils.struct.Stream;
import com.meituan.android.pay.debugkit.utils.system.AssetsUtils;
import com.meituan.android.pay.debugkit.widget.simple.SimpleSharedViewState;
import com.meituan.android.paybase.dialog.ProgressController;

import rx.Observable;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * <AUTHOR>
 * 由UI：PayDebugFragmentCommonLayoutBinding
 * 和VM：SimpleSharedViewState
 * 组合
 */
public class PayDemoFragment extends PayBaseFragment implements Jumper {
    protected final SimpleSharedViewState sharedViewState = new SimpleSharedViewState();

    protected PayDebugFragmentCommonLayoutBinding binding;

    private boolean reset;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = PayDebugFragmentCommonLayoutBinding.inflate(inflater);
        binding.title.setOnLongClickListener(v -> {
            reset = true;
            getActivity().finish();
            return true;
        });
        onCreateView(inflater, binding);
        getViewData().flatMap(viewData -> sharedViewState.getView(this, viewData))
                .subscribe(new RxLifeSubscriber<View>(this) {
                    @Override
                    public void onNext(View view) {
                        binding.container.removeAllViews();
                        binding.container.addView(view);
                        sharedViewState.bindInvoker(PayDemoFragment.this);
                        Stream.on(sharedViewState.boundLiveData())
                                .foreach(entry -> {
                                    MutableLiveData<String> sharedLiveData = entry.getValue();
                                    MutableLiveData<String> savedLiveData = SavedData.getLivedata(entry.getKey());
                                    LiveDataUtils.twoSideBind(PayDemoFragment.this, sharedLiveData, savedLiveData);
                                });
                    }
                });
        return binding.getRoot();
    }

    public void onCreateView(LayoutInflater inflater, PayDebugFragmentCommonLayoutBinding binding) {
    }

    public String viewFileName() {
        return "";
    }

    public Observable<String> getViewData() {
        return Observable.just(DebugManager.getStorage().getString(viewFileName(), ""))
                .flatMap(viewData -> {
                    if (!TextUtils.isEmpty(viewData)) {
                        return Observable.just(viewData);
                    } else {
                        return AssetsUtils.observable(viewFileName());
                    }
                }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
    }

    public Observable<String> saveViewData() {
        if (reset) {
            return AssetsUtils.observable(viewFileName())
                    .observeOn(Schedulers.io())
                    .doOnNext(viewData -> DebugManager.getStorage().setString(viewFileName(), viewData));
        } else {
            return sharedViewState.getViewData()
                    .observeOn(Schedulers.io())
                    .doOnNext(viewData -> DebugManager.getStorage().setString(viewFileName(), viewData));
        }
    }

    public Consumer<Boolean> isLoading() {
        return isLoading -> {
            if (BoolParser.bool(isLoading)) {
                ProgressController.of(getActivity()).show();
            } else {
                ProgressController.of(getActivity()).hide();
            }
        };
    }

}
