package com.meituan.android.pay.debugkit.widget.simple.state;

import android.arch.lifecycle.MutableLiveData;
import android.support.annotation.Keep;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import com.meituan.android.pay.base.container.PayBaseFragment;
import com.meituan.android.pay.base.utils.compute.BoolParser;
import com.meituan.android.pay.debugkit.databinding.PayDebugSimpleSelectionBinding;
import com.meituan.android.pay.debugkit.databinding.PayDebugSimpleSelectionDividerBinding;
import com.meituan.android.pay.debugkit.utils.mvvm.BindingActions;
import com.meituan.android.pay.debugkit.utils.mvvm.LiveDataUtils;
import com.meituan.android.pay.debugkit.widget.simple.base.SimpleViewState;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * <AUTHOR>
 */
@Keep
@ServiceLoaderInterface(key = "selection", interfaceClass = SimpleViewState.class)
public class SelectionViewState extends TitleViewState {
    private boolean divider;

    private String text = "";

    private final MutableLiveData<Boolean> selected = new MutableLiveData<>();

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public void onSelected(String selecting) {
        selected.setValue(TextUtils.equals(selecting, getVal()));
    }

    @Override
    public View inflate(PayBaseFragment fragment) {
        if (divider) {
            return inflateDivider(fragment);
        }
        PayDebugSimpleSelectionBinding binding = PayDebugSimpleSelectionBinding
                .inflate(fragment.getLayoutInflater(), (ViewGroup) fragment.getView(), false);

        binding.main.setOnClickListener(v -> {
            invokeSet();
        });
        binding.main.setOnLongClickListener(v -> invokeDelete());
        binding.title.setText(getTitle());
        binding.text.setText(getText());
        LiveDataUtils.observe(fragment, selected, isSelected ->
                BindingActions.setValueVisibility(binding.tailImg, isSelected));
        if (BoolParser.bool(selected.getValue())) {
            invokeSet();
        }
        return binding.getRoot();
    }

    public View inflateDivider(PayBaseFragment fragment) {
        PayDebugSimpleSelectionDividerBinding binding = PayDebugSimpleSelectionDividerBinding
                .inflate(fragment.getLayoutInflater(), (ViewGroup) fragment.getView(), false);
        binding.title.setText(getTitle());
        return binding.getRoot();
    }

    private void invokeSet() {
        invoke("set", getVal());
    }

    private boolean invokeDelete() {
        invoke("delete", getVal());
        return true;
    }
}
