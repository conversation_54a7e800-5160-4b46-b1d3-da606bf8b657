package com.meituan.android.pay.debugkit.utils.system;

import android.content.Context;
import android.os.Environment;
import android.text.TextUtils;

import com.meituan.android.pay.base.utils.exception.Catch;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

import rx.Observable;
import rx.functions.Action1;
import rx.functions.Func1;

/**
 * <AUTHOR>
 */
public class FileUtils {

    public static Func1<File, Boolean> isFileExist() {
        return file -> file != null && file.exists();
    }

    public static Func1<File, Boolean> isFileNotExist() {
        return file -> file != null && !file.exists();
    }

    public static Action1<File> createNewFile() {
        return file -> {
            try {
                if (file != null) {
                    file.createNewFile();
                }
            } catch (IOException e) {
                Catch.with(e).log();
            }
        };
    }

    public static Action1<File> deleteFile() {
        return file -> {
            if (file != null) {
                file.delete();
            }
        };
    }

    public static Func1<File, String> readFileString() {
        return file -> {
            if (file == null || !file.exists() || !file.isFile()) {
                return null;
            }
            StringBuilder content = new StringBuilder();
            try (FileInputStream fis = new FileInputStream(file);
                 InputStreamReader isr = new InputStreamReader(fis, StandardCharsets.UTF_8);
                 BufferedReader br = new BufferedReader(isr)) {
                char[] buffer = new char[1024];
                int length;
                while ((length = br.read(buffer)) != -1) {
                    content.append(buffer, 0, length);
                }
                return content.toString();
            } catch (IOException e) {
                return null;
            }
        };
    }

    public static Observable<File> openInternalFile(Context context, String fileName) {
        return Observable.create(subscriber -> {
            if (context != null && !TextUtils.isEmpty(fileName)) {
                File file = new File(context.getFilesDir(), fileName);
                subscriber.onNext(file);
            } else {
                subscriber.onNext(null);
            }
            subscriber.onCompleted();
        });
    }

    public static Observable<File> openExternalFile(Context context, String fileName) {
        return Observable.create(subscriber -> {
            if (context != null && !TextUtils.isEmpty(fileName)) {
                File externalFile = context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS);
                File file = new File(externalFile, fileName);
                subscriber.onNext(file);
            } else {
                subscriber.onNext(null);
            }
            subscriber.onCompleted();
        });

    }

    public static Observable<String> readFileString(File file) {
        return Observable.just(file).map(readFileString());
    }

}
