package com.meituan.android.pay.debugkit.widget.old.touchhelper;

import android.support.annotation.Nullable;
import android.support.v7.widget.RecyclerView;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;

import com.meituan.android.pay.debugkit.widget.old.adapter.PowerfulRecyclerAdapter;
import com.meituan.android.pay.debugkit.widget.old.listener.OnItemTouchListener;


/**
 * Created by ka<PERSON><PERSON><PERSON> on 16/3/9.
 * 封装item 点击，长按等事件
 */
public class ItemTouchListenerAdapter extends GestureDetector.SimpleOnGestureListener implements RecyclerView.OnItemTouchListener {

    private OnItemTouchListener listener;
    private RecyclerView recyclerView;
    private GestureDetector gestureDetector;

    public ItemTouchListenerAdapter(RecyclerView recyclerView, OnItemTouchListener listener) {
        if (recyclerView == null || listener == null) {
            throw new IllegalArgumentException("RecyclerView and Listener arguments can not be null");
        }
        this.recyclerView = recyclerView;
        this.listener = listener;
        this.gestureDetector = new GestureDetector(recyclerView.getContext(), this);
    }

    @Override
    public boolean onInterceptTouchEvent(RecyclerView recyclerView, MotionEvent motionEvent) {
        gestureDetector.onTouchEvent(motionEvent);
        if (motionEvent.getAction() == MotionEvent.ACTION_UP) {
            up(motionEvent);
        }
        return false;
    }

    @Override
    public void onTouchEvent(RecyclerView recyclerView, MotionEvent motionEvent) {
    }

    @Override
    public void onRequestDisallowInterceptTouchEvent(boolean disallowIntercept) {

    }

    @Override
    public boolean onDown(MotionEvent e) {
        View view = getChildViewUnder(e);
        if (view == null) return super.onDown(e);

        int position = recyclerView.getChildAdapterPosition(view);

        RecyclerView.Adapter mAdapter = recyclerView.getAdapter();

        if (mAdapter instanceof PowerfulRecyclerAdapter) {
            if ((position == (mAdapter.getItemCount() - 1)) && ((PowerfulRecyclerAdapter) mAdapter).hasFootView()) {
                return super.onDown(e);
            }
        }

        if (mAdapter instanceof PowerfulRecyclerAdapter) {
            position -= ((PowerfulRecyclerAdapter) mAdapter).getHeaderViewCount();
        }


        RecyclerView.ViewHolder holder = getViewHolderUnder(position);

        if (position < 0) {
            return super.onDown(e);
        }
        listener.onPress(holder, position);
        view.setPressed(true);
        return super.onDown(e);
    }


    @Override
    public boolean onSingleTapUp(MotionEvent e) {
        View view = getChildViewUnder(e);
        if (view == null) return false;

        view.setPressed(false);

        int position = recyclerView.getChildAdapterPosition(view);

        RecyclerView.ViewHolder holder = getViewHolderUnder(position);

        RecyclerView.Adapter mAdapter = recyclerView.getAdapter();

        if (mAdapter instanceof PowerfulRecyclerAdapter) {
            if ((position == (mAdapter.getItemCount() - 1)) && ((PowerfulRecyclerAdapter) mAdapter).hasFootView()) {
                return false;
            }
        }
        if (mAdapter instanceof PowerfulRecyclerAdapter) {
            position -= ((PowerfulRecyclerAdapter) mAdapter).getHeaderViewCount();
        }

        if (position < 0) {
            return false;
        }

        listener.onClick(holder, position);
        return true;
    }

    public void onLongPress(MotionEvent e) {
        View view = getChildViewUnder(e);
        if (view == null) return;

        int position = recyclerView.getChildAdapterPosition(view);

        RecyclerView.Adapter mAdapter = recyclerView.getAdapter();
        if (mAdapter instanceof PowerfulRecyclerAdapter) {
            if ((position == (mAdapter.getItemCount() - 1)) && ((PowerfulRecyclerAdapter) mAdapter).hasFootView()) {
                return;
            }
        }
        if (mAdapter instanceof PowerfulRecyclerAdapter) {
            position -= ((PowerfulRecyclerAdapter) mAdapter).getHeaderViewCount();
        }

        RecyclerView.ViewHolder holder = getViewHolderUnder(position);

        if (position < 0) {
            return;
        }

        listener.onLongClick(holder, position, e.getRawX(), e.getRawY());
        view.setPressed(false);
    }

    @Nullable
    private View getChildViewUnder(MotionEvent e) {
        return recyclerView.findChildViewUnder(e.getX(), e.getY());
    }

    private RecyclerView.ViewHolder getViewHolderUnder(int position) {
        return recyclerView.findViewHolderForAdapterPosition(position);
    }


    private void up(MotionEvent e) {
        View view = getChildViewUnder(e);
        if (view == null) return;

        int position = recyclerView.getChildAdapterPosition(view);

        RecyclerView.Adapter mAdapter = recyclerView.getAdapter();
        if (mAdapter instanceof PowerfulRecyclerAdapter) {
            if ((position == (mAdapter.getItemCount() - 1)) && ((PowerfulRecyclerAdapter) mAdapter).hasFootView()) {
                return;
            }
        }
        if (mAdapter instanceof PowerfulRecyclerAdapter) {
            position -= ((PowerfulRecyclerAdapter) mAdapter).getHeaderViewCount();
        }

        RecyclerView.ViewHolder holder = getViewHolderUnder(position);

        if (position < 0) {
            return;
        }
        listener.onUp(holder, position);
        view.setPressed(false);
    }
}

