package com.meituan.android.pay.debugkit.utils.serialize;

import android.arch.lifecycle.MutableLiveData;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 */
public class MutableLiveDataTypeAdapter implements JsonSerializer<MutableLiveData<?>>,
        JsonDeserializer<MutableLiveData<?>> {

    @Override
    public JsonElement serialize(MutableLiveData<?> src, Type typeOfSrc, JsonSerializationContext context) {
        return context.serialize(src.getValue());
    }

    @Override
    public MutableLiveData<?> deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        Object value = context.deserialize(json, ((ParameterizedType) typeOfT).getActualTypeArguments()[0]);
        MutableLiveData<Object> liveData = new MutableLiveData<>();
        liveData.setValue(value);
        return liveData;
    }
}
