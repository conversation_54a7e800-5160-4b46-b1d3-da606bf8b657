package com.meituan.android.pay.debugkit.utils.network.retrofit.interceptor;

import android.text.TextUtils;

import com.google.gson.JsonElement;
import com.meituan.android.pay.base.utils.exception.Catch;
import com.meituan.android.pay.base.utils.function.MapBuilder;
import com.meituan.android.pay.debugkit.utils.struct.Stream;
import com.meituan.android.pay.base.utils.serialize.GsonUtils;
import com.meituan.android.pay.base.utils.serialize.JsonFinder;
import com.meituan.android.pay.debugkit.utils.network.encrypt.PayEncrypt;
import com.meituan.android.pay.debugkit.utils.network.encrypt.PayTTEEncryptor;
import com.meituan.android.pay.debugkit.utils.network.encrypt.PayEncryptor;
import com.sankuai.meituan.retrofit2.Interceptor;
import com.sankuai.meituan.retrofit2.Request;
import com.sankuai.meituan.retrofit2.RequestBody;
import com.sankuai.meituan.retrofit2.ResponseBody;
import com.sankuai.meituan.retrofit2.raw.RawResponse;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class PayEncryptInterceptor implements Interceptor {
    private static final boolean useTTE = false;

    @Override
    public RawResponse intercept(Chain chain) throws IOException, RuntimeException {
        Request request = chain.request();
        // 不处理
        if (RequestUtils.isJsonBody(request)) {
            return chain.proceed(request);
        }

        // 数据预处理
        Map<String, String> formParams = RequestUtils.readForm(request.body());
        String encryptKey = formParams.get(PayEncrypt.KEY_ENCRYPT_PARAMS);
        List<String> encryptKeys = GsonUtils.toStringList(encryptKey);

        Map<String, String> encryptedParams = new HashMap<>();
        Map<String, String> remainingParams = new HashMap<>();
        Stream.on(formParams).foreach(entry -> {
            if (encryptKeys.contains(entry.getKey())) {
                encryptedParams.put(entry.getKey(), entry.getValue());
            } else {
                remainingParams.put(entry.getKey(), entry.getValue());
            }
        });

        // 加密数据
        PayEncrypt.Result rlt;
        try {
            if (useTTE) {
                rlt = PayTTEEncryptor.encryptFrom(encryptedParams);
            } else {
                rlt = PayEncryptor.encryptFrom(encryptedParams);
            }
        } catch (Exception e) {
            Catch.with(e).report("PayEncryptInterceptor_intercept");
            return chain.proceed(request);
        }

        RequestBody requestBody = RequestUtils.writeForm(MapBuilder.string()
                .addAll(remainingParams)
                .addAll(rlt.getParams())
                .build());
        RawResponse rawResponse = chain.proceed(request.newBuilder()
                .body(requestBody)
                .build());

        // 解密数据
        String decryptedRes = decryptResponse(rlt, rawResponse);
        return RequestUtils.writeBody(rawResponse, decryptedRes);
    }

    private String decryptResponse(PayEncrypt.Result result, RawResponse rawResponse) {
        String bodyString;
        try (ResponseBody responseBody = rawResponse.body()) {
            bodyString = responseBody.string();
        }

        JsonElement bodyJson = GsonUtils.toJsonElement(bodyString);
        JsonFinder data = JsonFinder.with(bodyJson).object(PayEncrypt.KEY_DATA);
        String encryptType = data.object(PayEncrypt.KEY_ENCRYPT_TYPE).getAsString();
        String encryptRes = data.object(PayEncrypt.KEY_ENCRYPT_RES).getAsString();

        if (TextUtils.equals(encryptType, PayEncrypt.ENCRYPT_TYPE_TTE)) {
            return PayTTEEncryptor.decrypt(encryptRes);
        } else if (!TextUtils.isEmpty(encryptRes)) {
            return PayEncryptor.decrypt(result.getAesKey(), encryptRes);
        } else {
            return bodyString;
        }
    }
}
