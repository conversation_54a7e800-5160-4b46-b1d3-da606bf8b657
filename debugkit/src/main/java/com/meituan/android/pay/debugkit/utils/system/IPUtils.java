package com.meituan.android.pay.debugkit.utils.system;

import android.text.TextUtils;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

/**
 * Created by ljj
 * Date:15/3/23
 * Time:下午4:29
 */
public class IPUtils {
    public static String getLocalIP() {
        Enumeration<NetworkInterface> ntlist = null;
        try {
            ntlist = NetworkInterface.getNetworkInterfaces();
        } catch (SocketException e) {
            e.printStackTrace();
        }
        if (ntlist != null) {
            while (ntlist.hasMoreElements()) {
                NetworkInterface nt = ntlist.nextElement();
                for (Enumeration<InetAddress> ips = nt.getInetAddresses(); ips.hasMoreElements(); ) {
                    InetAddress ip = ips.nextElement();
                    if (!ip.isLoopbackAddress()) {
                        if (ip.isSiteLocalAddress())
                            return ip.getHostAddress();
                    }
                }
            }
        }
        return null;
    }

    public static long IP2Int(String ip) {
        if (TextUtils.isEmpty(ip)) {
            return 0;
        }
        String[] parts = ip.split("\\.");
        long ipInt = 0;
        int total = 3;
        for (String part : parts) {
            try {
                ipInt = ipInt + (long) (Integer.parseInt(part) * Math.pow(256, total));
            } catch (Exception e) {

            }
            total--;

        }
        return ipInt;

    }
}
