package com.meituan.android.pay.debugkit.utils.ui;

import android.arch.lifecycle.LifecycleOwner;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;

import com.meituan.android.pay.base.utils.lifecycle.LifecycleObservation;
import com.meituan.android.pay.base.utils.observable.inf.OnDestroy;

/**
 * <AUTHOR>
 */
public class UIThreadHandler implements OnDestroy {
    private final Handler handler = new Handler(Looper.getMainLooper());

    private final Object TOKEN = new Object();

    private boolean disabled;

    public static UIThreadHandler get() {
        return new UIThreadHandler();
    }

    public static UIThreadHandler observe(LifecycleOwner lifecycleOwner) {
        UIThreadHandler handler = new UIThreadHandler();
        if (!LifecycleObservation.addObserver(lifecycleOwner, handler)) {
            handler.disabled = true;
        }
        return handler;
    }

    public void run(Runnable runnable) {
        if (!disabled && runnable != null) {
            if (Looper.myLooper() == Looper.getMainLooper()) {
                runnable.run();
            } else {
                handler.post(runnable);
            }
        }
    }

    public void post(Runnable runnable) {
        if (!disabled && runnable != null) {
            handler.post(runnable);
        }
    }

    public void postDelay(long delay, Runnable runnable) {
        if (!disabled && runnable != null) {
            Message message = Message.obtain(handler, runnable);
            message.obj = TOKEN;
            handler.sendMessageDelayed(message, delay);
        }
    }

    @Override
    public void onDestroy() {
        disabled = true;
        handler.removeCallbacksAndMessages(null);
    }
}
