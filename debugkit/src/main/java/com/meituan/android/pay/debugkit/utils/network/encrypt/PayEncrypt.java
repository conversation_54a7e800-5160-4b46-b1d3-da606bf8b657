package com.meituan.android.pay.debugkit.utils.network.encrypt;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface PayEncrypt {
    int ENCRYPT_SCENE_CASHIER = 1;
    String KEY_ENCRYPT_PARAMS = "encrypt_params";
    String KEY_ENCRYPT_KEY = "encrypt_key";
    String KEY_ENCRYPT_RES = "encrypt_res";
    String KEY_DATA = "data";

    String KEY_ENCRYPT_APP_DK = "encrypt_app_dk";
    String KEY_ENCRYPT_PARAMS_VALUE = "encrypt_params_value";

    String KEY_ENCRYPT_TYPE = "encrypt_type";
    String ENCRYPT_TYPE_AES = "2";
    String ENCRYPT_TYPE_TTE = "4";

    int ERROR_CODE_ENCRYPT = -103;
    String ERROR_MSG_ENCRYPT = "encrypt error: result illegal";

    int ERROR_CODE_ENCRYPT_RESULT = -104;
    String ERROR_MSG_ENCRYPT_RESULT = "encrypt error: result illegal";

    int ERROR_CODE_ENCRYPT_RESULT_CHECK = -105;
    String ERROR_MSG_ENCRYPT_RESULT_CHECK = "encrypt error: encrypt size before not equals after";

    int ERROR_CODE_DECRYPT_PARAMS = -201;
    String ERROR_MSG_DECRYPT_PARAMS = "decrypt error: params not illegal";

    int ERROR_CODE_DECRYPT_RESULT = -202;
    String ERROR_MSG_DECRYPT_RESULT = "decrypt error: result not illegal";

    int ERROR_CODE_DECRYPT = -203;
    String ERROR_MSG_DECRYPT = "decrypt error: exception";

    class Result {
        private final String aesKey;
        private final Map<String, String> params;

        public Result(String aesKey, Map<String, String> params) {
            this.aesKey = aesKey;
            this.params = params;
        }

        public String getAesKey() {
            return aesKey;
        }

        public Map<String, String> getParams() {
            return params;
        }
    }
}
