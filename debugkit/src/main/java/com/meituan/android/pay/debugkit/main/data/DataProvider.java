package com.meituan.android.pay.debugkit.main.data;

import com.meituan.android.pay.debugkit.main.data.local.OrderRepository;
import com.meituan.android.pay.debugkit.main.data.local.SettingRepository;

/**
 * <AUTHOR>
 */
public class DataProvider {
    private static final OrderRepository ORDER_REPOSITORY = new OrderRepository();

    private static final SettingRepository SETTING_REPOSITORY = new SettingRepository();

    public static OrderRepository order() {
        return ORDER_REPOSITORY;
    }

    public static SettingRepository setting() {
        return SETTING_REPOSITORY;
    }


}
