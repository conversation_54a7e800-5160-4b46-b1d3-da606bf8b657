package com.meituan.android.pay.debugkit.widget.old.views;

import android.content.Context;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.RelativeLayout;

import com.meituan.android.pay.debugkit.R;
import com.meituan.android.paybase.utils.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SimpleMultiButtonView extends RelativeLayout implements SimpleView<SimpleMultiButtonView.Bean> {

    public SimpleMultiButtonView(Context context) {
        super(context);
        init();
    }

    public SimpleMultiButtonView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public SimpleMultiButtonView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
    }

    public void refresh(Bean bean) {
        if (bean == null || CollectionUtils.isEmpty(bean.buttonBeanList)) {
            return;
        }
        for (ButtonBean buttonBean : bean.buttonBeanList) {
            addView(initSingleButton(buttonBean));
        }
    }

    private Button initSingleButton(ButtonBean buttonBean) {
        Button button = (Button) LayoutInflater.from(getContext()).inflate(R.layout.simple_view_single_button, null);
        button.setText(buttonBean.content);
        button.setOnClickListener(buttonBean.listener);
        return button;
    }

    @Override
    public boolean flip() {
        setVisibility(getVisibility() == VISIBLE ? GONE : VISIBLE);
        return true;
    }

    @Override
    public View getView() {
        return this;
    }

    public static class Holder implements SimpleViewHolder {
        private SimpleMultiButtonView view;

        private final Bean bean;

        public Holder(Bean bean) {
            this.bean = bean;
        }

        @Override
        public SimpleView<Bean> getView(Context context) {
            if (view != null) {
                return view;
            }
            this.view = new SimpleMultiButtonView(context);
            view.refresh(bean);
            return view;
        }

        @Override
        public String key() {
            return "";
        }

        @Override
        public String value() {
            return "";
        }
    }

    public static class Bean implements SimpleViewBean {
        private final List<ButtonBean> buttonBeanList;

        public Bean(ButtonBean... buttonBeans) {
            this.buttonBeanList = new ArrayList<>();
            if (buttonBeans != null && buttonBeans.length != 0) {
                buttonBeanList.addAll(Arrays.asList(buttonBeans));
            }
        }

        public Bean(List<ButtonBean> buttonBeanList) {
            this.buttonBeanList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(buttonBeanList)) {
                this.buttonBeanList.addAll(buttonBeanList);
            }
        }

        @Override
        public SimpleViewHolder holder() {
            return new Holder(this);
        }
    }

    public static class ButtonBean {
        private final String content;
        private final OnClickListener listener;

        public ButtonBean(String content, OnClickListener listener) {
            this.content = content;
            this.listener = listener;
        }
    }


}
