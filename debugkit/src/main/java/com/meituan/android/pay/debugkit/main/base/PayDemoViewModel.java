package com.meituan.android.pay.debugkit.main.base;

import android.arch.lifecycle.ViewModel;

import com.meituan.android.pay.base.utils.lifecycle.LifecycleObservable;

/**
 * <AUTHOR>
 */
public class PayDemoViewModel extends ViewModel implements LifecycleObservable {

    public PayDemoViewModel() {
        onLifecycleCreated();
        onLifecycleStarted();
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        onLifecycleDestroyed();
    }
}
