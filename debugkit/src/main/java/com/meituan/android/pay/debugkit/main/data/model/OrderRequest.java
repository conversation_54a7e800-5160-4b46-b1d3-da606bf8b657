package com.meituan.android.pay.debugkit.main.data.model;

import android.support.annotation.Keep;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Keep
public class OrderRequest implements Serializable {
    @SerializedName(value = "tradeNo", alternate = "tradeno")
    private String tradeNo;

    @SerializedName(value = "payToken", alternate = "pay_token")
    private String payToken;

    @SerializedName("cif")
    private String cif;

    @SerializedName(value = "transId", alternate = "trans_id")
    private String transId;

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getPayToken() {
        return payToken;
    }

    public void setPayToken(String payToken) {
        this.payToken = payToken;
    }

    public String getCif() {
        return cif;
    }

    public void setCif(String cif) {
        this.cif = cif;
    }

    public String getTransId() {
        return transId;
    }

    public void setTransId(String transId) {
        this.transId = transId;
    }
}
