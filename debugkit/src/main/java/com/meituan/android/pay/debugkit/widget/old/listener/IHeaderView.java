package com.meituan.android.pay.debugkit.widget.old.listener;

/**
 * 下拉刷新自定义view需要实现该接口
 */
public interface IHeaderView {

    /**
     * 下拉刷新
     */
    void pullToRefresh();

    /**
     * 松开刷新
     */
    void releaseToRefresh();

    /**
     * 开始刷新时调用一次
     */
    void onRefresh();

    /**
     * onReset和onPull在下拉和回弹的整个过程中都会调用
     */
    void onReset(float distance, float fraction);

    void onPull(float distance, float fraction);
}
