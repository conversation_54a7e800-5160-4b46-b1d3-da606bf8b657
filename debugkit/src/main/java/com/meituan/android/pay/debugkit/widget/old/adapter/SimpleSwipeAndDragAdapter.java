package com.meituan.android.pay.debugkit.widget.old.adapter;

import android.support.v7.widget.RecyclerView;
import android.view.ViewGroup;

import java.util.Collections;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/12/27.
 * 开启滑动删除和item拖拽可以让RecyclerView的Adapter继承该类，或者直接实现{@link ItemTouchAdapter}接口
 */

public class SimpleSwipeAndDragAdapter<T> extends RecyclerView.Adapter implements ItemTouchAdapter {

    protected List<T> datas;

    public SimpleSwipeAndDragAdapter(List<T> datas) {
        this.datas = datas;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return null;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {

    }

    @Override
    public int getItemCount() {
        return datas.size();
    }

    @Override
    public void onMove(int fromPosition, int toPosition) {
        if(fromPosition < 0 || toPosition >= datas.size()){
            return;
        }
        Collections.swap(datas, fromPosition, toPosition);
        notifyItemMoved(fromPosition, toPosition);
    }

    @Override
    public void onDismiss(int position) {
        if(position < 0 || position >= datas.size()){
            return;
        }
        datas.remove(position);
        notifyItemRemoved(position);
    }
}
