package com.meituan.android.pay.debugkit.utils.network.retrofit.interceptor;

import android.text.TextUtils;

import com.meituan.android.pay.debugkit.utils.struct.Stream;
import com.sankuai.meituan.retrofit2.Interceptor;
import com.sankuai.meituan.retrofit2.Request;
import com.sankuai.meituan.retrofit2.raw.RawResponse;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class PayFormAdditionInterceptor implements Interceptor {

    @Override
    public RawResponse intercept(Chain chain) throws IOException {
        Request request = chain.request();

        Map<String, String> originForm = RequestUtils.readForm(request.body());
        Map<String, String> additionalForm = PayFormAdditionParams.newInstance().toForm();

        // 去除黑名单中的key
        String path = RequestUtils.getRequestPath(request);
        Stream.on(PayFormAdditionParams.keyBlackList(path))
                .foreach(additionalForm::remove);

        Stream.on(additionalForm).foreach(entry -> {
            String additionalKey = entry.getKey();
            String additionalVal = entry.getValue();
            String originValue = originForm.get(additionalKey);

            if (TextUtils.isEmpty(originValue) && !TextUtils.isEmpty(additionalVal)) {
                originForm.put(additionalKey, additionalVal);
            }
        });

        return chain.proceed(request.newBuilder()
                .body(RequestUtils.writeForm(originForm))
                .build());
    }
}
