package com.meituan.android.pay.debugkit.main.paylater;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.support.v7.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paycommon.lib.retrofit.PayRetrofit;
import com.meituan.android.pay.debugkit.R;
import com.meituan.android.pay.debugkit.main.activity.PayDebugActivity;
import com.meituan.android.pay.debugkit.main.paylater.model.TradeOrderVO;
import com.meituan.android.pay.debugkit.widget.old.PowerfulRecyclerView;
import com.meituan.android.pay.debugkit.widget.old.adapter.SimpleSwipeAndDragAdapter;
import com.meituan.android.pay.debugkit.widget.old.listener.OnItemTouchListener;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 */
public class PayLaterOrderListActivity extends PayDebugActivity {
    private Controller controller;
    private SimpleSwipeAndDragAdapter<TradeOrderVO> adapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
//        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        super.onCreate(savedInstanceState);
        setTitle("后付订单列表");
        setContentView(R.layout.old_activity_paylater_order_list);
        init();
    }

    private void init() {
        PowerfulRecyclerView recyclerView = findViewById(R.id.pull_refresh_order_list);
        controller = new Controller(new OrderView() {
            @Override
            public void onHideLoading() {
                hideLoading();
                recyclerView.stopRefresh();
                recyclerView.stopLoadMore();
            }

            @Override
            public void onShowLoading() {
                showLoading();
            }

            @Override
            public void onException(Exception e) {
                ToastUtils.showSnackToast((Activity) recyclerView.getContext(), "数据异常：" + e.getMessage());
            }

            @Override
            public void onRefresh(int start) {
                adapter.notifyItemChanged(0);
            }
        });
        this.adapter = new SimpleSwipeAndDragAdapter<TradeOrderVO>(controller.data()) {
            @Override
            public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
                View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.old_pay_later_order_list_item, parent, false);
                return new RecyclerView.ViewHolder(view) {
                };
            }

            @Override
            public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
                TradeOrderVO orderInfo = controller.data().get(position);
                String title = orderInfo.getOrderSubject();
                ((TextView) holder.itemView.findViewById(R.id.title)).setText(title);
                String content = orderInfo.getTradeModelContent() + " " + orderInfo.getSellerId();
                ((TextView) holder.itemView.findViewById(R.id.seller_id)).setText(content);
                ((TextView) holder.itemView.findViewById(R.id.money)).setText(orderInfo.getTransFeeCentString());
                ((TextView) holder.itemView.findViewById(R.id.state)).setText(orderInfo.getTransStatusContent());
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA);
                ((TextView) holder.itemView.findViewById(R.id.date)).setText(format.format(orderInfo.getTransSuccessTime() * 1000L));
            }
        };
        recyclerView.setAdapter(adapter);
        recyclerView.setRefreshEnable(true);
        recyclerView.setLoadMoreEnable(false);
        recyclerView.setOnRefreshListener(controller::refresh);
        recyclerView.setOnItemTouchListener(new OnItemTouchListener() {

            @Override
            public void onClick(RecyclerView.ViewHolder holder, int position) {
                controller.onClick(PayLaterOrderListActivity.this, position);
            }

            @Override
            public boolean onLongClick(RecyclerView.ViewHolder holder, int position, float mRawX, float mRawY) {
                return false;
            }

            @Override
            public void onPress(RecyclerView.ViewHolder holder, int position) {

            }

            @Override
            public void onUp(RecyclerView.ViewHolder holder, int position) {

            }
        });
        controller.refresh();
    }

    public interface OrderView {
        void onHideLoading();

        void onShowLoading();

        void onException(Exception e);

        void onRefresh(int start);
    }

    public static class Controller implements IRequestCallback {
        private static final int REQ_PAY_LATER_ORDER_LIST = 0x1A;

        private final List<TradeOrderVO> payLaterOrderList;
        private final OrderView orderView;

        public Controller(OrderView orderView) {
            this.payLaterOrderList = new ArrayList<>();
            this.orderView = orderView;
        }

        private void request() {
            PayRetrofit.getInstance().create(PayLaterRequestService.class, this, REQ_PAY_LATER_ORDER_LIST)
                    .queryOrderList(PayProvider.app().getUserId());
        }

        public void refresh() {
            request();
        }

        public void onClick(Activity activity, int position) {
            if (activity != null) {
                Intent intent = new Intent(activity, PayLaterOrderDetailActivity.class);
                intent.putExtra("PayLaterOrderDetail", payLaterOrderList.get(position));
                activity.startActivity(intent);
            }
        }

        public List<TradeOrderVO> data() {
            return payLaterOrderList;
        }

        @Override
        public void onRequestSucc(int tag, Object obj) {
            payLaterOrderList.clear();
            //noinspection unchecked
            payLaterOrderList.addAll((List<TradeOrderVO>) obj);
            orderView.onRefresh(0);
        }

        @Override
        public void onRequestException(int tag, Exception e) {
            orderView.onException(e);
        }

        @Override
        public void onRequestFinal(int tag) {
            orderView.onHideLoading();
        }

        @Override
        public void onRequestStart(int tag) {
            orderView.onShowLoading();
        }
    }


}
