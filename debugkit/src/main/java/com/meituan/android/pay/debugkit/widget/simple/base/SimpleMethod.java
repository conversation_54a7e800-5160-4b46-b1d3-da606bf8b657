package com.meituan.android.pay.debugkit.widget.simple.base;

import android.support.annotation.Keep;

import com.meituan.android.pay.base.utils.function.ArrayUtils;
import com.meituan.android.pay.debugkit.utils.reflect.Reflection;
import com.meituan.android.pay.debugkit.utils.router.OpenUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Keep
public class SimpleMethod implements Serializable {

    private final String name;

    private final String[] args;

    public SimpleMethod(String name, String... args) {
        this.name = name;
        this.args = args;
    }

    public String getName() {
        return name;
    }

    public String[] getArgs() {
        return args;
    }

    public Object[] getArguments() {
        return args;
    }

    public String first() {
        return ArrayUtils.getFirst(args);
    }

    public void invoke(Object object) {
        if (!OpenUtils.open(getName())) {
            Reflection.invokeMethod(object, getName(), getArguments());
        }
    }

}
