package com.meituan.android.pay.debugkit.widget.old.views;

import android.content.Context;
import android.support.annotation.Nullable;
import android.support.v4.content.ContextCompat;
import android.util.AttributeSet;
import android.view.View;
import android.widget.CompoundButton;

import com.meituan.android.pay.debugkit.R;
import com.meituan.android.pay.debugkit.widget.old.CellView;

/**
 * <AUTHOR>
 */
public class SimpleCheckView extends CellView implements SimpleView<SimpleCheckView.Bean> {
    public SimpleCheckView(Context context) {
        super(context);
    }

    public SimpleCheckView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public SimpleCheckView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void refresh(Bean bean) {
        if (bean == null) {
            return;
        }
        setTitleColor(ContextCompat.getColor(getContext(), R.color.cashier__gray));
        setTitle(bean.title);
        setCheckBoxStatus(bean.checked);
        setUseCheckBox(true);
        setOnCheckedChangeListener(bean.listener);
    }

    @Override
    public boolean flip() {
        setVisibility(getVisibility() == VISIBLE ? GONE : VISIBLE);
        return true;
    }

    @Override
    public View getView() {
        return this;
    }

    public static class Holder implements SimpleViewHolder {
        private SimpleCheckView view;

        private final Bean bean;

        public Holder(Bean bean) {
            this.bean = bean;
        }

        @Override
        public SimpleView<Bean> getView(Context context) {
            if (view != null) {
                return view;
            }
            this.view = new SimpleCheckView(context);
            view.refresh(bean);
            return view;
        }

        @Override
        public String key() {
            return bean.key;
        }

        @Override
        public String value() {
            return String.valueOf(bean.checked);
        }
    }

    public static class Bean implements SimpleViewBean {
        private final String key;

        private String title;

        private boolean checked;

        private CompoundButton.OnCheckedChangeListener listener;

        public Bean(String key, String title, boolean checked, CompoundButton.OnCheckedChangeListener listener) {
            this.key = key;
            this.title = title;
            this.checked = checked;
            this.listener = listener;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public boolean isChecked() {
            return checked;
        }

        public void setChecked(boolean checked) {
            this.checked = checked;
        }

        public CompoundButton.OnCheckedChangeListener getListener() {
            return listener;
        }

        public void setListener(CompoundButton.OnCheckedChangeListener listener) {
            this.listener = listener;
        }

        @Override
        public SimpleViewHolder holder() {
            return new Holder(this);
        }
    }
}
