package com.meituan.android.pay.debugkit.widget.simple.state;

import android.arch.lifecycle.MutableLiveData;
import android.support.annotation.Keep;
import android.view.View;
import android.view.ViewGroup;

import com.meituan.android.pay.base.container.PayBaseFragment;
import com.meituan.android.pay.debugkit.databinding.PayDebugSimpleTextBinding;
import com.meituan.android.pay.debugkit.utils.mvvm.LiveDataUtils;
import com.meituan.android.pay.debugkit.widget.simple.base.SimpleViewState;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * <AUTHOR>
 */
@Keep
@ServiceLoaderInterface(key = "text", interfaceClass = SimpleViewState.class)
public class TextViewState extends TitleViewState {

    private final MutableLiveData<String> text = new MutableLiveData<>();

    public MutableLiveData<String> getText() {
        return text;
    }

    @Override
    public View inflate(PayBaseFragment fragment) {
        LiveDataUtils.twoSideBind(fragment, text, value);

        PayDebugSimpleTextBinding binding = PayDebugSimpleTextBinding.inflate(fragment.getLayoutInflater(),
                (ViewGroup) fragment.getView(), false);
        binding.title.setText(getTitle());
        LiveDataUtils.observe(fragment, getText(), binding.edit::setText);
        return binding.main;
    }
}
