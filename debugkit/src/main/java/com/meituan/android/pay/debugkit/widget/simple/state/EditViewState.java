package com.meituan.android.pay.debugkit.widget.simple.state;

import android.arch.lifecycle.MutableLiveData;
import android.support.annotation.Keep;
import android.text.InputType;
import android.view.View;
import android.view.ViewGroup;

import com.meituan.android.pay.base.container.PayBaseFragment;
import com.meituan.android.pay.base.utils.lifecycle.LifecycleObservable;
import com.meituan.android.pay.debugkit.databinding.PayDebugSimpleEditBinding;
import com.meituan.android.pay.debugkit.utils.mvvm.BindingActions;
import com.meituan.android.pay.debugkit.utils.mvvm.LiveDataUtils;
import com.meituan.android.pay.debugkit.widget.simple.base.SimpleViewState;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * <AUTHOR>
 */
@Keep
@ServiceLoaderInterface(key = "edit", interfaceClass = SimpleViewState.class)
public class EditViewState extends TitleViewState implements LifecycleObservable {
    private final MutableLiveData<String> text = new MutableLiveData<>();

    private String hint;

    private String inputType;

    private boolean right;

    private boolean lock;

    private MutableLiveData<Boolean> unlockStatus = new MutableLiveData<>();

    public String getHint() {
        return hint;
    }

    public int getInputType() {
        if ("number".equals(inputType)) {
            return InputType.TYPE_CLASS_NUMBER;
        } else if ("password".equals(inputType)) {
            return InputType.TYPE_TEXT_VARIATION_PASSWORD;
        }
        return InputType.TYPE_CLASS_TEXT;
    }

    @Override
    public View inflate(PayBaseFragment fragment) {
        bindLifecycle(fragment);
        LiveDataUtils.twoSideBind(this, text, value);
        PayDebugSimpleEditBinding binding = PayDebugSimpleEditBinding.inflate(
                fragment.getLayoutInflater(), (ViewGroup) fragment.getView(), false);

        binding.title.setText(getTitle());
        binding.edit.setHint(getHint());
        binding.edit.setInputType(getInputType());
        binding.edit.setOrder(right);
        binding.edit.bind(fragment, text);

        BindingActions.setValueVisibility(binding.checkbox, lock);
        binding.checkbox.setOnCheckedChangeListener((buttonView, isChecked) ->
                unlockStatus.setValue(!isChecked));
        LiveDataUtils.observe(fragment, unlockStatus, unlock ->
                binding.checkbox.setChecked(Boolean.TRUE.equals(!unlock)));
        LiveDataUtils.observe(fragment, unlockStatus, unlock -> {
            if (unlock) {
                LiveDataUtils.remove(this, text);
                LiveDataUtils.remove(this, value);
                setValue("");
            } else {
                LiveDataUtils.remove(this, text);
                LiveDataUtils.bind(fragment, text, value);
            }
        });
        return binding.getRoot();
    }
}
