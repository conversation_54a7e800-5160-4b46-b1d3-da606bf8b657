package com.meituan.android.pay.debugkit.utils.struct;

import android.support.annotation.NonNull;

import com.meituan.android.pay.base.utils.function.ArrayUtils;
import com.meituan.android.pay.base.utils.function.Consumer;
import com.meituan.android.pay.base.utils.function.Function;
import com.meituan.android.pay.base.utils.function.Predicate;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 非线程安全，一次性遍历行为
 */
public class Stream {

    public static <K, V> MapStream<K, V> on(Map<K, V> map) {
        if (map == null) {
            map = new HashMap<>();
        }
        return new MapStream<>(map);
    }

    @SafeVarargs
    public static <T> ListStream<T> on(T... array) {
        return Stream.on(ArrayUtils.asList(array));
    }

    public static <T> ListStream<T> on(Collection<T> collection) {
        if (collection == null) {
            collection = new ArrayList<>();
        }
        return new ListStream<>(collection);
    }

    @SafeVarargs
    public static <T> ListStream<Item<T>> index(T... array) {
        return Stream.index(ArrayUtils.asList(array));
    }

    public static <T> ListStream<Item<T>> index(Collection<T> collection) {
        return Stream.on(collection).index();
    }

    public static final class ListStream<T> {
        private final List<T> root;

        private ListStream(@NonNull Collection<T> collection) {
            this.root = new ArrayList<>(collection);
        }

        public ListStream<T> filter(@NonNull Predicate<T> filter) {
            return new ListStream<>(itr(filter));
        }

        public ListStream<T> foreach(@NonNull Consumer<T> consumer) {
            return new ListStream<>(itr(consumer.castPredicate()));
        }

        public ListStream<Item<T>> index() {
            return map(new Function<T, Item<T>>() {
                private int index;
                @Override
                public Item<T> apply(T t) {
                    return new Item<>(index++, t);
                }
            });
        }

        // 转换所有类型T
        public <R> ListStream<R> map(@NonNull Function<T, R> mapper) {
            List<R> resultList = new ArrayList<>();
            for (T t : root) {
                R result = mapper.apply(t);
                resultList.add(result);
            }
            return new ListStream<>(resultList);
        }

        // 转换部分类型T，筛选转换结果，当结果R不为null时，停止转换
        public <R> ListStream<R> mapForResult(@NonNull Function<T, R> mapper) {
            List<R> resultList = new ArrayList<>();
            for (T t : root) {
                R result = mapper.apply(t);
                resultList.add(result);
                if (result != null) {
                    break;
                }
            }
            return new ListStream<>(resultList);
        }

        private List<T> itr(@NonNull Predicate<T> filter) {
            List<T> resultList = new ArrayList<>();
            for (T t : root) {
                if (filter.test(t)) {
                    resultList.add(t);
                }
            }
            return resultList;
        }

        public List<T> collect() {
            return root;
        }

        public List<T> collectTo(List<T> list) {
            if (list != null) {
                list.addAll(root);
            }
            return list;
        }

        public <C extends Collection<T>> C collectTo(C collection) {
            if (collection != null) {
                collection.addAll(root);
            }
            return collection;
        }

        public T first() {
            return ArrayUtils.get(root, 0);
        }
    }

    public static final class MapStream<K, V> {
        private final ListStream<Entry<K, V>> listStream;

        private MapStream(@NonNull Map<K, V> map) {
            listStream = new ListStream<>(map.entrySet()).map(Entry::new);
        }

        private MapStream(@NonNull ListStream<Entry<K, V>> listStream) {
            this.listStream = listStream;
        }

        public MapStream<K, V> filter(Predicate<Entry<K, V>> filter) {
            return new MapStream<>(listStream.filter(filter));
        }

        public MapStream<K, V> foreach(Consumer<Entry<K, V>> consumer) {
            return new MapStream<>(listStream.foreach(consumer));
        }

        public MapStream<K, V> map(Function<Entry<K, V>, Entry<K, V>> mapper) {
            return new MapStream<>(listStream.map(mapper));
        }

        public Map<K, V> collect() {
            return collectTo(new LinkedHashMap<>());
        }

        public Map<K, V> collectTo(Map<K, V> map) {
            if (map != null && listStream.root != null) {
                for (Map.Entry<K, V> entry : listStream.root) {
                    map.put(entry.getKey(), entry.getValue());
                }
            }
            return map;
        }
    }

    public static final class Item<T> {
        public final int index;
        public final T value;

        private Item(int index, T value) {
            this.index = index;
            this.value = value;
        }
    }

    public static final class Entry<K, V> implements Map.Entry<K, V> {
        private final Map.Entry<K, V> delegate;

        public Entry(K k, V v) {
            this.delegate = new AbstractMap.SimpleEntry<>(k, v);
        }

        public Entry(Map.Entry<K, V> delegate) {
            this.delegate = delegate;
        }

        @Override
        public K getKey() {
            return delegate != null ? delegate.getKey() : null;
        }

        @Override
        public V getValue() {
            return delegate != null ? delegate.getValue() : null;
        }

        @Override
        public V setValue(V value) {
            return delegate != null ? delegate.setValue(value) : null;
        }

        public boolean isKVNonNull() {
            return getKey() != null && getValue() != null;
        }

        public Map.Entry<K, V> origin() {
            return delegate;
        }

    }
}
