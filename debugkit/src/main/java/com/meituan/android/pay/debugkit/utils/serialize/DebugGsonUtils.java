package com.meituan.android.pay.debugkit.utils.serialize;

import com.google.gson.JsonElement;
import com.google.gson.JsonSyntaxException;
import com.meituan.android.pay.base.utils.serialize.GsonUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DebugGsonUtils {

    public static String toString(Object object) {
        if (object == null) {
            return "";
        } else if (object instanceof String) {
            return (String) object;
        } else if (object instanceof JsonElement) {
            JsonElement jsonElement = (JsonElement) object;
            if (jsonElement.isJsonPrimitive()) {
                return jsonElement.getAsString();
            }
        } else if (object instanceof Serializable) {
            return DebugGsonProvider.get().toJson(object);
        }
        return String.valueOf(object);
    }

    /** @noinspection unchecked*/
    public static <T> T fromJson(String json, Class<T> clz) throws JsonSyntaxException {
        if (clz == null) {
            return null;
        } else if (clz.isAssignableFrom(String.class)) {
            return (T) json;
        } else if (clz.isAssignableFrom(Boolean.class)) {
            return (T) Boolean.valueOf(json);
        } else {
            return (T) DebugGsonProvider.get().fromJson(json, clz);
        }
    }

    public static <T> T fromJsonSafety(String json, Class<T> clz) {
        try {
            return fromJson(json, clz);
        } catch (Exception e) {
            return null;
        }
    }

    public static <T> List<T> fromJsonToList(String json, Class<T> clz) throws JsonSyntaxException {
        List<T> list = new ArrayList<>();
        if (json != null && clz != null) {
            List<JsonElement> jsonElementList = GsonUtils.toJsonElementList(json);
            for (JsonElement jsonElement : jsonElementList) {
                list.add(DebugGsonProvider.get().fromJson(jsonElement, clz));
            }
        }
        return list;
    }

}
