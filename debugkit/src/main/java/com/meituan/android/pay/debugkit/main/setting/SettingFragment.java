package com.meituan.android.pay.debugkit.main.setting;

import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;

import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.pay.debugkit.R;
import com.meituan.android.pay.debugkit.databinding.PayDebugFragmentCommonLayoutBinding;
import com.meituan.android.pay.debugkit.databinding.PayDebugPassportLayoutBinding;
import com.meituan.android.pay.debugkit.capacity.DebugPassport;
import com.meituan.android.pay.debugkit.main.base.PayDemoFragment;
import com.meituan.android.pay.debugkit.utils.mvvm.BindingActions;
import com.meituan.android.pay.debugkit.utils.mvvm.LiveDataUtils;
import com.meituan.android.pay.debugkit.utils.mvvm.ParameterViewModelProvider;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * <AUTHOR>
 */
@ServiceLoaderInterface(key = "main", interfaceClass = Fragment.class)
public class SettingFragment extends PayDemoFragment {
    private PayDebugPassportLayoutBinding passportBinding;

    @Override
    public void onCreateView(LayoutInflater inflater, PayDebugFragmentCommonLayoutBinding binding) {
        passportBinding = PayDebugPassportLayoutBinding.inflate(inflater, binding.header, false);
        binding.header.addView(passportBinding.passport);
        binding.title.setText("设置");
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        SettingViewModel viewModel = ParameterViewModelProvider.of(this, SettingViewModel.class).init();
        LiveDataUtils.observe(this, viewModel.passportState(), state -> {
            PayProvider.module()
                    .getImageLoader()
                    .load(state.getImage())
                    .error(R.drawable.pay_debug_user_icon_default)
                    .into(passportBinding.image);
            passportBinding.name.setText(state.getName());
            passportBinding.id.setText(state.getId());
            BindingActions.setValueVisibility(passportBinding.id, state.getId());
            passportBinding.login.setText(state.getButton());
            passportBinding.login.setOnClickListener(v -> DebugPassport.flip());
        });
        DebugManager.info().getOfflineStatus().subscribe(offline ->
                sharedViewState.liveDataOf("network_setting").setValue(offline ? "线下" : "线上"));
    }

    @Override
    public String viewFileName() {
        return "view_setting.json";
    }
}
