package com.meituan.android.pay.debugkit.main.setting.second;

import android.support.v4.app.Fragment;
import android.view.LayoutInflater;

import com.meituan.android.pay.debugkit.databinding.PayDebugFragmentCommonLayoutBinding;
import com.meituan.android.pay.debugkit.main.base.PayDemoFragment;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * <AUTHOR>
 */
@ServiceLoaderInterface(key = "network", interfaceClass = Fragment.class)
public class NetworkSettingFragment extends PayDemoFragment {

    @Override
    public void onCreateView(LayoutInflater inflater, PayDebugFragmentCommonLayoutBinding binding) {
        super.onCreateView(inflater, binding);
        binding.title.setText("网络设置");
    }

    @Override
    public void onDestroy() {
        saveViewData().subscribe();
        super.onDestroy();
    }

    @Override
    public String viewFileName() {
        return "view_setting_network.json";
    }
}
