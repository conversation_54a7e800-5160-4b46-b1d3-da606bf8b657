package com.meituan.android.pay.debugkit.env;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.google.zxing.client.android.BaseCaptureActivity;
import com.google.zxing.client.android.Result;
import com.meituan.android.pay.base.compat.ActivityCompat;
import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.pay.debugkit.env.manager.EnvironmentSwitchManager;
import com.meituan.android.pay.debugkit.env.manager.HornEnvironmentManager;
import com.meituan.android.pay.debugkit.env.manager.LaneEnvironmentManager;
import com.meituan.android.pay.debugkit.env.manager.StandardCashierManager;
import com.meituan.android.paybase.common.dialog.PayDialog;
import com.meituan.android.paybase.utils.UriUtils;
/**
 * 环境切换优化工具
 * 对应iOS的PPSwitchEnvOptimize类
 *
 * 功能：
 * 1. 二维码扫描识别环境配置
 * 2. 泳道环境切换
 * 3. 标准收银台切换
 * 4. Hybrid收银台版本切换
 * 5. Horn环境切换
 * 6. Mock环境切换
 *
 * <AUTHOR>
 * @since 2025/1/9
 */
public class SwitchEnvOptimizeActivity extends BaseCaptureActivity {
    
    private static final String TAG = "SwitchEnvOptimize";
    
    // 支付QA对应环境Id (对应iOS的GlobalEnvironmentId)
    private static final int GLOBAL_ENVIRONMENT_ID = 2270;
    
    // 原始Hybrid收银台版本 (对应iOS的OriginalHybridCashierVersion)
    private static final String ORIGINAL_HYBRID_CASHIER_VERSION = "8.8.0";
    
    // 支持的URL Scheme和Host
    private static final String SUPPORTED_SCHEME = "paydemo";
    private static final String SUPPORTED_HOST = "www.environment.com";
    
    // 环境管理器
    private EnvironmentSwitchManager environmentSwitchManager;
    private LaneEnvironmentManager laneEnvironmentManager;
    private StandardCashierManager standardCashierManager;
    private HornEnvironmentManager hornEnvironmentManager;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 隐藏ActionBar
        ActivityCompat.UI.hideActionBar(this);
        
        // 初始化环境管理器
        initEnvironmentManagers();
        
        // 设置标题
        setTitle("环境切换扫码");
    }

    /**
     * 初始化环境管理器
     */
    private void initEnvironmentManagers() {
        environmentSwitchManager = new EnvironmentSwitchManager(this);
        laneEnvironmentManager = new LaneEnvironmentManager(this, GLOBAL_ENVIRONMENT_ID);
        standardCashierManager = new StandardCashierManager(this);
        hornEnvironmentManager = new HornEnvironmentManager(this);
    }

    @Override
    protected void handleDecodeResult(Result rawResult) {
        String resultUrl = rawResult.getText();
        
        if (TextUtils.isEmpty(resultUrl)) {
            showErrorDialog("识别错误", "二维码内容为空");
            return;
        }
        
        try {
            Uri uri = Uri.parse(resultUrl);
            String scheme = uri.getScheme();
            String host = uri.getHost();
            
            // 检查是否为支持的环境切换URL
            if (SUPPORTED_SCHEME.equalsIgnoreCase(scheme) && 
                SUPPORTED_HOST.equalsIgnoreCase(host)) {
                switchEnvironmentWithUrl(uri);
            } else {
                // 不是环境切换URL，按普通URL处理
                UriUtils.open(this, resultUrl);
                finish();
            }
        } catch (Exception e) {
            showErrorDialog("URL解析错误", "无法解析二维码内容: " + e.getMessage());
        }
    }

    /**
     * 根据URL切换环境
     * 对应iOS的switchEnvironmentWithUrl方法
     */
    private void switchEnvironmentWithUrl(Uri resultURL) {
        try {
            // 解析URL参数
            String lanePath = resultURL.getQueryParameter("lanePath");
            String hybridCashierVersion = resultURL.getQueryParameter("hybridCashierVersion");
            String hornEnv = resultURL.getQueryParameter("hornEnv");
            
            // 泳道环境切换
            if (!TextUtils.isEmpty(lanePath)) {
                laneEnvironmentManager.switchLaneEnv(lanePath);
            }
            
            // 标准收银台切换
            standardCashierManager.switchStandardCashier();
            
            // Hybrid收银台版本切换
            if (!TextUtils.isEmpty(hybridCashierVersion)) {
                standardCashierManager.switchHybridCashierVersion(
                    hybridCashierVersion, ORIGINAL_HYBRID_CASHIER_VERSION);
            }
            
            // Horn环境切换
            if (!TextUtils.isEmpty(hornEnv)) {
                hornEnvironmentManager.switchHornEnv(hornEnv);
            }
            
            // SharkDebug环境切换为Mock环境
            environmentSwitchManager.switchSharkDebugEnv();
            
            // 显示成功提示
            showSuccessDialog();
            
        } catch (Exception e) {
            showErrorDialog("环境切换失败", "切换过程中发生错误: " + e.getMessage());
        }
    }

    /**
     * 显示成功对话框
     */
    private void showSuccessDialog() {
        new PayDialog.Builder(this)
                .title("提示")
                .msg("环境切换成功")
                .leftBtn("我知道了", dialog -> {
                    dialog.dismiss();
                    finish();
                })
                .build()
                .show();
    }

    /**
     * 显示错误对话框
     */
    private void showErrorDialog(String title, String message) {
        new PayDialog.Builder(this)
                .title(title)
                .msg(message)
                .leftBtn("重新扫描", dialog -> {
                    dialog.dismiss();
                    restartPreviewAfterDelay(500);
                })
                .rightBtn("取消", dialog -> {
                    dialog.dismiss();
                    finish();
                })
                .build()
                .show();
    }

    /**
     * 获取全局环境ID
     */
    public static int getGlobalEnvironmentId() {
        return GLOBAL_ENVIRONMENT_ID;
    }

    /**
     * 获取原始Hybrid收银台版本
     */
    public static String getOriginalHybridCashierVersion() {
        return ORIGINAL_HYBRID_CASHIER_VERSION;
    }
}
