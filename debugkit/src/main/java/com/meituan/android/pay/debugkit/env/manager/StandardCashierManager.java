package com.meituan.android.pay.debugkit.env.manager;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.meituan.android.pay.base.utils.exception.Catch;
import com.meituan.android.pay.debugkit.DebugManager;

/**
 * 标准收银台管理器
 * 对应iOS中的标准收银台和Hybrid收银台版本切换功能
 * 
 * <AUTHOR>
 * @since 2025/1/9
 */
public class StandardCashierManager {
    
    private static final String TAG = "StandardCashierManager";
    
    // SharedPreferences相关常量
    private static final String PREF_NAME = "standard_cashier_config";
    
    // Neo Debug相关Key (对应iOS的NSUserDefaults key)
    private static final String NEO_DEBUG_ENTRY_MOCK_SWITCH = "NeoDebug_EntryMockSwitch";
    private static final String NEO_DEBUG_HYBRID_MOCK_SWITCH = "NeoDebug_HybridMockSwitch";
    
    // Hybrid收银台配置Key
    private static final String HYBRID_CASHIER_CONFIG_KEY = "hybrid_cashier_config";
    private static final String HYBRID_CASHIER_PATH_KEY = "hybrid_cashier_path";
    
    private final Context context;
    private final SharedPreferences preferences;
    private final Gson gson;

    public StandardCashierManager(Context context) {
        this.context = context;
        this.preferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        this.gson = new Gson();
    }

    /**
     * 切换标准收银台
     * 对应iOS的switchStandardCashier方法
     */
    public void switchStandardCashier() {
        try {
            // 开启路由 mock 总开关
            enableEntryMockSwitch();
            
            // 开启Hybrid 收银台 mock 开关
            enableHybridMockSwitch();
            
            // 保存配置状态
            saveStandardCashierConfig();
            
        } catch (Exception e) {
            Catch.with(e).report(TAG);
        }
    }

    /**
     * 切换Hybrid收银台版本
     * 对应iOS的switchHybridCashierVersion方法
     */
    public void switchHybridCashierVersion(String newVersion, String originalVersion) {
        try {
            if (TextUtils.isEmpty(newVersion) || TextUtils.isEmpty(originalVersion)) {
                return;
            }
            
            // 获取当前Horn配置
            String currentHornConfig = getHybridCashierHornConfig();
            
            if (TextUtils.isEmpty(currentHornConfig)) {
                // 如果没有配置，创建默认配置
                currentHornConfig = createDefaultHornConfig();
            }
            
            // 更新版本号
            String updatedConfig = updateHybridCashierPath(currentHornConfig, originalVersion, newVersion);
            
            // 保存更新后的配置
            saveHybridCashierConfig(updatedConfig);
            
        } catch (Exception e) {
            Catch.with(e).report(TAG);
        }
    }

    /**
     * 启用路由Mock开关
     */
    private void enableEntryMockSwitch() {
        preferences.edit()
                .putBoolean(NEO_DEBUG_ENTRY_MOCK_SWITCH, true)
                .apply();
                
        DebugManager.getStorage().setBoolean(NEO_DEBUG_ENTRY_MOCK_SWITCH, true);
    }

    /**
     * 启用Hybrid Mock开关
     */
    private void enableHybridMockSwitch() {
        preferences.edit()
                .putBoolean(NEO_DEBUG_HYBRID_MOCK_SWITCH, true)
                .apply();
                
        DebugManager.getStorage().setBoolean(NEO_DEBUG_HYBRID_MOCK_SWITCH, true);
    }

    /**
     * 保存标准收银台配置
     */
    private void saveStandardCashierConfig() {
        preferences.edit()
                .putBoolean("standard_cashier_enabled", true)
                .putLong("standard_cashier_switch_time", System.currentTimeMillis())
                .apply();
    }

    /**
     * 获取Hybrid收银台Horn配置
     */
    private String getHybridCashierHornConfig() {
        // 首先尝试从DebugManager获取
        String config = DebugManager.getStorage().getString(HYBRID_CASHIER_CONFIG_KEY, "");
        
        if (TextUtils.isEmpty(config)) {
            // 从SharedPreferences获取
            config = preferences.getString(HYBRID_CASHIER_CONFIG_KEY, "");
        }
        
        return config;
    }

    /**
     * 创建默认Horn配置
     */
    private String createDefaultHornConfig() {
        JsonObject defaultConfig = new JsonObject();
        defaultConfig.addProperty(HYBRID_CASHIER_PATH_KEY, 
            "http://stable.pay.test.sankuai.com/hybrid-cashier/8.8.0/index.html");
        defaultConfig.addProperty("version", "8.8.0");
        defaultConfig.addProperty("enabled", true);
        
        return gson.toJson(defaultConfig);
    }

    /**
     * 更新Hybrid收银台路径中的版本号
     */
    private String updateHybridCashierPath(String hornConfig, String originalVersion, String newVersion) {
        try {
            JsonParser parser = new JsonParser();
            JsonObject configObject = parser.parse(hornConfig).getAsJsonObject();
            
            if (configObject.has(HYBRID_CASHIER_PATH_KEY)) {
                String currentPath = configObject.get(HYBRID_CASHIER_PATH_KEY).getAsString();
                String updatedPath = currentPath.replace(originalVersion, newVersion);
                configObject.addProperty(HYBRID_CASHIER_PATH_KEY, updatedPath);
                configObject.addProperty("version", newVersion);
                configObject.addProperty("update_time", System.currentTimeMillis());
            }
            
            return gson.toJson(configObject);
            
        } catch (Exception e) {
            Catch.with(e).report(TAG);
            return hornConfig;
        }
    }

    /**
     * 保存Hybrid收银台配置
     */
    private void saveHybridCashierConfig(String config) {
        preferences.edit()
                .putString(HYBRID_CASHIER_CONFIG_KEY, config)
                .apply();
                
        DebugManager.getStorage().setString(HYBRID_CASHIER_CONFIG_KEY, config);
    }

    /**
     * 检查路由Mock开关是否启用
     */
    public boolean isEntryMockSwitchEnabled() {
        return preferences.getBoolean(NEO_DEBUG_ENTRY_MOCK_SWITCH, false);
    }

    /**
     * 检查Hybrid Mock开关是否启用
     */
    public boolean isHybridMockSwitchEnabled() {
        return preferences.getBoolean(NEO_DEBUG_HYBRID_MOCK_SWITCH, false);
    }

    /**
     * 获取当前Hybrid收银台版本
     */
    public String getCurrentHybridCashierVersion() {
        try {
            String config = getHybridCashierHornConfig();
            if (!TextUtils.isEmpty(config)) {
                JsonParser parser = new JsonParser();
                JsonObject configObject = parser.parse(config).getAsJsonObject();
                if (configObject.has("version")) {
                    return configObject.get("version").getAsString();
                }
            }
        } catch (Exception e) {
            Catch.with(e).report(TAG);
        }
        return "8.8.0"; // 默认版本
    }

    /**
     * 重置标准收银台配置
     */
    public void resetStandardCashierConfig() {
        preferences.edit().clear().apply();
        DebugManager.getStorage().remove(NEO_DEBUG_ENTRY_MOCK_SWITCH);
        DebugManager.getStorage().remove(NEO_DEBUG_HYBRID_MOCK_SWITCH);
        DebugManager.getStorage().remove(HYBRID_CASHIER_CONFIG_KEY);
    }
}
