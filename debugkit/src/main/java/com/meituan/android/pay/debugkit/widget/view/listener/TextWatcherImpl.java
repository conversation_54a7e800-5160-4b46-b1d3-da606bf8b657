package com.meituan.android.pay.debugkit.widget.view.listener;

import android.text.Editable;
import android.text.TextWatcher;

/**
 * <AUTHOR>
 */
public class TextWatcherImpl implements TextWatcher {
    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {

    }
}
