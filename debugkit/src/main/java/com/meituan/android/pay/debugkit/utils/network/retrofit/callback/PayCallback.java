package com.meituan.android.pay.debugkit.utils.network.retrofit.callback;

import android.support.annotation.NonNull;

/**
 * <AUTHOR>
 */
public class PayCallback<T> implements PayCallbackStart, PayCallbackSuccess<T>,
        PayCallbackError, PayCallbackFinal {

    @Override
    public void onStart(@NonNull CallInfo info) {

    }

    @Override
    public void onSuccess(@NonNull CallInfo info, T response) {

    }
    @Override
    public void onError(@NonNull CallInfo info, @NonNull Exception exception) {

    }

    @Override
    public void onFinal(@NonNull CallInfo info) {

    }
}
