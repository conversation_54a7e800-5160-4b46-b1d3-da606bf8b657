package com.meituan.android.pay.debugkit.utils.ui;

import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.meituan.android.pay.base.context.PayProvider;

/**
 * <AUTHOR>
 */
public final class UIUtils {
    public static final int MATCH_PARENT = ViewGroup.LayoutParams.MATCH_PARENT;
    public static final int WRAP_CONTENT = ViewGroup.LayoutParams.WRAP_CONTENT;

    public static class Screen {
        private static int defaultHeight = -1;
        private static int defaultWidth = -1;

        public static int dip2px(float dip) {
            final float scale = PayProvider.getContext().getResources().getDisplayMetrics().density;
            return (int) (dip * scale + 0.5f);
        }

        public static int px2dip(float px) {
            final float scale = PayProvider.getContext().getResources().getDisplayMetrics().density;
            return (int) (px / scale + 0.5f);
        }

        public static int getWindowHeight() {
            if (defaultHeight < 0) {
                defaultHeight = PayProvider.getContext().getResources().getDisplayMetrics().heightPixels;
            }
            return defaultHeight;
        }

        public static int getWidowWidth() {
            if (defaultWidth < 0) {
                defaultWidth = PayProvider.getContext().getResources().getDisplayMetrics().widthPixels;
            }
            return defaultWidth;
        }
    }

    public static class LayoutParams {

        public static ViewGroup.LayoutParams LP() {
            return new ViewGroup.LayoutParams(MATCH_PARENT, MATCH_PARENT);
        }

        public static FrameLayout.LayoutParams FLP() {
            return new FrameLayout.LayoutParams(MATCH_PARENT, MATCH_PARENT);
        }

        public static LinearLayout.LayoutParams LLP() {
            return new LinearLayout.LayoutParams(MATCH_PARENT, MATCH_PARENT);
        }

        public static LinearLayout.LayoutParams LLP(int weight) {
            return new LinearLayout.LayoutParams(MATCH_PARENT, MATCH_PARENT, weight);
        }

        public static RelativeLayout.LayoutParams RLP() {
            return new RelativeLayout.LayoutParams(MATCH_PARENT, MATCH_PARENT);
        }
    }

    public static class Visibility {
        public static void flipVisible(View view) {
            if (view != null) {
                view.setVisibility(view.getVisibility() == View.VISIBLE ? View.GONE : View.VISIBLE);
            }
        }
    }

}
