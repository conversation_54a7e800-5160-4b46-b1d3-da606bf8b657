package com.meituan.android.pay.debugkit.main.data.model;

import android.support.annotation.Keep;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Keep
public class RefundResult implements Serializable {
    @SerializedName("payRefundTime")
    private String payRefundTime;
    @SerializedName("payRefundFlow")
    private String payRefundFlow;

    public String getPayRefundTime() {
        return payRefundTime;
    }

    public void setPayRefundTime(String payRefundTime) {
        this.payRefundTime = payRefundTime;
    }

    public String getPayRefundFlow() {
        return payRefundFlow;
    }

    public void setPayRefundFlow(String payRefundFlow) {
        this.payRefundFlow = payRefundFlow;
    }
}
