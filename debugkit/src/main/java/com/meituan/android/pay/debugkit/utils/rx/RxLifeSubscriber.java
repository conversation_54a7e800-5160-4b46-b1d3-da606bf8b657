package com.meituan.android.pay.debugkit.utils.rx;

import android.arch.lifecycle.LifecycleOwner;

import com.meituan.android.pay.base.utils.exception.Catch;

import rx.Subscriber;

/**
 * <AUTHOR>
 */
public abstract class RxLifeSubscriber<T> extends Subscriber<T> {

    public RxLifeSubscriber(LifecycleOwner lifecycleOwner) {
        RxLifecycle.bind(lifecycleOwner, this);
    }

    @Override
    public void onCompleted() {

    }

    @Override
    public void onError(Throwable e) {
        Catch.with(e).log();
    }

    @Override
    public void onNext(T t) {

    }
}
