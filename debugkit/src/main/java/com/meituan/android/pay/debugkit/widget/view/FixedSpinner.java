package com.meituan.android.pay.debugkit.widget.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;

/**
 * <AUTHOR>
 */
public class FixedSpinner extends android.support.v7.widget.AppCompatSpinner {
    private ArrayAdapter<String> adapter;

    private OnItemSelectedListener onItemSelectedListener;

    private FixedOnItemSelectedListener fixedOnItemSelectedListener;

    public FixedSpinner(Context context) {
        super(context);
        init();
    }

    public FixedSpinner(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public FixedSpinner(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        this.adapter = new ArrayAdapter<>(getContext(), android.R.layout.simple_dropdown_item_1line);
        this.onItemSelectedListener = new OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (fixedOnItemSelectedListener != null) {
                    fixedOnItemSelectedListener.onItemSelected(position, adapter.getItem(position));
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        };
        setAdapter(adapter);
        setOnItemSelectedListener(onItemSelectedListener);
    }

    @Override
    public void setSelection(int position, boolean animate) {
        boolean sameSelected = position == getSelectedItemPosition();
        super.setSelection(position, animate);
        if (sameSelected && onItemSelectedListener != null) {
            onItemSelectedListener.onItemSelected(this, getSelectedView(), position, getSelectedItemId());
        }
    }

    @Override
    public void setSelection(int position) {
        setSelection(position, false);
    }

    public ArrayAdapter<String> adapter() {
        return adapter;
    }

    public FixedOnItemSelectedListener getFixedOnItemSelectedListener() {
        return fixedOnItemSelectedListener;
    }

    public void setFixedOnItemSelectedListener(FixedOnItemSelectedListener fixedOnItemSelectedListener) {
        this.fixedOnItemSelectedListener = fixedOnItemSelectedListener;
    }

    public interface FixedOnItemSelectedListener {
        void onItemSelected(int position, String content);
    }

}
