package com.meituan.android.pay.debugkit.widget.simple.base;

import android.arch.lifecycle.MutableLiveData;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface SimpleValueProvider {
    void update(String key, String value);

    void update(Map<String, String> data);

    Map<String, String> valuesOf(List<String> keys);

    String valueOf(String key);

    MutableLiveData<String> liveDataOf(String key);
}
