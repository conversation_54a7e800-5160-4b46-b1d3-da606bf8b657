package com.meituan.android.pay.debugkit.widget.simple.base;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.meituan.android.pay.base.utils.cache.MapCache;
import com.meituan.android.pay.base.utils.reflect.Reflection;
import com.meituan.android.pay.base.utils.serialize.JsonFinder;
import com.meituan.android.pay.debugkit.utils.serialize.DebugGsonProvider;
import com.meituan.android.pay.debugkit.utils.struct.Lazy;
import com.meituan.android.pay.debugkit.utils.struct.PairList;
import com.sankuai.meituan.serviceloader.ServiceLoader;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 */
public class SimpleStateTypeAdapter implements JsonDeserializer<SimpleViewState> {
    private static final String SIMPLE_STATE_CLASS_NAME = SimpleViewState.class.getName();

    private static final Lazy<PairList<String, String>> serviceLoaderMap = Lazy.init(() -> new PairList<>(
            ServiceLoader.servicesMap().get(SIMPLE_STATE_CLASS_NAME)));

    private static final MapCache<String, Type> viewStateTypeCache = MapCache.init(typeString -> {
        String clzName = serviceLoaderMap.instance().getVal(typeString);
        return Reflection.Constructor.getClass(clzName);
    });

    @Override
    public SimpleViewState deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        String typeString = JsonFinder.with(json).object(SimpleViewState.SERIALIZE_KEY).getAsString();
        Type type = viewStateTypeCache.cache(typeString);
        if (type != null) {
            return DebugGsonProvider.get().fromJson(json, type);
        }
        return null;
    }
}
