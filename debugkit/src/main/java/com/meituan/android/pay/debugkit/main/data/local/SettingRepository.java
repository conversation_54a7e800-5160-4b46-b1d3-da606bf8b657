package com.meituan.android.pay.debugkit.main.data.local;

import com.meituan.android.pay.debugkit.main.data.model.PassportState;
import com.meituan.android.singleton.UserCenterSingleton;
import com.meituan.passport.UserCenter;

import rx.Observable;

/**
 * <AUTHOR>
 */
public class SettingRepository {

    public Observable<PassportState> getPassportState() {
        UserCenter userCenter = UserCenterSingleton.getInstance();
        boolean isLogin = userCenter.isLogin();
        Observable<PassportState> current = Observable.just(userCenter.getUser())
                .map(user -> {
                    if (isLogin) {
                        return PassportState.from(user);
                    } else {
                        return PassportState.noLogin();
                    }
                });
        Observable<PassportState> listener = userCenter
                .loginEventObservable()
                .map(loginEvent -> {
                    if (loginEvent.type == UserCenter.LoginEventType.login) {
                        return PassportState.from(loginEvent.user);
                    }
                    return PassportState.noLogin();
                });
        return Observable.concat(current, listener);
    }
}
