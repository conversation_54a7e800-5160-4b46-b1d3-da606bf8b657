package com.meituan.android.pay.debugkit.utils.struct;

import rx.functions.Func0;

/**
 * <AUTHOR>
 */
public class Lazy<T> {
    private final Func0<T> initialization;

    private volatile T value;

    public Lazy(Func0<T> initialization) {
        this.initialization = initialization;
    }

    public T instance() {
        return instance(null);
    }

    public T instance(T instance) {
        if (value == null && initialization != null) {
            synchronized (this) {
                if (value == null) {
                    value = instance;
                }
                if (value == null) {
                    value = initialization.call();
                }
            }
        }
        return value;
    }

    public boolean isInit() {
        return value != null;
    }

    public static <T> Lazy<T> init(Func0<T> initialization) {
        return new Lazy<>(initialization);
    }

}
