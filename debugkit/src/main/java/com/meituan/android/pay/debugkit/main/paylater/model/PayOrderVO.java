package com.meituan.android.pay.debugkit.main.paylater.model;

import com.google.gson.annotations.SerializedName;
import com.sankuai.model.JsonBean;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@JsonBean
public class PayOrderVO implements Serializable {

    @SerializedName("id")
    private long id; // 支付单id

    @SerializedName("partnerid")
    private String partnerId; // 业务识别码

    @SerializedName("orderno")
    private String orderNo; // 业务流水号

    @SerializedName("outno")
    private String outNo; // 支付流水号/美团外部订单号

    @SerializedName("tradeno")
    private String tradeNo; // 三方流水号

    @SerializedName("money")
    private String money; // 金额

    @SerializedName("subject")
    private String subject; // 订单标题

    @SerializedName("body")
    private String body; // 订单详情

    @SerializedName("buyer")
    private String buyer; // 用户

    @SerializedName("seller")
    private String seller; // 业务识别码

    @SerializedName("attach")
    private String attach; // 扩展字段

    @SerializedName("ordertime")
    private int orderTime; // 支付发起时间 unixtime 秒

    @SerializedName("paytime")
    private int payTime; // 支付成功时间

    @SerializedName("orderip")
    private long orderIp; // ip

    @SerializedName("paytype")
    private int payType; // 支付通道

    @SerializedName("status")
    private int status; // 支付单状态

    @SerializedName("userid")
    private long userId; // 用户id

    @SerializedName("outmoney")
    private String outMoney; // 支付金额

    @SerializedName("orderid")
    private String orderId; // 业务单号

    @SerializedName("transtradeno")
    private String transTradeNo; // 交易流水号

    @SerializedName("advancePay")
    private boolean advancePay; // 垫资标识

    @SerializedName("creditOrder")
    private boolean creditOrder; // 赊销单标识

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOutNo() {
        return outNo;
    }

    public void setOutNo(String outNo) {
        this.outNo = outNo;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getMoney() {
        return money;
    }

    public void setMoney(String money) {
        this.money = money;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getBuyer() {
        return buyer;
    }

    public void setBuyer(String buyer) {
        this.buyer = buyer;
    }

    public String getSeller() {
        return seller;
    }

    public void setSeller(String seller) {
        this.seller = seller;
    }

    public String getAttach() {
        return attach;
    }

    public void setAttach(String attach) {
        this.attach = attach;
    }

    public int getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(int orderTime) {
        this.orderTime = orderTime;
    }

    public int getPayTime() {
        return payTime;
    }

    public void setPayTime(int payTime) {
        this.payTime = payTime;
    }

    public long getOrderIp() {
        return orderIp;
    }

    public void setOrderIp(long orderIp) {
        this.orderIp = orderIp;
    }

    public int getPayType() {
        return payType;
    }

    public void setPayType(int payType) {
        this.payType = payType;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getOutMoney() {
        return outMoney;
    }

    public void setOutMoney(String outMoney) {
        this.outMoney = outMoney;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getTransTradeNo() {
        return transTradeNo;
    }

    public void setTransTradeNo(String transTradeNo) {
        this.transTradeNo = transTradeNo;
    }

    public boolean isAdvancePay() {
        return advancePay;
    }

    public void setAdvancePay(boolean advancePay) {
        this.advancePay = advancePay;
    }

    public boolean isCreditOrder() {
        return creditOrder;
    }

    public void setCreditOrder(boolean creditOrder) {
        this.creditOrder = creditOrder;
    }
}
