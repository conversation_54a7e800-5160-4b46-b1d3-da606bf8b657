package com.meituan.android.pay.debugkit.widget.view.recycler;

import android.support.v7.widget.RecyclerView;
import android.viewbinding.ViewBinding;

/**
 * <AUTHOR>
 */
public class BindingHolder<V extends ViewBinding> extends RecyclerView.ViewHolder {
    private final V viewDataBinding;

    public BindingHolder(V viewDataBinding) {
        super(viewDataBinding.getRoot());
        this.viewDataBinding = viewDataBinding;
    }

    public V binding() {
        return viewDataBinding;
    }
}
