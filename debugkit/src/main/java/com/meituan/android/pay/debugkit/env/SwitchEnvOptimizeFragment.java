package com.meituan.android.pay.debugkit.env;

import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.meituan.android.pay.debugkit.main.base.PayDemoFragment;
import com.meituan.android.pay.debugkit.widget.simple.SimpleSharedViewState;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import rx.Observable;

/**
 * 环境切换优化调试页面
 * 集成到现有的调试系统中
 * 
 * <AUTHOR>
 * @since 2025/1/9
 */
@ServiceLoaderInterface(key = "switch-env-optimize", interfaceClass = PayDemoFragment.class)
public class SwitchEnvOptimizeFragment extends PayDemoFragment {
    
    private static final String TAG = "SwitchEnvOptimizeFragment";
    
    private SimpleSharedViewState simpleSharedViewState;
    private SwitchEnvOptimizeUtils switchEnvUtils;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        switchEnvUtils = SwitchEnvOptimizeUtils.getInstance();
        simpleSharedViewState = new SimpleSharedViewState();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = super.onCreateView(inflater, container, savedInstanceState);
        
        // 绑定调用器
        simpleSharedViewState.bindInvoker(this);
        
        return view;
    }

    @Override
    public String viewFileName() {
        return "switch_env_optimize_config.json";
    }

    @Override
    public Observable<String> getViewData() {
        return super.getViewData()
                .onErrorReturn(throwable -> generateDefaultViewData());
    }

    /**
     * 生成默认的视图配置数据
     */
    private String generateDefaultViewData() {
        // 返回JSON格式的配置
        return "[\n" +
                "  {\n" +
                "    \"title\": \"环境切换优化工具\",\n" +
                "    \"type\": \"list\",\n" +
                "    \"list\": [\n" +
                "      {\n" +
                "        \"type\": \"navigate\",\n" +
                "        \"title\": \"扫码切换环境\",\n" +
                "        \"text\": \"启动扫码\",\n" +
                "        \"method\": \"startScanEnvironment\"\n" +
                "      }\n" +
                "    ]\n" +
                "  },\n" +
                "  {\n" +
                "    \"title\": \"快速切换功能\",\n" +
                "    \"type\": \"list\",\n" +
                "    \"list\": [\n" +
                "      {\n" +
                "        \"key\": \"泳道环境\",\n" +
                "        \"type\": \"edit\",\n" +
                "        \"title\": \"泳道环境\",\n" +
                "        \"hint\": \"输入泳道路径\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"type\": \"navigate\",\n" +
                "        \"title\": \"切换泳道\",\n" +
                "        \"method\": \"switchLaneEnvironment\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"type\": \"navigate\",\n" +
                "        \"title\": \"标准收银台\",\n" +
                "        \"text\": \"启用Mock\",\n" +
                "        \"method\": \"enableStandardCashier\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"key\": \"Hybrid版本\",\n" +
                "        \"type\": \"edit\",\n" +
                "        \"title\": \"Hybrid版本\",\n" +
                "        \"hint\": \"输入版本号\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"type\": \"navigate\",\n" +
                "        \"title\": \"切换版本\",\n" +
                "        \"method\": \"switchHybridVersion\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"key\": \"Horn环境\",\n" +
                "        \"type\": \"edit\",\n" +
                "        \"title\": \"Horn环境\",\n" +
                "        \"hint\": \"输入Horn类型(逗号分隔)\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"type\": \"navigate\",\n" +
                "        \"title\": \"启用Horn\",\n" +
                "        \"method\": \"enableHornDebug\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"type\": \"navigate\",\n" +
                "        \"title\": \"Mock环境\",\n" +
                "        \"text\": \"启用Mock\",\n" +
                "        \"method\": \"enableMockEnvironment\"\n" +
                "      }\n" +
                "    ]\n" +
                "  },\n" +
                "  {\n" +
                "    \"title\": \"环境状态\",\n" +
                "    \"type\": \"list\",\n" +
                "    \"list\": [\n" +
                "      {\n" +
                "        \"type\": \"navigate\",\n" +
                "        \"title\": \"当前状态\",\n" +
                "        \"text\": \"查看状态\",\n" +
                "        \"method\": \"showEnvironmentStatus\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"type\": \"navigate\",\n" +
                "        \"title\": \"重置环境\",\n" +
                "        \"text\": \"重置所有\",\n" +
                "        \"method\": \"resetAllEnvironments\"\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "]";
    }

    /**
     * 启动扫码切换环境
     */
    private void startScanEnvironment() {
        SwitchEnvOptimizeUtils.startSwitchEnvOptimize();
    }

    /**
     * 切换泳道环境
     */
    private void switchLaneEnvironment() {
        String lanePath = simpleSharedViewState.valueOf("泳道环境");
        if (lanePath != null) {
            switchEnvUtils.switchToLaneEnvironment(lanePath);
            showToast("泳道环境已切换到: " + lanePath);
        }
    }

    /**
     * 启用标准收银台
     */
    private void enableStandardCashier() {
        switchEnvUtils.enableStandardCashier();
        showToast("标准收银台Mock已启用");
    }

    /**
     * 切换Hybrid版本
     */
    private void switchHybridVersion() {
        String version = simpleSharedViewState.valueOf("Hybrid版本");
        if (version != null) {
            switchEnvUtils.switchHybridCashierVersion(version);
            showToast("Hybrid收银台版本已切换到: " + version);
        }
    }

    /**
     * 启用Horn调试
     */
    private void enableHornDebug() {
        String hornTypes = simpleSharedViewState.valueOf("Horn环境");
        if (hornTypes != null) {
            switchEnvUtils.enableHornDebug(hornTypes);
            showToast("Horn调试已启用: " + hornTypes);
        }
    }

    /**
     * 启用Mock环境
     */
    private void enableMockEnvironment() {
        switchEnvUtils.enableMockEnvironment();
        showToast("Mock环境已启用");
    }

    /**
     * 显示环境状态
     */
    private void showEnvironmentStatus() {
        SwitchEnvOptimizeUtils.EnvironmentStatus status = switchEnvUtils.getCurrentEnvironmentStatus();
        
        StringBuilder sb = new StringBuilder();
        sb.append("当前环境状态:\n\n");
        sb.append("泳道路径: ").append(status.currentLanePath).append("\n");
        sb.append("标准收银台: ").append(status.isStandardCashierEnabled ? "已启用" : "未启用").append("\n");
        sb.append("Hybrid Mock: ").append(status.isHybridMockEnabled ? "已启用" : "未启用").append("\n");
        sb.append("Hybrid版本: ").append(status.hybridCashierVersion).append("\n");
        sb.append("Horn调试: ").append(status.isHornDebugEnabled ? "已启用" : "未启用").append("\n");
        sb.append("Mock环境: ").append(status.isMockEnvironmentEnabled ? "已启用" : "未启用").append("\n");
        sb.append("SharkDebug: ").append(status.isSharkDebugEnabled ? "已启用" : "未启用").append("\n");
        
        if (status.enabledHornTypes != null && !status.enabledHornTypes.isEmpty()) {
            sb.append("Horn类型: ").append(status.enabledHornTypes.toString()).append("\n");
        }
        
        showDialog("环境状态", sb.toString());
    }

    /**
     * 重置所有环境
     */
    private void resetAllEnvironments() {
        switchEnvUtils.resetAllEnvironmentSettings();
        showToast("所有环境设置已重置");
    }

    /**
     * 显示Toast消息
     */
    private void showToast(String message) {
        if (getActivity() != null) {
            // 这里可以使用项目中的Toast工具类
            // ToastUtils.showToast(getActivity(), message);
        }
    }

    /**
     * 显示对话框
     */
    private void showDialog(String title, String message) {
        if (getActivity() != null) {
            // 这里可以使用项目中的Dialog工具类
            // PayDialog.Builder(getActivity()).title(title).msg(message).show();
        }
    }
}
