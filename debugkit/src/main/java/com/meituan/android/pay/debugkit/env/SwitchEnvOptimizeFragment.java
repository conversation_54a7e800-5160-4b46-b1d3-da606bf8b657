package com.meituan.android.pay.debugkit.env;

import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.meituan.android.pay.debugkit.main.base.PayDemoFragment;
import com.meituan.android.pay.debugkit.widget.simple.SimpleSharedViewState;
import com.meituan.android.pay.debugkit.widget.simple.base.SimpleViewState;
import com.meituan.android.pay.debugkit.widget.simple.state.EditViewState;
import com.meituan.android.pay.debugkit.widget.simple.state.NavigateViewState;
import com.meituan.android.pay.debugkit.widget.simple.state.SelectionViewState;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import java.util.ArrayList;
import java.util.List;

import rx.Observable;

/**
 * 环境切换优化调试页面
 * 集成到现有的调试系统中
 * 
 * <AUTHOR>
 * @since 2025/1/9
 */
@ServiceLoaderInterface(key = "switch-env-optimize", interfaceClass = PayDemoFragment.class)
public class SwitchEnvOptimizeFragment extends PayDemoFragment {
    
    private static final String TAG = "SwitchEnvOptimizeFragment";
    
    private SimpleSharedViewState simpleSharedViewState;
    private SwitchEnvOptimizeUtils switchEnvUtils;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        switchEnvUtils = SwitchEnvOptimizeUtils.getInstance();
        simpleSharedViewState = new SimpleSharedViewState();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = super.onCreateView(inflater, container, savedInstanceState);
        
        // 绑定调用器
        simpleSharedViewState.bindInvoker(this);
        
        return view;
    }

    @Override
    public String viewFileName() {
        return "switch_env_optimize_config.json";
    }

    @Override
    public Observable<String> getViewData() {
        return super.getViewData()
                .onErrorReturn(throwable -> generateDefaultViewData());
    }

    /**
     * 生成默认的视图配置数据
     */
    private String generateDefaultViewData() {
        List<SimpleViewState> viewStates = new ArrayList<>();

        // 标题分隔符
        SelectionViewState titleDivider = new SelectionViewState();
        titleDivider.setTitle("环境切换优化工具");
        // 使用反射设置divider字段
        try {
            java.lang.reflect.Field dividerField = SelectionViewState.class.getDeclaredField("divider");
            dividerField.setAccessible(true);
            dividerField.setBoolean(titleDivider, true);
        } catch (Exception e) {
            // 忽略反射异常
        }
        viewStates.add(titleDivider);

        // 扫码切换环境
        NavigateViewState scanButton = new NavigateViewState();
        scanButton.setTitle("扫码切换环境");
        // 使用反射设置method字段
        try {
            java.lang.reflect.Field methodField = SimpleViewState.class.getDeclaredField("method");
            methodField.setAccessible(true);
            methodField.set(scanButton, "startScanEnvironment");
        } catch (Exception e) {
            // 忽略反射异常
        }
        viewStates.add(scanButton);

        // 快速切换功能分隔符
        SelectionViewState functionDivider = new SelectionViewState();
        functionDivider.setTitle("快速切换功能");
        // 使用反射设置divider字段
        try {
            java.lang.reflect.Field dividerField = SelectionViewState.class.getDeclaredField("divider");
            dividerField.setAccessible(true);
            dividerField.setBoolean(functionDivider, true);
        } catch (Exception e) {
            // 忽略反射异常
        }
        viewStates.add(functionDivider);

        // 泳道环境切换
        EditViewState laneEdit = new EditViewState();
        laneEdit.setTitle("泳道环境");
        laneEdit.setValue("default");
        // 使用反射设置hint字段
        try {
            java.lang.reflect.Field hintField = EditViewState.class.getDeclaredField("hint");
            hintField.setAccessible(true);
            hintField.set(laneEdit, "输入泳道路径");
        } catch (Exception e) {
            // 忽略反射异常
        }
        viewStates.add(laneEdit);

        NavigateViewState laneButton = new NavigateViewState();
        laneButton.setTitle("切换泳道");
        laneButton.setMethod("switchLaneEnvironment");
        viewStates.add(laneButton);

        // 标准收银台
        NavigateViewState cashierButton = new NavigateViewState();
        cashierButton.setTitle("标准收银台");
        cashierButton.setMethod("enableStandardCashier");
        viewStates.add(cashierButton);

        // Hybrid收银台版本
        EditViewState hybridEdit = new EditViewState();
        hybridEdit.setTitle("Hybrid版本");
        hybridEdit.setValue("8.8.0");
        // 使用反射设置hint字段
        try {
            java.lang.reflect.Field hintField = EditViewState.class.getDeclaredField("hint");
            hintField.setAccessible(true);
            hintField.set(hybridEdit, "输入版本号");
        } catch (Exception e) {
            // 忽略反射异常
        }
        viewStates.add(hybridEdit);

        NavigateViewState hybridButton = new NavigateViewState();
        hybridButton.setTitle("切换版本");
        hybridButton.setMethod("switchHybridVersion");
        viewStates.add(hybridButton);

        // Horn环境
        EditViewState hornEdit = new EditViewState();
        hornEdit.setTitle("Horn环境");
        hornEdit.setValue("");
        // 使用反射设置hint字段
        try {
            java.lang.reflect.Field hintField = EditViewState.class.getDeclaredField("hint");
            hintField.setAccessible(true);
            hintField.set(hornEdit, "输入Horn类型(逗号分隔)");
        } catch (Exception e) {
            // 忽略反射异常
        }
        viewStates.add(hornEdit);

        NavigateViewState hornButton = new NavigateViewState();
        hornButton.setTitle("启用Horn");
        hornButton.setMethod("enableHornDebug");
        viewStates.add(hornButton);

        // Mock环境
        NavigateViewState mockButton = new NavigateViewState();
        mockButton.setTitle("Mock环境");
        mockButton.setMethod("enableMockEnvironment");
        viewStates.add(mockButton);

        // 环境状态分隔符
        SelectionViewState statusDivider = new SelectionViewState();
        statusDivider.setTitle("环境状态");
        // 使用反射设置divider字段
        try {
            java.lang.reflect.Field dividerField = SelectionViewState.class.getDeclaredField("divider");
            dividerField.setAccessible(true);
            dividerField.setBoolean(statusDivider, true);
        } catch (Exception e) {
            // 忽略反射异常
        }
        viewStates.add(statusDivider);

        NavigateViewState statusButton = new NavigateViewState();
        statusButton.setTitle("当前状态");
        statusButton.setMethod("showEnvironmentStatus");
        viewStates.add(statusButton);

        NavigateViewState resetButton = new NavigateViewState();
        resetButton.setTitle("重置环境");
        resetButton.setMethod("resetAllEnvironments");
        viewStates.add(resetButton);

        return simpleSharedViewState.getViewData().toBlocking().single();
    }

    /**
     * 启动扫码切换环境
     */
    private void startScanEnvironment() {
        SwitchEnvOptimizeUtils.startSwitchEnvOptimize();
    }

    /**
     * 切换泳道环境
     */
    private void switchLaneEnvironment() {
        String lanePath = simpleSharedViewState.valueOf("泳道环境");
        if (lanePath != null) {
            switchEnvUtils.switchToLaneEnvironment(lanePath);
            showToast("泳道环境已切换到: " + lanePath);
        }
    }

    /**
     * 启用标准收银台
     */
    private void enableStandardCashier() {
        switchEnvUtils.enableStandardCashier();
        showToast("标准收银台Mock已启用");
    }

    /**
     * 切换Hybrid版本
     */
    private void switchHybridVersion() {
        String version = simpleSharedViewState.valueOf("Hybrid版本");
        if (version != null) {
            switchEnvUtils.switchHybridCashierVersion(version);
            showToast("Hybrid收银台版本已切换到: " + version);
        }
    }

    /**
     * 启用Horn调试
     */
    private void enableHornDebug() {
        String hornTypes = simpleSharedViewState.valueOf("Horn环境");
        if (hornTypes != null) {
            switchEnvUtils.enableHornDebug(hornTypes);
            showToast("Horn调试已启用: " + hornTypes);
        }
    }

    /**
     * 启用Mock环境
     */
    private void enableMockEnvironment() {
        switchEnvUtils.enableMockEnvironment();
        showToast("Mock环境已启用");
    }

    /**
     * 显示环境状态
     */
    private void showEnvironmentStatus() {
        SwitchEnvOptimizeUtils.EnvironmentStatus status = switchEnvUtils.getCurrentEnvironmentStatus();
        
        StringBuilder sb = new StringBuilder();
        sb.append("当前环境状态:\n\n");
        sb.append("泳道路径: ").append(status.currentLanePath).append("\n");
        sb.append("标准收银台: ").append(status.isStandardCashierEnabled ? "已启用" : "未启用").append("\n");
        sb.append("Hybrid Mock: ").append(status.isHybridMockEnabled ? "已启用" : "未启用").append("\n");
        sb.append("Hybrid版本: ").append(status.hybridCashierVersion).append("\n");
        sb.append("Horn调试: ").append(status.isHornDebugEnabled ? "已启用" : "未启用").append("\n");
        sb.append("Mock环境: ").append(status.isMockEnvironmentEnabled ? "已启用" : "未启用").append("\n");
        sb.append("SharkDebug: ").append(status.isSharkDebugEnabled ? "已启用" : "未启用").append("\n");
        
        if (status.enabledHornTypes != null && !status.enabledHornTypes.isEmpty()) {
            sb.append("Horn类型: ").append(status.enabledHornTypes.toString()).append("\n");
        }
        
        showDialog("环境状态", sb.toString());
    }

    /**
     * 重置所有环境
     */
    private void resetAllEnvironments() {
        switchEnvUtils.resetAllEnvironmentSettings();
        showToast("所有环境设置已重置");
    }

    /**
     * 显示Toast消息
     */
    private void showToast(String message) {
        if (getActivity() != null) {
            // 这里可以使用项目中的Toast工具类
            // ToastUtils.showToast(getActivity(), message);
        }
    }

    /**
     * 显示对话框
     */
    private void showDialog(String title, String message) {
        if (getActivity() != null) {
            // 这里可以使用项目中的Dialog工具类
            // PayDialog.Builder(getActivity()).title(title).msg(message).show();
        }
    }
}
