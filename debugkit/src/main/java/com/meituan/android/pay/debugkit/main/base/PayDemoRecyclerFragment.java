package com.meituan.android.pay.debugkit.main.base;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.support.v7.widget.LinearLayoutManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.viewbinding.ViewBinding;

import com.meituan.android.pay.base.container.PayBaseFragment;
import com.meituan.android.pay.debugkit.databinding.PayDebugFragmentRecyclerLayoutBinding;
import com.meituan.android.pay.debugkit.widget.view.recycler.BindingAdapter;

/**
 * <AUTHOR>
 */
public class PayDemoRecyclerFragment<VDB extends ViewBinding, ViewState> extends PayBaseFragment {
    protected PayDebugFragmentRecyclerLayoutBinding binding;
    private BindingAdapter<VDB, ViewState> bindingAdapter;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        bindingAdapter = new BindingAdapter<>();
        binding = PayDebugFragmentRecyclerLayoutBinding.inflate(inflater);
        binding.recycler.setAdapter(bindingAdapter);
        binding.recycler.setLayoutManager(new LinearLayoutManager(getContext()));
        onCreateView(inflater, binding);
        return binding.getRoot();
    }

    public void onCreateView(LayoutInflater inflater, PayDebugFragmentRecyclerLayoutBinding binding) {
    }

    protected BindingAdapter<VDB, ViewState> adapter() {
        return bindingAdapter;
    }
}
