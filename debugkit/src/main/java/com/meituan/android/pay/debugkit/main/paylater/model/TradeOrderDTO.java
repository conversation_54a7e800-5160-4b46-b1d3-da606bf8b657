package com.meituan.android.pay.debugkit.main.paylater.model;

import com.google.gson.annotations.SerializedName;
import com.sankuai.model.JsonBean;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@JsonBean
public class TradeOrderDTO implements Serializable {

    @SerializedName("tradeNo")
    private String tradeNo;

    @SerializedName("payToken")
    private String payToken;

    @SerializedName("cif")
    private String cif;

    @SerializedName("outNo")
    private String outNo;

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getPayToken() {
        return payToken;
    }

    public void setPayToken(String payToken) {
        this.payToken = payToken;
    }

    public String getCif() {
        return cif;
    }

    public void setCif(String cif) {
        this.cif = cif;
    }

    public String getOutNo() {
        return outNo;
    }

    public void setOutNo(String outNo) {
        this.outNo = outNo;
    }
}
