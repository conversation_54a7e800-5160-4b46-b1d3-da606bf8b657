package com.meituan.android.pay.debugkit.utils.network.retrofit.callback;

import android.support.annotation.NonNull;

import rx.functions.Action1;

/**
 * <AUTHOR>
 */
public interface PayCallbackSuccess<T> {
    void onSuccess(@NonNull CallInfo info, T response);

    static <T> PayCallbackSuccess<T> simple(Action1<T> onSuccess) {
        return (info, response) -> {
            if (onSuccess != null) {
                onSuccess.call(response);
            }
        };
    }
}
