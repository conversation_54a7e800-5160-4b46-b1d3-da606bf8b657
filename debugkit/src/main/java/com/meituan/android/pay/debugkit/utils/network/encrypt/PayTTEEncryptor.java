package com.meituan.android.pay.debugkit.utils.network.encrypt;

import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.pay.base.utils.cache.MapCache;
import com.meituan.android.pay.base.utils.serialize.GsonProvider;
import com.meituan.android.paybase.utils.Base64;
import com.sankuai.meituan.tte.TTE;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class PayTTEEncryptor implements PayEncrypt {
    private static final MapCache<Boolean, TTE> TTE_CACHE =
            MapCache.init(PayTTEEncryptor::createTTE);

    public static final boolean PROD = true;

    public static Result encryptFrom(Map<String, String> params) {
        return PayTTEEncryptor.encrypt(params);
    }

    public static Result encrypt(Map<String, String> params) throws PayEncryptException {
        final Map<String, String> encryptedParams = new HashMap<>();
        try {
            final TTE tte = TTE_CACHE.cache(PROD);
            // EDK
            byte[] edkBytes = tte.getEDK();
            if (edkBytes != null && edkBytes.length != 0) {
                String edkString = Base64.encodeBytes(edkBytes);
                encryptedParams.put(KEY_ENCRYPT_APP_DK, edkString);
            }
            // Encrypt
            byte[] encryptedBytes = tte.encrypt(GsonProvider.get()
                    .toJson(params)
                    .getBytes());
            if (encryptedBytes != null) {
                String encryptedString = Base64.encodeBytes(encryptedBytes);
                encryptedParams.put(KEY_ENCRYPT_PARAMS_VALUE, encryptedString);
            }
            // AESKey
            String aesKey = PayEncryptor.onlyAesKey();
            encryptedParams.put(KEY_ENCRYPT_KEY, aesKey);
            encryptedParams.put(KEY_ENCRYPT_TYPE, ENCRYPT_TYPE_TTE);
            return new PayEncrypt.Result(aesKey, encryptedParams);
        } catch (Exception e) { // TTE 流程异常，使用普通加密
            return PayEncryptor.encryptFrom(params);
        }
    }

    public static String decrypt(String encryptedString) throws PayEncryptException {
        try {
            byte[] encryptedBytes = Base64.decode(encryptedString.getBytes());
            byte[] decryptedBytes = TTE_CACHE.cache(PROD).decrypt(encryptedBytes);
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new PayEncryptException(ERROR_CODE_DECRYPT, ERROR_MSG_DECRYPT);
        }
    }

    private static TTE createTTE(boolean prod) {
        TTE.Env tteEnv = prod ? TTE.Env.PROD : TTE.Env.TEST;
        return TTE.instance(PayProvider.getContext(),
                TTE.Config.builder().withEnv(tteEnv).build());
    }

}
