package com.meituan.android.pay.debugkit.main.paylater;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.meituan.android.pay.debugkit.main.data.DataProvider;
import com.meituan.android.pay.debugkit.main.paylater.model.OrderInfo;
import com.meituan.android.pay.debugkit.main.paylater.model.RefundInfo;
import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paycommon.lib.retrofit.PayRetrofit;
import com.meituan.android.pay.debugkit.R;
import com.meituan.android.pay.debugkit.main.activity.PayDebugActivity;
import com.meituan.android.pay.debugkit.main.data.remote.PayDemoRequestService;
import com.meituan.android.pay.debugkit.utils.system.ParamCreateUtils;
import com.meituan.android.pay.debugkit.widget.old.OrderExtraParamView;

import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * 订单详情界面，可以进行退款
 */
public class OrderDetailsActivity extends PayDebugActivity implements IRequestCallback, View.OnClickListener {

    // 传递订单信息的key
    public static final String PARAM_REFUND_ORDER_INFO = "refund_order_info";
    private static final int REQUEST_TAG_REFUND = 1;
    private OrderInfo orderInfo;
    private ArrayList<OrderExtraParamView> paramViews = new ArrayList<>();
    private LinearLayout containerView;
    private EditText orderMoney;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.old_activity_refund);
        orderInfo = (OrderInfo) getIntent().getSerializableExtra(PARAM_REFUND_ORDER_INFO);
        initView(orderInfo);
    }

    private void initView(OrderInfo orderInfo) {

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        TextView orderName = findViewById(R.id.order_name);
        TextView orderNo = findViewById(R.id.order_no);
        TextView payType = findViewById(R.id.pay_type);
        TextView orderTime = findViewById(R.id.order_time);
        TextView payTime = findViewById(R.id.pay_time);
        TextView orderState = findViewById(R.id.order_state);
        LinearLayout llRefundBtn = findViewById(R.id.ll_refund_btn);
        Button buttonAddParam = findViewById(R.id.btn_add_param);
        orderMoney = findViewById(R.id.order_money);
        containerView = findViewById(R.id.main_content);

        orderName.setText(orderInfo.getSubject());
        orderNo.setText(orderInfo.getObjId());
        payType.setText(orderInfo.getPayType());
        orderMoney.setText(orderInfo.getMoney());
        orderTime.setText(format.format(orderInfo.getOrderTime() * 1000L));
        payTime.setText(format.format(orderInfo.getPaytime() * 1000L));
        payTime.setText(format.format(System.currentTimeMillis()));
        orderState.setText(OrderInfo.getPayResultByStatus(orderInfo.getStatus()));

        if (OrderInfo.PAY_STATUS_SUCC == orderInfo.getStatus()) {
            findViewById(R.id.btn_refund_new).setOnClickListener(this);
            buttonAddParam.setOnClickListener(this);
            buttonAddParam.setVisibility(View.VISIBLE);
            llRefundBtn.setVisibility(View.VISIBLE);
        } else {
            buttonAddParam.setVisibility(View.GONE);
            llRefundBtn.setVisibility(View.GONE);
        }
    }

    @Override
    public void onRequestSucc(int tag, Object obj) {
        if (REQUEST_TAG_REFUND == tag) {
            RefundInfo info = (RefundInfo) obj;
            ToastUtils.showSnackToast(this, "退款成功");
            finish();
        }
    }

    @Override
    public void onRequestException(int tag, Exception e) {
        ToastUtils.showSnackToast(this, e.getMessage());
    }

    @Override
    public void onRequestFinal(int tag) {

    }

    @Override
    public void onRequestStart(int tag) {

    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.btn_refund_new) {
            requestRefundDemo();
        } else if (id == R.id.btn_add_param) {
            OrderExtraParamView view = generateView();
            containerView.addView(view);
            paramViews.add(view);
        }
    }

    // 参考接口文档: https://km.sankuai.com/page/322964502
    private void requestRefundDemo() {
        long money = (long) (Float.valueOf(orderInfo.getMoney()) * 100L);

        if (!TextUtils.isEmpty(orderMoney.getText().toString())) {
            money = (long) (Float.valueOf(orderMoney.getText().toString()) * 100L);
        }

        HashMap<String, Object> param = new HashMap<>();
        param.put("charset", "UTF-8");
        param.put("tradeNo", orderInfo.getTransTradeNo());
        param.put("refundFeeCent", money);
        param.put("refundNo", orderInfo.getObjId() + System.currentTimeMillis() / 1000);
        param.put("riskParams", new JSONObject());
        param.put("sellerId", orderInfo.getPartnerId());

        param = getDemeoRequestParams(param);
        param = appendExtraParams(paramViews, param);

        PayRetrofit.getInstance().create(PayDemoRequestService.class, this, REQUEST_TAG_REFUND).
                tradeRefundDemo(param);
    }

    public HashMap<String, Object> getDemeoRequestParams(HashMap<String, Object> param) {
        DataProvider.order().getOrderParam("partner_key")
                .subscribe(val -> {
                    try {
                        String signedStr = ParamCreateUtils.signInMD5(new JSONObject(param), val);
                        param.put("sign", signedStr);
                        param.put("signType", "MD5");
                    } catch (JSONException e) {
                        throw new RuntimeException(e);
                    }
                });
        return param;
    }

    private OrderExtraParamView generateView() {
        OrderExtraParamView paramView = new OrderExtraParamView(this);
        paramView.setOnRemoveListener(view -> {
            containerView.removeView(paramView);
            paramViews.remove(view);
        });
        return paramView;
    }

    private HashMap<String, Object> appendExtraParams(ArrayList<OrderExtraParamView> views, HashMap<String, Object> params) {
        if (!CollectionUtils.isEmpty(views)) {
            for (OrderExtraParamView view : views) {
                if (!TextUtils.isEmpty(view.getParamKey())) {
                    params.put(view.getParamKey(), view.getParamValue());
                }
            }
        }
        return params;
    }
}
