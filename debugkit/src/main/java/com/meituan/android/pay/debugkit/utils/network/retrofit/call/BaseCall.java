package com.meituan.android.pay.debugkit.utils.network.retrofit.call;

import android.arch.lifecycle.LifecycleOwner;
import android.support.annotation.NonNull;

import com.meituan.android.pay.base.utils.lifecycle.LifecycleObservation;
import com.meituan.android.pay.base.utils.lifecycle.LifecycleObserver;
import com.meituan.android.pay.base.utils.observable.Observable;
import com.meituan.android.pay.base.utils.observable.ObservableProvider;
import com.meituan.android.pay.debugkit.utils.network.retrofit.callback.PayCallback;
import com.meituan.android.pay.debugkit.utils.network.retrofit.callback.PayCallbackFinal;
import com.meituan.android.pay.debugkit.utils.network.retrofit.callback.PayCallbackStart;
import com.meituan.android.pay.debugkit.utils.network.retrofit.callback.PayCallbackSuccess;
import com.meituan.android.pay.debugkit.utils.network.retrofit.callback.CallInfo;
import com.meituan.android.pay.debugkit.utils.network.retrofit.callback.PayCallbackError;
import com.sankuai.meituan.retrofit2.Call;
import com.sankuai.meituan.retrofit2.Response;

import java.io.IOException;

import rx.functions.Action0;
import rx.functions.Action1;

/**
 * <AUTHOR>
 */
public abstract class BaseCall<T, R> implements LifecycleObserver {
    private final Call<T> delegate;
    private final CallInfo info;
    private boolean called;
    private boolean destroyed;

    private final Observable<PayCallbackStart> startObservable;
    /** @noinspection rawtypes*/
    private final Observable<PayCallbackSuccess> successObservable;
    private final Observable<PayCallbackError> errorObservable;
    private final Observable<PayCallbackFinal> finalObservable;

    protected BaseCall(Call<T> delegate) {
        this.delegate = delegate;
        this.info = new CallInfo();
        ObservableProvider provider = ObservableProvider.bind(this);
        startObservable = provider.dispatch(PayCallbackStart.class);
        successObservable = provider.dispatch(PayCallbackSuccess.class);
        errorObservable = provider.dispatch(PayCallbackError.class);
        finalObservable = provider.dispatch(PayCallbackFinal.class);
    }

    /**
     * 通过观察者发起网络请求
     */
    public void observeCall(LifecycleOwner lifecycleOwner) {
        if (LifecycleObservation.addObserver(lifecycleOwner, this)) {
            call();
        } else {
            destroy();
        }
    }

    /**
     * 发起网络请求
     */
    public void call() {
        if (delegate != null && !called && !destroyed) {
            called = true;
            onStart();
            delegate.enqueue(new com.sankuai.meituan.retrofit2.Callback<T>() {
                @Override
                public void onResponse(Call<T> call, Response<T> response) {
                    R result;
                    try {
                        result = transfer(response.body());
                    } catch (Exception e) {
                        onFailure(call, e);
                        return;
                    }
                    onSuccess(result);
                    onFinal();
                    destroy();
                }

                @Override
                public void onFailure(Call<T> call, Throwable t) {
                    onError(new IOException(t));
                    onFinal();
                    destroy();
                }
            });
        }
    }

    /**
     * 网络请求绑定的生命周期结束，取消网络请求
     */
    @Override
    public void onDestroy() {
        cancel();
        destroy();
    }

    /**
     * 取消网络请求
     */
    public void cancel() {
        if (delegate != null && delegate.isExecuted() && !delegate.isCanceled()) {
            delegate.cancel();
            destroy();
        }
    }

    /**
     * 销毁步骤
     */
    public void destroy() {
        if (!destroyed) {
            destroyed = true;
            ObservableProvider.unObserve(this);
        }
    }

    // 生命周期
    protected abstract R transfer(T response) throws Exception;

    protected void onStart() {
        startObservable.call(callback -> {
            callback.onStart(info);
        });
    }

    protected void onSuccess(R response) {
        successObservable.call(callback -> {
            //noinspection unchecked
            callback.onSuccess(info, response);
        });
    }

    protected void onError(Exception e) {
        errorObservable.call(callback -> {
            callback.onError(info, e);
        });
    }

    protected void onFinal() {
        finalObservable.call(callback -> {
            callback.onFinal(info);
        });
    }

    // callback
    public BaseCall<T, R> tag(int tag) {
        info.setTag(tag);
        return this;
    }

    public BaseCall<T, R> onCallback(PayCallback<R> callback) {
        return onStart(callback)
                .onSuccess(callback)
                .onError(callback)
                .onFinal(callback);
    }

    public BaseCall<T, R> onCallback(@NonNull Action1<R> onSuccess, @NonNull Action1<Exception> onError) {
        return onSuccess(onSuccess).onError(onError);
    }

    public BaseCall<T, R> onStart(@NonNull Action0 onStart) {
        return onStart(PayCallbackStart.simple(onStart));
    }

    public BaseCall<T, R> onStart(@NonNull PayCallbackStart onDetailStart) {
        startObservable.subscribe(onDetailStart);
        return this;
    }

    public BaseCall<T, R> onSuccess(@NonNull Action1<R> onSuccess) {
        return onSuccess(PayCallbackSuccess.simple(onSuccess));
    }

    public BaseCall<T, R> onSuccess(@NonNull PayCallbackSuccess<R> onDetailSuccess) {
        successObservable.subscribe(onDetailSuccess);
        return this;
    }

    public BaseCall<T, R> onError(@NonNull Action1<Exception> onError) {
        return onError(PayCallbackError.simple(onError));
    }

    public BaseCall<T, R> onError(@NonNull PayCallbackError onDetailError) {
        errorObservable.subscribe(onDetailError);
        return this;
    }

    public BaseCall<T, R> onFinal(@NonNull Action0 onFinal) {
        return onFinal(PayCallbackFinal.simple(onFinal));
    }

    public BaseCall<T, R> onFinal(@NonNull PayCallbackFinal onDetailFinal) {
        finalObservable.subscribe(onDetailFinal);
        return this;
    }
}
