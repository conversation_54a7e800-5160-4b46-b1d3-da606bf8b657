package com.meituan.android.pay.debugkit.utils.system;

import java.text.SimpleDateFormat;
import java.util.Locale;

/**
 * <AUTHOR>
 */
public class DateUtils {
    private static final ThreadLocal<SimpleDateFormat> FORMAT_THREAD_LOCAL = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.SIMPLIFIED_CHINESE);
        }
    };

    public static String format(Object obj) {
        SimpleDateFormat format = FORMAT_THREAD_LOCAL.get();
        if (format != null) {
            return format.format(obj);
        } else {
            return "";
        }
    }
}
