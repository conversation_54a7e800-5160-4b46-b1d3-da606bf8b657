package com.meituan.android.pay.debugkit.widget.old;

import android.content.Context;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.meituan.android.pay.debugkit.R;

/**
 * author:  kkli
 * date:    2020-09-24
 * description:
 */
public class TextDivider extends LinearLayout {
    private TextView textView;

    public TextDivider(Context context) {
        super(context);
        init();
    }

    public TextDivider(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public TextDivider(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        LayoutInflater.from(getContext()).inflate(R.layout.demo_text_divider, this, true);
        setLayoutParams(new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        setGravity(Gravity.CENTER_VERTICAL);
        textView = findViewById(R.id.demo_text_divider_content);
    }

    public TextDivider setDividerText(String content) {
        textView.setText(content);
        return this;
    }

    public String getDividerText() {
        return textView.getText().toString();
    }
}
