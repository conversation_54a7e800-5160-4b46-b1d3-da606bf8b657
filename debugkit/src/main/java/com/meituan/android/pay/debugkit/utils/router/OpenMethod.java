package com.meituan.android.pay.debugkit.utils.router;

import android.net.Uri;

import com.meituan.android.pay.base.utils.serviceloader.ServiceLoaderUtils;
import com.meituan.android.pay.debugkit.utils.reflect.Reflection;

/**
 * <AUTHOR>
 */
public interface OpenMethod {

    static void open(String url) {
        Uri uri = Uri.parse(url);
        String name = uri.getHost();
        String method = uri.getLastPathSegment();
        OpenMethod openMethod = ServiceLoaderUtils.loadSelected(OpenMethod.class, name);
        if (openMethod != null) {
            Reflection.invokeMethod(openMethod, method);
        }
    }
}
