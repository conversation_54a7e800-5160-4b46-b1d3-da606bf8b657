package com.meituan.android.pay.debugkit.main.order;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.Keep;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;

import com.meituan.android.pay.debugkit.databinding.PayDebugFragmentCommonLayoutBinding;
import com.meituan.android.pay.debugkit.databinding.PayDebugOrderLayoutBinding;
import com.meituan.android.pay.debugkit.main.base.PayDemoFragment;
import com.meituan.android.pay.debugkit.utils.mvvm.LiveDataUtils;
import com.meituan.android.pay.debugkit.utils.mvvm.ParameterViewModelProvider;
import com.meituan.android.paybase.dialog.ToastUtils;

/**
 * <AUTHOR>
 */
public class OrderFragment extends PayDemoFragment implements OrderNavigator {
    private PayDebugOrderLayoutBinding orderBinding;

    @Override
    public void onCreateView(LayoutInflater inflater, PayDebugFragmentCommonLayoutBinding binding) {
        orderBinding = PayDebugOrderLayoutBinding.inflate(inflater, binding.container, false);
        binding.bottom.addView(orderBinding.getRoot());
        binding.title.setText("订单");
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        OrderViewModel viewModel = ParameterViewModelProvider.of(this, OrderViewModel.class)
                .init(this, sharedViewState);
        LiveDataUtils.observe(this, viewModel.loading(), isLoading());
        LiveDataUtils.observe(this, viewModel.money(), money ->
                orderBinding.moneyNumber.setText(money));
        orderBinding.submitOrder.setOnClickListener(v -> viewModel.onSubmitOrder(true));
    }

    @Override
    public void onDestroy() {
        saveViewData().subscribe();
        super.onDestroy();
    }

    @Override
    public String viewFileName() {
        return "view_order.json";
    }

    @Override
    @Keep
    public void onCashier(Intent intent) {
        startActivityForResult(intent, 0x1);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 0x1) {
            if (resultCode == Activity.RESULT_OK) {
                ToastUtils.showSnackToast(getActivity(), "支付成功");
            } else {
                ToastUtils.showSnackToast(getActivity(), "支付取消");
            }
        }
    }
}
