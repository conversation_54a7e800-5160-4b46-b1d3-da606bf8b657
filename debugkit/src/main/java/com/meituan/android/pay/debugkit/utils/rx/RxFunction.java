package com.meituan.android.pay.debugkit.utils.rx;

import rx.functions.Action1;
import rx.functions.Func1;

/**
 * <AUTHOR>
 */
public class RxFunction {
    public static <T> Func1<T, Boolean> nonNull() {
        return t -> t != null;
    }

    public static <T> Func1<T, Boolean> allNull() {
        return t -> t == null;
    }

    public static <T> Func1<Throwable, T> onErrorReturnNull() {
        return throwable -> null;
    }

    public static <T> void call(Action1<T> action, T input) {
        if (action != null) {
            action.call(input);
        }
    }

    public static <T, R> R call(Func1<T, R> func, T input) {
        if (func != null) {
            return func.call(input);
        }
        return null;
    }
}
