package com.meituan.android.pay.debugkit.capacity;

import android.support.annotation.Keep;

import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.pay.debugkit.main.activity.PayDemoActivity;
import com.meituan.android.pay.debugkit.main.order.refund.OrderRefundListFragment;
import com.meituan.android.pay.debugkit.main.paylater.OrderListActivity;
import com.meituan.android.pay.debugkit.main.paylater.PayLaterOrderListActivity;
import com.meituan.android.pay.debugkit.main.paylater.RefundListActivity;
import com.meituan.android.pay.debugkit.utils.router.OpenUtils;

/**
 * <AUTHOR>
 */
@Keep
public interface Jumper {
    // 后付
    default void gotoOrderList() {
        OpenUtils.openActivity(DebugManager.getActivity(), OrderListActivity.class);
    }

    default void gotoRefundList() {
        OpenUtils.openActivity(DebugManager.getActivity(), RefundListActivity.class);
    }

    default void gotoOrderRefund() {
        DebugManager.startActivity(PayDemoActivity.fragment(OrderRefundListFragment.class));
    }

    default void gotoPayLaterRefund() {
        OpenUtils.openActivity(DebugManager.getActivity(), PayLaterOrderListActivity.class);
    }
}
