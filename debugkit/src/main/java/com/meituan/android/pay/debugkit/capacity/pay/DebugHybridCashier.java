package com.meituan.android.pay.debugkit.capacity.pay;

import android.content.Context;
import android.support.annotation.Keep;
import android.text.TextUtils;

import com.meituan.android.hybridcashier.config.bean.EnableOptions;
import com.meituan.android.hybridcashier.config.bean.HybridCashierConfig;
import com.meituan.android.hybridcashier.config.bean.NSROptions;
import com.meituan.android.hybridcashier.debug.HybridCashierDebug;
import com.meituan.android.pay.base.utils.debug.PayDebugCenter;
import com.meituan.android.pay.base.utils.exception.Getter;
import com.meituan.android.pay.debugkit.DebugInfo;
import com.meituan.android.pay.debugkit.capacity.DebugInit;
import com.meituan.android.pay.debugkit.utils.struct.SavedData;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Keep
@ServiceLoaderInterface(key = "debug_hybrid_cashier", interfaceClass = DebugInit.class)
public class DebugHybridCashier implements DebugInit, HybridCashierDebug {
    private static final String REGEX_BETA_CASHIER_VERSION = "([0-9]+.){2,3}[0-9]+";

    private static final SavedData<Boolean> ENABLE_DEBUG =
            SavedData.just("enable_hybrid_cashier_debug", false);
    private static final SavedData<Boolean> DISABLE_HYBRID_CASHIER =
            SavedData.just("disable_hybrid_cashier", false);
    private static final SavedData<Boolean> DISABLE_OFFLINE_CHECK =
            SavedData.just("disable_offline_check", true);
    private static final SavedData<Boolean> DISABLE_NSR =
            SavedData.just("disable_nsr", true);
    private static final SavedData<String> PRESET_HYBRID_CASHIER_VERSION =
            SavedData.just("preset_hybrid_cashier_version", "");

    @Override
    public void onInit(Context context, DebugInfo debugInfo) {
        PayDebugCenter.register(HybridCashierDebug.class, this);
    }

    @Override
    public HybridCashierConfig debugConfig(HybridCashierConfig config) {
        if (config == null) {
            return null;
        }
        // disable
        config.setHybridCashierEnable(!DISABLE_HYBRID_CASHIER.getFormattedValue());
        // path
        String version = PRESET_HYBRID_CASHIER_VERSION.getFormattedValue();
        if (!TextUtils.isEmpty(version)) {
            String path = config.getHybridCashierPath();
            String curVersion = getHybridCashierVersion(path);
            config.setHybridCashierPath(path.replace(curVersion, version));
        }
        // offline
        EnableOptions enableOptions = config.getEnableOptions();
        if (enableOptions != null) {
            enableOptions.setCheckOfflinePackageEnable(!DISABLE_OFFLINE_CHECK.getFormattedValue());
        }
        // nsr
        NSROptions nsrOptions = config.getNeoConfigurations().getNsrOptions();
        if (nsrOptions != null) {
            nsrOptions.setNsrEnabled(!DISABLE_NSR.getFormattedValue());
        }
        return config;
    }

    @Override
    public void setDebugEnabled(boolean disabled) {
        ENABLE_DEBUG.setFormattedValue(disabled);
    }

    @Override
    public void setHybridCashierDisabled(boolean disabled) {
        DISABLE_HYBRID_CASHIER.setFormattedValue(disabled);
    }

    @Override
    public void setOfflineCheckDisabled(boolean disabled) {
        DISABLE_OFFLINE_CHECK.setFormattedValue(disabled);
    }

    @Override
    public void setNSRDisabled(boolean disabled) {
        DISABLE_NSR.setFormattedValue(disabled);
    }

    @Override
    public void setHybridCashierVersion(String version) {
        PRESET_HYBRID_CASHIER_VERSION.setFormattedValue(version);
    }

    @Override
    public boolean isAvailable(String key) {
        return ENABLE_DEBUG.getFormattedValue();
    }

    public static String getHybridCashierVersion(String path) {
        return Getter.getOrError(() -> {
            Pattern pattern = Pattern.compile(REGEX_BETA_CASHIER_VERSION);
            Matcher matcher = pattern.matcher(path);
            return matcher.find() ? matcher.group() : "";
        }, () -> "");
    }
}
