package com.meituan.android.pay.debugkit.main.paylater;

import android.net.Uri;
import android.os.Bundle;
import android.support.v7.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.pay.debugkit.main.paylater.model.RefundDetails;
import com.meituan.android.paybase.config.PayBaseConfig;
import com.meituan.android.paybase.dialog.ToastUtils;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.utils.CollectionUtils;
import com.meituan.android.paybase.utils.UriUtils;
import com.meituan.android.paycommon.lib.config.MTPayConfig;
import com.meituan.android.paycommon.lib.retrofit.PayRetrofit;
import com.meituan.android.pay.debugkit.R;
import com.meituan.android.pay.debugkit.main.activity.PayDebugActivity;
import com.meituan.android.pay.debugkit.main.data.remote.PayDemoRequestService;
import com.meituan.android.pay.debugkit.widget.old.PowerfulRecyclerView;
import com.meituan.android.pay.debugkit.widget.old.adapter.SimpleSwipeAndDragAdapter;
import com.meituan.android.pay.debugkit.widget.old.listener.OnItemTouchListener;
import com.meituan.android.pay.debugkit.widget.old.listener.OnLoadMoreListener;
import com.meituan.android.pay.debugkit.widget.old.listener.OnRefreshListener;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

public class RefundListActivity extends PayDebugActivity implements IRequestCallback, OnLoadMoreListener, OnRefreshListener {

    private static final int REQUEST_TAG_REFUND_LIST = 1;

    private PowerfulRecyclerView mPowerfulRecyclerView;
    private RecyclerView.Adapter mAdapter;
    private List<RefundDetails> mData = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.old_activity_refund_list);
        mPowerfulRecyclerView = findViewById(R.id.pull_refresh_refund_list);
        init();
    }

    @SuppressWarnings("unchecked")
    private void init() {
        mAdapter = new SimpleSwipeAndDragAdapter(mData) {
            @Override
            public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
                View view = LayoutInflater.from(RefundListActivity.this).inflate(R.layout.refund_list_item, parent, false);
                return new RecyclerView.ViewHolder(view) {
                };
            }

            @Override
            public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
                RefundDetails details = (RefundDetails) datas.get(position);
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                ((TextView) holder.itemView.findViewById(R.id.order_name)).setText("refundNo：" + details.getRefundNo());
                ((TextView) holder.itemView.findViewById(R.id.order_price)).setText(details.getMoneyCent() / 100f + "元");
                ((TextView) holder.itemView.findViewById(R.id.order_date)).setText(format.format(details.getAcceptTime() * 1000L));

                TextView payState = holder.itemView.findViewById(R.id.order_state);
                payState.setText("refundStatus: " + details.getRefundStatus());

            }
        };
        mPowerfulRecyclerView.setAdapter(mAdapter);
        mPowerfulRecyclerView.setRefreshEnable(true);
        mPowerfulRecyclerView.setLoadMoreEnable(true);
        mPowerfulRecyclerView.setOnRefreshListener(this);
        mPowerfulRecyclerView.setOnLoadMoreListener(this);
        mPowerfulRecyclerView.setOnItemTouchListener(new OnItemTouchListener() {

            @Override
            public void onClick(RecyclerView.ViewHolder viewHolder, int i) {
                RefundDetails details = mData.get(i);
                openRefundDetails(details);
            }

            @Override
            public boolean onLongClick(RecyclerView.ViewHolder viewHolder, int i, float v, float v1) {
                return false;
            }

            @Override
            public void onPress(RecyclerView.ViewHolder viewHolder, int i) {

            }

            @Override
            public void onUp(RecyclerView.ViewHolder viewHolder, int i) {

            }
        });

        queryOrderList(0);
    }

    // 参考文档：https://km.sankuai.com/page/15542531
    private void queryOrderList(int offset) {
        PayRetrofit.getInstance().create(
                        PayDemoRequestService.class, this, REQUEST_TAG_REFUND_LIST)
                .refundList(PayProvider.app().getUserId(), "10", String.valueOf(offset));
    }

    @Override
    public void onStartRefresh() {
        queryOrderList(0);
    }

    @Override
    public void onLoadMore() {
        queryOrderList(mData.size());
    }

    @SuppressWarnings("unchecked")
    @Override
    public void onRequestSucc(int i, Object o) {
        if (i == REQUEST_TAG_REFUND_LIST) {
            if (mPowerfulRecyclerView.isRefreshing()) {
                mData.clear();
            }
            List<RefundDetails> lists = (List<RefundDetails>) o;
            if (CollectionUtils.isEmpty(lists)) {
                ToastUtils.showSnackToast(this, "无数据");
                mPowerfulRecyclerView.stopRefresh();
                mPowerfulRecyclerView.stopLoadMore();
            } else {
                mData.addAll(lists);
                mAdapter.notifyDataSetChanged();
            }
        }
    }

    @Override
    public void onRequestException(int i, Exception e) {
        ToastUtils.showSnackToast(this, "数据异常：" + e.getMessage());
        finish();
    }

    @Override
    public void onRequestFinal(int i) {
        mPowerfulRecyclerView.stopRefresh();
        mPowerfulRecyclerView.stopLoadMore();
    }

    @Override
    public void onRequestStart(int i) {
    }

    @Override
    public void onDestroy() {
        mData.clear();
        super.onDestroy();
    }

    /**
     * 参考文档：
     * https://km.sankuai.com/page/57176315
     * @param details
     */
    private void openRefundDetails(RefundDetails details) {
        Uri.Builder builder = Uri.parse(PayBaseConfig.getProvider().getHost()).buildUpon();
        builder.appendPath("resource");
        builder.appendPath("balance");
        builder.appendPath("refund.html");
        builder.appendQueryParameter("token", MTPayConfig.getProvider().getUserToken());
        builder.appendQueryParameter("direction", "0");
        builder.appendQueryParameter("status", "2");
        builder.appendQueryParameter("trade_no", details.getTradeNo());
        builder.appendQueryParameter("refund_no", details.getRefundNo());
        builder.appendQueryParameter("reject_time", "0");
        builder.appendQueryParameter("apply_time", "0");
        builder.appendQueryParameter("delay_time", "0");
        UriUtils.open(this, builder.build().toString(), false);
    }
}