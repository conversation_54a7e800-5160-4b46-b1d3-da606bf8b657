package com.meituan.android.pay.debugkit.main.paylater;

import com.meituan.android.pay.debugkit.main.paylater.model.RefundInfo;
import com.meituan.android.pay.debugkit.main.paylater.model.TradeInfoVO;
import com.meituan.android.pay.debugkit.main.paylater.model.TradeOrderDTO;
import com.meituan.android.pay.debugkit.main.paylater.model.TradeOrderVO;
import com.sankuai.meituan.retrofit2.Call;
import com.sankuai.meituan.retrofit2.http.Field;
import com.sankuai.meituan.retrofit2.http.FieldMap;
import com.sankuai.meituan.retrofit2.http.FormUrlEncoded;
import com.sankuai.meituan.retrofit2.http.POST;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface PayLaterRequestService {

    @FormUrlEncoded
    @POST("/demo/trade/querytradebyuid")
    Call<List<TradeOrderVO>> queryOrderList(@Field("userid") String userid);


    @FormUrlEncoded
    @POST("/demo/trade/querypayinfobytradeno")
    Call<TradeInfoVO> queryOrderDetail(@Field("tradeNo") String tradeNo);

    @FormUrlEncoded
    @POST("/demo/trade/paylatercancel")
    Call<TradeOrderDTO> cancel(@Field("tradeNo") String tradeNo,
                               @Field("sellerId") long sellerId,
                               @Field("cancelFeeCent") String cancelFeeCent,
                               @FieldMap Map<String, Object> extras);

    @FormUrlEncoded
    @POST("/demo/trade/paylatercomplete")
    Call<TradeOrderDTO> complete(@Field("tradeNo") String tradeNo,
                                 @Field("completeFeeCent") String completeFeeCent,
                                 @FieldMap Map<String, Object> extras);

    @FormUrlEncoded
    @POST("/demo/refund/apply")
    Call<RefundInfo> refund(@FieldMap Map<String, Object> params);
}
