package com.meituan.android.pay.debugkit.utils.system;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;

import com.meituan.android.pay.debugkit.R;
import com.meituan.android.pay.debugkit.main.data.DataProvider;
import com.meituan.android.paycommon.lib.config.MTPayConfig;

import org.json.JSONException;
import org.json.JSONObject;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * Created by huangchao on 15-4-27.
 */
public class ParamCreateUtils {
    public static String signInMD5(JSONObject data, String key) throws JSONException {
        ArrayList<String> list = new ArrayList<String>();
        for (Iterator keys = data.keys(); keys.hasNext(); ) {
            list.add(keys.next().toString());
        }
        Collections.sort(list);
        String originStr = "";
        for (Iterator traversal = list.iterator(); traversal.hasNext(); ) {
            String jsonKey = traversal.next().toString();
            String jsonValue = data.get(jsonKey).toString();
            if (jsonValue.equals(""))//跳过value为空的key
                continue;
            originStr = originStr + jsonKey + "=" + jsonValue + "&";

        }
        originStr = originStr.substring(0, originStr.length() - 1) + key;
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(originStr.getBytes());
            byte[] signedData = md5.digest();
            StringBuffer strBuffer = new StringBuffer();
            for (int i = 0; i < signedData.length; i++) {
                String hex = Integer.toHexString(0xff & signedData[i]);
                if (hex.length() == 1) {
                    strBuffer.append('0');
                }
                strBuffer.append(hex);
            }
            return strBuffer.toString();
        } catch (NoSuchAlgorithmException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return null;

    }

    public static void appendBusinessParamsForGet(Uri.Builder uri, Map<String, String> param) {
        for (String key : param.keySet()) {
            uri.appendQueryParameter(key, param.get(key));
        }
    }

    /**
     * 日期转换成字符串
     * @param date
     * @return str
     */
    public static String DateToStr(Date date) {

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String str = format.format(date);
        return str;
    }

    /**
     * 字符串转换成日期
     * @param str
     * @return date
     */
    public static Date StrToDate(String str) {

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = format.parse(str);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    public static String intToTime(long millSec) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date(millSec * 1000);
        return format.format(date).substring(5, 16);
    }

    public static HashMap<String, String> setRequestForms(Context context, int payExpire, String payMoney) {
        HashMap<String, String> params4sign = new HashMap<String, String>();
        String outNo = "";
        if (TextUtils.isEmpty(outNo)) {
            outNo = "QA-" + System.currentTimeMillis() / 1000;
        }
        params4sign.put("out_no", outNo);
        if (!TextUtils.isEmpty(payMoney)) {
            params4sign.put("total_fee", payMoney);
        } else {
            params4sign.put("total_fee", "0.01");
        }
        params4sign.put("subject", "测试单标题");
        params4sign.put("body", "测试单描述");

        params4sign.put("partner_id", "2015010800000001");//放到首选项data中

//        params4sign.put("iph_pay_merchant_no", Settings.getInstance(context).getMerchantNo());

        DataProvider.order().getOrderParam("seller_id")
                .subscribe(s -> params4sign.put("iph_pay_merchant_no", s));

        params4sign.put("userid", MTPayConfig.getProvider().getUserId());//放到首选项data中
        params4sign.put("notify_url", "http://************:8080/cashier/paynotify");//放到首选项host中
        params4sign.put("orderid", "79401677");//放到首选项data中
        params4sign.put("head_info", "");

        if (payExpire > 0) {
            params4sign.put("pay_expire", String.valueOf(payExpire));
        }

        params4sign.put("order_ip", Long.toString(IPUtils.IP2Int(IPUtils.getLocalIP())));
        params4sign.put("_input_charset", "UTF-8");
        params4sign.put("return_url", "");

        params4sign.put("product_type", "");
        params4sign.put("magiccard", "");
        params4sign.put("point", "");
        params4sign.put("attach", "");
        params4sign.put("support_paytypes", "");
        params4sign.put("risk_param", "");

        DataProvider.order().getOrderParam("partner_key")
                .subscribe(strPartnerKey1 -> {
                    try {
                        params4sign.put("sign", ParamCreateUtils.signInMD5(new JSONObject(params4sign), strPartnerKey1));
                    } catch (JSONException e) {
                    }
                });

        params4sign.put("sign_type", "MD5");
        return params4sign;
    }
}
