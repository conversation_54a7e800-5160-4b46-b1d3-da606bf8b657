package com.meituan.android.pay.debugkit.widget.old.touchhelper;

import android.support.v7.widget.GridLayoutManager;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.support.v7.widget.StaggeredGridLayoutManager;
import android.support.v7.widget.helper.ItemTouchHelper;

import com.meituan.android.pay.debugkit.widget.old.adapter.ItemTouchAdapter;

/**
 * 开启滑动删除和item拖拽相关，外界无需关心
 */
public class ItemTouchHelperFactory {

    private static final String TAG = "ItemTouchHelperFactory";

    public static ItemTouchHelper.Callback createCallback(RecyclerView mRecycler, RecyclerView.LayoutManager manager, boolean shouldDrag, boolean shouldSwipe){
        if((manager instanceof GridLayoutManager || manager instanceof StaggeredGridLayoutManager) && mRecycler.getAdapter() instanceof ItemTouchAdapter){
            return new GridItemTouchHelperCallback((ItemTouchAdapter)mRecycler.getAdapter(),shouldDrag,shouldSwipe);
        }else if(manager instanceof LinearLayoutManager && mRecycler.getAdapter() instanceof ItemTouchAdapter){
            return new LinearLayoutItemTouchHelperCallback((ItemTouchAdapter)mRecycler.getAdapter(),shouldDrag,shouldSwipe);
        }

        throw new RuntimeException("invalid LayoutManager!it must be linear,grid or staggered");
    }
}
