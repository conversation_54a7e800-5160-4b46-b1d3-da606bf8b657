package com.meituan.android.pay.debugkit.env.manager;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.meituan.android.pay.base.utils.exception.Catch;
import com.meituan.android.pay.debugkit.DebugManager;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Horn环境管理器
 * 对应iOS中的Horn环境切换功能
 * 
 * <AUTHOR>
 * @since 2025/1/9
 */
public class HornEnvironmentManager {
    
    private static final String TAG = "HornEnvironmentManager";
    
    // SharedPreferences相关常量
    private static final String PREF_NAME = "horn_environment_config";
    private static final String KEY_ENABLED_HORN_TYPES = "enabled_horn_types";
    private static final String KEY_HORN_DEBUG_ENABLED = "horn_debug_enabled";
    private static final String KEY_LAST_HORN_ENV = "last_horn_env";
    
    private final Context context;
    private final SharedPreferences preferences;
    private final Gson gson;
    
    // 支持的Horn类型
    private final Set<String> enabledHornTypes;

    public HornEnvironmentManager(Context context) {
        this.context = context;
        this.preferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        this.gson = new Gson();
        this.enabledHornTypes = new HashSet<>();
        
        // 加载已启用的Horn类型
        loadEnabledHornTypes();
    }

    /**
     * 切换Horn环境
     * 对应iOS的switchHornEnv方法
     */
    public void switchHornEnv(String hornEnv) {
        try {
            if (TextUtils.isEmpty(hornEnv)) {
                return;
            }
            
            // 解析Horn环境字符串 (支持中英文逗号分隔)
            List<String> hornTypes = parseHornEnvironments(hornEnv);
            
            // 启用指定的Horn类型
            for (String hornType : hornTypes) {
                if (!TextUtils.isEmpty(hornType.trim())) {
                    enableHornDebug(hornType.trim());
                }
            }
            
            // 保存最后设置的Horn环境
            saveLastHornEnvironment(hornEnv);
            
        } catch (Exception e) {
            Catch.with(e).report(TAG);
        }
    }

    /**
     * 解析Horn环境字符串
     */
    private List<String> parseHornEnvironments(String hornEnv) {
        List<String> result = new ArrayList<>();
        
        // 支持中英文逗号分隔
        String[] separators = {",", "，"};
        String[] parts = hornEnv.split("[,，]");
        
        for (String part : parts) {
            String trimmed = part.trim();
            if (!TextUtils.isEmpty(trimmed)) {
                result.add(trimmed);
            }
        }
        
        return result;
    }

    /**
     * 启用Horn调试
     * 对应iOS的[SAKHorn setEnableDebug:YES forType:obj]
     */
    private void enableHornDebug(String hornType) {
        try {
            // 添加到启用列表
            enabledHornTypes.add(hornType);
            
            // 保存到SharedPreferences
            saveEnabledHornTypes();
            
            // 设置Horn调试开关
            setHornDebugEnabled(hornType, true);
            
        } catch (Exception e) {
            Catch.with(e).report(TAG);
        }
    }

    /**
     * 设置Horn调试开关
     */
    private void setHornDebugEnabled(String hornType, boolean enabled) {
        String key = "horn_debug_" + hornType;
        
        preferences.edit()
                .putBoolean(key, enabled)
                .apply();
                
        DebugManager.getStorage().setBoolean(key, enabled);
    }

    /**
     * 保存已启用的Horn类型
     */
    private void saveEnabledHornTypes() {
        try {
            String json = gson.toJson(new ArrayList<>(enabledHornTypes));
            preferences.edit()
                    .putString(KEY_ENABLED_HORN_TYPES, json)
                    .putBoolean(KEY_HORN_DEBUG_ENABLED, !enabledHornTypes.isEmpty())
                    .apply();
                    
            DebugManager.getStorage().setString(KEY_ENABLED_HORN_TYPES, json);
            DebugManager.getStorage().setBoolean(KEY_HORN_DEBUG_ENABLED, !enabledHornTypes.isEmpty());
            
        } catch (Exception e) {
            Catch.with(e).report(TAG);
        }
    }

    /**
     * 加载已启用的Horn类型
     */
    private void loadEnabledHornTypes() {
        try {
            String json = preferences.getString(KEY_ENABLED_HORN_TYPES, "");
            if (!TextUtils.isEmpty(json)) {
                Type listType = new TypeToken<List<String>>(){}.getType();
                List<String> hornTypes = gson.fromJson(json, listType);
                if (hornTypes != null) {
                    enabledHornTypes.addAll(hornTypes);
                }
            }
        } catch (Exception e) {
            Catch.with(e).report(TAG);
        }
    }

    /**
     * 保存最后设置的Horn环境
     */
    private void saveLastHornEnvironment(String hornEnv) {
        preferences.edit()
                .putString(KEY_LAST_HORN_ENV, hornEnv)
                .putLong("horn_env_switch_time", System.currentTimeMillis())
                .apply();
    }

    /**
     * 检查指定Horn类型是否启用
     */
    public boolean isHornTypeEnabled(String hornType) {
        return enabledHornTypes.contains(hornType);
    }

    /**
     * 检查Horn调试是否启用
     */
    public boolean isHornDebugEnabled() {
        return preferences.getBoolean(KEY_HORN_DEBUG_ENABLED, false);
    }

    /**
     * 获取已启用的Horn类型列表
     */
    public List<String> getEnabledHornTypes() {
        return new ArrayList<>(enabledHornTypes);
    }

    /**
     * 获取最后设置的Horn环境
     */
    public String getLastHornEnvironment() {
        return preferences.getString(KEY_LAST_HORN_ENV, "");
    }

    /**
     * 禁用指定Horn类型
     */
    public void disableHornType(String hornType) {
        enabledHornTypes.remove(hornType);
        setHornDebugEnabled(hornType, false);
        saveEnabledHornTypes();
    }

    /**
     * 禁用所有Horn类型
     */
    public void disableAllHornTypes() {
        for (String hornType : new ArrayList<>(enabledHornTypes)) {
            setHornDebugEnabled(hornType, false);
        }
        enabledHornTypes.clear();
        saveEnabledHornTypes();
    }

    /**
     * 重置Horn环境配置
     */
    public void resetHornEnvironment() {
        disableAllHornTypes();
        preferences.edit().clear().apply();
        DebugManager.getStorage().remove(KEY_ENABLED_HORN_TYPES);
        DebugManager.getStorage().remove(KEY_HORN_DEBUG_ENABLED);
    }

    /**
     * 获取Horn环境切换时间
     */
    public long getHornEnvironmentSwitchTime() {
        return preferences.getLong("horn_env_switch_time", 0);
    }
}
