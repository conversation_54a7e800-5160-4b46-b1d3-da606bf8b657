package com.meituan.android.pay.debugkit.main.paylater;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.widget.LinearLayout;

import com.meituan.android.pay.debugkit.main.paylater.model.OrderInfo;
import com.meituan.android.pay.debugkit.main.paylater.model.PayOrderVO;
import com.meituan.android.pay.debugkit.main.paylater.model.TradeInfoVO;
import com.meituan.android.pay.debugkit.main.paylater.model.TradeOrderVO;
import com.meituan.android.paybase.config.PayBaseConfig;
import com.meituan.android.paybase.dialog.ProgressController;
import com.meituan.android.paybase.dialog.ProgressMode;
import com.meituan.android.paybase.dialog.ProgressType;
import com.meituan.android.paybase.retrofit.IRequestCallback;
import com.meituan.android.paybase.utils.UriUtils;
import com.meituan.android.paycommon.lib.retrofit.PayRetrofit;
import com.meituan.android.paycommon.lib.utils.ExceptionUtils;
import com.meituan.android.pay.debugkit.R;
import com.meituan.android.pay.debugkit.main.activity.PayDebugActivity;
import com.meituan.android.pay.debugkit.widget.old.views.SimpleContentView;
import com.meituan.android.pay.debugkit.widget.old.views.SimpleDividerView;
import com.meituan.android.pay.debugkit.widget.old.views.SimpleEditView;
import com.meituan.android.pay.debugkit.widget.old.views.SimpleMultiButtonView;
import com.meituan.android.pay.debugkit.widget.old.views.SimpleViewTools;

import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class PayLaterOrderDetailActivity extends PayDebugActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setTitle("后付订单详情页");
        setContentView(R.layout.old_activity_paylater_order_detail);
        init();
    }

    private void init() {
        Intent intent = getIntent();
        if (intent == null) {
            return;
        }
        LinearLayout detailView = findViewById(R.id.demo_pay_later_order_detail);
        LinearLayout rootView = findViewById(R.id.demo_pay_later_order);
        Controller controller = new Controller(this, detailView, rootView, (TradeOrderVO) intent.getSerializableExtra("PayLaterOrderDetail"));
        controller.queryDetail();
    }

    public static class Controller implements IRequestCallback {
        private static final int REQ_DETAIL = 0x1;
        private static final int REQ_CANCEL = 0x2;
        private static final int REQ_COMPLETE = 0x3;
        private static final int REQ_REFUND = 0x4;

        private final Activity activity;
        private final LinearLayout detailView;
        private final LinearLayout rootView;
        private final TradeOrderVO initData;
        private TradeInfoVO detailData;

        private SimpleViewTools simpleViewTools;

        public Controller(Activity activity, LinearLayout detailView, LinearLayout rootView, TradeOrderVO initData) {
            this.activity = activity;
            this.detailView = detailView;
            this.rootView = rootView;
            this.initData = initData;
        }

        private void refreshView(TradeInfoVO detailData) {
            if (detailView == null || !TradeInfoVO.isLegal(detailData)) {
                return;
            }
            this.detailView.removeAllViews();
            this.rootView.removeAllViews();
            this.detailData = detailData;
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA);
            SimpleViewTools.Builder orderBuilder = SimpleViewTools.builder(detailView);
            TradeOrderVO detailOrder = detailData.getTradeOrderVO();
            orderBuilder.addSimpleView(new SimpleDividerView.Bean("交易单信息", false, true))
                    .addSimpleView(new SimpleContentView.Bean("order_subject", "标题", detailOrder.getOrderSubject()))
                    .addSimpleView(new SimpleContentView.Bean("tradeNo", "交易单号", detailOrder.getTradeNo()))
                    .addSimpleView(new SimpleContentView.Bean("transStatus", "交易状态", String.valueOf(detailOrder.getTransStatusContent())))
                    .addSimpleView(new SimpleContentView.Bean("transFeeCent", "下单金额", detailOrder.getTransFeeCentString()))
                    .addSimpleView(new SimpleContentView.Bean("transCancelFund", "撤销金额", detailOrder.getTransCancelFundString()))
                    .addSimpleView(new SimpleContentView.Bean("transSuccessTime", "交易成功时间", detailOrder.getTransSuccessTime() != 0 ? format.format(detailOrder.getTransSuccessTime()) : "-")); // 如果非预期的内容，则展示 "-"
            for (PayOrderVO payOrderVO : detailData.getPayOrderVOs()) {
                String type = "-";
                if (payOrderVO.isAdvancePay()) {
                    type = "垫资";
                }
                if (payOrderVO.isCreditOrder()) {
                    type = "赊销";
                }
                orderBuilder.addSimpleView(new SimpleDividerView.Bean("支付单信息-" + getPayResultByStatus(payOrderVO.getStatus()), true, true))
                        .addSimpleView(new SimpleContentView.Bean("body", "描述", payOrderVO.getBody()))
                        .addSimpleView(new SimpleContentView.Bean("type", "类型", type))
                        .addSimpleView(new SimpleContentView.Bean("orderTime", "金额", String.valueOf(payOrderVO.getOutMoney())))
                        .addSimpleView(new SimpleContentView.Bean("payTime", "支付成功时间", String.valueOf(payOrderVO.getPayTime())));
            }
            orderBuilder.build();
            SimpleViewTools.Builder buttonBuilder = SimpleViewTools.builder(rootView);
            List<SimpleMultiButtonView.ButtonBean> buttonBeanList = new ArrayList<>();
            if (detailOrder.showComplete()) {
                buttonBeanList.add(new SimpleMultiButtonView.ButtonBean("完结", v -> complete()));
            }
            if (detailOrder.showCancel()) {
                buttonBeanList.add(new SimpleMultiButtonView.ButtonBean("撤销", v -> cancel()));
            }
            if (detailOrder.showRefund()) {
                buttonBeanList.add(new SimpleMultiButtonView.ButtonBean("退款", v -> refund()));
            }
            if (detailOrder.showRepayment()) {
                buttonBeanList.add(new SimpleMultiButtonView.ButtonBean("还款", v -> repayment()));
            }
            buttonBuilder.addSimpleView(new SimpleDividerView.Bean("按钮", false, false));
            if (detailOrder.showComplete() || detailOrder.showCancel()) {
                buttonBuilder.addSimpleView(new SimpleEditView.Bean("feeCent", "金额", String.valueOf(detailOrder.getTransFeeCent()), "分"));
            }
            simpleViewTools = buttonBuilder.addSimpleView(new SimpleMultiButtonView.Bean(buttonBeanList))
                    .build();
        }

        private void queryDetail() {
            if (initData != null) {
                PayRetrofit.getInstance().create(PayLaterRequestService.class, this, REQ_DETAIL).queryOrderDetail(
                        initData.getTradeNo());
            }
        }

        private void cancel() {
            if (TradeInfoVO.isLegal(detailData)) {
                PayRetrofit.getInstance().create(PayLaterRequestService.class, this, REQ_CANCEL).cancel(
                        detailData.getTradeOrderVO().getTradeNo(),
                        detailData.getTradeOrderVO().getSellerId(),
                        simpleViewTools.value("feeCent"),
//                        detailData.getTradeOrderVO().getTransFeeCent(),
                        new HashMap<>());
            }
        }

        private void complete() {
            if (TradeInfoVO.isLegal(detailData)) {
                PayRetrofit.getInstance().create(PayLaterRequestService.class, this, REQ_COMPLETE).complete(
                        detailData.getTradeOrderVO().getTradeNo(),
                        simpleViewTools.value("feeCent"),
//                        detailData.getTradeOrderVO().getTransFeeCent(),
                        new HashMap<>());
            }
        }

        private void refund() {
            if (TradeInfoVO.isLegal(detailData)) {
                PayRetrofit.getInstance().create(PayLaterRequestService.class, this, REQ_REFUND).refund(
                        getParams(detailData.getTradeOrderVO()));
            }
        }

        public static Map<String, Object> getParams(TradeOrderVO orderInfo) {
            HashMap<String, Object> params = new HashMap<>();
            params.put("charset", "UTF-8");
            params.put("tradeNo", orderInfo.getTradeNo());
            params.put("refundFeeCent", orderInfo.getTransFeeCent());
            params.put("refundNo", orderInfo.getOrderId() + System.currentTimeMillis() / 1000);
            params.put("riskParams", new JSONObject());
            params.put("sellerId", orderInfo.getSellerId());
            return params;
        }

        private void repayment() {
            if (TradeInfoVO.isLegal(detailData)) {
                Uri.Builder builder = Uri.parse("meituanpayment://halfpage/launch?target_scene=repayment").buildUpon();
                String url = PayBaseConfig.getProvider().getHost() + "/pay-defer/payment" +
                        ".html?creditType=delaypay&hideHalfPage=1";
                url = url + "&tradeno=" + detailData.getTradeOrderVO().getTradeNo();
                builder.appendQueryParameter("url", url);
                UriUtils.open(activity, builder.toString());
            }
        }

        @Override
        public void onRequestSucc(int tag, Object obj) {
            if (tag == REQ_DETAIL) {
                refreshView((TradeInfoVO) obj);
            } else {
                queryDetail();
            }
        }

        @Override
        public void onRequestException(int tag, Exception e) {
            ExceptionUtils.handleException(activity, e, null);
        }

        @Override
        public void onRequestFinal(int tag) {
            ProgressController.of(activity).hide();
        }

        @Override
        public void onRequestStart(int tag) {
            ProgressController.of(activity)
                    .setTimeout(10000)
                    .setProgressType(ProgressType.DEFAULT)
                    .show(ProgressMode.ASYNC);
        }
    }

    public static String getPayResultByStatus(int status) {
        String result = "";
        switch (status) {
            case OrderInfo.PAY_STATUS_NO:
                result = "未支付";
                break;
            case OrderInfo.PAY_STATUS_CONFIRM:
                result = "用户已确认";
                break;
            case OrderInfo.PAY_STATUS_COMPLETED:
                result = "商户已完结";
                break;
            case OrderInfo.PAY_STATUS_CLOSE:
                result = "支付关闭";
                break;
            case OrderInfo.PAY_STATUS_FAIL:
                result = "支付失败";
                break;
            case OrderInfo.PAY_STATUS_SUCC:
                result = "支付成功";
                break;
            case OrderInfo.PAY_STATUS_NOTIFY_SUCC:
                result = "重复支付退款";
                break;
            case OrderInfo.PAY_STATUS_REFUND_PART:
                result = "部分退款";
                break;
            case OrderInfo.PAY_STATUS_REFUND_ALL:
                result = "全部退款";
                break;
            default:
                result = "未知状态";
                break;
        }
        return result + "-" + +status;
    }
}
