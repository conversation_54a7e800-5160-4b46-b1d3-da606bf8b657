package com.meituan.android.pay.debugkit.capacity;

import android.content.Context;

import com.meituan.android.pay.base.utils.compute.NumberParser;
import com.meituan.android.pay.base.utils.log.PayLogger;
import com.meituan.android.pay.debugkit.DebugMonitor;
import com.meituan.android.pay.debugkit.DebugInfo;
import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.pay.debugkit.utils.struct.SavedData;
import com.meituan.android.yoda.plugins.NetEnvHook;
import com.meituan.android.yoda.plugins.YodaPlugins;
import com.sankuai.meituan.android.knb.KNBWebManager;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * <AUTHOR>
 */
@ServiceLoaderInterface(key = "debug_setting", interfaceClass = DebugInit.class)
public class DebugSetting implements DebugInit {

    @Override
    public void onInit(Context context, DebugInfo debugInfo) {
        SavedData.create("enable_knb_debug",
                KNBWebManager::isDebug, KNBWebManager::enableDebugMode);
        SavedData.create("enable_pay_log",
                PayLogger::isDebug, PayLogger::setDebug);
        SavedData.just("enable_three_point_debug", false, enable ->
                DebugMonitor.set3PointDebug(DebugManager.getApplication(), enable));
        SavedData.just("enable_debug_toast", false);
        SavedData.create("yoda_env",
                DebugSetting::getYodaEnv, DebugSetting::setYodaEnv);
    }

    public static String getYodaEnv() {
        int env = YodaPlugins.getInstance().getNetEnvHook().getNetEnv();
        return String.valueOf(env);
    }

    public static void setYodaEnv(String env) {
        YodaPlugins.getInstance().registerNetEnvHook(new NetEnvHook() {
            @Override
            public int getNetEnv() {
                return NumberParser.parseInt(env);
            }
        });
    }

    public static boolean enableDebugToast() {
        return SavedData.isData("enable_debug_toast");
    }
}
