package com.meituan.android.pay.debugkit.utils.mvvm;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.pay.base.utils.compute.NumberParser;

/**
 * <AUTHOR>
 */
public class BindingActions {
    public static void loadImage(ImageView imageView, String url) {
        PayProvider.module().getImageLoader().load(url).into(imageView);
    }

    public static void setValueVisibility(View view, Object value) {
        if (view != null) {
            view.setVisibility(isVisible(value) ? View.VISIBLE : View.GONE);
        }
    }

    public static void setValueVisibilityWithLayout(View view, Object value) {
        if (view != null) {
            view.setVisibility(isVisible(value) ? View.VISIBLE : View.INVISIBLE);
        }
    }

    public static void setValueVisibilityReversed(View view, Object value) {
        if (view != null) {
            view.setVisibility(isVisible(value) ? View.GONE : View.VISIBLE);
        }
    }

    public static boolean isVisible(Object value) {
        if (value instanceof Boolean) {
            return (boolean) value;
        } else if (value instanceof String) {
            return !TextUtils.isEmpty((String) value);
        } else {
            return value != null;
        }
    }

    public static void setLayoutWeight(View view, Object weight) {
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) view.getLayoutParams();
        layoutParams.weight = NumberParser.parseInt(String.valueOf(weight));
        view.setLayoutParams(layoutParams);
    }
}