package com.meituan.android.pay.debugkit.utils.network.encrypt;

import android.text.TextUtils;

import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.pay.base.utils.function.ArrayUtils;
import com.meituan.android.pay.base.utils.function.Cond;
import com.meituan.android.pay.debugkit.utils.struct.Stream;
import com.meituan.android.payguard.RequestCryptUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class PayEncryptor implements PayEncrypt {

    public static String onlyAesKey() {
        return encryptFrom(null).getAesKey();
    }

    public static Result encryptFrom(Map<String, String> params) {
        return encrypt(params);
    }

    /**
     * 加密逻辑
     * @param params 需要加密的key、value
     * @return 加密结果
     */
    private static Result encrypt(Map<String, String> params) throws PayEncryptException {
        Map<String, String> encryptingParams = Cond.notNullOrCallable(params, HashMap::new);
        List<String> encryptingKeys = new ArrayList<>();
        List<String> encryptingValues = new ArrayList<>();

        Stream.on(encryptingParams)
                .filter(Stream.Entry::isKVNonNull)
                .foreach(entry -> {
                    encryptingKeys.add(entry.getKey());
                    encryptingValues.add(entry.getValue());
                });

        // 【加密逻辑】
        String[] result;
        try {
            result = RequestCryptUtils.wrapEncryptRequestWithRandom(
                    PayProvider.app().getUUID(), // uuid
                    String.valueOf(System.currentTimeMillis()), // time
                    ArrayUtils.list2Array(encryptingValues, String.class), // content
                    ENCRYPT_SCENE_CASHIER); //scene
        } catch (Throwable t) {
            throw new PayEncryptException(ERROR_CODE_ENCRYPT, ERROR_MSG_ENCRYPT, t);
        }
        if (!ArrayUtils.isIndex(result, 1)) { // 加密结果长度不满足要求
            throw new PayEncryptException(ERROR_CODE_ENCRYPT_RESULT, ERROR_MSG_ENCRYPT_RESULT);
        }

        String aesKey = ArrayUtils.getReversed(result, 0); // 最后一位是aesKey
        String encryptAesKey = ArrayUtils.getReversed(result, 1); // 倒数第二位是加密的 aesKey
        List<String> encryptedValues = ArrayUtils.copyList(result, 0, result.length - 2); // 剩下的是加密的内容
        if (encryptingKeys.size() != encryptedValues.size()) {
            throw new PayEncryptException(ERROR_CODE_ENCRYPT_RESULT_CHECK, ERROR_MSG_ENCRYPT_RESULT_CHECK);
        }
        // 覆盖被加密的 value
        Stream.index(encryptingKeys)
                .foreach(item -> {
                    String value = encryptingValues.get(item.index);
                    encryptingParams.put(item.value, value);
                });
        // 添加加密的 aesKey
        encryptingParams.put(KEY_ENCRYPT_KEY, encryptAesKey);
        encryptingParams.put(KEY_ENCRYPT_TYPE, ENCRYPT_TYPE_AES);

        return new Result(aesKey, encryptingParams);
    }

    /**
     * 解密逻辑
     * @param aesKey          加密返回的 aesKey，用于解密
     * @param encryptedString 被加密的字符串，用于解密
     * @return 被解密的字符串
     */
    public static String decrypt(String aesKey, String encryptedString) throws PayEncryptException {
        if (TextUtils.isEmpty(encryptedString)) {
            throw new PayEncryptException(ERROR_CODE_DECRYPT_PARAMS, ERROR_MSG_DECRYPT_PARAMS);
        }
        try {
            String[] decryptedResult = RequestCryptUtils
                    .wrapDecryptRequest(aesKey, ArrayUtils.asArray(encryptedString));
            if (ArrayUtils.isSingle(decryptedResult)) {
                return decryptedResult[0];
            } else {
                throw new PayEncryptException(ERROR_CODE_DECRYPT_RESULT, ERROR_MSG_DECRYPT_RESULT);
            }
        } catch (Exception e) {
            throw new PayEncryptException(ERROR_CODE_DECRYPT, ERROR_MSG_DECRYPT);
        }
    }

}
