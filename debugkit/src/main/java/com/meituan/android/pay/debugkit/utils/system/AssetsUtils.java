package com.meituan.android.pay.debugkit.utils.system;

import android.content.res.AssetManager;
import android.text.TextUtils;

import com.meituan.android.pay.base.context.PayProvider;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;

import rx.Observable;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * <AUTHOR>
 */
public class AssetsUtils {
    public static AssetManager manager() {
        return PayProvider.getContext().getAssets();
    }

    public static String get(String fileName) {
        try (InputStream inputStream = manager().open(fileName)) {
            return get(inputStream);
        } catch (IOException e) {
            return null;
        }
    }

    public static String get(InputStream inputStream) {
        if (inputStream != null) {
            try (BufferedInputStream bis = new BufferedInputStream(inputStream)) {
                byte[] bytes = new byte[inputStream.available()];
                for (int res = 0; res != -1; ) {
                    res = bis.read(bytes);
                }
                return new String(bytes);
            } catch (IOException e) {
                return null;
            }
        }
        return null;
    }

    public static Observable<String> observable(String fileName) {
        return Observable.create((Observable.OnSubscribe<String>) subscriber -> {
                    String result = AssetsUtils.get(fileName);
                    if (!TextUtils.isEmpty(result)) {
                        subscriber.onNext(result);
                    } else {
                        subscriber.onError(new IOException("empty"));
                    }
                    subscriber.onCompleted();
                }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
    }
}
