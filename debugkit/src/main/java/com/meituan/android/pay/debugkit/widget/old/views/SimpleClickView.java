package com.meituan.android.pay.debugkit.widget.old.views;

import android.content.Context;
import android.support.annotation.Nullable;
import android.support.v4.content.ContextCompat;
import android.util.AttributeSet;
import android.view.View;

import com.meituan.android.pay.debugkit.widget.old.CellView;
import com.meituan.android.pay.debugkit.R;

/**
 * <AUTHOR>
 */
public class SimpleClickView extends CellView implements SimpleView<SimpleClickView.Bean> {
    public SimpleClickView(Context context) {
        super(context);
    }

    public SimpleClickView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public SimpleClickView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void refresh(Bean bean) {
        if (bean == null) {
            return;
        }
        setTitleColor(ContextCompat.getColor(getContext(), R.color.cashier__gray));
        setTitle(bean.title);
        setButtonClick(bean.button, bean.listener);
    }

    @Override
    public boolean flip() {
        setVisibility(getVisibility() == VISIBLE ? GONE : VISIBLE);
        return true;
    }

    @Override
    public View getView() {
        return this;
    }

    public static class Holder implements SimpleViewHolder {
        private SimpleClickView view;

        private final Bean bean;

        public Holder(Bean bean) {
            this.bean = bean;
        }

        @Override
        public SimpleView<Bean> getView(Context context) {
            if (view != null) {
                return view;
            }
            this.view = new SimpleClickView(context);
            view.refresh(bean);
            return view;
        }

        @Override
        public String key() {
            return bean.key;
        }

        @Override
        public String value() {
            return bean.button;
        }
    }

    public static class Bean implements SimpleViewBean {
        private final String key;

        private String title;

        private String button;

        private OnClickListener listener;

        public Bean(String key, String title, String button, OnClickListener listener) {
            this.key = key;
            this.title = title;
            this.button = button;
            this.listener = listener;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getButton() {
            return button;
        }

        public void setButton(String button) {
            this.button = button;
        }

        public OnClickListener getListener() {
            return listener;
        }

        public void setListener(OnClickListener listener) {
            this.listener = listener;
        }

        @Override
        public SimpleViewHolder holder() {
            return new Holder(this);
        }
    }
}
