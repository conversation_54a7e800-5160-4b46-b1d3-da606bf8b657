package com.meituan.android.pay.debugkit.utils.network.retrofit.callback;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class CallInfo {
    private int tag;
    private final Map<String, String> statics;

    public CallInfo() {
        this.tag = 0;
        this.statics = new ConcurrentHashMap<>();
    }

    public CallInfo(int tag) {
        this.tag = tag;
        this.statics = new ConcurrentHashMap<>();
    }

    public int getTag() {
        return tag;
    }

    public Map<String, String> getStatics() {
        return statics;
    }

    public void setTag(int tag) {
        this.tag = tag;
    }
}
