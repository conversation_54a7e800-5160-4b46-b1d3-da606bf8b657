package com.meituan.android.pay.debugkit.env.manager;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.meituan.android.pay.base.utils.exception.Catch;
import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.pay.debugkit.env.model.LaneEnvironmentModule;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

/**
 * 泳道环境管理器
 * 对应iOS中的泳道环境切换功能
 * 
 * <AUTHOR>
 * @since 2025/1/9
 */
public class LaneEnvironmentManager {
    
    private static final String TAG = "LaneEnvironmentManager";
    
    // SharedPreferences相关常量
    private static final String PREF_NAME = "lane_environment_config";
    private static final String KEY_CACHE_ARRAY = "cache_array";
    private static final String KEY_CURRENT_LANE_PATH = "current_lane_path";
    private static final String KEY_ENVIRONMENT_ID = "environment_id";
    
    // 默认URL模板
    private static final String DEFAULT_URL = "http://stable.pay.test.sankuai.com";
    private static final String LANE_URL_TEMPLATE = "http://%s-sl-stable.pay.test.sankuai.com";
    
    private final Context context;
    private final SharedPreferences preferences;
    private final int globalEnvironmentId;
    private final Gson gson;
    
    // 缓存的环境模块列表 (对应iOS的cacheArray)
    private List<LaneEnvironmentModule> cacheArray;

    public LaneEnvironmentManager(Context context, int globalEnvironmentId) {
        this.context = context;
        this.globalEnvironmentId = globalEnvironmentId;
        this.preferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        this.gson = new Gson();
        this.cacheArray = new ArrayList<>();
        
        // 加载缓存的环境配置
        loadCachedEnvironments();
    }

    /**
     * 切换泳道环境
     * 对应iOS的switchLaneEnv方法
     */
    public void switchLaneEnv(String lanePath) {
        try {
            if (TextUtils.isEmpty(lanePath)) {
                return;
            }
            
            // 如果缓存为空，先初始化默认环境
            if (cacheArray.isEmpty()) {
                initializeDefaultEnvironments();
            }
            
            // 更新泳道环境
            updateLaneEnv(lanePath);
            
            // 保存当前泳道路径
            preferences.edit()
                    .putString(KEY_CURRENT_LANE_PATH, lanePath)
                    .apply();
                    
        } catch (Exception e) {
            Catch.with(e).report(TAG);
        }
    }

    /**
     * 更新泳道环境
     * 对应iOS的updateLaneEnv方法
     */
    private void updateLaneEnv(String lanePath) {
        try {
            String effectUrl;
            
            if ("default".equals(lanePath)) {
                effectUrl = DEFAULT_URL;
            } else {
                effectUrl = String.format(LANE_URL_TEMPLATE, lanePath);
            }
            
            // 更新所有环境模块的URL
            for (LaneEnvironmentModule module : cacheArray) {
                module.setEffectUrl(effectUrl);
            }
            
            // 保存更新后的环境配置
            saveEnvironmentModules();
            
            // 设置默认环境ID
            setDefaultEnvironmentId(globalEnvironmentId);
            
        } catch (Exception e) {
            Catch.with(e).report(TAG);
        }
    }

    /**
     * 初始化默认环境
     */
    private void initializeDefaultEnvironments() {
        cacheArray.clear();
        
        // 创建默认的环境模块 (模拟iOS中的两个模块)
        LaneEnvironmentModule module1 = new LaneEnvironmentModule();
        module1.setModuleId(1);
        module1.setModuleName("支付模块1");
        module1.setEffectUrl(DEFAULT_URL);
        
        LaneEnvironmentModule module2 = new LaneEnvironmentModule();
        module2.setModuleId(2);
        module2.setModuleName("支付模块2");
        module2.setEffectUrl(DEFAULT_URL);
        
        cacheArray.add(module1);
        cacheArray.add(module2);
    }

    /**
     * 保存环境模块配置
     */
    private void saveEnvironmentModules() {
        try {
            String json = gson.toJson(cacheArray);
            preferences.edit()
                    .putString(KEY_CACHE_ARRAY, json)
                    .apply();
                    
            // 同时保存到DebugManager的存储中
            DebugManager.getStorage().setString("lane_environment_modules", json);
            
        } catch (Exception e) {
            Catch.with(e).report(TAG);
        }
    }

    /**
     * 加载缓存的环境配置
     */
    private void loadCachedEnvironments() {
        try {
            String json = preferences.getString(KEY_CACHE_ARRAY, "");
            if (!TextUtils.isEmpty(json)) {
                Type listType = new TypeToken<List<LaneEnvironmentModule>>(){}.getType();
                cacheArray = gson.fromJson(json, listType);
                if (cacheArray == null) {
                    cacheArray = new ArrayList<>();
                }
            }
        } catch (Exception e) {
            Catch.with(e).report(TAG);
            cacheArray = new ArrayList<>();
        }
    }

    /**
     * 设置默认环境ID
     */
    private void setDefaultEnvironmentId(int environmentId) {
        preferences.edit()
                .putInt(KEY_ENVIRONMENT_ID, environmentId)
                .apply();
                
        // CIPStorageCenter没有setInt方法，使用setString代替
        DebugManager.getStorage().setString("default_environment_id", String.valueOf(environmentId));
    }

    /**
     * 获取当前泳道路径
     */
    public String getCurrentLanePath() {
        return preferences.getString(KEY_CURRENT_LANE_PATH, "default");
    }

    /**
     * 获取当前环境模块列表
     */
    public List<LaneEnvironmentModule> getCurrentEnvironmentModules() {
        return new ArrayList<>(cacheArray);
    }

    /**
     * 获取默认环境ID
     */
    public int getDefaultEnvironmentId() {
        return preferences.getInt(KEY_ENVIRONMENT_ID, globalEnvironmentId);
    }

    /**
     * 重置泳道环境
     */
    public void resetLaneEnvironment() {
        cacheArray.clear();
        preferences.edit().clear().apply();
        DebugManager.getStorage().remove("lane_environment_modules");
        DebugManager.getStorage().remove("default_environment_id");
    }
}
