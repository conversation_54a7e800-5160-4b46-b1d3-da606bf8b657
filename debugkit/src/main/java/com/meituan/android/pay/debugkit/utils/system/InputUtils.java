package com.meituan.android.pay.debugkit.utils.system;

import android.content.Context;
import android.os.IBinder;
import android.view.View;
import android.view.inputmethod.InputMethodManager;

/**
 * <AUTHOR>
 */
public class InputUtils {

    public static void hideKeyBoard(View view) {
        if (view == null) {
            return;
        }
        Context context = view.getContext();
        IBinder windowToken = view.getWindowToken();
        if (context != null && windowToken != null) {
            InputMethodManager inputMethodManager = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
            inputMethodManager.hideSoftInputFromWindow(windowToken, 0);
        }
    }
}
