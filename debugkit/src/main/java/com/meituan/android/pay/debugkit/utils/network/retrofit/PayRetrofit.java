package com.meituan.android.pay.debugkit.utils.network.retrofit;

import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.pay.base.utils.cache.MapCache;
import com.meituan.android.pay.base.utils.serialize.GsonProvider;
import com.meituan.android.pay.debugkit.utils.network.retrofit.factory.PayCallFactory;
import com.meituan.android.pay.debugkit.utils.network.retrofit.interceptor.PayEncryptInterceptor;
import com.meituan.android.pay.debugkit.utils.network.retrofit.interceptor.PayFormAdditionInterceptor;
import com.meituan.android.pay.debugkit.utils.network.retrofit.interceptor.PayQueryAdditionInterceptor;
import com.sankuai.meituan.retrofit2.PayCallAdapterFactory;
import com.sankuai.meituan.retrofit2.Retrofit;
import com.sankuai.meituan.retrofit2.converter.gson.GsonConverterFactory;

/**
 * <AUTHOR>
 */
public class PayRetrofit {
    private static final MapCache<String, Retrofit> HOST_RETROFIT_CACHE =
            MapCache.init(host -> new Retrofit.Builder()
                    .baseUrl(host)
                    .callFactory(PayCallFactory.create())
                    .addCallAdapterFactory(PayCallAdapterFactory.create())
                    .addConverterFactory(GsonConverterFactory.create(GsonProvider.get()))
                    .addInterceptor(new PayQueryAdditionInterceptor())
                    .addInterceptor(new PayFormAdditionInterceptor())
                    .addInterceptor(new PayEncryptInterceptor())
                    .build());

    public static Retrofit get() {
        return HOST_RETROFIT_CACHE.cache(PayProvider.pay().getHost());
    }

    public static Retrofit get(String host) {
        return HOST_RETROFIT_CACHE.cache(host);
    }
}