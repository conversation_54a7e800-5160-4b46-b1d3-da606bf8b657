package com.meituan.android.pay.debugkit.env;

import com.meituan.android.pay.debugkit.DebugManager;
import com.meituan.android.pay.debugkit.capacity.Jumper;
import com.meituan.android.pay.debugkit.main.activity.PayDemoActivity;
import com.meituan.android.pay.debugkit.utils.router.OpenUtils;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * 环境切换优化跳转器
 * 扩展现有的Jumper接口，添加环境切换相关功能
 * 
 * <AUTHOR>
 * @since 2025/1/9
 */
@ServiceLoaderInterface(key = "switch-env-optimize-jumper", interfaceClass = Jumper.class)
public class SwitchEnvOptimizeJumper implements Jumper {

    /**
     * 跳转到环境切换扫码页面
     */
    public void gotoSwitchEnvScan() {
        SwitchEnvOptimizeUtils.startSwitchEnvOptimize();
    }

    /**
     * 跳转到环境切换配置页面
     */
    public void gotoSwitchEnvConfig() {
        DebugManager.startActivity(PayDemoActivity.fragment(SwitchEnvOptimizeFragment.class));
    }

    /**
     * 跳转到环境状态查看页面
     */
    public void gotoEnvironmentStatus() {
        DebugManager.startActivity(PayDemoActivity.fragment(SwitchEnvOptimizeFragment.class, "show_status"));
    }

    /**
     * 快速切换到默认泳道
     */
    public void switchToDefaultLane() {
        SwitchEnvOptimizeUtils.getInstance().switchToLaneEnvironment("default");
    }

    /**
     * 快速启用所有调试功能
     */
    public void enableAllDebugFeatures() {
        SwitchEnvOptimizeUtils utils = SwitchEnvOptimizeUtils.getInstance();
        
        // 启用标准收银台
        utils.enableStandardCashier();
        
        // 启用Mock环境
        utils.enableMockEnvironment();
        
        // 切换到默认泳道
        utils.switchToLaneEnvironment("default");
    }

    /**
     * 重置所有环境设置
     */
    public void resetAllEnvironments() {
        SwitchEnvOptimizeUtils.getInstance().resetAllEnvironmentSettings();
    }

    /**
     * 检查当前是否为调试环境
     */
    public boolean isInDebugEnvironment() {
        return SwitchEnvOptimizeUtils.isDebugEnvironment();
    }

    /**
     * 检查当前是否为测试环境
     */
    public boolean isInTestEnvironment() {
        return SwitchEnvOptimizeUtils.isTestEnvironment();
    }

    /**
     * 获取当前环境描述
     */
    public String getCurrentEnvironmentDescription() {
        SwitchEnvOptimizeUtils.EnvironmentStatus status = 
            SwitchEnvOptimizeUtils.getInstance().getCurrentEnvironmentStatus();
        
        StringBuilder sb = new StringBuilder();
        sb.append("泳道: ").append(status.currentLanePath);
        
        if (status.isStandardCashierEnabled) {
            sb.append(" | 标准收银台");
        }
        
        if (status.isHybridMockEnabled) {
            sb.append(" | Hybrid Mock");
        }
        
        if (status.isHornDebugEnabled) {
            sb.append(" | Horn调试");
        }
        
        if (status.isMockEnvironmentEnabled) {
            sb.append(" | Mock环境");
        }
        
        return sb.toString();
    }
}
