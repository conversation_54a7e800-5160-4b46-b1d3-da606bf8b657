package com.meituan.android.pay.debugkit.main.paylater.model;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.paybase.utils.JsonBean;
import com.meituan.android.paybase.utils.Strings;

import java.io.Serializable;

/**
 * Created by lvshuhua on 2018/4/18.
 */

@JsonBean
public class TradeOrderVO implements Serializable {
    private static final long serialVersionUID = -7242507102867383173L;

    @SerializedName("trans_status")
    private String transStatusContent; // 交易状态：支付成功/新建等

    @SerializedName("transStatus")
    private int transStatus; // 交易状态，枚举值

    @SerializedName("seller_id")
    private long sellerId; // 下单业务识别码

    @SerializedName("order_id")
    private String orderId; // 业务订单号

    @SerializedName("order_subject")
    private String orderSubject; // 订单标题

    @SerializedName("order_body")
    private String orderBody; //订单详情

    @SerializedName("tradeNo")
    private String tradeNo; // 交易流水号

    @SerializedName("outNo")
    private String outNo; // 支付成功的支付流水号

    @SerializedName("creditOutNo")
    private String creditOutNo; // 赊销流水号

    @SerializedName("trade_model")
    private String tradeModelContent; // 交易模式---可以拼接到订单标题前

    @SerializedName("tradeModel")
    private int tradeModel;

    @SerializedName("transCancelFund")
    private long transCancelFund; // 撤销金额

    @SerializedName("transSuccessTime")
    private int transSuccessTime; // 交易成功时间

    @SerializedName("subSellerId")
    private Long subSellerId; // 子商户号

    @SerializedName("transFeeCent")
    private Long transFeeCent; // 下单金额

    @SerializedName("repaymentTradeNo")
    private String repaymentTradeNo; // 还款交易单号

    public static String getPayResultByStatus(int status) {
        return OrderInfo.getPayResultByStatus(status);
    }

    public String getTransStatusContent() {
        return transStatusContent;
    }

    public void setTransStatusContent(String transStatusContent) {
        this.transStatusContent = transStatusContent;
    }

    public int getTransStatus() {
        return transStatus;
    }

    public void setTransStatus(int transStatus) {
        this.transStatus = transStatus;
    }

    public long getSellerId() {
        return sellerId;
    }

    public void setSellerId(long sellerId) {
        this.sellerId = sellerId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderSubject() {
        return orderSubject;
    }

    public void setOrderSubject(String orderSubject) {
        this.orderSubject = orderSubject;
    }

    public String getOrderBody() {
        return orderBody;
    }

    public void setOrderBody(String orderBody) {
        this.orderBody = orderBody;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getOutNo() {
        return outNo;
    }

    public void setOutNo(String outNo) {
        this.outNo = outNo;
    }

    public String getCreditOutNo() {
        return creditOutNo;
    }

    public void setCreditOutNo(String creditOutNo) {
        this.creditOutNo = creditOutNo;
    }

    public String getTradeModelContent() {
        return tradeModelContent;
    }

    public void setTradeModelContent(String tradeModelContent) {
        this.tradeModelContent = tradeModelContent;
    }

    public int getTradeModel() {
        return tradeModel;
    }

    public void setTradeModel(int tradeModel) {
        this.tradeModel = tradeModel;
    }

    public long getTransCancelFund() {
        return transCancelFund;
    }

    public String getTransCancelFundString() {
        return Strings.getFormattedDoubleValueWithZero((double) transCancelFund / 100) + " 元";
    }

    public void setTransCancelFund(long transCancelFund) {
        this.transCancelFund = transCancelFund;
    }

    public int getTransSuccessTime() {
        return transSuccessTime;
    }

    public void setTransSuccessTime(int transSuccessTime) {
        this.transSuccessTime = transSuccessTime;
    }

    public Long getSubSellerId() {
        return subSellerId;
    }

    public void setSubSellerId(Long subSellerId) {
        this.subSellerId = subSellerId;
    }

    public Long getTransFeeCent() {
        return transFeeCent;
    }

    public String getTransFeeCentString() {
        return Strings.getFormattedDoubleValueWithZero((double) transFeeCent / 100) + " 元";
    }

    public void setTransFeeCent(Long transFeeCent) {
        this.transFeeCent = transFeeCent;
    }

    public String getRepaymentTradeNo() {
        return repaymentTradeNo;
    }

    public void setRepaymentTradeNo(String repaymentTradeNo) {
        this.repaymentTradeNo = repaymentTradeNo;
    }

    public boolean showComplete() {
        return transStatus == 4 || transStatus == 8;
    }

    public boolean showCancel() {
        return transStatus == 4 || transStatus == 8 || transStatus == 6 || transStatus == 10;
    }

    public boolean showRefund() {
        return transStatus == 4 || transStatus == 8 || transStatus == 6 || transStatus == 10 || transStatus == 16 || transStatus == 98 || transStatus == 99;
    }

    public boolean showRepayment() {
        return transStatus == 98;
    }
}
