package com.meituan.android.pay.debugkit.main.setting;

import android.arch.lifecycle.MutableLiveData;

import com.meituan.android.pay.debugkit.main.base.PayDemoViewModel;
import com.meituan.android.pay.debugkit.main.data.DataProvider;
import com.meituan.android.pay.debugkit.main.data.model.PassportState;
import com.meituan.android.pay.debugkit.utils.rx.RxLifeSubscriber;

/**
 * <AUTHOR>
 */
public class SettingViewModel extends PayDemoViewModel {
    private final MutableLiveData<PassportState> passportState = new MutableLiveData<>();

    public SettingViewModel() {
        DataProvider.setting()
                .getPassportState()
                .subscribe(new RxLifeSubscriber<PassportState>(this) {
                    @Override
                    public void onNext(PassportState state) {
                        passportState.setValue(state);
                    }
                });
    }

    public MutableLiveData<PassportState> passportState() {
        return passportState;
    }
}
