package com.meituan.android.pay.debugkit.utils.network.retrofit.interceptor;

import android.support.annotation.Keep;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.pay.base.utils.serialize.GsonUtils;
import com.meituan.android.paybase.utils.CashierSessionIdUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Keep
public class PayFormAdditionParams implements Serializable {
    private static final List<String> BLACKLIST_PATH =
            Collections.singletonList("/api/mpm/member/getorcreatemember");

    private static final List<String> BLACKLIST_KEYS =
            Collections.singletonList("nb_location");

    @SerializedName("nb_channel")
    private String nbChannel;

    @SerializedName("nb_platform")
    private String nbPlatform;

    @SerializedName("nb_osversion")
    private String nbOSVersion;

    @SerializedName("nb_version")
    private String nbVersion;

    @SerializedName("nb_location")
    private String nbLocation;

    @SerializedName("nb_ci")
    private String nbCi;

    @SerializedName("nb_deviceid")
    private String nbDeviceId;

    @SerializedName("nb_uuid")
    private String nbUUID;

    @SerializedName("nb_app")
    private String nbApp;

    @SerializedName("nb_appversion")
    private String nbAppVersion;

    @SerializedName("nb_device_model")
    private String nbDeviceModel;

    @SerializedName("token")
    private String token;

    @SerializedName("cashierSessionId")
    private String cashierSessionId;

    public String getNbChannel() {
        return nbChannel;
    }

    public String getNbPlatform() {
        return nbPlatform;
    }

    public String getNbOSVersion() {
        return nbOSVersion;
    }

    public String getNbVersion() {
        return nbVersion;
    }

    public String getNbLocation() {
        return nbLocation;
    }

    public String getNbCi() {
        return nbCi;
    }

    public String getNbDeviceId() {
        return nbDeviceId;
    }

    public String getNbUUID() {
        return nbUUID;
    }

    public String getNbApp() {
        return nbApp;
    }

    public String getNbAppVersion() {
        return nbAppVersion;
    }

    public String getNbDeviceModel() {
        return nbDeviceModel;
    }

    public String getToken() {
        return token;
    }

    public String getCashierSessionId() {
        return cashierSessionId;
    }

    public static List<String> keyBlackList(String path) {
        if (!BLACKLIST_PATH.contains(path)) {
            return new ArrayList<>();
        } else {
            return BLACKLIST_KEYS;
        }
    }

    public static PayFormAdditionParams newInstance() {
        PayFormAdditionParams params = new PayFormAdditionParams();
        params.token = PayProvider.app().getToken();
        params.nbCi = PayProvider.app().getCityId();
        params.nbChannel = PayProvider.app().getChannel();
        params.nbPlatform = PayProvider.app().getPlatform();
        params.nbLocation = PayProvider.app().getLocation();
        params.nbUUID = PayProvider.app().getUUID();
        params.nbApp = PayProvider.app().getAppName();
        params.nbAppVersion = PayProvider.app().getAppVersionName();
        params.nbOSVersion = PayProvider.app().getOSVersion();
        params.nbDeviceId = PayProvider.app().getDeviceId();
        params.nbDeviceModel = PayProvider.app().getDeviceModel();
        params.nbVersion = PayProvider.pay().getPaySdkVersion();
        params.cashierSessionId = CashierSessionIdUtil.getCashierSessionId();
        return params;
    }

    public Map<String, String> toForm() {
        return GsonUtils.toStringMap(this);
    }

}
