package com.meituan.android.pay.debugkit;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.support.annotation.NonNull;

import com.meituan.android.cipstorage.CIPStorageCenter;
import com.meituan.android.pay.base.context.PayProvider;
import com.meituan.android.pay.debugkit.capacity.DebugInit;
import com.meituan.android.pay.debugkit.utils.ui.UIThreadHandler;
import com.meituan.android.paycommon.lib.IInitSDK;
import com.sankuai.meituan.serviceloader.ServiceLoader;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import java.util.List;

/**
 * <AUTHOR>
 */
@ServiceLoaderInterface(key = "pay-debug", interfaceClass = IInitSDK.class, singleton = true)
public class DebugManager implements IInitSDK {
    private static final String VERSION = "1";
    private static final DebugInfo INFO = new DebugInfo();

    @Override
    public void onInit(@NonNull Context context) {
        UIThreadHandler.get().post(() -> {
            List<DebugInit> debugInitList = ServiceLoader.load(DebugInit.class, "");
            for (DebugInit debugInit : debugInitList) {
                debugInit.onInit(context, INFO);
            }
        });
        if (!DebugManager.getStorage().getBoolean("hasLaunch", false)) {
            DebugManager.getStorage().setBoolean("hasLaunch", true);
            INFO.setFirstLaunch(true);
        }
    }

    public static void startActivity(Intent intent) {
        Activity activity = getActivity();
        if (activity != null) {
            activity.startActivity(intent);
        }
    }

    public static Activity getActivity() {
        return DebugMonitor.getActivity();
    }

    public static Application getApplication() {
        return PayProvider.getApplication();
    }

    public static CIPStorageCenter getStorage() {
        return PayProvider.module().getStorage("pay-debug-" + VERSION);
    }

    public static boolean isOffline() {
        String host = PayProvider.pay().getHost();
        return host.contains(".test.") || host.contains(".dev.");
    }

    public static boolean isStage() {
        String host = PayProvider.pay().getHost();
        return host.contains(".st.");
    }

    public static String getOfflineString() {
        return isOffline() ? "线下" : "线上";
    }

    public static DebugInfo info() {
        return INFO;
    }
}
