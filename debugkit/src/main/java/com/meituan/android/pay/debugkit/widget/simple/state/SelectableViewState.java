package com.meituan.android.pay.debugkit.widget.simple.state;

import android.support.annotation.Keep;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import com.meituan.android.pay.base.container.PayBaseFragment;
import com.meituan.android.pay.base.utils.function.Consumer;
import com.meituan.android.pay.debugkit.databinding.PayDebugSimpleSelectableBinding;
import com.meituan.android.pay.debugkit.utils.mvvm.BindingActions;
import com.meituan.android.pay.debugkit.utils.mvvm.LiveDataUtils;
import com.meituan.android.pay.debugkit.utils.struct.Stream;
import com.meituan.android.pay.debugkit.widget.simple.base.SimpleViewState;
import com.meituan.android.pay.debugkit.widget.simple.dialog.SimpleAdditionDialog;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import java.util.List;

/**
 * <AUTHOR>
 */
@Keep
@ServiceLoaderInterface(key = "selectable", interfaceClass = SimpleViewState.class)
public class SelectableViewState extends TitleViewState {

    protected List<SelectionViewState> selections;

    protected AdditionViewState addition;

    @Override
    public View inflate(PayBaseFragment fragment) {
        PayDebugSimpleSelectableBinding binding = PayDebugSimpleSelectableBinding
                .inflate(fragment.getLayoutInflater(), (ViewGroup) fragment.getView(), false);
        BindingActions.setValueVisibility(binding.titleLayout, getTitle());
        binding.title.setText(getTitle());
        binding.selections.removeAllViews();
        for (SelectionViewState viewState : selections) {
            addSelection(fragment, binding, viewState);
        }
        addAddition(fragment, binding);
        observeNewSelection(fragment, binding);
        return binding.getRoot();
    }

    protected void addSelection(PayBaseFragment fragment, PayDebugSimpleSelectableBinding binding, SelectionViewState viewState) {
        View view = viewState.inflate(fragment);
        binding.selections.addView(view);
        LiveDataUtils.observe(fragment, getValue(), viewState::onSelected);
        LiveDataUtils.observe(fragment, viewState.getInvokedMethod(), Consumer.onPredicate(
                simpleMethod -> "set".equals(simpleMethod.getName()),
                simpleMethod -> setValue(simpleMethod.first())));
        LiveDataUtils.observe(fragment, viewState.getInvokedMethod(), Consumer.onPredicate(
                simpleMethod -> "delete".equals(simpleMethod.getName()),
                simpleMethod -> {
                    selections.remove(viewState);
                    binding.selections.removeView(view);
                }
        ));
    }

    protected void addAddition(PayBaseFragment fragment, PayDebugSimpleSelectableBinding binding) {
        if (addition != null) {
            binding.additional.addView(addition.inflate(fragment));
            LiveDataUtils.observe(fragment, addition.getInvokedMethod(), Consumer.onPredicate(
                    simpleMethod -> "add".equals(simpleMethod.getName()),
                    simpleMethod -> new SimpleAdditionDialog.Builder(fragment.getActivity())
                            .setTitle(addition.getTitle())
                            .setSelectionTitle(addition.getSelectionTitle())
                            .setSelectionText(addition.getSelectionText())
                            .setNegativeButton("取消")
                            .setPositiveButton("确认", (title, text) -> addNewSelection(fragment, binding, title, text))
                            .create()
                            .show()));
        }
    }

    protected void observeNewSelection(PayBaseFragment fragment, PayDebugSimpleSelectableBinding binding) {
        LiveDataUtils.observe(fragment, getValue(), val -> {
            boolean hasSelection = Stream.on(selections).filter(viewState ->
                    TextUtils.equals(viewState.getVal(), val)).first() != null;
            if (!hasSelection) {
                addNewSelection(fragment, binding, "新内容", val);
            }
        });
    }

    protected void addNewSelection(PayBaseFragment fragment, PayDebugSimpleSelectableBinding binding,
                                   String title, String text) {
        SelectionViewState viewState = new SelectionViewState();
        viewState.setTitle(title);
        viewState.setText(text);
        viewState.setValue(text);
        selections.add(viewState);
        addSelection(fragment, binding, viewState);
    }
}
