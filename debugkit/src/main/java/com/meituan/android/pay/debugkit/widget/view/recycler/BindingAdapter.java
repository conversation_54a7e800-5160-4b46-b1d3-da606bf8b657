package com.meituan.android.pay.debugkit.widget.view.recycler;

import android.support.annotation.NonNull;
import android.support.v7.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.viewbinding.ViewBinding;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class BindingAdapter<V extends ViewBinding, ViewState> extends
        RecyclerView.Adapter<BindingHolder<V>> {

    private final List<ViewState> data = new ArrayList<>();
    private Inflater<V> inflater;
    private Binder<V, ViewState> binder;

    @Override
    public BindingHolder<V> onCreateViewHolder(ViewGroup parent, int viewType) {
        LayoutInflater layoutInflater = LayoutInflater.from(parent.getContext());
        V viewDataBinding = inflater.inflate(layoutInflater, parent);
        return new BindingHolder<>(viewDataBinding);
    }

    @Override
    public void onBindViewHolder(BindingHolder<V> holder, int position) {
        binder.bind(holder.binding(), data.get(position));
    }

    @Override
    public int getItemCount() {
        if (inflater != null && binder != null) {
            return data.size();
        }
        return 0;
    }

    public BindingAdapter<V, ViewState> setInflater(@NonNull Inflater<V> inflater) {
        this.inflater = inflater;
        return this;
    }

    public BindingAdapter<V, ViewState> setBinder(@NonNull Binder<V, ViewState> binder) {
        this.binder = binder;
        return this;
    }

    public BindingAdapter<V, ViewState> add(@NonNull ViewState viewState) {
        this.data.add(viewState);
        return this;
    }

    public BindingAdapter<V, ViewState> add(@NonNull List<ViewState> viewStates) {
        this.data.addAll(viewStates);
        return this;
    }

    public BindingAdapter<V, ViewState> clear() {
        this.data.clear();
        return this;
    }

    public void notifyRender() {
        notifyItemRangeChanged(0, getItemCount());
    }

    public static <V extends ViewBinding, ViewState> BindingAdapter<V, ViewState> init(@NonNull Inflater<V> inflater,
                                                                                       @NonNull Binder<V, ViewState> binder) {
        BindingAdapter<V, ViewState> adapter = new BindingAdapter<>();
        adapter.setInflater(inflater);
        adapter.setBinder(binder);
        return adapter;
    }

}
