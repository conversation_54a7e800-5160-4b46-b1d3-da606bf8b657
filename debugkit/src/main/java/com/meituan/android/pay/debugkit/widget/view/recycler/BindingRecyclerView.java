package com.meituan.android.pay.debugkit.widget.view.recycler;

import android.content.Context;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v7.widget.RecyclerView;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.viewbinding.ViewBinding;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class BindingRecyclerView<V extends ViewBinding, ViewState> extends RecyclerView {
    private final Adapter adapter;

    public BindingRecyclerView(Context context) {
        super(context);
        setAdapter(adapter = new Adapter());
    }

    public BindingRecyclerView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        setAdapter(adapter = new Adapter());
    }

    public BindingRecyclerView(Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        setAdapter(adapter = new Adapter());
    }

    public Adapter adapter() {
        return adapter;
    }

    public static class Holder<V extends ViewBinding> extends RecyclerView.ViewHolder {
        private final V viewDataBinding;

        public Holder(V viewDataBinding) {
            super(viewDataBinding.getRoot());
            this.viewDataBinding = viewDataBinding;
        }

        public V binding() {
            return viewDataBinding;
        }
    }

    public class Adapter extends RecyclerView.Adapter<Holder<V>> {
        private final List<ViewState> viewStates = new ArrayList<>();

        private ViewInflater<V> viewInflater;
        private ViewBinder<V, ViewState> viewBinder;

        @NonNull
        @Override
        public Holder<V> onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            return new Holder<>(viewInflater.inflate(LayoutInflater.from(parent.getContext()), parent));
        }

        @Override
        public void onBindViewHolder(@NonNull Holder<V> holder, int position) {
            viewBinder.bind(holder.binding(), viewStates.get(position));
        }

        @Override
        public int getItemCount() {
            if (ready()) {
                return viewStates.size();
            }
            return 0;
        }

        private boolean ready() {
            return viewInflater != null && viewBinder != null;
        }

        public Adapter setViewInflater(ViewInflater<V> bindingInflater) {
            this.viewInflater = bindingInflater;
            return this;
        }

        public Adapter setViewBinder(ViewBinder<V, ViewState> viewBinder) {
            this.viewBinder = viewBinder;
            return this;
        }

        public Adapter add(ViewState viewState) {
            this.viewStates.add(viewState);
            return this;
        }

        public Adapter add(List<ViewState> viewStates) {
            this.viewStates.addAll(viewStates);
            return this;
        }

        public Adapter clear() {
            this.viewStates.clear();
            return this;
        }

        public void notifyRender() {
            notifyItemRangeChanged(0, getItemCount());
        }

    }

    public interface ViewInflater<V extends ViewBinding> {
        V inflate(LayoutInflater inflater, ViewGroup parent);
    }

    public interface ViewBinder<V extends ViewBinding, ViewState> {
        void bind(V binding, ViewState viewState);
    }

}
