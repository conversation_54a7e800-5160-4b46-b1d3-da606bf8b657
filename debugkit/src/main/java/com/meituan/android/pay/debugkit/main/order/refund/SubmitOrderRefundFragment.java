package com.meituan.android.pay.debugkit.main.order.refund;

import android.os.Bundle;
import android.support.annotation.Nullable;

import com.meituan.android.pay.debugkit.main.data.model.OrderInfo;
import com.meituan.android.pay.debugkit.main.base.PayDemoFragment;
import com.meituan.android.pay.debugkit.main.activity.PayDemoActivity;
import com.meituan.android.pay.debugkit.utils.serialize.DebugGsonProvider;

/**
 * <AUTHOR>
 */
public class SubmitOrderRefundFragment extends PayDemoFragment {

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        String businessData = PayDemoActivity.getBusinessData(getIntent());
        if (businessData == null) {
            finish();
            return;
        }
        OrderInfo orderInfo = DebugGsonProvider.get().from<PERSON>son(businessData, OrderInfo.class);
        sharedViewState.update("subject", orderInfo.getSubject());
        sharedViewState.update("tradeNo", orderInfo.getTradeNo());
        sharedViewState.update("payType", orderInfo.getPayType().content());
        sharedViewState.update("money", orderInfo.getMoney());
        sharedViewState.update("orderTime", orderInfo.getOrderTimeFormatted());
        sharedViewState.update("payTime", orderInfo.getPayTimeFormatted());
    }

    @Override
    public String viewFileName() {
        return "view_order_refund.json";
    }
}
