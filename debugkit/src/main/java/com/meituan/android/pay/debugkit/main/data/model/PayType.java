package com.meituan.android.pay.debugkit.main.data.model;

import android.support.annotation.Keep;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import com.google.gson.annotations.JsonAdapter;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 */
@Keep
@JsonAdapter(PayType.Adapter.class)
public enum PayType {
    CREDIT(35, "信用付"),
    PLATFORM(66, "平台支付"),
    BALANCE(255, "余额支付"),
    FOREIGN(301, "国际卡"),
    UNKNOWN(999, "未知");

    private final int code;
    private final String message;

    PayType(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int code() {
        return code;
    }

    public String message() {
        return message;
    }

    public String content() {
        return message + "-" + code;
    }

    public static PayType of(int code) {
        for (PayType payType : PayType.values()) {
            if (payType.code == code) {
                return payType;
            }
        }
        return UNKNOWN;
    }

    public static class Adapter implements JsonDeserializer<PayType>, JsonSerializer<PayType> {

        @Override
        public PayType deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
            return PayType.of(json.getAsNumber().intValue());
        }

        @Override
        public JsonElement serialize(PayType src, Type typeOfSrc, JsonSerializationContext context) {
            return new JsonPrimitive(src.code);
        }
    }
}
