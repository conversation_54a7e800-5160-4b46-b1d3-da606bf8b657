package com.meituan.android.pay.debugkit.widget.old;

import android.content.Context;
import android.content.res.TypedArray;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.meituan.android.paybase.utils.TransferUtils;
import com.meituan.android.paybase.widgets.agreement.AgreementBean;
import com.meituan.android.paybase.widgets.agreement.AgreementView;
import com.meituan.android.pay.debugkit.R;


/**
 * Created by zhongcheng on 2017/12/21.
 */

public class CellView extends RelativeLayout implements CompoundButton.OnCheckedChangeListener, View.OnClickListener {

    /**
     * 是否使用上分割线
     */
    private boolean useTopDivider;

    /**
     * 左标题
     */
    private String title;

    /**
     * 副标题
     */
    private String assistantTitle;

    /**
     * 标题下协议
     */
    private AgreementBean agreement;

    /**
     * 是否展示checkbox
     */
    private boolean useCheckBox;

    /**
     * 输入框提示文案
     */
    private String hintText;

    private String editContent;

    private boolean useEditText;

    /**
     * 左图标资源
     */
    private int leftIconResId;

    /**
     * 右图标资源
     */
    private int rightIconResId;

    /**
     * 右端描述文案
     */
    private String description;

    /**
     * 是否展示右箭头
     */
    private boolean showRightArrow;

    /**
     * 是否展示红点
     */
    private boolean showRedAlert;

    /**
     * checkbox选中状态
     */
    private boolean checkBoxChecked;


    private View topDivider;

    private TextView titleTextView;

    private TextView assistantTitleTextView;

    private AgreementView agreementView;

    private TextView descTextView;

    private ImageView leftImageView;

    private ImageView rightArrow;

    private ImageView rightImageView;

    private EditText editText;

    private CheckBox checkBox;

    private ImageView redDot;

    private CompoundButton.OnCheckedChangeListener checkedChangeListener;

    private OnClickListener checkBoxListener;

    private Button button;

    private String buttonText;

    private OnClickListener buttonClickListener;

    CellView(Context context, boolean useTopDivider, String title, String assistantTitle, AgreementBean agreement, boolean useCheckBox, String hintText, int leftIconResId, int
            rightIconResId, String description, boolean showRightArrow, boolean showRedAlert, boolean
                     checkBoxChecked, CompoundButton.OnCheckedChangeListener checkedChangeListener, OnClickListener

                     checkBoxListener) {
        super(context);
        this.useTopDivider = useTopDivider;
        this.title = title;
        this.assistantTitle = assistantTitle;
        this.agreement = agreement;
        this.useCheckBox = useCheckBox;
        this.hintText = hintText;
        this.leftIconResId = leftIconResId;
        this.rightIconResId = rightIconResId;
        this.description = description;
        this.showRightArrow = showRightArrow;
        this.showRedAlert = showRedAlert;
        this.checkBoxChecked = checkBoxChecked;
        this.checkedChangeListener = checkedChangeListener;
        this.checkBoxListener = checkBoxListener;
        init();
    }

    public CellView(Context context) {
        super(context);
        init();
    }


    public CellView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        getAttr(context, attrs);
        init();
        bindDataView();
    }


    public CellView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        getAttr(context, attrs);
        init();
        bindDataView();
    }


    private void getAttr(Context context, AttributeSet attrs) {
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.paybase__Cell);
        useTopDivider = ta.getBoolean(R.styleable.paybase__Cell_useTopDivider, false);
        title = ta.getString(R.styleable.paybase__Cell_title);
        assistantTitle = ta.getString(R.styleable.paybase__Cell_assistantTitle);
        useCheckBox = ta.getBoolean(R.styleable.paybase__Cell_useCheckBox, false);
        hintText = ta.getString(R.styleable.paybase__Cell_hintText);
        leftIconResId = ta.getResourceId(R.styleable.paybase__Cell_leftIcon, 0);
        rightIconResId = ta.getResourceId(R.styleable.paybase__Cell_rightIcon, 0);
        description = ta.getString(R.styleable.paybase__Cell_description);
        showRedAlert = ta.getBoolean(R.styleable.paybase__Cell_showRedAlert, false);
        showRightArrow = ta.getBoolean(R.styleable.paybase__Cell_showRightArrow, false);
        checkBoxChecked = ta.getBoolean(R.styleable.paybase__Cell_checkBoxChecked, false);
        ta.recycle();
    }


    private void init() {
        inflate(getContext(), R.layout.old_cell_view, this);
        topDivider = findViewById(R.id.top_divider);
        titleTextView = findViewById(R.id.title);
        assistantTitleTextView = findViewById(R.id.assistant_title);
        agreementView = findViewById(R.id.agreement);
        descTextView = findViewById(R.id.desc);
        leftImageView = findViewById(R.id.leftIcon);
        rightArrow = findViewById(R.id.rightArrow);
        rightImageView = findViewById(R.id.rightIcon);
        editText = findViewById(R.id.editText);
        checkBox = findViewById(R.id.cellview_checkbox);
        button = findViewById(R.id.btn_confirm);
        redDot = findViewById(R.id.red_dot);
        checkBox.setOnCheckedChangeListener(this);
        checkBox.setOnClickListener(this);
    }

    void bindDataView() {
        if (!isAttachedToWindow) {
            return;
        }
        //上分割线
        if (useTopDivider) {
            topDivider.setVisibility(VISIBLE);
        } else {
            topDivider.setVisibility(GONE);
        }
        //标题
        if (!TextUtils.isEmpty(title)) {
            titleTextView.setText(title);
            titleTextView.setMinWidth(TransferUtils.dip2px(getContext(), 70));
            if (titleColor != 0) {
                try {
                    titleTextView.setTextColor(titleColor);
                } catch (Exception ignored) {
                }
            }
        }
        //副标题
        if (!TextUtils.isEmpty(assistantTitle)) {
            assistantTitleTextView.setVisibility(VISIBLE);
            assistantTitleTextView.setText(assistantTitle);
        } else {
            assistantTitleTextView.setVisibility(GONE);
        }
        //标题下协议
        if (agreement != null) {
            agreementView.setVisibility(VISIBLE);
            agreementView.setAgreement(agreement);
            ViewGroup.LayoutParams params = getLayoutParams();
            params.width = ViewGroup.LayoutParams.MATCH_PARENT;
            params.height = TransferUtils.dip2px(getContext(), 90);
            setLayoutParams(params);

        } else {
            agreementView.setVisibility(GONE);
        }

        //左图标
        if (leftIconResId != 0) {
            leftImageView.setVisibility(VISIBLE);
            leftImageView.setImageResource(leftIconResId);
        } else {
            leftImageView.setVisibility(GONE);
        }
        //输入框
        if (useEditText) {
            editText.setText(editContent);
            editText.setHint(hintText);
            editText.setVisibility(VISIBLE);
        } else {
            editText.setVisibility(GONE);
        }
        if (buttonClickListener != null) {
            button.setVisibility(VISIBLE);
            button.setText(buttonText);
            button.setOnClickListener(buttonClickListener);
        }

        //开关与右边文案不同时出现
        if (useCheckBox) {
            checkBox.setChecked(checkBoxChecked);
            findViewById(R.id.rightContainer).setVisibility(GONE);
            findViewById(R.id.cellview_checkbox).setVisibility(VISIBLE);
            findViewById(R.id.btn_confirm).setVisibility(GONE);
        } else {
            findViewById(R.id.rightContainer).setVisibility(VISIBLE);
            findViewById(R.id.cellview_checkbox).setVisibility(GONE);

            if (showRightArrow) {
                rightArrow.setVisibility(VISIBLE);
            } else {
                rightArrow.setVisibility(GONE);
            }

            //描述文案与由右图标不同时出现
            if (!TextUtils.isEmpty(description)) {
                descTextView.setText(description);
                if (showRedAlert) {
                    redDot.setVisibility(VISIBLE);
                } else {
                    redDot.setVisibility(GONE);
                }
            } else if (rightIconResId != 0) {
                rightImageView.setImageResource(rightIconResId);
                rightImageView.setVisibility(VISIBLE);

            }
        }

    }

    private boolean isAttachedToWindow;

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        isAttachedToWindow = true;
        ViewGroup.LayoutParams params = getLayoutParams();
        //如果 CellView 指定了具体的高度 dp 值，则保留不变；如果使用的是 MATCH_PARENT 或 WRAP_CONTENT，则指定高度为 50dp.
        if (params.height == ViewGroup.LayoutParams.MATCH_PARENT || params.height == ViewGroup.LayoutParams.WRAP_CONTENT) {
            params.height = TransferUtils.dip2px(getContext(), dip > 0 ? dip : 50);
        }
        params.width = ViewGroup.LayoutParams.MATCH_PARENT;
        setLayoutParams(params);
        setBackgroundColor(getResources().getColor(R.color.paybase__white));
        bindDataView();
    }

    public void hideRedDot() {
        showRedAlert = false;
        bindDataView();
    }

    public void showRedDot() {
        showRedAlert = true;
        bindDataView();
    }

    public String getContent() {
        return editText.getText().toString();
    }

    public boolean isCheckBoxChecked() {
        return checkBox.isChecked();
    }

    public boolean isUseCheckBox() {
        return this.useCheckBox;
    }

    public void setCheckBoxStatus(boolean checked) {
        checkBoxChecked = checked;
        bindDataView();
    }

    public void setCheckBoxClickListener(OnClickListener l) {
        this.checkBoxListener = l;
    }

    public void setOnCheckedChangeListener(CompoundButton.OnCheckedChangeListener listener) {

        this.checkedChangeListener = listener;
    }

    public void setUseTopDivider(boolean useTopDivider) {
        this.useTopDivider = useTopDivider;
        bindDataView();
    }

    public void setTitle(String title) {
        this.title = title;
        bindDataView();
    }

    public void setAssistantTitle(String assistantTitle) {
        this.assistantTitle = assistantTitle;
        bindDataView();
    }

    public void setAgreement(AgreementBean agreement) {
        this.agreement = agreement;
        bindDataView();
    }

    public void setUseEditText(boolean useEditText) {
        this.useEditText = useEditText;
        bindDataView();
    }

    public void setUseCheckBox(boolean useCheckBox) {
        this.useCheckBox = useCheckBox;
        bindDataView();
    }

    public void setCheckBoxEnabled(boolean enabled){
        this.checkBox.setEnabled(enabled);
        bindDataView();
    }

    public void setHintText(String hintText) {
        this.hintText = hintText;
        bindDataView();
    }

    public void setEditText(String editContent) {
        this.editContent = editContent;
        bindDataView();
    }

    public void setLeftIconResId(int leftIconResId) {
        this.leftIconResId = leftIconResId;
        bindDataView();
    }

    public void setRightIconResId(int rightIconResId) {
        this.rightIconResId = rightIconResId;
        bindDataView();

    }

    public void setDescription(String description) {
        this.description = description;
        bindDataView();
    }

    public void setDescTextViewVis(int visibility) {
        descTextView.setVisibility(visibility);
    }

    public void setShowRightArrow(boolean showRightArrow) {
        this.showRightArrow = showRightArrow;
        bindDataView();
    }

    public void setShowRedAlert(boolean showRedAlert) {
        this.showRedAlert = showRedAlert;
        bindDataView();
    }

    private int dip;

    public void setHeight(int dip) {
        this.dip = dip;
    }

    private int titleColor;

    public void setTitleColor(int titleColor) {
        this.titleColor = titleColor;
        bindDataView();
    }

    public void setButtonClick(String buttonText, OnClickListener onClickListener) {
        this.buttonText = buttonText;
        this.buttonClickListener = onClickListener;
        bindDataView();
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        checkBoxChecked = isChecked;
        if (checkedChangeListener != null) {
            checkedChangeListener.onCheckedChanged(buttonView, isChecked);
        }
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.cellview_checkbox) {
            if (checkBoxListener != null) {
                checkBoxListener.onClick(v);
            }
        }
    }

    public static class Builder {
        CellView cellView;
        private Context context;

        /**
         * 是否使用上分割线
         */
        private boolean useTopDivider;

        /**
         * 左标题
         */
        private String title;

        /**
         * 副标题
         */
        private String assistantTitle;

        /**
         * 标题下协议
         */
        private AgreementBean agreement;

        /**
         * 是否展示checkbox
         */
        private boolean useCheckBox;

        /**
         * 输入框提示文案
         */
        private String hintText;

        /**
         * 左图标资源
         */
        private int leftIconResId;

        /**
         * 右图标资源
         */
        private int rightIconResId;

        /**
         * 右端描述文案
         */
        private String description;

        /**
         * 是否展示右箭头
         */
        private boolean showRightArrow;

        /**
         * 是否展示红点
         */
        private boolean showRedAlert;

        /**
         * checkbox初始状态
         */
        private boolean checkBoxChecked;

        private CompoundButton.OnCheckedChangeListener checkedChangeListener;

        private OnClickListener checkBoxListener;


        public Builder(Context context) {
            this.context = context;
        }

        public Builder setUseTopDivider(boolean useTopDivider) {
            this.useTopDivider = useTopDivider;
            return this;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setAssistantTitle(String assistantTitle) {
            this.assistantTitle = assistantTitle;
            return this;
        }

        public Builder setAgreement(AgreementBean agreement) {
            this.agreement = agreement;
            return this;
        }

        public Builder setUseCheckBox(boolean useCheckBox) {
            this.useCheckBox = useCheckBox;
            return this;
        }

        public Builder setHintText(String hint) {
            this.hintText = hint;
            return this;
        }

        public Builder setLeftIconResId(int resId) {
            this.leftIconResId = resId;
            return this;
        }

        public Builder setRightIconResId(int resId) {
            this.rightIconResId = resId;
            return this;
        }

        public Builder setDescription(String desc) {
            this.description = desc;
            return this;
        }

        public Builder setShowRightArrow(boolean showRightArrow) {
            this.showRightArrow = showRightArrow;
            return this;
        }

        public Builder setShowRedAlert(boolean showRedAlert) {
            this.showRedAlert = showRedAlert;
            return this;
        }

        public Builder setCheckBoxChecked(boolean checked) {
            this.checkBoxChecked = checked;
            return this;
        }

        public Builder setCheckBoxClickListener(OnClickListener l) {
            this.checkBoxListener = l;
            return this;
        }

        public Builder setOnCheckedChangeListener(CompoundButton.OnCheckedChangeListener listener) {
            this.checkedChangeListener = listener;
            return this;
        }

        public CellView build() {
            cellView = new CellView(context, useTopDivider, title, assistantTitle, agreement, useCheckBox, hintText, leftIconResId, rightIconResId,
                    description, showRightArrow, showRedAlert, checkBoxChecked, checkedChangeListener, checkBoxListener);
            cellView.bindDataView();
            return cellView;
        }
    }
}
