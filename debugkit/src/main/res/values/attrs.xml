<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="BounceScrollView">
        <attr name="damping" format="float" />
        <attr name="scrollOrientation" format="enum">
            <enum name="vertical" value="0" />
            <enum name="horizontal" value="1" />
        </attr>
        <attr name="incrementalDamping" format="boolean" />
        <attr name="bounceDelay" format="integer" />
        <attr name="triggerOverScrollThreshold" format="integer" />
        <attr name="disableBounce" format="boolean" />
        <attr name="nestedScrollingEnabled" format="boolean" />
    </declare-styleable>
    <declare-styleable name="pay_debug_powerful_recyclerview">
        <attr name="refresh_distance" format="dimension" />
        <attr name="max_to_pull" format="dimension" />
        <attr name="back_top_duration" format="integer" />
        <attr name="position_to_show" format="integer" />
        <attr name="NoDataView" format="reference" />
        <attr name="isSwipeToRefresh" format="boolean" />
    </declare-styleable>

    <declare-styleable name="pay_debug_common_spin_loading_view">
        <!--转圈部分-->
        <attr name="spinWidth" format="dimension" />
        <attr name="spinColor" format="color" />
        <attr name="spinLength" format="float" /><!--角度值-->

        <!--背景圆环的宽度-->
        <attr name="bgWidth" format="dimension" />
        <attr name="bgColor" format="color" />

        <!--动画相关-->
        <attr name="spinSpeed" format="float" />
        <attr name="delayMillis" format="integer" />
    </declare-styleable>
</resources>