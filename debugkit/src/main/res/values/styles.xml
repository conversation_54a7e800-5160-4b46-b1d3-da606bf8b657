<resources>

    <style name="cashier_demo_text_view_title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="cashier_demo_edit_view_content">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="cashier_demo_layout_for_item">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:orientation">horizontal</item>
    </style>

    <style name="MainActivityButton" parent="android:Widget.DeviceDefault.Button.Borderless">
        <item name="android:textColor">@color/mtpaysdk__button_textcolor</item>
        <item name="android:layout_marginTop">20dp</item>
        <item name="android:layout_marginBottom">20dp</item>
        <item name="android:paddingTop">10dp</item>
        <item name="android:paddingBottom">10dp</item>
        <item name="android:background">@drawable/mtpaysdk__bg_button</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:padding">0dp</item>
        <item name="android:textSize">12dp</item>
    </style>

    <style name="pay_debug_activity_theme" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@null</item>
    </style>

    <style name="pay_debug_simple_title_start">
        <item name="android:layout_width">100dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/pay_debug_text</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="pay_debug_simple_title_top">
        <item name="android:layout_width">100dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:gravity">center</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/pay_debug_text</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="pay_debug_simple_linearlayout_horizontal" parent="pay_debug_simple_linearlayout">
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="pay_debug_simple_linearlayout_vertical" parent="pay_debug_simple_linearlayout">
        <item name="android:orientation">vertical</item>
        <item name="android:gravity">center_horizontal</item>
    </style>

    <style name="pay_debug_simple_linearlayout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingStart">15dp</item>
        <item name="android:paddingEnd">15dp</item>
    </style>

    <style name="pay_debug_simple_divider">
        <item name="android:background">@drawable/pay_debug_divider_item</item>
    </style>

    <!--<style name="MainActivityLabel" parent="@style/cashier__order_label" />-->
</resources>
