<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/paybase__white">

    <TextView
        android:id="@+id/demo_hybrid_title"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_alignParentTop="true"
        android:background="@color/pay_debug_white"
        android:gravity="center"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:text="后付工具"
        android:textColor="@color/pay_debug_black"
        android:textSize="18dp"
        tools:ignore="HardcodedText,SpUsage" />

    <View
        android:id="@+id/demo_hybrid_divider"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_below="@+id/demo_hybrid_title"
        android:background="@color/pay_debug_black2" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/demo_hybrid_divider"
        android:background="@color/pay_debug_white">

        <LinearLayout
            android:id="@+id/demo_pay_later_settings"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:divider="@drawable/demo_hybrid_divider_vertical"
            android:orientation="vertical"
            android:showDividers="middle">
        </LinearLayout>
    </ScrollView>

    <LinearLayout
        android:id="@+id/demo_pay_later_buttons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
    </LinearLayout>

</RelativeLayout>