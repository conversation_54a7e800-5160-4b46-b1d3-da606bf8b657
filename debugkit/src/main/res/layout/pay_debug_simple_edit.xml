<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:background="@color/pay_debug_white"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="15dp"
    android:paddingEnd="0dp">

    <TextView
        android:id="@+id/title"
        android:layout_width="100dp"
        android:layout_height="match_parent"
        android:gravity="center_vertical|start"
        android:singleLine="true"
        android:textColor="@color/pay_debug_black"
        android:textSize="16sp"
        tools:text="标题" />

    <com.meituan.android.pay.debugkit.widget.view.FixedEditText
        android:id="@+id/edit"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="15dp"
        android:layout_weight="1"
        android:importantForAutofill="no"
        android:singleLine="true"
        android:textColor="@color/pay_debug_black"
        android:textSize="16sp"
        tools:ignore="LabelFor"
        tools:text="内容" />

    <com.meituan.android.pay.debugkit.widget.view.FixedCheckBox
        android:id="@+id/checkbox"
        android:layout_width="30dp"
        android:layout_height="24dp"
        android:layout_marginEnd="15dp"
        android:checked="true"
        android:visibility="gone" />

</LinearLayout>
