<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
             xmlns:progress="http://schemas.android.com/apk/res-auto"
             android:layout_width="match_parent"
             android:layout_height="wrap_content"
             android:background="@color/paybase__background_color">

    <com.meituan.android.pay.debugkit.widget.old.CommonSpinLoadingView
        android:id="@+id/refreshing_icon"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_gravity="center"
        android:layout_marginBottom="20dp"
        android:layout_marginTop="20dp"
        progress:bgColor="@color/paybase__background_color"
        progress:bgWidth="1.5dp"
        progress:delayMillis="20"
        progress:spinColor="@color/paybase__divider_color1"
        progress:spinLength="160"
        progress:spinSpeed="10"
        progress:spinWidth="1.5dp" />

</FrameLayout>