<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:background="@color/pay_debug_white"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/title"
        android:layout_width="100dp"
        android:layout_height="match_parent"
        android:layout_marginEnd="15dp"
        android:gravity="center_vertical|start"
        android:singleLine="true"
        android:textColor="@color/pay_debug_black"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/text"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginEnd="15dp"
        android:layout_weight="1"
        android:gravity="center_vertical|start"
        android:singleLine="true"
        android:textColor="@color/pay_debug_text_sub"
        android:textSize="16sp" />

    <ImageView
        android:id="@+id/tail_img"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:layout_marginEnd="15dp"
        android:background="@drawable/mpay__halfpage_bank_selected"
        android:visibility="gone" />

</LinearLayout>