<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    android:layout_alignParentBottom="true"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:custom="http://schemas.android.com/apk/res-auto"
    android:id="@+id/head_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/paybase__background_color">



    <com.meituan.android.pay.debugkit.widget.old.CommonSpinLoadingView
        android:layout_alignParentBottom="true"

        android:id="@+id/refreshing_icon"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_centerHorizontal="true"
        custom:bgColor="@color/paybase__divider_color"
        custom:bgWidth="1.5dp"
        custom:delayMillis="20"
        custom:spinColor="@color/paybase__base_green"
        custom:spinLength="160"
        custom:spinSpeed="10"
        custom:spinWidth="1.5dp" />



    <TextView
        android:id="@+id/text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:textColor="@color/paybase__text_color_3"
        android:textSize="13sp"/>



</RelativeLayout>