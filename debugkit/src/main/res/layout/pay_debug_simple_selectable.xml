<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/pay_debug_white"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/title_layout"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:background="@color/pay_debug_background_primary"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="15dp">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical|start"
            android:singleLine="true"
            android:textColor="@color/pay_debug_text"
            android:textSize="14sp"
            tools:text="标题" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/selections"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:divider="@drawable/pay_debug_divider_item"
        android:orientation="vertical"
        android:paddingStart="15dp"
        android:paddingEnd="0dp"
        android:showDividers="middle" />

    <LinearLayout
        android:id="@+id/additional"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:divider="@drawable/pay_debug_divider_item"
        android:orientation="vertical"
        android:paddingStart="15dp"
        android:paddingEnd="0dp"
        android:showDividers="beginning" />

</LinearLayout>
