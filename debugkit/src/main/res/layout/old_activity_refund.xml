<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/main_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="15dp"
        tools:context="com.meituan.android.pay.debugkit.paylater.OrderDetailsActivity">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="40dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="订单名称"
                android:textColor="@color/pay_debug_black" />

            <TextView
                android:id="@+id/order_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:textColor="@color/textColorSecondary"
                tools:text="测试单标题" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/pay_debug_black1" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="40dp">

            <TextView
                android:id="@+id/order_no_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="订单编号"
                android:textColor="@color/pay_debug_black" />

            <TextView
                android:id="@+id/order_no"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:textColor="@color/textColorSecondary"
                tools:text="2200022XX" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/pay_debug_black1" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="40dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="付款类型"
                android:textColor="@color/pay_debug_black" />

            <TextView
                android:id="@+id/pay_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:textColor="@color/textColorSecondary"
                tools:text="测试单标题" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/pay_debug_black1" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="40dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="金额"
                android:textColor="@color/pay_debug_black" />

            <EditText
                android:id="@+id/order_money"
                android:layout_width="150dp"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:gravity="center_vertical|right"
                android:inputType="numberDecimal"
                tools:text="0.01" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/pay_debug_black1" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="40dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="下单时间"
                android:textColor="@color/pay_debug_black" />

            <TextView
                android:id="@+id/order_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:textColor="@color/textColorSecondary"
                tools:text="2020-04-27" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/pay_debug_black1" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="40dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="支付时间"
                android:textColor="@color/pay_debug_black" />

            <TextView
                android:id="@+id/pay_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:textColor="@color/textColorSecondary"
                tools:text="支付时间" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/pay_debug_black1" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="40dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="状态"
                android:textColor="@color/pay_debug_black" />

            <TextView
                android:id="@+id/order_state"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:textColor="@color/textColorSecondary"
                tools:text="16" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/pay_debug_black1" />

        <LinearLayout
            android:id="@+id/ll_refund_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingLeft="15dp"
            android:paddingRight="15dp">

            <Button
                android:id="@+id/btn_refund_new"
                style="@style/MainActivityButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="点击退款" />

        </LinearLayout>

        <Button
            android:id="@+id/btn_add_param"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:background="@color/pay_debug_transparent"
            android:padding="5dp"
            android:text="添加参数"
            android:textColor="@color/paydemo__color"
            android:textSize="16sp"
            tools:visibility="visible" />

    </LinearLayout>

</ScrollView>

