<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="45dp"
    android:background="@color/pay_debug_white"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="15dp"
    android:paddingEnd="15dp">

    <TextView
        android:id="@+id/title"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:textColor="@color/pay_debug_black"
        android:textSize="14sp"
        android:textStyle="bold"
        tools:text="标题" />

    <View
        android:layout_width="6dp"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:singleLine="true"
        android:textColor="@color/pay_debug_black"
        android:textSize="14sp"
        android:textStyle="bold"
        tools:text="内容" />

</LinearLayout>