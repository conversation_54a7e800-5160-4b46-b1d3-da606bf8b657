<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/paybase__background_color"
        android:fillViewport="true"
        tools:ignore="UselessParent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">


                <com.meituan.android.paybase.widgets.keyboard.SafeEditText
                    android:id="@+id/set1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:hint="纯数字"
                    android:inputType="number"
                    app:safeEditTextType="1" />

                <com.meituan.android.paybase.widgets.keyboard.SafeEditText
                    android:id="@+id/set2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:hint="带点数字"
                    android:inputType="numberDecimal"
                    app:safeEditTextType="2" />

            </LinearLayout>

            <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">


                <com.meituan.android.paybase.widgets.keyboard.SafeEditText
                    android:id="@+id/set3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:hint="身份证"
                    app:safeEditTextType="3" />

                <com.meituan.android.paybase.widgets.keyboard.SafeEditText
                    android:id="@+id/set4"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:hint="普通键盘" />
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</LinearLayout>