<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/paybase__white">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:paddingEnd="24dp"
        android:paddingRight="24dp">

        <TextView
            android:id="@+id/tab_item_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:textColor="@color/paybase__text_color_3"
            android:textSize="14sp"
            tools:text="美食" />

        <ImageView
            android:id="@+id/tab_item_underline"
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:layout_alignParentBottom="true"
            android:scaleType="centerCrop"
            android:src="@drawable/tab_item_divider"
            android:visibility="gone" />
    </RelativeLayout>
</merge>