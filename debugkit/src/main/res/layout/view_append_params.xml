<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/cashier_demo_layout_for_item"
    android:layout_marginTop="5dp"
    android:gravity="center_vertical">

    <EditText
        android:id="@+id/edit_key"
        style="@style/cashier_demo_edit_view_content"
        android:hint="参数名" />

    <TextView
        style="@style/cashier_demo_text_view_title"
        android:text=" ：" />

    <EditText
        android:id="@+id/edit_value"
        style="@style/cashier_demo_edit_view_content"
        android:hint="参数值" />

    <Button
        android:id="@+id/btn_remove_param"
        android:layout_width="70dp"
        android:layout_height="40dp"
        android:background="@drawable/paydemo__bg_button"
        android:text="移除"
        android:textColor="@color/pay_debug_white" />
</LinearLayout>
