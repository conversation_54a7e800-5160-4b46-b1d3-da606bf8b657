<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/pay_debug_background_white"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/pay_debug_text"
        android:textSize="18sp"
        android:textStyle="bold" />

    <EditText
        android:id="@+id/selection_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="24dp" />

    <EditText
        android:id="@+id/selection_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="24dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/negative"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:background="?android:attr/selectableItemBackground"
            android:textColor="@color/pay_debug_text_sub" />

        <Button
            android:id="@+id/positive"
            android:layout_width="0dp"
            android:gravity="center"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:background="?android:attr/selectableItemBackground"
            android:textColor="@color/pay_debug_text" />

    </LinearLayout>
</LinearLayout>