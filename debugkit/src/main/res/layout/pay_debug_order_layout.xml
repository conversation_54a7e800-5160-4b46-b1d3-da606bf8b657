<?xml version="1.0" encoding="utf-8"?>
    <LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/main"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/pay_debug_white"
        android:baselineAligned="false"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/money"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:paddingStart="15dp"
            android:paddingEnd="0dp">

            <TextView
                android:id="@+id/money_symbol"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="¥ "
                android:textColor="@color/pay_debug_primary"
                android:textSize="18sp" />

            <TextView
                android:id="@+id/money_number"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:textColor="@color/pay_debug_text"
                android:textSize="18sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/submit"
            android:layout_width="wrap_content"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/submit_order"
                android:layout_width="140dp"
                android:layout_height="match_parent"
                android:background="@color/pay_debug_primary"
                android:gravity="center"
                android:text="提交订单"
                android:textColor="@color/pay_debug_text"
                android:textSize="18sp" />
        </LinearLayout>

    </LinearLayout>
