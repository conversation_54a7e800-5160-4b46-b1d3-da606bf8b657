<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="75dp"
    android:background="@color/pay_debug_white"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingLeft="15dp"
    android:paddingRight="15dp">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:layout_marginTop="5dp"
        android:singleLine="true"
        android:textColor="@color/pay_debug_black"
        android:textSize="14sp"
        android:textStyle="bold"
        tools:text="名字" />

    <TextView
        android:id="@+id/content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/title"
        android:layout_alignParentStart="true"
        android:layout_marginTop="5dp"
        android:singleLine="true"
        android:textColor="@color/pay_debug_black"
        android:textSize="14sp"
        tools:text="内容" />

    <TextView
        android:id="@+id/money"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="5dp"
        android:textColor="@color/pay_debug_red"
        android:textSize="14sp"
        tools:text="￥1.0" />

    <TextView
        android:id="@+id/status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="5dp"
        android:textColor="@color/pay_debug_red"
        android:textSize="14sp"
        tools:text="支付成功" />

    <TextView
        android:id="@+id/date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignTop="@+id/money"
        android:textColor="@color/pay_debug_black"
        android:textSize="14sp"
        tools:text="2020-04-28" />

    <View
        style="@style/paybase__horizonal_divider"
        android:layout_height="0.5dp"
        android:layout_alignParentBottom="true" />
</RelativeLayout>