<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/passport"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:background="@color/pay_debug_white"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="10dp"
    android:paddingEnd="0dp">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/image"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:scaleType="centerCrop"
            android:src="@drawable/pay_debug_user_icon_default" />

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="20dp"
            android:paddingEnd="0dp"
            android:textColor="@color/pay_debug_tertiary" />

        <TextView
            android:id="@+id/id"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="20dp"
            android:paddingEnd="0dp"
            android:textColor="@color/pay_debug_text" />
    </LinearLayout>

    <TextView
        android:id="@+id/login"
        android:layout_width="100dp"
        android:layout_height="match_parent"
        android:background="@color/pay_debug_primary"
        android:gravity="center"
        android:textColor="@color/pay_debug_text"
        android:textSize="16sp" />
</LinearLayout>
