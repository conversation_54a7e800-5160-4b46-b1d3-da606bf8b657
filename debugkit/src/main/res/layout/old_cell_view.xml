<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource">

    <View
        android:id="@+id/top_divider"
        style="@style/paybase__horizonal_divider"
        android:layout_marginLeft="15dp" />

    <LinearLayout
        android:id="@+id/cellview_solid"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="15dp"
        android:paddingRight="15dp">

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_vertical">

            <ImageView
                android:id="@+id/leftIcon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="8dp"
                android:visibility="gone"
                tools:background="@color/paybase__base_green"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toEndOf="@id/leftIcon"
                android:textColor="@color/paybase__text_color_1"
                android:textSize="15sp"
                tools:ignore="RelativeOverlap"
                tools:text="title" />

            <TextView
                android:id="@+id/assistant_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/title"
                android:layout_alignLeft="@id/title"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/paybase__text_color_3"
                android:textSize="13sp"
                android:visibility="gone"
                tools:text="assistantTitle"
                tools:visibility="visible" />

            <com.meituan.android.paybase.widgets.agreement.AgreementView
                android:id="@+id/agreement"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/assistant_title"
                android:layout_alignLeft="@id/assistant_title"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/paybase__text_color_3"
                android:textSize="13sp"
                android:visibility="gone"
                tools:text="agreement"
                tools:visibility="visible" />

            <EditText
                android:id="@+id/editText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="16dp"
                android:layout_toRightOf="@+id/title"
                android:inputType="text"
                android:textSize="15sp"
                android:visibility="gone"
                tools:visibility="visible" />

        </RelativeLayout>

        <com.meituan.android.paybase.widgets.DefaultCheckBox

            android:id="@+id/cellview_checkbox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible" />

        <Button
            android:id="@+id/btn_confirm"
            style="@style/cashieroneclick__button"
            android:layout_width="wrap_content"
            android:layout_height="35dp"
            android:textSize="12sp"
            android:visibility="gone"
            tools:text="确认"
            tools:visibility="visible" />

        <LinearLayout
            android:id="@+id/rightContainer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/red_dot"
                android:layout_width="6dp"
                android:layout_height="6dp"
                android:layout_marginRight="6dp"
                android:src="@drawable/red_dot"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                tools:text="miaoshu" />

            <ImageView
                android:id="@+id/rightIcon"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_marginRight="10dp"
                android:visibility="gone"
                tools:background="@color/paybase__base_green"
                tools:visibility="visible" />


            <ImageView
                android:id="@+id/rightArrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/paybase__ic_global_arrow_right" />

        </LinearLayout>
    </LinearLayout>
</merge>
