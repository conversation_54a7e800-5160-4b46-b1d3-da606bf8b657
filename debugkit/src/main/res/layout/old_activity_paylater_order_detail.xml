<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/paybase__white"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/pay_debug_white">

        <LinearLayout
            android:id="@+id/demo_pay_later_order_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:divider="@drawable/demo_hybrid_divider_vertical"
            android:orientation="vertical"
            android:showDividers="middle">
        </LinearLayout>
    </ScrollView>

    <LinearLayout
        android:id="@+id/demo_pay_later_order"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

    </LinearLayout>

</LinearLayout>