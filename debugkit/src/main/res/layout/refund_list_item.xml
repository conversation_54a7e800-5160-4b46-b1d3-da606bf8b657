<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="75dp"
    android:layout_centerVertical="true"
    android:orientation="vertical"
    android:paddingLeft="5dp"
    android:paddingRight="5dp"
    android:background="@color/pay_debug_white">

    <TextView
        android:id="@+id/order_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/pay_debug_black"
        android:textSize="14sp"
        android:textStyle="bold"
        android:singleLine="true"
        tools:text="名字" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="6dp">

        <TextView
            android:id="@+id/order_price"
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:textColor="@color/pay_debug_red"
            android:textSize="14sp"
            tools:text="￥1.0" />

        <TextView
            android:id="@+id/order_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:textColor="@color/pay_debug_red"
            android:textSize="12sp"
            tools:text="支付成功" />

    </LinearLayout>

    <TextView
        android:id="@+id/order_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/pay_debug_black"
        android:textSize="14sp"
        android:layout_marginTop="6dp"
        tools:text="2020-04-28" />

    <View
        style="@style/paybase__horizonal_divider"
        android:layout_height="0.5dp"
        android:layout_alignParentBottom="true" />

</LinearLayout>