<?xml version="1.0" encoding="utf-8"?><!--suppress AndroidDomInspection -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.meituan.android.pay.debugkit">

    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <application>

        <activity
            android:name="com.meituan.android.pay.debugkit.main.activity.PayDebugMainActivity"
            android:launchMode="singleTop"
            android:theme="@style/pay_debug_activity_theme"
            android:windowSoftInputMode="adjustPan|stateHidden">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="debugkit"
                    android:path="/main"
                    android:scheme="pay" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.meituan.android.pay.debugkit.main.activity.PayDemoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light"
            tools:ignore="AppLinkUrlError">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="debugkit"
                    android:path="/setting"
                    android:scheme="pay" />
            </intent-filter>
        </activity>

        <activity android:name="com.meituan.android.pay.debugkit.main.paylater.RefundListActivity" />
        <activity android:name="com.meituan.android.pay.debugkit.main.paylater.OrderListActivity" />
        <activity android:name="com.meituan.android.pay.debugkit.main.paylater.OrderDetailsActivity" />
        <activity android:name="com.meituan.android.pay.debugkit.main.paylater.PayLaterOrderListActivity" />
        <activity android:name="com.meituan.android.pay.debugkit.main.paylater.PayLaterOrderDetailActivity" />

        <provider
            android:name="com.meituan.android.pay.debugkit.DebugMonitor"
            android:authorities="${applicationId}.pay.debugkit.DebugMonitor"
            android:exported="false"/>
    </application>

</manifest>