dependencies {
    implementation('com.sankuai.meituan.serviceloader:serviceloader:2.2.33') {
        force = true
    }
    implementation('com.sankuai.meituan.serviceloader:annotation:2.2.30') {
        force = true
    }
    annotationProcessor 'com.sankuai.meituan.serviceloader:processor:2.2.33'


    def hornVersion = '0.3.63'
    implementation("com.meituan.android.common:horn:${hornVersion}") {
        force = true
    }
    implementation("com.meituan.android.common:horn-core:${hornVersion}") {
        force = true
    }
    implementation("com.meituan.android.common:horn-uuid:${hornVersion}") {
        force = true
    }
    implementation("com.meituan.android.common:horn-sharkpush:${hornVersion}") {
        force = true
    }
    implementation("com.meituan.android.common:horn-interface:${hornVersion}") {
        force = true
    }
    implementation("com.meituan.android.common:horn-devtools:${hornVersion}") {
        force = true
    }
    implementation("com.meituan.android.common:horn-monitor:${hornVersion}") {
        force = true
    }

    implementation('com.dianping.android.sdk:nvnetwork:7.0.31') {
        force = true
    }
    implementation('com.meituan.android.cipstorage:library:1.0.5-embed') {
        force = true
    }
    implementation('com.sankuai.meituan.kernel:net:3.0.41') {
        force = true
    }

    implementation('com.sankuai.meituan.retrofit2:retrofit-mt:1.10.17') {
        force = true
    }
    implementation('com.sankuai.meituan.retrofit2:androidadapter:1.7.9') {
        force = true
    }
    implementation('com.sankuai.meituan.retrofit2:adapter-rxjava:1.7.24') {
        force = true
    }
    implementation('com.sankuai.meituan.retrofit2:callfactory-oknv:1.8.1') {
        force = true
    }
    implementation('com.sankuai.meituan.retrofit2:callfactory-okhttp:1.8.8') {
        force = true
    }
    implementation('com.sankuai.meituan.retrofit2:callfactory-okhttp3:1.10.17') {
        force = true
    }
    implementation('com.sankuai.meituan.retrofit2:callfactory-nvnetwork:1.10.2') {
        force = true
    }
    implementation('com.sankuai.meituan.retrofit2:callfactory-urlconnection:1.9.7') {
        force = true
    }
    implementation('com.sankuai.meituan.retrofit2:cache:1.7.4') {
        force = true
    }
    implementation('com.sankuai.meituan.retrofit2:converter-gson:1.9.7') {
        force = true
    }
    implementation('com.dianping.android.sdk:networklog:2.4.14') {
        force = true
    }
    implementation('io.reactivex:rxjava:1.1.6') {
        force = true
    }
    implementation('io.reactivex:rxandroid:1.2.1') {
        force = true
    }
    implementation('com.sankuai.meituan.pylon:basemodule:3.0.49.20') {
        force = true
    }
    implementation('com.meituan.passport:library:5.113.1') {
        exclude group: 'com.android.support', module: 'appcompat-v7'
        force = true
    }
    implementation('com.meituan.passport:basemodule:5.113.1') {
        exclude group: 'com.android.support', module: 'appcompat-v7'
        force = true
    }
    implementation('com.meituan.android.yoda:library:1.18.0.216') {
        force = true
    }
    implementation('com.meituan.android.knb:knb-web:13.0.7') {
        force = true
    }
    implementation('com.meituan.android.knb:titans-debug-adapter:2.0.1') {
        force = true
    }
    implementation('com.meituan.android.knb:titans-debug-business:2.0.22') {
        force = true
    }

    implementation('android.arch.lifecycle:extensions:1.1.1')
    implementation('com.google.code.gson:gson:2.8.2') {
        force = true
    }
    implementation('com.sankuai.meituan.android:tte:1.4.1') {
        force = true
    }
    implementation('com.meituan.android.mtguard:mtguard:6.3.8') {
        force = true
    }

}