#!/usr/bin/env python3
# coding=utf-8
import os
import json
from io import open
import sys
import re
import fileinput

# N 轮测试
test_step = 1
# 工作空间-项目所在目录
work_space = ''
# 正则表达式匹配字符串双引号中的内容
str_pattern = re.compile(r'"(.*)"')
# BaseConfig.java的相对路径
BASE_CONFIG_PATH = './pay-common/src/main/java/com/meituan/android/paycommon/lib/BaseConfig.java'

# wallet 中 gradle.properties 相对路径
BASE_GRADLE_PATH = './gradle.properties'
# 常用仓库合集
__project_repository_url__ = {
    'wallet': 'ssh://*******************/payan/wallet.git'
}

# 基础库
__base_repository_url__ = {
    'pay-base': {
        'repo': 'ssh://*******************/payan/pay-base.git',
        'dependency_key': 'com.meituan.android.pay-base:library'
    },
    'fin-thirdpay-adpater': {
        'repo': 'ssh://*******************/payan/fin-thirdpay-adpater.git',
        'dependency_key': 'com.meituan.android.fin-thirdpay-adapter:library'
    },
    'identifycard-recognizer': {
        'repo': 'ssh://*******************/payan/identifycard-recognizer.git',
        'dependency_key': 'com.meituan.android.identifycard-recognizer:library'
    },
    'neo-hybrid': {
        'repo': 'ssh://*******************/payan/neo-hybrid.git',
        'dependency_key': 'com.meituan.android.neohybrid:library'
    }
}


# 下载源码并切出分支
def cloneAndCheckout(module_name, source_branch, dest_branch):
    print("正在下载源码")
    repository = __project_repository_url__.get(module_name)
    if os.path.exists(os.path.join(work_space, module_name)) is False:  # 不存在即下载
        os.system('git clone %s' % repository)
        print("源码下载完毕")
    # 进入 module 目录
    os.chdir(os.path.join(work_space, module_name))
    print("正在切出目标分支")
    os.system('git checkout %s' % source_branch)
    if test_step == 1:
        os.system('git checkout %s' % source_branch)
        os.system('git branch -D %s' % dest_branch)
        os.system('git checkout -b %s' % dest_branch)
        os.system('git push --set-upstream origin %s' % dest_branch)
    os.system('git checkout %s' % dest_branch)
    # 返回主工程目录
    print("分支已经切出")
    os.chdir(work_space)


# 更新paydemo中jaccon.json里边的teststep 为2
def changeTestStep():
    print("正在执行更新 test_step")
    jacoco_path = os.path.join(work_space, 'jacoco.json')
    print(jacoco_path)
    for line in fileinput.FileInput(jacoco_path, inplace=True):
        if line.find('test_step') >= 0:
            line = '"test_step": 3,\n'
        sys.stdout.write(line)
    os.system('git add %s' % jacoco_path)
    os.system('git commit -m "PAY-0: 修改test_step"')
    os.system('git push')


# 更新paydemo中gradle.properties里边的版本号，使用dest_branch中的版本号进行替换
def changePayDemoAppVersion(dest_branch):
    print("正在执行更新 app 版本号")
    if dest_branch is None or dest_branch == '' or len(dest_branch.split('_')) < 2:
        raise Exception("dest_branch is invalid!")
    new_version = dest_branch.split('_')[1]
    new_version_str = new_version.replace(".", "")
    gradle_path = os.path.join(work_space, 'gradle.properties')
    for line in fileinput.FileInput(gradle_path, inplace=True):
        if line.find('VERSION_NAME') >= 0:
            line = 'VERSION_NAME=%s.0.0\n' % new_version_str
        if line.find('VERSION_CODE') >= 0:
            line = 'VERSION_CODE=%s00\n' % new_version_str
        sys.stdout.write(line)
    os.system('git add %s' % gradle_path)
    os.system('git commit -m "PAY-0: 修改 paydemo 版本号"')
    os.system('git push')


# 更新wallet中gradle.properties里边的版本号，使用dest_branch中的版本号进行替换
def changeWalletVersion(module_name, dest_branch):
    print("正在执行更新 SDK 版本号")
    os.chdir(os.path.join(work_space, module_name))
    if dest_branch is None or dest_branch == '' or len(dest_branch.split('_')) < 2:
        raise Exception("dest_branch is invalid!")
    os.system('git checkout %s' % dest_branch)
    new_version = dest_branch.split('_')[1]
    new_version_str = new_version.replace(".", "")
    for line in fileinput.FileInput(BASE_GRADLE_PATH, inplace=True):
        if line.find('VERSION_NAME') >= 0:
            line = 'VERSION_NAME=%s.0.0\n' % new_version_str
        if line.find('VERSION_CODE') >= 0:
            line = 'VERSION_CODE=%s00\n' % new_version_str
        sys.stdout.write(line)
    os.system('git add %s' % BASE_GRADLE_PATH)
    os.system('git commit -m "PAY-1: 修改 wallet 组件版本号"')
    os.system('git push')
    os.chdir(work_space)


# 更新pay-common中BaseConfig里边的版本号，使用dest_branch中的版本号进行替换
def changeBaseConfigVersion(module_name, dest_branch):
    print("正在执行更新 SDK config 版本号")
    os.chdir(os.path.join(work_space, module_name))
    if dest_branch is None or dest_branch == '' or len(dest_branch.split('_')) < 2:
        raise Exception("dest_branch is invalid!")
    os.system('git checkout %s' % dest_branch)
    new_version = dest_branch.split('_')[1]
    for line in fileinput.FileInput(BASE_CONFIG_PATH, inplace=True):
        if line.find('VERSION') >= 0:
            old_version = str_pattern.findall(line)[0]
            if old_version != new_version:
                line = line.replace(old_version, new_version)
        sys.stdout.write(line)
    if old_version != new_version:
        os.system('git add %s' % BASE_CONFIG_PATH)
        os.system('git commit -m "PAY-1: 修改 pay-common 中 BaseConfig版本号"')
        os.system('git push')
    os.chdir(work_space)


if __name__ == '__main__':
    work_space = sys.argv[1]
    jacoco_path = os.path.join(work_space, 'jacoco.json')
    data = ''
    with open(jacoco_path, 'r') as jsonFile:
        data = json.load(jsonFile)
    print(data)
    if data == '' or data is None:
        raise Exception('djacoco.json data is illegal!')

    source_branch = data['main']['source_branch']
    dest_branch = data['main']['dest_branch']
    test_step = data['test_step'] if data['test_step'] is not None else 1
    if dest_branch == 'master':
        raise Exception('dest_branch can not be master')
    cloneAndCheckout("wallet", source_branch, dest_branch)
    if test_step == 1:
        changeTestStep()
        changePayDemoAppVersion(dest_branch)
        changeWalletVersion("wallet", dest_branch)
        changeBaseConfigVersion("wallet", dest_branch)
    print("脚本执行完毕!")
