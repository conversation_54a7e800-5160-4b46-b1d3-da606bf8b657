ext {
    AARDEPENDENCY = false
}

buildscript {
    repositories {
        mavenLocal()
        maven {
            url 'http://depot.sankuai.com/nexus/content/groups/public/'
            allowInsecureProtocol = true
        }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.6.21'
        classpath 'com.meituan.gradle.defensor:defensor:1.2.6'
        classpath 'com.meituan.android.common:probe:1.0.5'
        classpath 'com.sankuai.meituan.serviceloader:plugin:2.2.34'
        classpath 'com.meituan.android.loader:dynloader-uploader:1.1.2'
        classpath "digital.wup:android-maven-publish:3.4.0"
        classpath 'com.github.ben-manes:gradle-versions-plugin:0.39.0'
    }
}
allprojects {

    repositories {
        mavenLocal()
        maven {
            url 'http://depot.sankuai.com/nexus/content/groups/public/'
            allowInsecureProtocol = true
        }
        maven {
            url "http://pixel.sankuai.com/repository/mtdp"
            allowInsecureProtocol = true
        }
        maven {
            url "http://pixel.sankuai.com/repository/proxy-releases"
            allowInsecureProtocol = true
        }
    }

    tasks.withType(JavaCompile) { options.encoding = "UTF-8" }
}